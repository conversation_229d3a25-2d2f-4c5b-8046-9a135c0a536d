// Simple server with database and basic routes
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const db = require('./src/config/database');

console.log('🚀 Starting TrackNew Server with Database...');

const app = express();
const PORT = process.env.PORT || 8000;

// Basic middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    await db.authenticate();
    res.status(200).json({
      status: 'success',
      message: 'Server is running',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      database: 'connected',
      uptime: process.uptime()
    });
  } catch (error) {
    res.status(503).json({
      status: 'error',
      message: 'Service unavailable',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error.message
    });
  }
});

// API health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    await db.authenticate();
    res.status(200).json({
      status: 'success',
      message: 'API is running',
      timestamp: new Date().toISOString(),
      database: 'connected',
      uptime: process.uptime()
    });
  } catch (error) {
    res.status(503).json({
      status: 'error',
      message: 'API service unavailable',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error.message
    });
  }
});

// Test database sync
app.get('/api/test-sync', async (req, res) => {
  try {
    console.log('🔄 Testing database sync...');
    
    // Load models
    const models = require('./src/models');
    console.log('✅ Models loaded');
    
    // Test sync (without force)
    await db.sync({ alter: false });
    console.log('✅ Database sync successful');
    
    res.json({
      status: 'success',
      message: 'Database sync completed',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Database sync failed:', error.message);
    res.status(500).json({
      status: 'error',
      message: 'Database sync failed',
      error: error.message
    });
  }
});

// Start server
const startServer = async () => {
  try {
    // Test database connection
    await db.authenticate();
    console.log('✅ Database connection established successfully');

    // Start server
    app.listen(PORT, () => {
      console.log(`✅ TrackNew Server running on port ${PORT}`);
      console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
      console.log(`📡 API Health: http://localhost:${PORT}/api/health`);
      console.log(`🔧 Test Sync: http://localhost:${PORT}/api/test-sync`);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
};

// Handle errors
process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Promise Rejection:', err);
});

process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
});

startServer();
