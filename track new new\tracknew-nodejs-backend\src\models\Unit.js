const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Unit = sequelize.define('Unit', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  short_name: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: 'Short abbreviation (e.g., kg, pcs, m)'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  unit_type: {
    type: DataTypes.ENUM('weight', 'length', 'area', 'volume', 'quantity', 'time', 'temperature', 'other'),
    defaultValue: 'quantity'
  },
  base_unit_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'units',
      key: 'id'
    },
    comment: 'Reference to base unit for conversions'
  },
  conversion_factor: {
    type: DataTypes.DECIMAL(15, 6),
    defaultValue: 1.000000,
    comment: 'Conversion factor to base unit'
  },
  is_base_unit: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is a base unit'
  },
  decimal_places: {
    type: DataTypes.INTEGER,
    defaultValue: 2,
    comment: 'Number of decimal places for this unit'
  },
  symbol: {
    type: DataTypes.STRING(10),
    allowNull: true,
    comment: 'Unit symbol (e.g., ₹, $, %)'
  },
  prefix: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: 'Prefix to display before value'
  },
  suffix: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: 'Suffix to display after value'
  },
  format_pattern: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Display format pattern'
  },
  is_fractional: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether fractional values are allowed'
  },
  minimum_value: {
    type: DataTypes.DECIMAL(15, 6),
    allowNull: true,
    comment: 'Minimum allowed value'
  },
  maximum_value: {
    type: DataTypes.DECIMAL(15, 6),
    allowNull: true,
    comment: 'Maximum allowed value'
  },
  step_value: {
    type: DataTypes.DECIMAL(15, 6),
    defaultValue: 1.000000,
    comment: 'Step increment for this unit'
  },
  rounding_method: {
    type: DataTypes.ENUM('round', 'floor', 'ceil'),
    defaultValue: 'round',
    comment: 'Rounding method for calculations'
  },
  is_system_unit: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is a system-defined unit'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  usage_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of products using this unit'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'units',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['company_id', 'name'],
      unique: true
    },
    {
      fields: ['company_id', 'short_name'],
      unique: true
    },
    {
      fields: ['unit_type']
    },
    {
      fields: ['base_unit_id']
    },
    {
      fields: ['is_base_unit']
    },
    {
      fields: ['is_system_unit']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['sort_order']
    }
  ]
});

module.exports = Unit;
