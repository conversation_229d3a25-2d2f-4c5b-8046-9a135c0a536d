const { ExpenseCategory, User, Company, Expense } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all expense categories with filtering and pagination
const getExpenseCategories = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    parent_id,
    expense_type,
    is_active = true,
    is_billable,
    requires_approval,
    sort_by = 'name',
    sort_order = 'ASC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } },
      { category_code: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add filters
  if (parent_id !== undefined) {
    whereConditions.parent_id = parent_id === 'null' ? null : parent_id;
  }

  if (expense_type) {
    whereConditions.expense_type = expense_type;
  }

  if (is_active !== undefined) {
    whereConditions.is_active = is_active === 'true';
  }

  if (is_billable !== undefined) {
    whereConditions.is_billable = is_billable === 'true';
  }

  if (requires_approval !== undefined) {
    whereConditions.requires_approval = requires_approval === 'true';
  }

  // Get expense categories with associations
  const { count, rows: expenseCategories } = await ExpenseCategory.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: ExpenseCategory,
        as: 'parent',
        attributes: ['id', 'name']
      },
      {
        model: ExpenseCategory,
        as: 'children',
        attributes: ['id', 'name', 'is_active'],
        required: false
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ],
    limit: parseInt(limit),
    offset: offset,
    order: [[sort_by, sort_order.toUpperCase()]],
    distinct: true
  });

  // Calculate pagination
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      expense_categories: expenseCategories,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single expense category by ID
const getExpenseCategory = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const expenseCategory = await ExpenseCategory.findOne({
    where: {
      id,
      company_id: companyId
    },
    include: [
      {
        model: ExpenseCategory,
        as: 'parent',
        attributes: ['id', 'name', 'description']
      },
      {
        model: ExpenseCategory,
        as: 'children',
        attributes: ['id', 'name', 'description', 'is_active', 'sort_order'],
        order: [['sort_order', 'ASC'], ['name', 'ASC']]
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!expenseCategory) {
    return next(new AppError('Expense category not found', 404));
  }

  // Get recent expenses in this category
  const recentExpenses = await Expense.findAll({
    where: {
      company_id: companyId,
      expense_category_id: id
    },
    attributes: ['id', 'expense_number', 'title', 'amount', 'expense_date', 'status'],
    order: [['expense_date', 'DESC']],
    limit: 10
  });

  res.status(200).json({
    status: 'success',
    data: {
      expense_category: {
        ...expenseCategory.toJSON(),
        recent_expenses: recentExpenses
      }
    }
  });
});

// Create new expense category
const createExpenseCategory = catchAsync(async (req, res, next) => {
  const {
    name,
    description,
    category_code,
    parent_id,
    icon,
    color_code,
    expense_type = 'operational',
    is_billable = false,
    default_markup_percentage = 0,
    requires_receipt = true,
    requires_approval = false,
    approval_threshold,
    auto_approval_limit,
    approval_workflow,
    gl_account_code,
    tax_category,
    is_reimbursable = false,
    default_reimbursement_rate = 100,
    mileage_rate,
    per_diem_rate,
    daily_limit,
    monthly_limit,
    yearly_limit,
    notification_threshold,
    workflow_template,
    custom_fields = {},
    validation_rules = {},
    sort_order = 0,
    is_active = true,
    notes
  } = req.body;

  const companyId = req.user.company_id;

  // Check for duplicate name within company
  const existingCategory = await ExpenseCategory.findOne({
    where: {
      company_id: companyId,
      name: name
    }
  });

  if (existingCategory) {
    return next(new AppError('Expense category with this name already exists', 400));
  }

  // Check for duplicate code if provided
  if (category_code) {
    const existingCode = await ExpenseCategory.findOne({
      where: {
        company_id: companyId,
        category_code: category_code
      }
    });

    if (existingCode) {
      return next(new AppError('Expense category with this code already exists', 400));
    }
  }

  // Validate parent category if provided
  if (parent_id) {
    const parentCategory = await ExpenseCategory.findOne({
      where: {
        id: parent_id,
        company_id: companyId,
        is_active: true
      }
    });

    if (!parentCategory) {
      return next(new AppError('Parent category not found or inactive', 400));
    }
  }

  // Calculate level and path
  let level = 1;
  let path = name;

  if (parent_id) {
    const parentCategory = await ExpenseCategory.findByPk(parent_id);
    level = parentCategory.level + 1;
    path = `${parentCategory.path} > ${name}`;
  }

  const expenseCategory = await ExpenseCategory.create({
    company_id: companyId,
    name,
    description,
    category_code,
    parent_id,
    level,
    path,
    icon,
    color_code,
    expense_type,
    is_billable,
    default_markup_percentage,
    requires_receipt,
    requires_approval,
    approval_threshold,
    auto_approval_limit,
    approval_workflow,
    gl_account_code,
    tax_category,
    is_reimbursable,
    default_reimbursement_rate,
    mileage_rate,
    per_diem_rate,
    daily_limit,
    monthly_limit,
    yearly_limit,
    notification_threshold,
    workflow_template,
    custom_fields,
    validation_rules,
    sort_order,
    is_active,
    notes,
    created_by: req.user.id
  });

  // Fetch the created expense category with associations
  const createdExpenseCategory = await ExpenseCategory.findByPk(expenseCategory.id, {
    include: [
      {
        model: ExpenseCategory,
        as: 'parent',
        attributes: ['id', 'name']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      expense_category: createdExpenseCategory
    }
  });
});

// Update expense category
const updateExpenseCategory = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const expenseCategory = await ExpenseCategory.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!expenseCategory) {
    return next(new AppError('Expense category not found', 404));
  }

  // Check for duplicate name (excluding current category)
  if (req.body.name) {
    const existingCategory = await ExpenseCategory.findOne({
      where: {
        company_id: companyId,
        id: { [Op.ne]: id },
        name: req.body.name
      }
    });

    if (existingCategory) {
      return next(new AppError('Expense category with this name already exists', 400));
    }
  }

  // Check for duplicate code (excluding current category)
  if (req.body.category_code) {
    const existingCode = await ExpenseCategory.findOne({
      where: {
        company_id: companyId,
        id: { [Op.ne]: id },
        category_code: req.body.category_code
      }
    });

    if (existingCode) {
      return next(new AppError('Expense category with this code already exists', 400));
    }
  }

  // Validate parent category if provided
  if (req.body.parent_id) {
    // Cannot set self as parent
    if (req.body.parent_id === parseInt(id)) {
      return next(new AppError('Category cannot be its own parent', 400));
    }

    const parentCategory = await ExpenseCategory.findOne({
      where: {
        id: req.body.parent_id,
        company_id: companyId,
        is_active: true
      }
    });

    if (!parentCategory) {
      return next(new AppError('Parent category not found or inactive', 400));
    }
  }

  // Update path if name or parent changed
  if (req.body.name || req.body.parent_id !== undefined) {
    const name = req.body.name || expenseCategory.name;
    let path = name;
    let level = 1;

    if (req.body.parent_id) {
      const parentCategory = await ExpenseCategory.findByPk(req.body.parent_id);
      level = parentCategory.level + 1;
      path = `${parentCategory.path} > ${name}`;
    }

    req.body.path = path;
    req.body.level = level;
  }

  // Update expense category
  await expenseCategory.update({
    ...req.body,
    updated_by: req.user.id
  });

  // Fetch updated expense category with associations
  const updatedExpenseCategory = await ExpenseCategory.findByPk(id, {
    include: [
      {
        model: ExpenseCategory,
        as: 'parent',
        attributes: ['id', 'name']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      expense_category: updatedExpenseCategory
    }
  });
});

// Delete expense category
const deleteExpenseCategory = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const expenseCategory = await ExpenseCategory.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!expenseCategory) {
    return next(new AppError('Expense category not found', 404));
  }

  // Check if category has child categories
  const childCount = await ExpenseCategory.count({
    where: {
      parent_id: id,
      company_id: companyId
    }
  });

  if (childCount > 0) {
    return next(new AppError(`Cannot delete category. It has ${childCount} sub-categories. Please delete or reassign them first.`, 400));
  }

  // Check if category has expenses
  const expenseCount = await Expense.count({
    where: {
      expense_category_id: id,
      company_id: companyId
    }
  });

  if (expenseCount > 0) {
    return next(new AppError(`Cannot delete category. It has ${expenseCount} expense(s). Please reassign them first.`, 400));
  }

  await expenseCategory.destroy();

  res.status(200).json({
    status: 'success',
    message: 'Expense category deleted successfully'
  });
});

// Get expense category statistics
const getExpenseCategoryStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  // Total categories count
  const totalCategories = await ExpenseCategory.count({
    where: { company_id: companyId }
  });

  // Active categories count
  const activeCategories = await ExpenseCategory.count({
    where: {
      company_id: companyId,
      is_active: true
    }
  });

  // Categories by expense type
  const categoriesByType = await ExpenseCategory.findAll({
    where: { company_id: companyId },
    attributes: [
      'expense_type',
      [ExpenseCategory.sequelize.fn('COUNT', ExpenseCategory.sequelize.col('id')), 'count']
    ],
    group: ['expense_type'],
    raw: true
  });

  // Billable categories
  const billableCategories = await ExpenseCategory.count({
    where: {
      company_id: companyId,
      is_billable: true
    }
  });

  // Categories requiring approval
  const approvalCategories = await ExpenseCategory.count({
    where: {
      company_id: companyId,
      requires_approval: true
    }
  });

  // Most used categories
  const mostUsedCategories = await ExpenseCategory.findAll({
    where: { company_id: companyId },
    include: [{
      model: Expense,
      as: 'expenses',
      attributes: []
    }],
    attributes: [
      'id',
      'name',
      'color_code',
      'icon',
      [ExpenseCategory.sequelize.fn('COUNT', ExpenseCategory.sequelize.col('expenses.id')), 'expense_count'],
      [ExpenseCategory.sequelize.fn('SUM', ExpenseCategory.sequelize.col('expenses.amount')), 'total_amount']
    ],
    group: ['ExpenseCategory.id'],
    order: [[ExpenseCategory.sequelize.fn('COUNT', ExpenseCategory.sequelize.col('expenses.id')), 'DESC']],
    limit: 5,
    raw: false
  });

  // Average markup percentage
  const avgMarkup = await ExpenseCategory.findOne({
    where: {
      company_id: companyId,
      default_markup_percentage: { [Op.ne]: null }
    },
    attributes: [
      [ExpenseCategory.sequelize.fn('AVG', ExpenseCategory.sequelize.col('default_markup_percentage')), 'avg_markup']
    ],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_categories: totalCategories,
      active_categories: activeCategories,
      inactive_categories: totalCategories - activeCategories,
      categories_by_type: categoriesByType,
      billable_categories: billableCategories,
      approval_categories: approvalCategories,
      most_used_categories: mostUsedCategories,
      average_markup: parseFloat(avgMarkup?.avg_markup || 0).toFixed(2)
    }
  });
});

// Get expense category tree
const getExpenseCategoryTree = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const categories = await ExpenseCategory.findAll({
    where: {
      company_id: companyId,
      parent_id: null,
      is_active: true
    },
    include: [
      {
        model: ExpenseCategory,
        as: 'children',
        where: { is_active: true },
        required: false,
        include: [
          {
            model: ExpenseCategory,
            as: 'children',
            where: { is_active: true },
            required: false
          }
        ]
      }
    ],
    order: [
      ['sort_order', 'ASC'],
      ['name', 'ASC'],
      [{ model: ExpenseCategory, as: 'children' }, 'sort_order', 'ASC'],
      [{ model: ExpenseCategory, as: 'children' }, 'name', 'ASC']
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      categories
    }
  });
});

// Get active expense categories (for dropdowns)
const getActiveExpenseCategories = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const categories = await ExpenseCategory.findAll({
    where: {
      company_id: companyId,
      is_active: true
    },
    attributes: [
      'id',
      'name',
      'category_code',
      'color_code',
      'icon',
      'expense_type',
      'is_billable',
      'default_markup_percentage',
      'requires_receipt',
      'requires_approval',
      'approval_threshold'
    ],
    order: [['sort_order', 'ASC'], ['name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      categories
    }
  });
});

// Get categories by type
const getCategoriesByType = catchAsync(async (req, res) => {
  const { expense_type } = req.params;
  const companyId = req.user.company_id;

  const categories = await ExpenseCategory.findAll({
    where: {
      company_id: companyId,
      expense_type: expense_type,
      is_active: true
    },
    attributes: ['id', 'name', 'description', 'color_code', 'icon', 'is_billable', 'requires_approval'],
    order: [['sort_order', 'ASC'], ['name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      categories,
      expense_type
    }
  });
});

// Bulk update sort order
const updateSortOrder = catchAsync(async (req, res, next) => {
  const { categories } = req.body;
  const companyId = req.user.company_id;

  if (!Array.isArray(categories) || categories.length === 0) {
    return next(new AppError('Categories array is required', 400));
  }

  // Validate all categories belong to the company
  const categoryIds = categories.map(cat => cat.id);
  const validCategories = await ExpenseCategory.findAll({
    where: {
      id: { [Op.in]: categoryIds },
      company_id: companyId
    },
    attributes: ['id']
  });

  if (validCategories.length !== categoryIds.length) {
    return next(new AppError('Some categories not found or do not belong to your company', 400));
  }

  // Update sort orders
  const updatePromises = categories.map(cat =>
    ExpenseCategory.update(
      {
        sort_order: cat.sort_order,
        updated_by: req.user.id
      },
      {
        where: {
          id: cat.id,
          company_id: companyId
        }
      }
    )
  );

  await Promise.all(updatePromises);

  res.status(200).json({
    status: 'success',
    message: 'Sort order updated successfully'
  });
});

module.exports = {
  getExpenseCategories,
  getExpenseCategory,
  createExpenseCategory,
  updateExpenseCategory,
  deleteExpenseCategory,
  getExpenseCategoryStats,
  getExpenseCategoryTree,
  getActiveExpenseCategories,
  getCategoriesByType,
  updateSortOrder
};
