const express = require('express');
const { body, query } = require('express-validator');
const settingsController = require('../controllers/settingsController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for updating settings
const updateSettingsValidation = [
  body('settings')
    .notEmpty()
    .withMessage('Settings object is required')
    .isObject()
    .withMessage('Settings must be a valid JSON object'),
  
  body('settings.general.company_name')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Company name must be between 2 and 255 characters'),
  
  body('settings.general.email')
    .optional()
    .isEmail()
    .withMessage('Email must be valid'),
  
  body('settings.general.phone')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Phone must be maximum 20 characters'),
  
  body('settings.general.currency')
    .optional()
    .isLength({ min: 3, max: 3 })
    .withMessage('Currency must be 3 characters (e.g., USD, EUR)'),
  
  body('settings.general.timezone')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Timezone must be maximum 50 characters'),
  
  body('settings.general.date_format')
    .optional()
    .isIn(['YYYY-MM-DD', 'DD-MM-YYYY', 'MM-DD-YYYY', 'DD/MM/YYYY', 'MM/DD/YYYY'])
    .withMessage('Invalid date format'),
  
  body('settings.general.time_format')
    .optional()
    .isIn(['12', '24'])
    .withMessage('Time format must be 12 or 24'),
  
  body('settings.general.language')
    .optional()
    .isLength({ min: 2, max: 5 })
    .withMessage('Language code must be between 2 and 5 characters'),
  
  body('settings.invoice.payment_terms')
    .optional()
    .isInt({ min: 0, max: 365 })
    .withMessage('Payment terms must be between 0 and 365 days'),
  
  body('settings.invoice.late_fee_percentage')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Late fee percentage must be between 0 and 100'),
  
  body('settings.invoice.discount_percentage')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Discount percentage must be between 0 and 100'),
  
  body('settings.service.sla_hours')
    .optional()
    .isInt({ min: 1, max: 8760 })
    .withMessage('SLA hours must be between 1 and 8760 (1 year)'),
  
  body('settings.inventory.low_stock_threshold')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Low stock threshold must be a positive integer'),
  
  body('settings.inventory.reorder_quantity')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Reorder quantity must be a positive integer'),
  
  body('settings.security.password_policy.min_length')
    .optional()
    .isInt({ min: 6, max: 128 })
    .withMessage('Password minimum length must be between 6 and 128'),
  
  body('settings.security.session_timeout')
    .optional()
    .isInt({ min: 5, max: 1440 })
    .withMessage('Session timeout must be between 5 and 1440 minutes'),
  
  body('settings.security.max_login_attempts')
    .optional()
    .isInt({ min: 3, max: 10 })
    .withMessage('Max login attempts must be between 3 and 10'),
  
  body('settings.security.lockout_duration')
    .optional()
    .isInt({ min: 5, max: 1440 })
    .withMessage('Lockout duration must be between 5 and 1440 minutes'),
  
  body('settings.backup.retention_days')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Backup retention days must be between 1 and 365')
];

// Validation for category-specific updates
const updateCategoryValidation = [
  body('settings')
    .notEmpty()
    .withMessage('Settings object is required')
    .isObject()
    .withMessage('Settings must be a valid JSON object')
];

// Validation for reset settings
const resetSettingsValidation = [
  query('category')
    .optional()
    .isIn([
      'general', 
      'business', 
      'invoice', 
      'sales', 
      'service', 
      'inventory', 
      'notifications', 
      'security', 
      'backup', 
      'integrations'
    ])
    .withMessage('Invalid settings category')
];

// Routes
router
  .route('/')
  .get(settingsController.getSettings)
  .put(updateSettingsValidation, validateRequest, restrictTo('admin', 'sub_admin'), settingsController.updateSettings);

router
  .route('/reset')
  .post(resetSettingsValidation, validateRequest, restrictTo('admin'), settingsController.resetSettings);

router
  .route('/:category')
  .get(settingsController.getSettingCategory)
  .put(updateCategoryValidation, validateRequest, restrictTo('admin', 'sub_admin'), settingsController.updateSettingCategory);

module.exports = router;
