const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Role = sequelize.define('Role', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  },
  display_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  guard_name: {
    type: DataTypes.STRING(255),
    defaultValue: 'web'
  },
  role_type: {
    type: DataTypes.ENUM('system', 'company', 'custom'),
    defaultValue: 'custom',
    comment: 'Type of role - system (global), company (per company), custom (user-defined)'
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'companies',
      key: 'id'
    },
    comment: 'Company ID for company-specific roles'
  },
  level: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: 'Role hierarchy level (1 = highest, 10 = lowest)'
  },
  is_admin: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this role has admin privileges'
  },
  is_super_admin: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this role has super admin privileges'
  },
  is_default: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is the default role for new users'
  },
  is_system_role: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is a system-defined role (cannot be deleted)'
  },
  max_users: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum number of users that can have this role'
  },
  current_users: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Current number of users with this role'
  },
  permissions_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of permissions assigned to this role'
  },
  color_code: {
    type: DataTypes.STRING(7),
    allowNull: true,
    comment: 'Hex color code for role display'
  },
  icon: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Icon class or image path'
  },
  badge_text: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Badge text to display for this role'
  },
  dashboard_route: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Default dashboard route for users with this role'
  },
  landing_page: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Default landing page after login'
  },
  menu_access: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of accessible menu items'
  },
  feature_access: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of feature access settings'
  },
  data_access_level: {
    type: DataTypes.ENUM('none', 'own', 'team', 'department', 'company', 'all'),
    defaultValue: 'own',
    comment: 'Level of data access for this role'
  },
  can_view_reports: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_export_data: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_import_data: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_manage_users: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_manage_roles: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_manage_settings: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  can_access_api: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  api_rate_limit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'API requests per minute limit'
  },
  session_timeout: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Session timeout in minutes'
  },
  ip_restrictions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of allowed IP addresses/ranges'
  },
  time_restrictions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of time-based access restrictions'
  },
  two_factor_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether 2FA is required for this role'
  },
  password_policy: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of password policy settings'
  },
  notification_settings: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of default notification settings'
  },
  workflow_permissions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of workflow-specific permissions'
  },
  approval_limits: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of approval limits for various operations'
  },
  custom_permissions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of custom permission settings'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  valid_from: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Role validity start date'
  },
  valid_until: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Role validity end date'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'roles',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['name'],
      unique: true
    },
    {
      fields: ['company_id']
    },
    {
      fields: ['guard_name']
    },
    {
      fields: ['role_type']
    },
    {
      fields: ['level']
    },
    {
      fields: ['is_admin']
    },
    {
      fields: ['is_super_admin']
    },
    {
      fields: ['is_default']
    },
    {
      fields: ['is_system_role']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['sort_order']
    }
  ]
});

module.exports = Role;
