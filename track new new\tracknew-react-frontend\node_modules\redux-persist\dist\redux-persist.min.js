!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e.ReduxPersist={})}(this,function(e){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;arguments.length>t;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?n(o,!0).forEach(function(t){r(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):n(o).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e}function i(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;i.length>n;n++)0>t.indexOf(r=i[n])&&(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;i.length>n;n++)0>t.indexOf(r=i[n])&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function u(e){return function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);e.length>t;t++)r[t]=e[t];return r}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var s="persist:",c="persist/FLUSH",a="persist/REHYDRATE",f="persist/PAUSE",l="persist/PERSIST",d="persist/PURGE",p="persist/REGISTER",y=-1;function h(e,r,n,i){var u=o({},n);return e&&"object"===t(e)&&Object.keys(e).forEach(function(t){"_persist"!==t&&r[t]===n[t]&&(u[t]=e[t])}),u}function v(e){var t,r=e.blacklist||null,n=e.whitelist||null,o=e.transforms||[],i=e.throttle||0,u="".concat(void 0!==e.keyPrefix?e.keyPrefix:s).concat(e.key),c=e.storage;t=!1===e.serialize?function(e){return e}:"function"==typeof e.serialize?e.serialize:b;var a=e.writeFailHandler||null,f={},l={},d=[],p=null,y=null;function h(){if(0===d.length)return p&&clearInterval(p),void(p=null);var e=d.shift(),r=o.reduce(function(t,r){return r.in(t,e,f)},f[e]);if(void 0!==r)try{l[e]=t(r)}catch(e){console.error("redux-persist/createPersistoid: error serializing state",e)}else delete l[e];0===d.length&&(Object.keys(l).forEach(function(e){void 0===f[e]&&delete l[e]}),y=c.setItem(u,t(l)).catch(g))}function v(e){return(!n||-1!==n.indexOf(e)||"_persist"===e)&&(!r||-1===r.indexOf(e))}function g(e){a&&a(e)}return{update:function(e){Object.keys(e).forEach(function(t){v(t)&&f[t]!==e[t]&&-1===d.indexOf(t)&&d.push(t)}),Object.keys(f).forEach(function(t){void 0===e[t]&&v(t)&&-1===d.indexOf(t)&&void 0!==f[t]&&d.push(t)}),null===p&&(p=setInterval(h,i)),f=e},flush:function(){for(;0!==d.length;)h();return y||Promise.resolve()}}}function b(e){return JSON.stringify(e)}function g(e){var t,r=e.transforms||[],n="".concat(void 0!==e.keyPrefix?e.keyPrefix:s).concat(e.key);return t=!1===e.deserialize?function(e){return e}:"function"==typeof e.deserialize?e.deserialize:m,e.storage.getItem(n).then(function(e){if(e)try{var n={},o=t(e);return Object.keys(o).forEach(function(e){n[e]=r.reduceRight(function(t,r){return r.out(t,e,o)},t(o[e]))}),n}catch(e){throw e}})}function m(e){return JSON.parse(e)}function O(e){var t=e.storage,r="".concat(void 0!==e.keyPrefix?e.keyPrefix:s).concat(e.key);return t.removeItem(r,E)}function E(e){0}var w=5e3;function P(e,t){var r=void 0!==e.version?e.version:y,n=void 0===e.stateReconciler?h:e.stateReconciler,u=e.getStoredState||g,s=void 0!==e.timeout?e.timeout:w,p=null,b=!1,m=!0,E=function(e){return e._persist.rehydrated&&p&&!m&&p.update(e),e};return function(y,h){var g=y||{},w=g._persist,P=i(g,["_persist"]);if(h.type===l){var j=!1,x=function(t,r){j||(h.rehydrate(e.key,t,r),j=!0)};if(s&&setTimeout(function(){!j&&x(void 0,Error('redux-persist: persist timed out for persist key "'.concat(e.key,'"')))},s),m=!1,p||(p=v(e)),w)return o({},t(P,h),{_persist:w});if("function"!=typeof h.rehydrate||"function"!=typeof h.register)throw Error("redux-persist: either rehydrate or register is not a function on the PERSIST action. This can happen if the action is being replayed. This is an unexplored use case, please open an issue and we will figure out a resolution.");return h.register(e.key),u(e).then(function(t){(e.migrate||function(e,t){return Promise.resolve(e)})(t,r).then(function(e){x(e)},function(e){x(void 0,e)})},function(e){x(void 0,e)}),o({},t(P,h),{_persist:{version:r,rehydrated:!1}})}if(h.type===d)return b=!0,h.result(O(e)),o({},t(P,h),{_persist:w});if(h.type===c)return h.result(p&&p.flush()),o({},t(P,h),{_persist:w});if(h.type===f)m=!0;else if(h.type===a){if(b)return o({},P,{_persist:o({},w,{rehydrated:!0})});if(h.key===e.key){var S=t(P,h),k=h.payload,I=o({},!1!==n&&void 0!==k?n(k,y,S,e):S,{_persist:o({},w,{rehydrated:!0})});return E(I)}}if(!w)return t(y,h);var R=t(P,h);return R===P?y:E(o({},R,{_persist:w}))}}var j=function(e){var t,r=e.Symbol;return"function"==typeof r?r.observable?t=r.observable:(t=r("observable"),r.observable=t):t="@@observable",t}("undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof module?module:Function("return this")()),x=function(){return Math.random().toString(36).substring(7).split("").join(".")},S={INIT:"@@redux/INIT"+x(),REPLACE:"@@redux/REPLACE"+x(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+x()}};function k(e,t,r){var n;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error("It looks like you are passing several store enhancers to createStore(). This is not supported. Instead, compose them together to a single function.");if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw Error("Expected the enhancer to be a function.");return r(k)(e,t)}if("function"!=typeof e)throw Error("Expected the reducer to be a function.");var o=e,i=t,u=[],s=u,c=!1;function a(){s===u&&(s=u.slice())}function f(){if(c)throw Error("You may not call store.getState() while the reducer is executing. The reducer has already received the state as an argument. Pass it down from the top reducer instead of reading it from the store.");return i}function l(e){if("function"!=typeof e)throw Error("Expected the listener to be a function.");if(c)throw Error("You may not call store.subscribe() while the reducer is executing. If you would like to be notified after the store has been updated, subscribe from a component and invoke store.getState() in the callback to access the latest state. See https://redux.js.org/api-reference/store#subscribe(listener) for more details.");var t=!0;return a(),s.push(e),function(){if(t){if(c)throw Error("You may not unsubscribe from a store listener while the reducer is executing. See https://redux.js.org/api-reference/store#subscribe(listener) for more details.");t=!1,a();var r=s.indexOf(e);s.splice(r,1)}}}function d(e){if(!function(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(e))throw Error("Actions must be plain objects. Use custom middleware for async actions.");if(void 0===e.type)throw Error('Actions may not have an undefined "type" property. Have you misspelled a constant?');if(c)throw Error("Reducers may not dispatch actions.");try{c=!0,i=o(i,e)}finally{c=!1}for(var t=u=s,r=0;t.length>r;r++){(0,t[r])()}return e}return d({type:S.INIT}),(n={dispatch:d,subscribe:l,getState:f,replaceReducer:function(e){if("function"!=typeof e)throw Error("Expected the nextReducer to be a function.");o=e,d({type:S.REPLACE})}})[j]=function(){var e,t=l;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new TypeError("Expected the observer to be an object.");function r(){e.next&&e.next(f())}return r(),{unsubscribe:t(r)}}})[j]=function(){return this},e},n}function I(e,t){var r=t&&t.type;return"Given "+(r&&'action "'+r+'"'||"an action")+', reducer "'+e+'" returned undefined. To ignore an action, you must explicitly return the previous state. If you want this reducer to hold no value, you can return null instead of undefined.'}function R(e){for(var t=Object.keys(e),r={},n=0;t.length>n;n++){var o=t[n];"function"==typeof e[o]&&(r[o]=e[o])}var i,u=Object.keys(r);try{!function(e){Object.keys(e).forEach(function(t){var r=e[t];if(void 0===r(void 0,{type:S.INIT}))throw Error('Reducer "'+t+"\" returned undefined during initialization. If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.");if(void 0===r(void 0,{type:S.PROBE_UNKNOWN_ACTION()}))throw Error('Reducer "'+t+"\" returned undefined when probed with a random type. Don't try to handle "+S.INIT+' or other actions in "redux/*" namespace. They are considered private. Instead, you must return the current state for any unknown actions, unless it is undefined, in which case you must return the initial state, regardless of the action type. The initial state may not be undefined, but can be null.')})}(r)}catch(e){i=e}return function(e,t){if(void 0===e&&(e={}),i)throw i;for(var n=!1,o={},s=0;u.length>s;s++){var c=u[s],a=e[c],f=(0,r[c])(a,t);if(void 0===f){var l=I(c,t);throw Error(l)}o[c]=f,n=n||f!==a}return n?o:e}}function T(e,r,n,i){var u=o({},n);return e&&"object"===t(e)&&Object.keys(e).forEach(function(i){var s;"_persist"!==i&&(r[i]===n[i]&&(u[i]=null===(s=n[i])||Array.isArray(s)||"object"!==t(s)?e[i]:o({},u[i],{},e[i])))}),u}var _={registry:[],bootstrapped:!1},A=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case p:return o({},e,{registry:[].concat(u(e.registry),[t.key])});case a:var r=e.registry.indexOf(t.key),n=u(e.registry);return n.splice(r,1),o({},e,{registry:n,bootstrapped:0===n.length});default:return e}};e.persistReducer=P,e.persistCombineReducers=function(e,t){return e.stateReconciler=void 0===e.stateReconciler?T:e.stateReconciler,P(e,R(t))},e.persistStore=function(e,t,r){var n=r||!1,i=k(A,_,t&&t.enhancer?t.enhancer:void 0),u=function(e){i.dispatch({type:p,key:e})},s=function(t,r,o){var u={type:a,payload:r,err:o,key:t};e.dispatch(u),i.dispatch(u),n&&y.getState().bootstrapped&&(n(),n=!1)},y=o({},i,{purge:function(){var t=[];return e.dispatch({type:d,result:function(e){t.push(e)}}),Promise.all(t)},flush:function(){var t=[];return e.dispatch({type:c,result:function(e){t.push(e)}}),Promise.all(t)},pause:function(){e.dispatch({type:f})},persist:function(){e.dispatch({type:l,register:u,rehydrate:s})}});return t&&t.manualPersist||y.persist(),y},e.createMigrate=function(e,t){return function(t,r){if(!t)return Promise.resolve(void 0);var n=t._persist&&void 0!==t._persist.version?t._persist.version:y;if(n===r)return Promise.resolve(t);if(n>r)return Promise.resolve(t);var o=Object.keys(e).map(function(e){return parseInt(e)}).filter(function(e){return r>=e&&e>n}).sort(function(e,t){return e-t});try{var i=o.reduce(function(t,r){return e[r](t)},t);return Promise.resolve(i)}catch(e){return Promise.reject(e)}}},e.createTransform=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=r.whitelist||null,o=r.blacklist||null;function i(e){return!(!n||-1!==n.indexOf(e))||!(!o||-1===o.indexOf(e))}return{in:function(t,r,n){return!i(r)&&e?e(t,r,n):t},out:function(e,r,n){return!i(r)&&t?t(e,r,n):e}}},e.getStoredState=g,e.createPersistoid=v,e.purgeStoredState=O,e.KEY_PREFIX=s,e.FLUSH=c,e.REHYDRATE=a,e.PAUSE=f,e.PERSIST=l,e.PURGE=d,e.REGISTER=p,e.DEFAULT_VERSION=y,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=redux-persist.min.js.map
