const express = require('express');
const { body } = require('express-validator');
const customerCategoryController = require('../controllers/customerCategoryController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating customer category
const createCustomerCategoryValidation = [
  body('category_name')
    .notEmpty()
    .withMessage('Category name is required')
    .isLength({ min: 2, max: 255 })
    .withMessage('Category name must be between 2 and 255 characters'),
  
  body('category_code')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category code must be between 1 and 50 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('color')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color must be a valid hex color (e.g., #FF0000)'),
  
  body('icon')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Icon must be maximum 100 characters'),
  
  body('discount_percentage')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Discount percentage must be between 0 and 100'),
  
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Credit limit must be a positive number'),
  
  body('payment_terms')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Payment terms must be a positive integer (days)'),
  
  body('priority_level')
    .optional()
    .isIn(['low', 'normal', 'high', 'vip'])
    .withMessage('Invalid priority level'),
  
  body('auto_assign_sales_rep')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Sales rep ID must be a positive integer'),
  
  body('auto_assign_service_rep')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Service rep ID must be a positive integer'),
  
  body('notification_preferences')
    .optional()
    .isObject()
    .withMessage('Notification preferences must be a valid JSON object'),
  
  body('service_preferences')
    .optional()
    .isObject()
    .withMessage('Service preferences must be a valid JSON object'),
  
  body('billing_preferences')
    .optional()
    .isObject()
    .withMessage('Billing preferences must be a valid JSON object'),
  
  body('custom_fields')
    .optional()
    .isObject()
    .withMessage('Custom fields must be a valid JSON object'),
  
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean'),
  
  body('is_default')
    .optional()
    .isBoolean()
    .withMessage('Is default must be a boolean'),
  
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer')
];

// Validation rules for updating customer category
const updateCustomerCategoryValidation = [
  body('category_name')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Category name must be between 2 and 255 characters'),
  
  body('category_code')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category code must be between 1 and 50 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('color')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color must be a valid hex color (e.g., #FF0000)'),
  
  body('icon')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Icon must be maximum 100 characters'),
  
  body('discount_percentage')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Discount percentage must be between 0 and 100'),
  
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Credit limit must be a positive number'),
  
  body('payment_terms')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Payment terms must be a positive integer (days)'),
  
  body('priority_level')
    .optional()
    .isIn(['low', 'normal', 'high', 'vip'])
    .withMessage('Invalid priority level'),
  
  body('auto_assign_sales_rep')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Sales rep ID must be a positive integer'),
  
  body('auto_assign_service_rep')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Service rep ID must be a positive integer'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean'),
  
  body('is_default')
    .optional()
    .isBoolean()
    .withMessage('Is default must be a boolean'),
  
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer')
];

// Validation for bulk sort order update
const updateSortOrderValidation = [
  body('categories')
    .isArray({ min: 1 })
    .withMessage('Categories array is required and must not be empty'),
  
  body('categories.*.id')
    .isInt({ min: 1 })
    .withMessage('Each category must have a valid ID'),
  
  body('categories.*.sort_order')
    .isInt({ min: 0 })
    .withMessage('Each category must have a valid sort order')
];

// Routes
router
  .route('/')
  .get(customerCategoryController.getCustomerCategories)
  .post(createCustomerCategoryValidation, validateRequest, restrictTo('admin', 'sub_admin', 'sales_manager'), customerCategoryController.createCustomerCategory);

router
  .route('/stats')
  .get(customerCategoryController.getCustomerCategoryStats);

router
  .route('/active')
  .get(customerCategoryController.getActiveCustomerCategories);

router
  .route('/sort-order')
  .put(updateSortOrderValidation, validateRequest, restrictTo('admin', 'sub_admin', 'sales_manager'), customerCategoryController.updateSortOrder);

router
  .route('/by-priority/:priority_level')
  .get(customerCategoryController.getCategoriesByPriority);

router
  .route('/:id/set-default')
  .put(restrictTo('admin', 'sub_admin'), customerCategoryController.setDefaultCategory);

router
  .route('/:id')
  .get(customerCategoryController.getCustomerCategory)
  .put(updateCustomerCategoryValidation, validateRequest, restrictTo('admin', 'sub_admin', 'sales_manager'), customerCategoryController.updateCustomerCategory)
  .delete(restrictTo('admin', 'sub_admin'), customerCategoryController.deleteCustomerCategory);

module.exports = router;
