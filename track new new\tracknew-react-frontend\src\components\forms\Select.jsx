import React, { forwardRef, Fragment } from 'react';
import { Listbox, Transition } from '@headlessui/react';
import { CheckIcon, ChevronUpDownIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { classNames } from '../../utils/helpers';

const Select = forwardRef(({
  label,
  value,
  onChange,
  options = [],
  placeholder = 'Select an option...',
  error,
  helperText,
  required = false,
  disabled = false,
  multiple = false,
  searchable = false,
  clearable = false,
  size = 'md',
  className = '',
  labelClassName = '',
  errorClassName = '',
  helperClassName = '',
  optionKey = 'value',
  optionLabel = 'label',
  optionDisabled = 'disabled',
  renderOption,
  renderValue,
  ...props
}, ref) => {
  const [searchQuery, setSearchQuery] = React.useState('');
  const inputId = React.useId();
  const errorId = React.useId();
  const helperId = React.useId();

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const filteredOptions = React.useMemo(() => {
    if (!searchable || !searchQuery) return options;
    
    return options.filter(option => {
      const label = typeof option === 'string' ? option : option[optionLabel];
      return label.toLowerCase().includes(searchQuery.toLowerCase());
    });
  }, [options, searchQuery, searchable, optionLabel]);

  const getOptionValue = (option) => {
    return typeof option === 'string' ? option : option[optionKey];
  };

  const getOptionLabel = (option) => {
    return typeof option === 'string' ? option : option[optionLabel];
  };

  const getOptionDisabled = (option) => {
    return typeof option === 'string' ? false : option[optionDisabled];
  };

  const isSelected = (option) => {
    const optionValue = getOptionValue(option);
    if (multiple) {
      return Array.isArray(value) && value.includes(optionValue);
    }
    return value === optionValue;
  };

  const handleChange = (selectedValue) => {
    onChange?.(selectedValue);
  };

  const handleClear = (e) => {
    e.stopPropagation();
    handleChange(multiple ? [] : '');
  };

  const getDisplayValue = () => {
    if (multiple) {
      if (!Array.isArray(value) || value.length === 0) {
        return placeholder;
      }
      if (value.length === 1) {
        const option = options.find(opt => getOptionValue(opt) === value[0]);
        return option ? getOptionLabel(option) : value[0];
      }
      return `${value.length} selected`;
    }
    
    if (!value) return placeholder;
    
    const option = options.find(opt => getOptionValue(opt) === value);
    return option ? getOptionLabel(option) : value;
  };

  const shouldShowClear = clearable && (
    (multiple && Array.isArray(value) && value.length > 0) ||
    (!multiple && value)
  );

  return (
    <div className={classNames('w-full', className)}>
      {/* Label */}
      {label && (
        <label 
          htmlFor={inputId}
          className={classNames(
            'block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1',
            required && "after:content-['*'] after:ml-0.5 after:text-red-500",
            labelClassName
          )}
        >
          {label}
        </label>
      )}

      <Listbox
        value={value}
        onChange={handleChange}
        multiple={multiple}
        disabled={disabled}
        {...props}
      >
        <div className="relative">
          <Listbox.Button
            ref={ref}
            className={classNames(
              'relative w-full cursor-default rounded-md border bg-white text-left shadow-sm transition-colors duration-200',
              'focus:outline-none focus:ring-1 focus:border-blue-500 focus:ring-blue-500',
              'dark:bg-gray-700 dark:border-gray-600 dark:text-white',
              sizeClasses[size],
              error 
                ? 'border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500 dark:border-red-500' 
                : 'border-gray-300',
              disabled && 'bg-gray-50 text-gray-500 cursor-not-allowed dark:bg-gray-800'
            )}
          >
            <span className={classNames(
              'block truncate',
              (!value || (multiple && (!Array.isArray(value) || value.length === 0))) && 'text-gray-400'
            )}>
              {renderValue ? renderValue(value, options) : getDisplayValue()}
            </span>
            
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              {shouldShowClear && (
                <button
                  type="button"
                  onClick={handleClear}
                  className="pointer-events-auto mr-1 text-gray-400 hover:text-gray-600 focus:outline-none"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
              <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </span>
          </Listbox.Button>

          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-700 dark:ring-gray-600 sm:text-sm">
              {searchable && (
                <div className="sticky top-0 bg-white dark:bg-gray-700 px-3 py-2 border-b border-gray-200 dark:border-gray-600">
                  <input
                    type="text"
                    placeholder="Search..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                  />
                </div>
              )}
              
              {filteredOptions.length === 0 ? (
                <div className="relative cursor-default select-none py-2 px-4 text-gray-700 dark:text-gray-300">
                  {searchQuery ? 'No results found' : 'No options available'}
                </div>
              ) : (
                filteredOptions.map((option, index) => (
                  <Listbox.Option
                    key={`${getOptionValue(option)}-${index}`}
                    className={({ active }) =>
                      classNames(
                        'relative cursor-default select-none py-2 pl-10 pr-4',
                        active ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-gray-100',
                        getOptionDisabled(option) && 'opacity-50 cursor-not-allowed'
                      )
                    }
                    value={getOptionValue(option)}
                    disabled={getOptionDisabled(option)}
                  >
                    {({ selected }) => (
                      <>
                        <span className={classNames(
                          'block truncate',
                          selected ? 'font-medium' : 'font-normal'
                        )}>
                          {renderOption ? renderOption(option, selected) : getOptionLabel(option)}
                        </span>
                        {selected && (
                          <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-blue-600 dark:text-blue-400">
                            <CheckIcon className="h-5 w-5" aria-hidden="true" />
                          </span>
                        )}
                      </>
                    )}
                  </Listbox.Option>
                ))
              )}
            </Listbox.Options>
          </Transition>
        </div>
      </Listbox>

      {/* Error Message */}
      {error && (
        <p 
          id={errorId}
          className={classNames(
            'mt-1 text-sm text-red-600 dark:text-red-400',
            errorClassName
          )}
        >
          {error}
        </p>
      )}

      {/* Helper Text */}
      {helperText && !error && (
        <p 
          id={helperId}
          className={classNames(
            'mt-1 text-sm text-gray-500 dark:text-gray-400',
            helperClassName
          )}
        >
          {helperText}
        </p>
      )}
    </div>
  );
});

Select.displayName = 'Select';

export default Select;
