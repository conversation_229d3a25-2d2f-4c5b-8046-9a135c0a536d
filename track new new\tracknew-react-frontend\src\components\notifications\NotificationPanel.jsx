import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { 
  XMarkIcon,
  BellIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

// Redux
import {
  selectNotifications,
  selectShowNotificationPanel,
  selectNotificationLoading,
  selectUnreadCount,
  setNotificationPanel,
  fetchNotifications,
  markAsRead,
  markAllAsRead,
  deleteNotification
} from '../../store/slices/notificationSlice';

// Utils
import { formatRelativeTime, classNames } from '../../utils/helpers';

const NotificationPanel = () => {
  const dispatch = useDispatch();
  
  // Selectors
  const notifications = useSelector(selectNotifications);
  const showPanel = useSelector(selectShowNotificationPanel);
  const loading = useSelector(selectNotificationLoading);
  const unreadCount = useSelector(selectUnreadCount);

  // Fetch notifications when panel opens
  useEffect(() => {
    if (showPanel) {
      dispatch(fetchNotifications({ limit: 50 }));
    }
  }, [showPanel, dispatch]);

  const handleClose = () => {
    dispatch(setNotificationPanel(false));
  };

  const handleMarkAsRead = (notificationId) => {
    dispatch(markAsRead(notificationId));
  };

  const handleMarkAllAsRead = () => {
    dispatch(markAllAsRead());
  };

  const handleDelete = (notificationId) => {
    dispatch(deleteNotification(notificationId));
  };

  const getNotificationIcon = (type) => {
    const iconClasses = "h-6 w-6";
    
    switch (type) {
      case 'success':
        return <CheckCircleIcon className={classNames(iconClasses, "text-green-500")} />;
      case 'warning':
        return <ExclamationTriangleIcon className={classNames(iconClasses, "text-yellow-500")} />;
      case 'error':
        return <ExclamationTriangleIcon className={classNames(iconClasses, "text-red-500")} />;
      case 'info':
        return <InformationCircleIcon className={classNames(iconClasses, "text-blue-500")} />;
      default:
        return <BellIcon className={classNames(iconClasses, "text-gray-500")} />;
    }
  };

  const getNotificationBorderColor = (type) => {
    switch (type) {
      case 'success':
        return 'border-l-green-500';
      case 'warning':
        return 'border-l-yellow-500';
      case 'error':
        return 'border-l-red-500';
      case 'info':
        return 'border-l-blue-500';
      default:
        return 'border-l-gray-500';
    }
  };

  return (
    <Transition.Root show={showPanel} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-in-out duration-500"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in-out duration-500"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto relative w-screen max-w-md">
                  <Transition.Child
                    as={Fragment}
                    enter="ease-in-out duration-500"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in-out duration-500"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <div className="absolute left-0 top-0 -ml-8 flex pr-2 pt-4 sm:-ml-10 sm:pr-4">
                      <button
                        type="button"
                        className="relative rounded-md text-gray-300 hover:text-white focus:outline-none focus:ring-2 focus:ring-white"
                        onClick={handleClose}
                      >
                        <span className="absolute -inset-2.5" />
                        <span className="sr-only">Close panel</span>
                        <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                      </button>
                    </div>
                  </Transition.Child>
                  
                  <div className="flex h-full flex-col overflow-y-scroll bg-white dark:bg-gray-800 py-6 shadow-xl">
                    <div className="px-4 sm:px-6">
                      <Dialog.Title className="text-base font-semibold leading-6 text-gray-900 dark:text-white">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <BellIcon className="h-6 w-6" />
                            <span>Notifications</span>
                            {unreadCount > 0 && (
                              <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                                {unreadCount}
                              </span>
                            )}
                          </div>
                          {unreadCount > 0 && (
                            <button
                              onClick={handleMarkAllAsRead}
                              className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
                            >
                              Mark all read
                            </button>
                          )}
                        </div>
                      </Dialog.Title>
                    </div>
                    
                    <div className="relative mt-6 flex-1 px-4 sm:px-6">
                      {loading ? (
                        <div className="flex items-center justify-center h-32">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        </div>
                      ) : notifications.length === 0 ? (
                        <div className="text-center py-12">
                          <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
                          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                            No notifications
                          </h3>
                          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            You're all caught up!
                          </p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {notifications.map((notification) => (
                            <div
                              key={notification.id}
                              className={classNames(
                                'relative rounded-lg border-l-4 bg-white dark:bg-gray-700 p-4 shadow-sm',
                                getNotificationBorderColor(notification.type),
                                !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                              )}
                            >
                              <div className="flex">
                                <div className="flex-shrink-0">
                                  {getNotificationIcon(notification.type)}
                                </div>
                                <div className="ml-3 flex-1">
                                  <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                                        {notification.title}
                                      </p>
                                      <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
                                        {notification.message}
                                      </p>
                                      <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                                        {formatRelativeTime(notification.created_at)}
                                      </p>
                                    </div>
                                    <div className="ml-4 flex space-x-2">
                                      {!notification.is_read && (
                                        <button
                                          onClick={() => handleMarkAsRead(notification.id)}
                                          className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
                                          title="Mark as read"
                                        >
                                          <CheckIcon className="h-4 w-4" />
                                        </button>
                                      )}
                                      <button
                                        onClick={() => handleDelete(notification.id)}
                                        className="text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300"
                                        title="Delete"
                                      >
                                        <XMarkIcon className="h-4 w-4" />
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default NotificationPanel;
