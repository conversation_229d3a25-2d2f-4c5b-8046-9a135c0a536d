// Debug server startup
console.log('🔧 Starting debug server...');

try {
  console.log('1. Loading environment variables...');
  require('dotenv').config();
  console.log('✅ Environment loaded');

  console.log('2. Loading Express...');
  const express = require('express');
  console.log('✅ Express loaded');

  console.log('3. Loading database...');
  const db = require('./src/config/database');
  console.log('✅ Database config loaded');

  console.log('4. Testing database connection...');
  db.authenticate()
    .then(() => {
      console.log('✅ Database connection successful');
      
      console.log('5. Loading models...');
      const models = require('./src/models');
      console.log('✅ Models loaded successfully');
      
      console.log('6. Creating Express app...');
      const app = express();
      console.log('✅ Express app created');
      
      console.log('7. Setting up basic middleware...');
      app.use(express.json());
      console.log('✅ Basic middleware set');
      
      console.log('8. Setting up test route...');
      app.get('/debug', (req, res) => {
        res.json({ status: 'success', message: 'Debug server working' });
      });
      console.log('✅ Test route set');
      
      console.log('9. Starting server...');
      const PORT = process.env.PORT || 8000;
      app.listen(PORT, () => {
        console.log(`✅ Debug server running on port ${PORT}`);
        console.log(`🔧 Test URL: http://localhost:${PORT}/debug`);
      });
      
    })
    .catch(error => {
      console.error('❌ Database connection failed:', error.message);
      process.exit(1);
    });

} catch (error) {
  console.error('❌ Error during startup:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}
