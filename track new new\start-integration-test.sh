#!/bin/bash

echo "🚀 Starting Phase 4 Integration Testing"
echo ""

echo "📦 Installing backend dependencies..."
cd "tracknew-nodejs-backend"
npm install
if [ $? -ne 0 ]; then
    echo "❌ Backend dependency installation failed"
    exit 1
fi

echo ""
echo "🗄️ Setting up database..."
echo "Please ensure PostgreSQL is running and create database 'tracknew_development'"
echo ""

echo "🔄 Starting backend server..."
gnome-terminal --title="Backend Server" -- bash -c "npm run dev; exec bash" 2>/dev/null || \
xterm -title "Backend Server" -e "npm run dev; bash" 2>/dev/null || \
osascript -e 'tell app "Terminal" to do script "cd \"$(pwd)\" && npm run dev"' 2>/dev/null || \
echo "⚠️ Could not open new terminal. Please run 'npm run dev' manually in the backend directory."

echo ""
echo "⏳ Waiting for backend to start..."
sleep 10

echo ""
echo "📦 Installing frontend dependencies..."
cd "../tracknew-react-frontend"
npm install
if [ $? -ne 0 ]; then
    echo "❌ Frontend dependency installation failed"
    exit 1
fi

echo ""
echo "🎨 Starting frontend development server..."
gnome-terminal --title="Frontend Server" -- bash -c "npm start; exec bash" 2>/dev/null || \
xterm -title "Frontend Server" -e "npm start; bash" 2>/dev/null || \
osascript -e 'tell app "Terminal" to do script "cd \"$(pwd)\" && npm start"' 2>/dev/null || \
echo "⚠️ Could not open new terminal. Please run 'npm start' manually in the frontend directory."

echo ""
echo "✅ Both servers are starting!"
echo ""
echo "📋 Next steps:"
echo "1. Wait for both servers to fully start"
echo "2. Backend will be available at: http://localhost:8000"
echo "3. Frontend will be available at: http://localhost:3000"
echo "4. Navigate to: http://localhost:3000/integration-test"
echo "5. Run the integration tests"
echo ""
echo "🔧 If you encounter issues:"
echo "- Ensure PostgreSQL is running"
echo "- Check that ports 8000 and 3000 are available"
echo "- Review the console output in both server windows"
echo ""
echo "Press any key to continue..."
read -n 1
