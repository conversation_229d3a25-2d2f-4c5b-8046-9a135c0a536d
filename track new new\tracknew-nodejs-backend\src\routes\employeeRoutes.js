const express = require('express');
const { body } = require('express-validator');
const employeeController = require('../controllers/employeeController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating employee
const createEmployeeValidation = [
  body('employee_id')
    .notEmpty()
    .withMessage('Employee ID is required')
    .isLength({ min: 1, max: 50 })
    .withMessage('Employee ID must be between 1 and 50 characters'),
  
  body('first_name')
    .notEmpty()
    .withMessage('First name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('First name must be between 2 and 100 characters'),
  
  body('last_name')
    .notEmpty()
    .withMessage('Last name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Last name must be between 2 and 100 characters'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address'),
  
  body('phone')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Phone number must be maximum 20 characters'),
  
  body('mobile')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Mobile number must be maximum 20 characters'),
  
  body('date_of_birth')
    .optional()
    .isISO8601()
    .withMessage('Date of birth must be a valid date'),
  
  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('Invalid gender'),
  
  body('marital_status')
    .optional()
    .isIn(['single', 'married', 'divorced', 'widowed'])
    .withMessage('Invalid marital status'),
  
  body('department')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Department must be maximum 100 characters'),
  
  body('designation')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Designation must be maximum 100 characters'),
  
  body('job_title')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Job title must be maximum 100 characters'),
  
  body('employment_type')
    .optional()
    .isIn(['full_time', 'part_time', 'contract', 'temporary', 'intern'])
    .withMessage('Invalid employment type'),
  
  body('employment_status')
    .optional()
    .isIn(['active', 'inactive', 'terminated', 'resigned', 'on_leave'])
    .withMessage('Invalid employment status'),
  
  body('hire_date')
    .optional()
    .isISO8601()
    .withMessage('Hire date must be a valid date'),
  
  body('probation_end_date')
    .optional()
    .isISO8601()
    .withMessage('Probation end date must be a valid date'),
  
  body('confirmation_date')
    .optional()
    .isISO8601()
    .withMessage('Confirmation date must be a valid date'),
  
  body('reporting_manager_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Reporting manager ID must be a positive integer'),
  
  body('salary')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Salary must be a positive number'),
  
  body('salary_currency')
    .optional()
    .isLength({ max: 3 })
    .withMessage('Salary currency must be maximum 3 characters'),
  
  body('salary_frequency')
    .optional()
    .isIn(['hourly', 'daily', 'weekly', 'monthly', 'yearly'])
    .withMessage('Invalid salary frequency'),
  
  body('work_shift')
    .optional()
    .isIn(['day', 'night', 'rotating'])
    .withMessage('Invalid work shift'),
  
  body('work_hours_per_week')
    .optional()
    .isFloat({ min: 0, max: 168 })
    .withMessage('Work hours per week must be between 0 and 168'),
  
  body('overtime_eligible')
    .optional()
    .isBoolean()
    .withMessage('Overtime eligible must be a boolean'),
  
  body('remote_work_allowed')
    .optional()
    .isBoolean()
    .withMessage('Remote work allowed must be a boolean'),
  
  body('travel_required')
    .optional()
    .isBoolean()
    .withMessage('Travel required must be a boolean'),
  
  body('access_level')
    .optional()
    .isIn(['basic', 'standard', 'advanced', 'admin'])
    .withMessage('Invalid access level'),
  
  body('can_login')
    .optional()
    .isBoolean()
    .withMessage('Can login must be a boolean'),
  
  body('login_allowed_from')
    .optional()
    .isISO8601()
    .withMessage('Login allowed from must be a valid date'),
  
  body('login_allowed_until')
    .optional()
    .isISO8601()
    .withMessage('Login allowed until must be a valid date'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean'),
  
  body('skills')
    .optional()
    .isArray()
    .withMessage('Skills must be an array'),
  
  body('certifications')
    .optional()
    .isArray()
    .withMessage('Certifications must be an array'),
  
  body('education')
    .optional()
    .isArray()
    .withMessage('Education must be an array'),
  
  body('experience')
    .optional()
    .isArray()
    .withMessage('Experience must be an array'),
  
  body('languages')
    .optional()
    .isArray()
    .withMessage('Languages must be an array'),
  
  body('benefits')
    .optional()
    .isArray()
    .withMessage('Benefits must be an array'),
  
  body('leave_balance')
    .optional()
    .isObject()
    .withMessage('Leave balance must be a valid JSON object'),
  
  body('custom_fields')
    .optional()
    .isObject()
    .withMessage('Custom fields must be a valid JSON object')
];

// Validation rules for updating employee
const updateEmployeeValidation = [
  body('employee_id')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Employee ID must be between 1 and 50 characters'),
  
  body('first_name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('First name must be between 2 and 100 characters'),
  
  body('last_name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Last name must be between 2 and 100 characters'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address'),
  
  body('phone')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Phone number must be maximum 20 characters'),
  
  body('mobile')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Mobile number must be maximum 20 characters'),
  
  body('date_of_birth')
    .optional()
    .isISO8601()
    .withMessage('Date of birth must be a valid date'),
  
  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('Invalid gender'),
  
  body('marital_status')
    .optional()
    .isIn(['single', 'married', 'divorced', 'widowed'])
    .withMessage('Invalid marital status'),
  
  body('employment_type')
    .optional()
    .isIn(['full_time', 'part_time', 'contract', 'temporary', 'intern'])
    .withMessage('Invalid employment type'),
  
  body('employment_status')
    .optional()
    .isIn(['active', 'inactive', 'terminated', 'resigned', 'on_leave'])
    .withMessage('Invalid employment status'),
  
  body('reporting_manager_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Reporting manager ID must be a positive integer'),
  
  body('salary')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Salary must be a positive number'),
  
  body('access_level')
    .optional()
    .isIn(['basic', 'standard', 'advanced', 'admin'])
    .withMessage('Invalid access level'),
  
  body('can_login')
    .optional()
    .isBoolean()
    .withMessage('Can login must be a boolean'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean')
];

// Validation for status update
const updateStatusValidation = [
  body('employment_status')
    .notEmpty()
    .withMessage('Employment status is required')
    .isIn(['active', 'inactive', 'terminated', 'resigned', 'on_leave'])
    .withMessage('Invalid employment status'),
  
  body('termination_date')
    .optional()
    .isISO8601()
    .withMessage('Termination date must be a valid date'),
  
  body('termination_reason')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Termination reason must be maximum 500 characters')
];

// Routes
router
  .route('/')
  .get(employeeController.getEmployees)
  .post(createEmployeeValidation, validateRequest, restrictTo('admin', 'sub_admin', 'hr'), employeeController.createEmployee);

router
  .route('/stats')
  .get(employeeController.getEmployeeStats);

router
  .route('/active')
  .get(employeeController.getActiveEmployees);

router
  .route('/by-department/:department')
  .get(employeeController.getEmployeesByDepartment);

router
  .route('/by-manager/:manager_id')
  .get(employeeController.getEmployeesByManager);

router
  .route('/:id/status')
  .put(updateStatusValidation, validateRequest, restrictTo('admin', 'sub_admin', 'hr'), employeeController.updateEmployeeStatus);

router
  .route('/:id')
  .get(employeeController.getEmployee)
  .put(updateEmployeeValidation, validateRequest, restrictTo('admin', 'sub_admin', 'hr'), employeeController.updateEmployee)
  .delete(restrictTo('admin', 'sub_admin'), employeeController.deleteEmployee);

module.exports = router;
