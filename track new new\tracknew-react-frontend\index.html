<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="TrackNew - Professional Service Management System" />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <link rel="manifest" href="/manifest.json" />

    <!-- Tailwind CSS CDN for development -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Configure Tailwind -->
    <script>
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            colors: {
              primary: {
                50: '#eff6ff',
                100: '#dbeafe',
                200: '#bfdbfe',
                300: '#93c5fd',
                400: '#60a5fa',
                500: '#3b82f6',
                600: '#2563eb',
                700: '#1d4ed8',
                800: '#1e40af',
                900: '#1e3a8a',
              },
              secondary: {
                50: '#f8fafc',
                100: '#f1f5f9',
                200: '#e2e8f0',
                300: '#cbd5e1',
                400: '#94a3b8',
                500: '#64748b',
                600: '#475569',
                700: '#334155',
                800: '#1e293b',
                900: '#0f172a',
              }
            }
          }
        }
      }
    </script>

    <title>TrackNew - Service Management System</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root">
      <h1 style="color: green; padding: 20px;">🎉 Track New Application</h1>
      <p style="padding: 0 20px;">If you can see this, the HTML is loading correctly!</p>
      <div style="background: #f0f0f0; padding: 20px; margin: 20px;">
        <h3>System Status:</h3>
        <ul>
          <li>✅ HTML: Working</li>
          <li>✅ CSS: Working</li>
          <li>✅ Server: Running on port 3001</li>
        </ul>
      </div>
    </div>
    <script>
      console.log('JavaScript is working!');
      console.log('Current URL:', window.location.href);
      console.log('User Agent:', navigator.userAgent);

      // Add a test button
      const button = document.createElement('button');
      button.textContent = 'Test JavaScript';
      button.style.padding = '10px 20px';
      button.style.margin = '20px';
      button.style.backgroundColor = '#007bff';
      button.style.color = 'white';
      button.style.border = 'none';
      button.style.borderRadius = '4px';
      button.onclick = function() {
        alert('JavaScript is working! Time: ' + new Date().toLocaleString());
      };
      document.getElementById('root').appendChild(button);
    </script>
  </body>
</html>
