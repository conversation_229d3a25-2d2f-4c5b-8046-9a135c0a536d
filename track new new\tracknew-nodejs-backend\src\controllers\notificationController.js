const { Notification, User } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all notifications for user with filtering and pagination
const getNotifications = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    type,
    category,
    priority,
    is_read,
    is_archived = false,
    channel,
    related_type,
    search,
    sort_by = 'created_at',
    sort_order = 'DESC'
  } = req.query;

  const userId = req.user.id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    user_id: userId,
    is_archived: is_archived === 'true'
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { title: { [Op.iLike]: `%${search}%` } },
      { message: { [Op.iLike]: `%${search}%` } },
      { category: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add filters
  if (type) {
    whereConditions.type = type;
  }

  if (category) {
    whereConditions.category = category;
  }

  if (priority) {
    whereConditions.priority = priority;
  }

  if (is_read !== undefined) {
    whereConditions.is_read = is_read === 'true';
  }

  if (channel) {
    whereConditions.channel = channel;
  }

  if (related_type) {
    whereConditions.related_type = related_type;
  }

  // Filter out expired notifications
  whereConditions[Op.or] = [
    { expires_at: null },
    { expires_at: { [Op.gt]: new Date() } }
  ];

  // Get notifications with associations
  const { count, rows: notifications } = await Notification.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: User,
        as: 'sender',
        attributes: ['id', 'name', 'email', 'avatar']
      }
    ],
    limit: parseInt(limit),
    offset: offset,
    order: [[sort_by, sort_order.toUpperCase()]],
    distinct: true
  });

  // Calculate pagination
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      notifications,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single notification by ID
const getNotification = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const userId = req.user.id;

  const notification = await Notification.findOne({
    where: {
      id,
      user_id: userId
    },
    include: [
      {
        model: User,
        as: 'sender',
        attributes: ['id', 'name', 'email', 'avatar']
      }
    ]
  });

  if (!notification) {
    return next(new AppError('Notification not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      notification
    }
  });
});

// Create new notification
const createNotification = catchAsync(async (req, res, next) => {
  const {
    user_id,
    type = 'info',
    category,
    title,
    message,
    data,
    action_url,
    action_text,
    priority = 'medium',
    channel = 'in_app',
    related_type,
    related_id,
    expires_at,
    scheduled_at,
    template_id,
    template_data,
    attachments,
    tags,
    metadata
  } = req.body;

  // Validate target user exists
  const targetUser = await User.findByPk(user_id);
  if (!targetUser) {
    return next(new AppError('Target user not found', 404));
  }

  const notification = await Notification.create({
    user_id,
    type,
    category,
    title,
    message,
    data,
    action_url,
    action_text,
    priority,
    channel,
    related_type,
    related_id,
    sender_id: req.user.id,
    expires_at: expires_at ? new Date(expires_at) : null,
    scheduled_at: scheduled_at ? new Date(scheduled_at) : null,
    template_id,
    template_data,
    attachments,
    tags,
    metadata
  });

  // Fetch the created notification with associations
  const createdNotification = await Notification.findByPk(notification.id, {
    include: [
      {
        model: User,
        as: 'sender',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      notification: createdNotification
    }
  });
});

// Mark notification as read
const markAsRead = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const userId = req.user.id;

  const notification = await Notification.findOne({
    where: {
      id,
      user_id: userId
    }
  });

  if (!notification) {
    return next(new AppError('Notification not found', 404));
  }

  if (!notification.is_read) {
    await notification.update({
      is_read: true,
      read_at: new Date()
    });
  }

  res.status(200).json({
    status: 'success',
    message: 'Notification marked as read',
    data: {
      notification: {
        id: notification.id,
        is_read: true,
        read_at: notification.read_at
      }
    }
  });
});

// Mark notification as unread
const markAsUnread = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const userId = req.user.id;

  const notification = await Notification.findOne({
    where: {
      id,
      user_id: userId
    }
  });

  if (!notification) {
    return next(new AppError('Notification not found', 404));
  }

  await notification.update({
    is_read: false,
    read_at: null
  });

  res.status(200).json({
    status: 'success',
    message: 'Notification marked as unread',
    data: {
      notification: {
        id: notification.id,
        is_read: false,
        read_at: null
      }
    }
  });
});

// Archive notification
const archiveNotification = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const userId = req.user.id;

  const notification = await Notification.findOne({
    where: {
      id,
      user_id: userId
    }
  });

  if (!notification) {
    return next(new AppError('Notification not found', 404));
  }

  await notification.update({
    is_archived: true,
    archived_at: new Date()
  });

  res.status(200).json({
    status: 'success',
    message: 'Notification archived successfully'
  });
});

// Delete notification
const deleteNotification = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const userId = req.user.id;

  const notification = await Notification.findOne({
    where: {
      id,
      user_id: userId
    }
  });

  if (!notification) {
    return next(new AppError('Notification not found', 404));
  }

  await notification.destroy();

  res.status(200).json({
    status: 'success',
    message: 'Notification deleted successfully'
  });
});

// Get notification statistics
const getNotificationStats = catchAsync(async (req, res) => {
  const userId = req.user.id;

  // Total notifications
  const totalNotifications = await Notification.count({
    where: { user_id: userId }
  });

  // Unread notifications
  const unreadCount = await Notification.count({
    where: {
      user_id: userId,
      is_read: false,
      is_archived: false,
      [Op.or]: [
        { expires_at: null },
        { expires_at: { [Op.gt]: new Date() } }
      ]
    }
  });

  // Notifications by type
  const notificationsByType = await Notification.findAll({
    where: { user_id: userId },
    attributes: [
      'type',
      [Notification.sequelize.fn('COUNT', Notification.sequelize.col('id')), 'count']
    ],
    group: ['type'],
    raw: true
  });

  // Notifications by priority
  const notificationsByPriority = await Notification.findAll({
    where: { user_id: userId },
    attributes: [
      'priority',
      [Notification.sequelize.fn('COUNT', Notification.sequelize.col('id')), 'count']
    ],
    group: ['priority'],
    raw: true
  });

  // Recent activity (last 7 days)
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

  const recentActivity = await Notification.count({
    where: {
      user_id: userId,
      created_at: { [Op.gte]: sevenDaysAgo }
    }
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_notifications: totalNotifications,
      unread_count: unreadCount,
      read_count: totalNotifications - unreadCount,
      notifications_by_type: notificationsByType,
      notifications_by_priority: notificationsByPriority,
      recent_activity: recentActivity
    }
  });
});

// Mark all notifications as read
const markAllAsRead = catchAsync(async (req, res) => {
  const userId = req.user.id;

  const [updatedCount] = await Notification.update(
    {
      is_read: true,
      read_at: new Date()
    },
    {
      where: {
        user_id: userId,
        is_read: false
      }
    }
  );

  res.status(200).json({
    status: 'success',
    message: `${updatedCount} notification(s) marked as read`,
    data: {
      updated_count: updatedCount
    }
  });
});

// Bulk archive notifications
const bulkArchive = catchAsync(async (req, res, next) => {
  const { notification_ids } = req.body;
  const userId = req.user.id;

  if (!Array.isArray(notification_ids) || notification_ids.length === 0) {
    return next(new AppError('Notification IDs array is required', 400));
  }

  const [updatedCount] = await Notification.update(
    {
      is_archived: true,
      archived_at: new Date()
    },
    {
      where: {
        id: { [Op.in]: notification_ids },
        user_id: userId
      }
    }
  );

  res.status(200).json({
    status: 'success',
    message: `${updatedCount} notification(s) archived successfully`,
    data: {
      archived_count: updatedCount
    }
  });
});

// Bulk delete notifications
const bulkDelete = catchAsync(async (req, res, next) => {
  const { notification_ids } = req.body;
  const userId = req.user.id;

  if (!Array.isArray(notification_ids) || notification_ids.length === 0) {
    return next(new AppError('Notification IDs array is required', 400));
  }

  const deletedCount = await Notification.destroy({
    where: {
      id: { [Op.in]: notification_ids },
      user_id: userId
    }
  });

  res.status(200).json({
    status: 'success',
    message: `${deletedCount} notification(s) deleted successfully`,
    data: {
      deleted_count: deletedCount
    }
  });
});

// Get notification preferences
const getNotificationPreferences = catchAsync(async (req, res) => {
  const userId = req.user.id;

  // Get user's notification preferences from user profile
  const user = await User.findByPk(userId, {
    attributes: ['id', 'notification_preferences']
  });

  const defaultPreferences = {
    email_notifications: true,
    sms_notifications: false,
    push_notifications: true,
    in_app_notifications: true,
    notification_types: {
      service_updates: true,
      payment_reminders: true,
      system_alerts: true,
      marketing: false
    },
    quiet_hours: {
      enabled: false,
      start_time: '22:00',
      end_time: '08:00'
    }
  };

  const preferences = user.notification_preferences || defaultPreferences;

  res.status(200).json({
    status: 'success',
    data: {
      preferences
    }
  });
});

// Update notification preferences
const updateNotificationPreferences = catchAsync(async (req, res, next) => {
  const userId = req.user.id;
  const { preferences } = req.body;

  const user = await User.findByPk(userId);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  await user.update({
    notification_preferences: preferences
  });

  res.status(200).json({
    status: 'success',
    message: 'Notification preferences updated successfully',
    data: {
      preferences
    }
  });
});

// Send notification to multiple users
const sendBulkNotification = catchAsync(async (req, res, next) => {
  const {
    user_ids,
    type = 'info',
    category,
    title,
    message,
    data,
    action_url,
    action_text,
    priority = 'medium',
    channel = 'in_app',
    related_type,
    related_id,
    expires_at,
    scheduled_at
  } = req.body;

  if (!Array.isArray(user_ids) || user_ids.length === 0) {
    return next(new AppError('User IDs array is required', 400));
  }

  // Validate all users exist
  const users = await User.findAll({
    where: { id: { [Op.in]: user_ids } },
    attributes: ['id']
  });

  if (users.length !== user_ids.length) {
    return next(new AppError('Some users not found', 400));
  }

  // Create notifications for all users
  const notificationsToCreate = user_ids.map(userId => ({
    user_id: userId,
    type,
    category,
    title,
    message,
    data,
    action_url,
    action_text,
    priority,
    channel,
    related_type,
    related_id,
    sender_id: req.user.id,
    expires_at: expires_at ? new Date(expires_at) : null,
    scheduled_at: scheduled_at ? new Date(scheduled_at) : null
  }));

  const createdNotifications = await Notification.bulkCreate(notificationsToCreate);

  res.status(201).json({
    status: 'success',
    message: `${createdNotifications.length} notification(s) sent successfully`,
    data: {
      sent_count: createdNotifications.length,
      notification_ids: createdNotifications.map(n => n.id)
    }
  });
});

// Clean up expired notifications
const cleanupExpiredNotifications = catchAsync(async (req, res) => {
  const deletedCount = await Notification.destroy({
    where: {
      expires_at: { [Op.lt]: new Date() }
    }
  });

  res.status(200).json({
    status: 'success',
    message: `${deletedCount} expired notification(s) cleaned up`,
    data: {
      deleted_count: deletedCount
    }
  });
});

module.exports = {
  getNotifications,
  getNotification,
  createNotification,
  markAsRead,
  markAsUnread,
  archiveNotification,
  deleteNotification,
  getNotificationStats,
  markAllAsRead,
  bulkArchive,
  bulkDelete,
  getNotificationPreferences,
  updateNotificationPreferences,
  sendBulkNotification,
  cleanupExpiredNotifications
};
