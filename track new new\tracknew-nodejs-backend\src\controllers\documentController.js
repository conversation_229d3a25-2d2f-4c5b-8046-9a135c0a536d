const { User, Company } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');
const path = require('path');
const fs = require('fs').promises;
const multer = require('multer');
const crypto = require('crypto');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const companyId = req.user.company_id;
    const uploadPath = path.join(__dirname, '../../uploads/documents', companyId.toString());
    
    try {
      await fs.mkdir(uploadPath, { recursive: true });
      cb(null, uploadPath);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const fileExtension = path.extname(file.originalname);
    const fileName = `${file.fieldname}-${uniqueSuffix}${fileExtension}`;
    cb(null, fileName);
  }
});

const fileFilter = (req, file, cb) => {
  // Allowed file types
  const allowedTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new AppError('Invalid file type. Only images, PDFs, and office documents are allowed.', 400), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Get all documents with filtering and pagination
const getDocuments = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    category,
    file_type,
    uploaded_by,
    start_date,
    end_date,
    sort_by = 'created_at',
    sort_order = 'DESC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { original_name: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } },
      { tags: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add date range filter
  if (start_date || end_date) {
    whereConditions.created_at = {};
    if (start_date) {
      whereConditions.created_at[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      whereConditions.created_at[Op.lte] = new Date(end_date);
    }
  }

  // Add filters
  if (category) {
    whereConditions.category = category;
  }

  if (file_type) {
    whereConditions.file_type = file_type;
  }

  if (uploaded_by) {
    whereConditions.uploaded_by = uploaded_by;
  }

  // Simulate document records (since we don't have a Document model)
  // In a real implementation, you would query the Document model
  const documents = [];
  const totalCount = 0;

  // Calculate pagination
  const totalPages = Math.ceil(totalCount / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      documents,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: totalCount,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Upload document
const uploadDocument = catchAsync(async (req, res, next) => {
  const companyId = req.user.company_id;

  // Use multer middleware
  upload.single('document')(req, res, async (err) => {
    if (err) {
      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return next(new AppError('File too large. Maximum size is 10MB.', 400));
        }
      }
      return next(err);
    }

    if (!req.file) {
      return next(new AppError('No file uploaded', 400));
    }

    const {
      description,
      category = 'general',
      tags,
      is_public = false,
      related_type,
      related_id
    } = req.body;

    // Generate file hash for duplicate detection
    const fileBuffer = await fs.readFile(req.file.path);
    const fileHash = crypto.createHash('md5').update(fileBuffer).digest('hex');

    // File information
    const documentData = {
      company_id: companyId,
      original_name: req.file.originalname,
      file_name: req.file.filename,
      file_path: req.file.path,
      file_size: req.file.size,
      file_type: req.file.mimetype,
      file_hash: fileHash,
      description,
      category,
      tags,
      is_public: is_public === 'true',
      related_type,
      related_id: related_id ? parseInt(related_id) : null,
      uploaded_by: req.user.id,
      upload_date: new Date()
    };

    // In a real implementation, you would save to Document model
    // const document = await Document.create(documentData);

    res.status(201).json({
      status: 'success',
      message: 'Document uploaded successfully',
      data: {
        document: {
          id: Date.now(), // Temporary ID
          ...documentData,
          download_url: `/api/documents/download/${req.file.filename}`
        }
      }
    });
  });
});

// Download document
const downloadDocument = catchAsync(async (req, res, next) => {
  const { filename } = req.params;
  const companyId = req.user.company_id;

  // Build file path
  const filePath = path.join(__dirname, '../../uploads/documents', companyId.toString(), filename);

  try {
    // Check if file exists
    await fs.access(filePath);

    // Get file stats
    const stats = await fs.stat(filePath);

    // Set headers
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Stream file
    const fileStream = require('fs').createReadStream(filePath);
    fileStream.pipe(res);

  } catch (error) {
    return next(new AppError('File not found', 404));
  }
});

// Delete document
const deleteDocument = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  // In a real implementation, you would:
  // 1. Find the document in the database
  // 2. Check permissions
  // 3. Delete the file from filesystem
  // 4. Delete the database record

  res.status(200).json({
    status: 'success',
    message: 'Document deleted successfully'
  });
});

// Get document categories
const getDocumentCategories = catchAsync(async (req, res) => {
  const categories = [
    { value: 'general', label: 'General', icon: 'folder' },
    { value: 'invoices', label: 'Invoices', icon: 'receipt' },
    { value: 'contracts', label: 'Contracts', icon: 'document-text' },
    { value: 'certificates', label: 'Certificates', icon: 'badge-check' },
    { value: 'reports', label: 'Reports', icon: 'chart-bar' },
    { value: 'images', label: 'Images', icon: 'photograph' },
    { value: 'presentations', label: 'Presentations', icon: 'presentation-chart-line' },
    { value: 'spreadsheets', label: 'Spreadsheets', icon: 'table' },
    { value: 'legal', label: 'Legal Documents', icon: 'scale' },
    { value: 'hr', label: 'HR Documents', icon: 'users' }
  ];

  res.status(200).json({
    status: 'success',
    data: {
      categories
    }
  });
});

// Get document statistics
const getDocumentStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  // In a real implementation, you would query the Document model
  const stats = {
    total_documents: 0,
    total_size: 0,
    documents_by_type: [],
    documents_by_category: [],
    recent_uploads: 0,
    storage_used_percentage: 0
  };

  res.status(200).json({
    status: 'success',
    data: stats
  });
});

// Bulk delete documents
const bulkDeleteDocuments = catchAsync(async (req, res, next) => {
  const { document_ids } = req.body;
  const companyId = req.user.company_id;

  if (!Array.isArray(document_ids) || document_ids.length === 0) {
    return next(new AppError('Document IDs array is required', 400));
  }

  // In a real implementation, you would:
  // 1. Find all documents
  // 2. Check permissions
  // 3. Delete files from filesystem
  // 4. Delete database records

  res.status(200).json({
    status: 'success',
    message: `${document_ids.length} document(s) deleted successfully`,
    data: {
      deleted_count: document_ids.length
    }
  });
});

module.exports = {
  getDocuments,
  uploadDocument,
  downloadDocument,
  deleteDocument,
  getDocumentCategories,
  getDocumentStats,
  bulkDeleteDocuments
};
