// Test database connection with new configuration
require('dotenv').config();
const { Sequelize } = require('sequelize');

console.log('🔧 Testing Database Connection...');
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Not set');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_USERNAME:', process.env.DB_USERNAME);

async function testConnection() {
  try {
    let sequelize;
    
    if (process.env.DATABASE_URL) {
      console.log('📡 Using DATABASE_URL connection...');
      sequelize = new Sequelize(process.env.DATABASE_URL, {
        dialect: 'postgres',
        logging: console.log,
        dialectOptions: {
          ssl: process.env.DB_SSL === 'true' ? {
            require: true,
            rejectUnauthorized: false
          } : false
        }
      });
    } else {
      console.log('📡 Using individual parameters...');
      sequelize = new Sequelize(
        process.env.DB_NAME,
        process.env.DB_USERNAME,
        process.env.DB_PASSWORD,
        {
          host: process.env.DB_HOST,
          port: process.env.DB_PORT,
          dialect: 'postgres',
          logging: console.log,
          dialectOptions: {
            ssl: process.env.DB_SSL === 'true' ? {
              require: true,
              rejectUnauthorized: false
            } : false
          }
        }
      );
    }

    console.log('🔄 Attempting to connect...');
    await sequelize.authenticate();
    console.log('✅ Database connection successful!');
    
    // Test a simple query
    const result = await sequelize.query('SELECT version();');
    console.log('📊 PostgreSQL Version:', result[0][0].version);
    
    await sequelize.close();
    console.log('🔒 Connection closed.');
    
  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

testConnection();
