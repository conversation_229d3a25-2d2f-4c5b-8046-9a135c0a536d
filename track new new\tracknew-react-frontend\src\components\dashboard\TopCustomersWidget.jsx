import React from 'react';
import { Link } from 'react-router-dom';
import { UserCircleIcon, PhoneIcon, EnvelopeIcon } from '@heroicons/react/24/outline';
import { classNames, formatCurrency, getInitials, getAvatarColor } from '../../utils/helpers';
import { LoadingSpinner } from '../ui';

const TopCustomersWidget = ({ customers = [], loading = false }) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <LoadingSpinner />
      </div>
    );
  }

  if (!customers || customers.length === 0) {
    return (
      <div className="text-center py-8">
        <UserCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          No customer data
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Customer data will appear here once you have sales.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {customers.map((customer, index) => (
        <div
          key={customer.id}
          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
        >
          <div className="flex items-center space-x-4">
            {/* Rank Badge */}
            <div className={classNames(
              'flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold',
              index === 0 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
              index === 1 ? 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200' :
              index === 2 ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
              'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
            )}>
              {index + 1}
            </div>

            {/* Customer Avatar */}
            <div className="flex-shrink-0">
              {customer.avatar ? (
                <img
                  className="h-10 w-10 rounded-full"
                  src={customer.avatar}
                  alt={customer.name}
                />
              ) : (
                <div className={classNames(
                  'h-10 w-10 rounded-full flex items-center justify-center text-white font-medium text-sm',
                  getAvatarColor(customer.name)
                )}>
                  {getInitials(customer.name)}
                </div>
              )}
            </div>

            {/* Customer Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <Link
                  to={`/customers/${customer.id}`}
                  className="text-sm font-medium text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 truncate"
                >
                  {customer.name}
                </Link>
                {customer.is_vip && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                    VIP
                  </span>
                )}
              </div>
              
              <div className="flex items-center space-x-4 mt-1">
                {customer.email && (
                  <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                    <EnvelopeIcon className="h-3 w-3" />
                    <span className="truncate max-w-32">{customer.email}</span>
                  </div>
                )}
                {customer.phone && (
                  <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                    <PhoneIcon className="h-3 w-3" />
                    <span>{customer.phone}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Customer Stats */}
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {formatCurrency(customer.total_spent || 0)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {customer.total_orders || 0} orders
            </div>
            {customer.last_order_date && (
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Last: {new Date(customer.last_order_date).toLocaleDateString()}
              </div>
            )}
          </div>
        </div>
      ))}

      {/* View All Link */}
      <div className="text-center pt-4">
        <Link
          to="/customers"
          className="text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
        >
          View all customers →
        </Link>
      </div>
    </div>
  );
};

export default TopCustomersWidget;
