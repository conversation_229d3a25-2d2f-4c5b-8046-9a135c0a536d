const { Role, Permission, User, Company, RolePermission, UserRole } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all roles with filtering and pagination
const getRoles = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    role_type,
    guard_name,
    is_active = true,
    is_admin,
    is_system_role,
    data_access_level,
    sort_by = 'display_name',
    sort_order = 'ASC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    [Op.or]: [
      { company_id: companyId },
      { role_type: 'system' }
    ]
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.and] = [
      whereConditions[Op.or],
      {
        [Op.or]: [
          { name: { [Op.iLike]: `%${search}%` } },
          { display_name: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } }
        ]
      }
    ];
    delete whereConditions[Op.or];
  }

  // Add filters
  if (role_type) {
    whereConditions.role_type = role_type;
  }

  if (guard_name) {
    whereConditions.guard_name = guard_name;
  }

  if (is_active !== undefined) {
    whereConditions.is_active = is_active === 'true';
  }

  if (is_admin !== undefined) {
    whereConditions.is_admin = is_admin === 'true';
  }

  if (is_system_role !== undefined) {
    whereConditions.is_system_role = is_system_role === 'true';
  }

  if (data_access_level) {
    whereConditions.data_access_level = data_access_level;
  }

  // Get roles with associations
  const { count, rows: roles } = await Role.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: Permission,
        as: 'permissions',
        attributes: ['id', 'name', 'display_name', 'module', 'action'],
        through: { attributes: [] }
      },
      {
        model: User,
        as: 'users',
        attributes: ['id', 'name', 'email'],
        through: { attributes: [] }
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ],
    limit: parseInt(limit),
    offset: offset,
    order: [[sort_by, sort_order.toUpperCase()]],
    distinct: true
  });

  // Calculate pagination
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      roles,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single role by ID
const getRole = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const role = await Role.findOne({
    where: {
      id,
      [Op.or]: [
        { company_id: companyId },
        { role_type: 'system' }
      ]
    },
    include: [
      {
        model: Permission,
        as: 'permissions',
        attributes: ['id', 'name', 'display_name', 'description', 'module', 'action', 'scope', 'risk_level'],
        through: {
          attributes: ['granted_at', 'granted_by', 'valid_from', 'valid_until', 'conditions', 'restrictions']
        }
      },
      {
        model: User,
        as: 'users',
        attributes: ['id', 'name', 'email', 'user_type', 'is_active'],
        through: { attributes: ['assigned_at', 'assigned_by'] }
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!role) {
    return next(new AppError('Role not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      role
    }
  });
});

// Create new role
const createRole = catchAsync(async (req, res, next) => {
  const {
    name,
    display_name,
    description,
    guard_name = 'web',
    role_type = 'custom',
    level = 1,
    is_admin = false,
    is_super_admin = false,
    is_default = false,
    is_system_role = false,
    inherits_from,
    landing_page,
    menu_access = [],
    feature_access = {},
    data_access_level = 'own',
    can_view_reports = false,
    can_export_data = false,
    can_import_data = false,
    can_manage_users = false,
    can_manage_roles = false,
    can_manage_settings = false,
    can_access_api = false,
    api_rate_limit,
    session_timeout,
    ip_restrictions = [],
    time_restrictions = {},
    two_factor_required = false,
    password_policy = {},
    notification_settings = {},
    workflow_permissions = {},
    approval_limits = {},
    custom_permissions = {},
    sort_order = 0,
    is_active = true,
    permission_ids = []
  } = req.body;

  const companyId = req.user.company_id;

  // Check for duplicate role name within company
  const existingRole = await Role.findOne({
    where: {
      [Op.or]: [
        {
          name: name,
          company_id: companyId
        },
        {
          name: name,
          role_type: 'system'
        }
      ]
    }
  });

  if (existingRole) {
    return next(new AppError('Role with this name already exists', 400));
  }

  // Validate inherits_from role if provided
  if (inherits_from) {
    const parentRole = await Role.findOne({
      where: {
        id: inherits_from,
        [Op.or]: [
          { company_id: companyId },
          { role_type: 'system' }
        ],
        is_active: true
      }
    });

    if (!parentRole) {
      return next(new AppError('Parent role not found or inactive', 400));
    }
  }

  // Only allow system roles for super admins
  if (role_type === 'system' && req.user.user_type !== 'admin') {
    return next(new AppError('Only super admins can create system roles', 403));
  }

  const role = await Role.create({
    company_id: role_type === 'system' ? null : companyId,
    name,
    display_name,
    description,
    guard_name,
    role_type,
    level,
    is_admin,
    is_super_admin,
    is_default,
    is_system_role,
    inherits_from,
    landing_page,
    menu_access,
    feature_access,
    data_access_level,
    can_view_reports,
    can_export_data,
    can_import_data,
    can_manage_users,
    can_manage_roles,
    can_manage_settings,
    can_access_api,
    api_rate_limit,
    session_timeout,
    ip_restrictions,
    time_restrictions,
    two_factor_required,
    password_policy,
    notification_settings,
    workflow_permissions,
    approval_limits,
    custom_permissions,
    sort_order,
    is_active,
    created_by: req.user.id
  });

  // Assign permissions if provided
  if (permission_ids && permission_ids.length > 0) {
    const permissions = await Permission.findAll({
      where: {
        id: { [Op.in]: permission_ids },
        is_active: true
      }
    });

    if (permissions.length !== permission_ids.length) {
      return next(new AppError('Some permissions not found or inactive', 400));
    }

    // Create role-permission associations
    const rolePermissions = permission_ids.map(permissionId => ({
      role_id: role.id,
      permission_id: permissionId,
      granted_by: req.user.id,
      granted_at: new Date()
    }));

    await RolePermission.bulkCreate(rolePermissions);
  }

  // Fetch the created role with associations
  const createdRole = await Role.findByPk(role.id, {
    include: [
      {
        model: Permission,
        as: 'permissions',
        attributes: ['id', 'name', 'display_name', 'module', 'action'],
        through: { attributes: [] }
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      role: createdRole
    }
  });
});

// Update role
const updateRole = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const role = await Role.findOne({
    where: {
      id,
      [Op.or]: [
        { company_id: companyId },
        { role_type: 'system' }
      ]
    }
  });

  if (!role) {
    return next(new AppError('Role not found', 404));
  }

  // Prevent modification of system roles by non-super admins
  if (role.role_type === 'system' && req.user.user_type !== 'admin') {
    return next(new AppError('Only super admins can modify system roles', 403));
  }

  // Check for duplicate role name (excluding current role)
  if (req.body.name) {
    const existingRole = await Role.findOne({
      where: {
        id: { [Op.ne]: id },
        name: req.body.name,
        [Op.or]: [
          { company_id: companyId },
          { role_type: 'system' }
        ]
      }
    });

    if (existingRole) {
      return next(new AppError('Role with this name already exists', 400));
    }
  }

  // Validate inherits_from role if provided
  if (req.body.inherits_from) {
    const parentRole = await Role.findOne({
      where: {
        id: req.body.inherits_from,
        [Op.or]: [
          { company_id: companyId },
          { role_type: 'system' }
        ],
        is_active: true
      }
    });

    if (!parentRole) {
      return next(new AppError('Parent role not found or inactive', 400));
    }
  }

  // Update role
  await role.update({
    ...req.body,
    updated_by: req.user.id
  });

  // Handle permission updates if provided
  if (req.body.permission_ids !== undefined) {
    // Remove existing permissions
    await RolePermission.destroy({
      where: { role_id: id }
    });

    // Add new permissions
    if (req.body.permission_ids.length > 0) {
      const permissions = await Permission.findAll({
        where: {
          id: { [Op.in]: req.body.permission_ids },
          is_active: true
        }
      });

      if (permissions.length !== req.body.permission_ids.length) {
        return next(new AppError('Some permissions not found or inactive', 400));
      }

      const rolePermissions = req.body.permission_ids.map(permissionId => ({
        role_id: id,
        permission_id: permissionId,
        granted_by: req.user.id,
        granted_at: new Date()
      }));

      await RolePermission.bulkCreate(rolePermissions);
    }
  }

  // Fetch updated role with associations
  const updatedRole = await Role.findByPk(id, {
    include: [
      {
        model: Permission,
        as: 'permissions',
        attributes: ['id', 'name', 'display_name', 'module', 'action'],
        through: { attributes: [] }
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      role: updatedRole
    }
  });
});

// Delete role
const deleteRole = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const role = await Role.findOne({
    where: {
      id,
      [Op.or]: [
        { company_id: companyId },
        { role_type: 'system' }
      ]
    }
  });

  if (!role) {
    return next(new AppError('Role not found', 404));
  }

  // Prevent deletion of system roles
  if (role.role_type === 'system') {
    return next(new AppError('System roles cannot be deleted', 400));
  }

  // Check if role is assigned to users
  const userCount = await UserRole.count({
    where: { role_id: id }
  });

  if (userCount > 0) {
    return next(new AppError(`Cannot delete role. It is assigned to ${userCount} user(s)`, 400));
  }

  // Remove role permissions
  await RolePermission.destroy({
    where: { role_id: id }
  });

  await role.destroy();

  res.status(200).json({
    status: 'success',
    message: 'Role deleted successfully'
  });
});

// Get role statistics
const getRoleStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  // Total roles count
  const totalRoles = await Role.count({
    where: {
      [Op.or]: [
        { company_id: companyId },
        { role_type: 'system' }
      ]
    }
  });

  // Active roles count
  const activeRoles = await Role.count({
    where: {
      [Op.or]: [
        { company_id: companyId },
        { role_type: 'system' }
      ],
      is_active: true
    }
  });

  // Roles by type
  const rolesByType = await Role.findAll({
    where: {
      [Op.or]: [
        { company_id: companyId },
        { role_type: 'system' }
      ]
    },
    attributes: [
      'role_type',
      [Role.sequelize.fn('COUNT', Role.sequelize.col('id')), 'count']
    ],
    group: ['role_type'],
    raw: true
  });

  // Admin roles count
  const adminRoles = await Role.count({
    where: {
      [Op.or]: [
        { company_id: companyId },
        { role_type: 'system' }
      ],
      is_admin: true
    }
  });

  // Most assigned roles
  const mostAssignedRoles = await Role.findAll({
    where: {
      [Op.or]: [
        { company_id: companyId },
        { role_type: 'system' }
      ]
    },
    include: [{
      model: User,
      as: 'users',
      attributes: []
    }],
    attributes: [
      'id',
      'name',
      'display_name',
      'role_type',
      [Role.sequelize.fn('COUNT', Role.sequelize.col('users.id')), 'user_count']
    ],
    group: ['Role.id'],
    order: [[Role.sequelize.fn('COUNT', Role.sequelize.col('users.id')), 'DESC']],
    limit: 5,
    raw: false
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_roles: totalRoles,
      active_roles: activeRoles,
      inactive_roles: totalRoles - activeRoles,
      admin_roles: adminRoles,
      roles_by_type: rolesByType,
      most_assigned_roles: mostAssignedRoles
    }
  });
});

// Get roles by type
const getRolesByType = catchAsync(async (req, res) => {
  const { role_type } = req.params;
  const companyId = req.user.company_id;

  const roles = await Role.findAll({
    where: {
      [Op.or]: [
        { company_id: companyId },
        { role_type: 'system' }
      ],
      role_type: role_type,
      is_active: true
    },
    attributes: ['id', 'name', 'display_name', 'description', 'level', 'data_access_level'],
    order: [['level', 'ASC'], ['display_name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      roles,
      role_type
    }
  });
});

// Get active roles (for dropdowns)
const getActiveRoles = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const roles = await Role.findAll({
    where: {
      [Op.or]: [
        { company_id: companyId },
        { role_type: 'system' }
      ],
      is_active: true
    },
    attributes: ['id', 'name', 'display_name', 'role_type', 'level', 'data_access_level'],
    order: [['role_type', 'ASC'], ['level', 'ASC'], ['display_name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      roles
    }
  });
});

// Assign role to user
const assignRoleToUser = catchAsync(async (req, res, next) => {
  const { role_id, user_id } = req.body;
  const companyId = req.user.company_id;

  // Validate role
  const role = await Role.findOne({
    where: {
      id: role_id,
      [Op.or]: [
        { company_id: companyId },
        { role_type: 'system' }
      ],
      is_active: true
    }
  });

  if (!role) {
    return next(new AppError('Role not found or inactive', 404));
  }

  // Validate user
  const user = await User.findOne({
    where: {
      id: user_id,
      company_id: companyId,
      is_active: true
    }
  });

  if (!user) {
    return next(new AppError('User not found or inactive', 404));
  }

  // Check if role is already assigned
  const existingAssignment = await UserRole.findOne({
    where: {
      user_id: user_id,
      role_id: role_id
    }
  });

  if (existingAssignment) {
    return next(new AppError('Role is already assigned to this user', 400));
  }

  // Create role assignment
  await UserRole.create({
    user_id: user_id,
    role_id: role_id,
    assigned_by: req.user.id,
    assigned_at: new Date()
  });

  res.status(200).json({
    status: 'success',
    message: 'Role assigned to user successfully'
  });
});

// Remove role from user
const removeRoleFromUser = catchAsync(async (req, res, next) => {
  const { role_id, user_id } = req.body;

  const assignment = await UserRole.findOne({
    where: {
      user_id: user_id,
      role_id: role_id
    }
  });

  if (!assignment) {
    return next(new AppError('Role assignment not found', 404));
  }

  await assignment.destroy();

  res.status(200).json({
    status: 'success',
    message: 'Role removed from user successfully'
  });
});

module.exports = {
  getRoles,
  getRole,
  createRole,
  updateRole,
  deleteRole,
  getRoleStats,
  getRolesByType,
  getActiveRoles,
  assignRoleToUser,
  removeRoleFromUser
};
