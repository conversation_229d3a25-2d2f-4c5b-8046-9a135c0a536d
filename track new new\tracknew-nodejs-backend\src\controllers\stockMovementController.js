const { StockMovement, Product, Warehouse, User, Company } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all stock movements with filtering and pagination
const getStockMovements = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    movement_type,
    transaction_type,
    reference_type,
    product_id,
    warehouse_id,
    from_warehouse_id,
    to_warehouse_id,
    date_from,
    date_to,
    batch_number,
    sort_by = 'movement_date',
    sort_order = 'DESC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { reference_number: { [Op.iLike]: `%${search}%` } },
      { batch_number: { [Op.iLike]: `%${search}%` } },
      { reason: { [Op.iLike]: `%${search}%` } },
      { notes: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add filters
  if (movement_type) {
    whereConditions.movement_type = movement_type;
  }

  if (transaction_type) {
    whereConditions.transaction_type = transaction_type;
  }

  if (reference_type) {
    whereConditions.reference_type = reference_type;
  }

  if (product_id) {
    whereConditions.product_id = product_id;
  }

  if (warehouse_id) {
    whereConditions.warehouse_id = warehouse_id;
  }

  if (from_warehouse_id) {
    whereConditions.from_warehouse_id = from_warehouse_id;
  }

  if (to_warehouse_id) {
    whereConditions.to_warehouse_id = to_warehouse_id;
  }

  if (batch_number) {
    whereConditions.batch_number = { [Op.iLike]: `%${batch_number}%` };
  }

  // Date range filter
  if (date_from || date_to) {
    whereConditions.movement_date = {};
    if (date_from) {
      whereConditions.movement_date[Op.gte] = new Date(date_from);
    }
    if (date_to) {
      whereConditions.movement_date[Op.lte] = new Date(date_to);
    }
  }

  // Get stock movements with associations
  const { count, rows: stockMovements } = await StockMovement.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'product_name', 'product_code', 'sku', 'current_stock']
      },
      {
        model: Warehouse,
        as: 'warehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code']
      },
      {
        model: Warehouse,
        as: 'fromWarehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code']
      },
      {
        model: Warehouse,
        as: 'toWarehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      }
    ],
    limit: parseInt(limit),
    offset: offset,
    order: [[sort_by, sort_order.toUpperCase()]],
    distinct: true
  });

  // Calculate pagination
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      stock_movements: stockMovements,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single stock movement by ID
const getStockMovement = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const stockMovement = await StockMovement.findOne({
    where: {
      id,
      company_id: companyId
    },
    include: [
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'product_name', 'product_code', 'sku', 'current_stock', 'unit_id'],
        include: [
          {
            model: require('../models').Unit,
            as: 'unit',
            attributes: ['id', 'name', 'short_name', 'symbol']
          }
        ]
      },
      {
        model: Warehouse,
        as: 'warehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code', 'address']
      },
      {
        model: Warehouse,
        as: 'fromWarehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code', 'address']
      },
      {
        model: Warehouse,
        as: 'toWarehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code', 'address']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email', 'phone']
      }
    ]
  });

  if (!stockMovement) {
    return next(new AppError('Stock movement not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      stock_movement: stockMovement
    }
  });
});

// Create new stock movement
const createStockMovement = catchAsync(async (req, res, next) => {
  const {
    product_id,
    warehouse_id,
    movement_type,
    transaction_type,
    reference_type,
    reference_id,
    reference_number,
    movement_date = new Date(),
    quantity,
    unit_cost = 0,
    batch_number,
    serial_numbers = [],
    expiry_date,
    from_warehouse_id,
    to_warehouse_id,
    reason,
    notes
  } = req.body;

  const companyId = req.user.company_id;

  // Validate product exists and belongs to company
  const product = await Product.findOne({
    where: {
      id: product_id,
      company_id: companyId,
      is_active: true
    }
  });

  if (!product) {
    return next(new AppError('Product not found or inactive', 404));
  }

  // Validate warehouse exists and belongs to company
  const warehouse = await Warehouse.findOne({
    where: {
      id: warehouse_id,
      company_id: companyId,
      is_active: true
    }
  });

  if (!warehouse) {
    return next(new AppError('Warehouse not found or inactive', 404));
  }

  // Validate transfer warehouses if movement is transfer
  if (movement_type === 'transfer') {
    if (!from_warehouse_id || !to_warehouse_id) {
      return next(new AppError('From and To warehouses are required for transfer movements', 400));
    }

    if (from_warehouse_id === to_warehouse_id) {
      return next(new AppError('From and To warehouses cannot be the same', 400));
    }

    const fromWarehouse = await Warehouse.findOne({
      where: { id: from_warehouse_id, company_id: companyId, is_active: true }
    });

    const toWarehouse = await Warehouse.findOne({
      where: { id: to_warehouse_id, company_id: companyId, is_active: true }
    });

    if (!fromWarehouse || !toWarehouse) {
      return next(new AppError('Transfer warehouses not found or inactive', 404));
    }
  }

  // Validate quantity
  if (quantity <= 0) {
    return next(new AppError('Quantity must be greater than 0', 400));
  }

  // Check stock availability for outbound movements
  if (movement_type === 'out' || movement_type === 'transfer') {
    const currentStock = await getCurrentStock(product_id, movement_type === 'transfer' ? from_warehouse_id : warehouse_id);

    if (currentStock < quantity && !warehouse.allow_negative_stock) {
      return next(new AppError(`Insufficient stock. Available: ${currentStock}, Required: ${quantity}`, 400));
    }
  }

  // Calculate total cost
  const total_cost = parseFloat(quantity) * parseFloat(unit_cost);

  // Get current running balance
  const running_balance = await calculateRunningBalance(product_id, warehouse_id, movement_type, quantity);

  const stockMovement = await StockMovement.create({
    company_id: companyId,
    product_id,
    warehouse_id,
    movement_type,
    transaction_type,
    reference_type,
    reference_id,
    reference_number,
    movement_date: new Date(movement_date),
    quantity,
    unit_cost,
    total_cost,
    running_balance,
    batch_number,
    serial_numbers,
    expiry_date: expiry_date ? new Date(expiry_date) : null,
    from_warehouse_id,
    to_warehouse_id,
    reason,
    notes,
    created_by: req.user.id
  });

  // Update product current stock
  await updateProductStock(product_id, movement_type, quantity, warehouse_id, from_warehouse_id, to_warehouse_id);

  // Fetch the created stock movement with associations
  const createdStockMovement = await StockMovement.findByPk(stockMovement.id, {
    include: [
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'product_name', 'product_code', 'sku']
      },
      {
        model: Warehouse,
        as: 'warehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      stock_movement: createdStockMovement
    }
  });
});

// Helper function to get current stock
const getCurrentStock = async (productId, warehouseId) => {
  const result = await StockMovement.findAll({
    where: {
      product_id: productId,
      warehouse_id: warehouseId
    },
    attributes: [
      [StockMovement.sequelize.fn('SUM',
        StockMovement.sequelize.literal(`CASE
          WHEN movement_type = 'in' THEN quantity
          WHEN movement_type = 'out' THEN -quantity
          ELSE 0
        END`)
      ), 'total_stock']
    ],
    raw: true
  });

  return parseFloat(result[0]?.total_stock || 0);
};

// Helper function to calculate running balance
const calculateRunningBalance = async (productId, warehouseId, movementType, quantity) => {
  const currentStock = await getCurrentStock(productId, warehouseId);

  if (movementType === 'in') {
    return currentStock + parseFloat(quantity);
  } else if (movementType === 'out') {
    return currentStock - parseFloat(quantity);
  }

  return currentStock;
};

// Helper function to update product stock
const updateProductStock = async (productId, movementType, quantity, warehouseId, fromWarehouseId, toWarehouseId) => {
  const product = await Product.findByPk(productId);

  if (product && product.track_inventory) {
    let stockChange = 0;

    if (movementType === 'in') {
      stockChange = parseFloat(quantity);
    } else if (movementType === 'out') {
      stockChange = -parseFloat(quantity);
    }
    // For transfers, stock change is handled separately for each warehouse

    if (stockChange !== 0) {
      await product.update({
        current_stock: parseFloat(product.current_stock) + stockChange
      });
    }
  }
};

// Get stock movement statistics
const getStockMovementStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  // Total movements count
  const totalMovements = await StockMovement.count({
    where: { company_id: companyId }
  });

  // Movements by type
  const movementsByType = await StockMovement.findAll({
    where: { company_id: companyId },
    attributes: [
      'movement_type',
      [StockMovement.sequelize.fn('COUNT', StockMovement.sequelize.col('id')), 'count'],
      [StockMovement.sequelize.fn('SUM', StockMovement.sequelize.col('quantity')), 'total_quantity'],
      [StockMovement.sequelize.fn('SUM', StockMovement.sequelize.col('total_cost')), 'total_value']
    ],
    group: ['movement_type'],
    raw: true
  });

  // Movements by transaction type
  const movementsByTransaction = await StockMovement.findAll({
    where: { company_id: companyId },
    attributes: [
      'transaction_type',
      [StockMovement.sequelize.fn('COUNT', StockMovement.sequelize.col('id')), 'count'],
      [StockMovement.sequelize.fn('SUM', StockMovement.sequelize.col('total_cost')), 'total_value']
    ],
    group: ['transaction_type'],
    order: [[StockMovement.sequelize.fn('COUNT', StockMovement.sequelize.col('id')), 'DESC']],
    raw: true
  });

  // Recent movements (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const recentMovements = await StockMovement.count({
    where: {
      company_id: companyId,
      movement_date: { [Op.gte]: thirtyDaysAgo }
    }
  });

  // Most active products
  const mostActiveProducts = await StockMovement.findAll({
    where: { company_id: companyId },
    include: [{
      model: Product,
      as: 'product',
      attributes: ['id', 'product_name', 'product_code', 'current_stock']
    }],
    attributes: [
      'product_id',
      [StockMovement.sequelize.fn('COUNT', StockMovement.sequelize.col('StockMovement.id')), 'movement_count'],
      [StockMovement.sequelize.fn('SUM', StockMovement.sequelize.col('quantity')), 'total_quantity']
    ],
    group: ['product_id', 'product.id'],
    order: [[StockMovement.sequelize.fn('COUNT', StockMovement.sequelize.col('StockMovement.id')), 'DESC']],
    limit: 10,
    raw: false
  });

  // Most active warehouses
  const mostActiveWarehouses = await StockMovement.findAll({
    where: { company_id: companyId },
    include: [{
      model: Warehouse,
      as: 'warehouse',
      attributes: ['id', 'warehouse_name', 'warehouse_code']
    }],
    attributes: [
      'warehouse_id',
      [StockMovement.sequelize.fn('COUNT', StockMovement.sequelize.col('StockMovement.id')), 'movement_count']
    ],
    group: ['warehouse_id', 'warehouse.id'],
    order: [[StockMovement.sequelize.fn('COUNT', StockMovement.sequelize.col('StockMovement.id')), 'DESC']],
    limit: 5,
    raw: false
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_movements: totalMovements,
      recent_movements: recentMovements,
      movements_by_type: movementsByType,
      movements_by_transaction: movementsByTransaction,
      most_active_products: mostActiveProducts,
      most_active_warehouses: mostActiveWarehouses
    }
  });
});

// Get stock movements by product
const getMovementsByProduct = catchAsync(async (req, res) => {
  const { product_id } = req.params;
  const { warehouse_id, limit = 50 } = req.query;
  const companyId = req.user.company_id;

  const whereConditions = {
    company_id: companyId,
    product_id: product_id
  };

  if (warehouse_id) {
    whereConditions.warehouse_id = warehouse_id;
  }

  const movements = await StockMovement.findAll({
    where: whereConditions,
    include: [
      {
        model: Warehouse,
        as: 'warehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code']
      },
      {
        model: Warehouse,
        as: 'fromWarehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code']
      },
      {
        model: Warehouse,
        as: 'toWarehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name']
      }
    ],
    order: [['movement_date', 'DESC']],
    limit: parseInt(limit)
  });

  res.status(200).json({
    status: 'success',
    data: {
      movements
    }
  });
});

// Get stock movements by warehouse
const getMovementsByWarehouse = catchAsync(async (req, res) => {
  const { warehouse_id } = req.params;
  const { product_id, movement_type, limit = 50 } = req.query;
  const companyId = req.user.company_id;

  const whereConditions = {
    company_id: companyId,
    warehouse_id: warehouse_id
  };

  if (product_id) {
    whereConditions.product_id = product_id;
  }

  if (movement_type) {
    whereConditions.movement_type = movement_type;
  }

  const movements = await StockMovement.findAll({
    where: whereConditions,
    include: [
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'product_name', 'product_code', 'sku']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name']
      }
    ],
    order: [['movement_date', 'DESC']],
    limit: parseInt(limit)
  });

  res.status(200).json({
    status: 'success',
    data: {
      movements
    }
  });
});

// Get current stock levels
const getCurrentStockLevels = catchAsync(async (req, res) => {
  const { warehouse_id, product_id, low_stock_only } = req.query;
  const companyId = req.user.company_id;

  let whereConditions = {
    company_id: companyId,
    track_inventory: true,
    is_active: true
  };

  if (product_id) {
    whereConditions.id = product_id;
  }

  const products = await Product.findAll({
    where: whereConditions,
    attributes: ['id', 'product_name', 'product_code', 'sku', 'current_stock', 'minimum_stock', 'reorder_level'],
    include: [
      {
        model: require('../models').Unit,
        as: 'unit',
        attributes: ['id', 'name', 'short_name', 'symbol']
      }
    ]
  });

  // If warehouse_id is specified, calculate stock for that specific warehouse
  let stockLevels = [];

  for (const product of products) {
    let warehouseStock = product.current_stock;

    if (warehouse_id) {
      warehouseStock = await getCurrentStock(product.id, warehouse_id);
    }

    const stockLevel = {
      product_id: product.id,
      product_name: product.product_name,
      product_code: product.product_code,
      sku: product.sku,
      current_stock: warehouseStock,
      minimum_stock: product.minimum_stock,
      reorder_level: product.reorder_level,
      unit: product.unit,
      stock_status: getStockStatus(warehouseStock, product.minimum_stock, product.reorder_level)
    };

    // Filter for low stock if requested
    if (low_stock_only === 'true') {
      if (stockLevel.stock_status === 'low' || stockLevel.stock_status === 'out_of_stock') {
        stockLevels.push(stockLevel);
      }
    } else {
      stockLevels.push(stockLevel);
    }
  }

  res.status(200).json({
    status: 'success',
    data: {
      stock_levels: stockLevels,
      warehouse_id: warehouse_id || 'all'
    }
  });
});

// Helper function to determine stock status
const getStockStatus = (currentStock, minimumStock, reorderLevel) => {
  if (currentStock <= 0) return 'out_of_stock';
  if (currentStock <= minimumStock) return 'low';
  if (reorderLevel && currentStock <= reorderLevel) return 'reorder';
  return 'normal';
};

// Create stock adjustment
const createStockAdjustment = catchAsync(async (req, res, next) => {
  const {
    product_id,
    warehouse_id,
    adjustment_type, // 'increase' or 'decrease'
    quantity,
    reason,
    notes
  } = req.body;

  const companyId = req.user.company_id;

  // Validate inputs
  if (!['increase', 'decrease'].includes(adjustment_type)) {
    return next(new AppError('Adjustment type must be either increase or decrease', 400));
  }

  if (quantity <= 0) {
    return next(new AppError('Quantity must be greater than 0', 400));
  }

  // Validate product and warehouse
  const product = await Product.findOne({
    where: { id: product_id, company_id: companyId, is_active: true }
  });

  if (!product) {
    return next(new AppError('Product not found or inactive', 404));
  }

  const warehouse = await Warehouse.findOne({
    where: { id: warehouse_id, company_id: companyId, is_active: true }
  });

  if (!warehouse) {
    return next(new AppError('Warehouse not found or inactive', 404));
  }

  // Check stock availability for decrease adjustments
  if (adjustment_type === 'decrease') {
    const currentStock = await getCurrentStock(product_id, warehouse_id);
    if (currentStock < quantity && !warehouse.allow_negative_stock) {
      return next(new AppError(`Insufficient stock. Available: ${currentStock}, Required: ${quantity}`, 400));
    }
  }

  // Determine movement type
  const movement_type = adjustment_type === 'increase' ? 'in' : 'out';

  // Calculate running balance
  const running_balance = await calculateRunningBalance(product_id, warehouse_id, movement_type, quantity);

  // Create stock movement record
  const stockMovement = await StockMovement.create({
    company_id: companyId,
    product_id,
    warehouse_id,
    movement_type,
    transaction_type: 'adjustment',
    reference_type: 'adjustment',
    movement_date: new Date(),
    quantity,
    unit_cost: 0,
    total_cost: 0,
    running_balance,
    reason,
    notes,
    created_by: req.user.id
  });

  // Update product stock
  await updateProductStock(product_id, movement_type, quantity, warehouse_id);

  // Fetch created movement with associations
  const createdMovement = await StockMovement.findByPk(stockMovement.id, {
    include: [
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'product_name', 'product_code', 'current_stock']
      },
      {
        model: Warehouse,
        as: 'warehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    message: 'Stock adjustment created successfully',
    data: {
      stock_movement: createdMovement
    }
  });
});

module.exports = {
  getStockMovements,
  getStockMovement,
  createStockMovement,
  getStockMovementStats,
  getMovementsByProduct,
  getMovementsByWarehouse,
  getCurrentStockLevels,
  createStockAdjustment
};
