const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const UserRole = sequelize.define('UserRole', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  role_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'roles',
      key: 'id'
    }
  },
  assigned_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who assigned this role'
  },
  assigned_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  is_primary: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is the primary role for the user'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  valid_from: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Role assignment validity start date'
  },
  valid_until: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Role assignment validity end date'
  },
  context: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object for context-specific role assignments'
  },
  restrictions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of additional restrictions for this role assignment'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'user_roles',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['role_id']
    },
    {
      fields: ['user_id', 'role_id'],
      unique: true
    },
    {
      fields: ['assigned_by']
    },
    {
      fields: ['is_primary']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['assigned_at']
    }
  ]
});

module.exports = UserRole;
