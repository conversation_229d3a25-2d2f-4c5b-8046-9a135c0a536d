const express = require('express');
const { body, query } = require('express-validator');
const templateController = require('../controllers/templateController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating template
const createTemplateValidation = [
  body('name')
    .notEmpty()
    .withMessage('Template name is required')
    .isLength({ min: 2, max: 255 })
    .withMessage('Template name must be between 2 and 255 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('type')
    .notEmpty()
    .withMessage('Template type is required')
    .isIn(['email', 'sms', 'whatsapp', 'push', 'in_app'])
    .withMessage('Invalid template type'),
  
  body('category')
    .notEmpty()
    .withMessage('Template category is required')
    .isIn([
      'welcome', 
      'invoice', 
      'payment', 
      'service', 
      'marketing', 
      'notification', 
      'reminder', 
      'confirmation', 
      'follow_up', 
      'support'
    ])
    .withMessage('Invalid template category'),
  
  body('subject')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('Subject must be between 1 and 255 characters'),
  
  body('content')
    .notEmpty()
    .withMessage('Template content is required')
    .isLength({ min: 1, max: 10000 })
    .withMessage('Content must be between 1 and 10000 characters'),
  
  body('variables')
    .optional()
    .isArray()
    .withMessage('Variables must be an array'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean'),
  
  body('is_default')
    .optional()
    .isBoolean()
    .withMessage('Is default must be a boolean'),
  
  body('language')
    .optional()
    .isLength({ min: 2, max: 5 })
    .withMessage('Language code must be between 2 and 5 characters'),
  
  body('tags')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Tags must be maximum 500 characters')
];

// Validation rules for updating template
const updateTemplateValidation = [
  body('name')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Template name must be between 2 and 255 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('type')
    .optional()
    .isIn(['email', 'sms', 'whatsapp', 'push', 'in_app'])
    .withMessage('Invalid template type'),
  
  body('category')
    .optional()
    .isIn([
      'welcome', 
      'invoice', 
      'payment', 
      'service', 
      'marketing', 
      'notification', 
      'reminder', 
      'confirmation', 
      'follow_up', 
      'support'
    ])
    .withMessage('Invalid template category'),
  
  body('subject')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('Subject must be between 1 and 255 characters'),
  
  body('content')
    .optional()
    .isLength({ min: 1, max: 10000 })
    .withMessage('Content must be between 1 and 10000 characters'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean'),
  
  body('is_default')
    .optional()
    .isBoolean()
    .withMessage('Is default must be a boolean')
];

// Validation for preview template
const previewTemplateValidation = [
  body('sample_data')
    .notEmpty()
    .withMessage('Sample data is required')
    .isObject()
    .withMessage('Sample data must be a valid JSON object')
];

// Validation for sending test template
const sendTestTemplateValidation = [
  body('sample_data')
    .notEmpty()
    .withMessage('Sample data is required')
    .isObject()
    .withMessage('Sample data must be a valid JSON object'),
  
  body('test_email')
    .optional()
    .isEmail()
    .withMessage('Test email must be valid'),
  
  body('test_phone')
    .optional()
    .isLength({ min: 10, max: 15 })
    .withMessage('Test phone must be between 10 and 15 characters')
];

// Validation for query parameters
const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  query('type')
    .optional()
    .isIn(['email', 'sms', 'whatsapp', 'push', 'in_app'])
    .withMessage('Invalid template type'),
  
  query('category')
    .optional()
    .isIn([
      'welcome', 
      'invoice', 
      'payment', 
      'service', 
      'marketing', 
      'notification', 
      'reminder', 
      'confirmation', 
      'follow_up', 
      'support'
    ])
    .withMessage('Invalid template category'),
  
  query('is_active')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean'),
  
  query('sort_by')
    .optional()
    .isIn(['name', 'created_at', 'updated_at', 'type', 'category'])
    .withMessage('Invalid sort field'),
  
  query('sort_order')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC')
];

// Routes
router
  .route('/')
  .get(queryValidation, validateRequest, templateController.getTemplates)
  .post(createTemplateValidation, validateRequest, restrictTo('admin', 'sub_admin', 'manager'), templateController.createTemplate);

router
  .route('/categories')
  .get(templateController.getTemplateCategories);

router
  .route('/variables')
  .get(templateController.getTemplateVariables);

router
  .route('/:id/preview')
  .post(previewTemplateValidation, validateRequest, templateController.previewTemplate);

router
  .route('/:id/test')
  .post(sendTestTemplateValidation, validateRequest, restrictTo('admin', 'sub_admin', 'manager'), templateController.sendTestTemplate);

router
  .route('/:id')
  .get(templateController.getTemplate)
  .put(updateTemplateValidation, validateRequest, restrictTo('admin', 'sub_admin', 'manager'), templateController.updateTemplate)
  .delete(restrictTo('admin', 'sub_admin'), templateController.deleteTemplate);

module.exports = router;
