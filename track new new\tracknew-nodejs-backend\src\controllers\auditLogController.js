const { User, Company } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all audit logs with filtering and pagination
const getAuditLogs = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    action,
    entity_type,
    user_id,
    start_date,
    end_date,
    ip_address,
    sort_by = 'created_at',
    sort_order = 'DESC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { action: { [Op.iLike]: `%${search}%` } },
      { entity_type: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } },
      { ip_address: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add date range filter
  if (start_date || end_date) {
    whereConditions.created_at = {};
    if (start_date) {
      whereConditions.created_at[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      whereConditions.created_at[Op.lte] = new Date(end_date);
    }
  }

  // Add filters
  if (action) {
    whereConditions.action = action;
  }

  if (entity_type) {
    whereConditions.entity_type = entity_type;
  }

  if (user_id) {
    whereConditions.user_id = user_id;
  }

  if (ip_address) {
    whereConditions.ip_address = ip_address;
  }

  // Simulate audit log records (since we don't have an AuditLog model)
  // In a real implementation, you would query the AuditLog model
  const auditLogs = [];
  const totalCount = 0;

  // Calculate pagination
  const totalPages = Math.ceil(totalCount / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      audit_logs: auditLogs,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: totalCount,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single audit log by ID
const getAuditLog = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  // In a real implementation, you would query the AuditLog model
  const auditLog = null;

  if (!auditLog) {
    return next(new AppError('Audit log not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      audit_log: auditLog
    }
  });
});

// Create audit log entry
const createAuditLog = catchAsync(async (req, res, next) => {
  const {
    action,
    entity_type,
    entity_id,
    description,
    old_values,
    new_values,
    metadata
  } = req.body;

  const companyId = req.user.company_id;
  const userId = req.user.id;
  const ipAddress = req.ip || req.connection.remoteAddress;
  const userAgent = req.get('User-Agent');

  // Create audit log entry
  const auditLogData = {
    company_id: companyId,
    user_id: userId,
    action,
    entity_type,
    entity_id: entity_id ? parseInt(entity_id) : null,
    description,
    old_values,
    new_values,
    ip_address: ipAddress,
    user_agent: userAgent,
    metadata,
    created_at: new Date()
  };

  // In a real implementation, you would save to AuditLog model
  // const auditLog = await AuditLog.create(auditLogData);

  res.status(201).json({
    status: 'success',
    message: 'Audit log created successfully',
    data: {
      audit_log: {
        id: Date.now(), // Temporary ID
        ...auditLogData
      }
    }
  });
});

// Get audit log statistics
const getAuditLogStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const { start_date, end_date } = req.query;

  // Build date filter
  const dateFilter = {};
  if (start_date || end_date) {
    dateFilter.created_at = {};
    if (start_date) {
      dateFilter.created_at[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      dateFilter.created_at[Op.lte] = new Date(end_date);
    }
  }

  // In a real implementation, you would query the AuditLog model
  const stats = {
    total_logs: 0,
    logs_by_action: [],
    logs_by_entity_type: [],
    logs_by_user: [],
    recent_activity: 0,
    top_ip_addresses: []
  };

  res.status(200).json({
    status: 'success',
    data: stats
  });
});

// Get user activity
const getUserActivity = catchAsync(async (req, res) => {
  const { user_id } = req.params;
  const {
    start_date,
    end_date,
    limit = 50
  } = req.query;

  const companyId = req.user.company_id;

  // Build date filter
  const dateFilter = {};
  if (start_date || end_date) {
    dateFilter.created_at = {};
    if (start_date) {
      dateFilter.created_at[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      dateFilter.created_at[Op.lte] = new Date(end_date);
    }
  }

  // Validate user exists and belongs to company
  const user = await User.findOne({
    where: {
      id: user_id,
      company_id: companyId
    },
    attributes: ['id', 'name', 'email']
  });

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // In a real implementation, you would query the AuditLog model
  const userActivity = [];

  res.status(200).json({
    status: 'success',
    data: {
      user,
      activity: userActivity,
      total_activities: userActivity.length
    }
  });
});

// Get entity history
const getEntityHistory = catchAsync(async (req, res, next) => {
  const { entity_type, entity_id } = req.params;
  const companyId = req.user.company_id;

  // In a real implementation, you would query the AuditLog model
  const entityHistory = [];

  res.status(200).json({
    status: 'success',
    data: {
      entity_type,
      entity_id: parseInt(entity_id),
      history: entityHistory,
      total_changes: entityHistory.length
    }
  });
});

// Export audit logs
const exportAuditLogs = catchAsync(async (req, res) => {
  const {
    format = 'csv',
    start_date,
    end_date,
    action,
    entity_type,
    user_id
  } = req.query;

  const companyId = req.user.company_id;

  // Build filters
  const whereConditions = {
    company_id: companyId
  };

  if (start_date || end_date) {
    whereConditions.created_at = {};
    if (start_date) {
      whereConditions.created_at[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      whereConditions.created_at[Op.lte] = new Date(end_date);
    }
  }

  if (action) whereConditions.action = action;
  if (entity_type) whereConditions.entity_type = entity_type;
  if (user_id) whereConditions.user_id = user_id;

  // In a real implementation, you would:
  // 1. Query the AuditLog model with filters
  // 2. Format the data according to the requested format
  // 3. Generate and return the file

  res.status(200).json({
    status: 'success',
    message: 'Audit logs export initiated',
    data: {
      export_format: format,
      download_url: `/api/audit-logs/download/export-${Date.now()}.${format}`
    }
  });
});

// Clean up old audit logs
const cleanupAuditLogs = catchAsync(async (req, res) => {
  const { retention_days = 365 } = req.body;
  const companyId = req.user.company_id;

  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - retention_days);

  // In a real implementation, you would delete old audit logs
  const deletedCount = 0;

  res.status(200).json({
    status: 'success',
    message: `${deletedCount} old audit log(s) cleaned up`,
    data: {
      deleted_count: deletedCount,
      retention_days: retention_days,
      cutoff_date: cutoffDate
    }
  });
});

// Middleware to automatically log actions
const logAction = (action, entityType) => {
  return (req, res, next) => {
    // Store original res.json
    const originalJson = res.json;

    // Override res.json to capture response
    res.json = function(data) {
      // Log the action after successful response
      if (res.statusCode >= 200 && res.statusCode < 300) {
        const auditLogData = {
          company_id: req.user?.company_id,
          user_id: req.user?.id,
          action,
          entity_type: entityType,
          entity_id: req.params?.id ? parseInt(req.params.id) : null,
          description: `${action} ${entityType}`,
          ip_address: req.ip || req.connection.remoteAddress,
          user_agent: req.get('User-Agent'),
          metadata: {
            method: req.method,
            url: req.originalUrl,
            body: req.body,
            params: req.params,
            query: req.query
          }
        };

        // In a real implementation, you would save to AuditLog model
        // AuditLog.create(auditLogData).catch(console.error);
      }

      // Call original res.json
      return originalJson.call(this, data);
    };

    next();
  };
};

module.exports = {
  getAuditLogs,
  getAuditLog,
  createAuditLog,
  getAuditLogStats,
  getUserActivity,
  getEntityHistory,
  exportAuditLogs,
  cleanupAuditLogs,
  logAction
};
