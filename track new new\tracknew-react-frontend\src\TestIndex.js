// Ultra-simple test to verify <PERSON><PERSON> is working
import React from 'react';
import <PERSON>actDOM from 'react-dom/client';

// Simplest possible component
function TestApp() {
  return React.createElement('div', {
    style: {
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh'
    }
  }, [
    React.createElement('h1', { key: 'title' }, '🎉 Track New Application'),
    React.createElement('p', { key: 'message' }, 'If you can see this, React is working!'),
    React.createElement('div', {
      key: 'info',
      style: {
        backgroundColor: 'white',
        padding: '15px',
        borderRadius: '8px',
        marginTop: '20px'
      }
    }, [
      React.createElement('h3', { key: 'status' }, 'System Status:'),
      React.createElement('ul', { key: 'list' }, [
        React.createElement('li', { key: 'react' }, '✅ React: Working'),
        React.createElement('li', { key: 'js' }, '✅ JavaScript: Working'),
        React.createElement('li', { key: 'vite' }, '✅ Vite: Running on port 3001')
      ])
    ])
  ]);
}

// Create root and render
const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(React.createElement(TestApp));
