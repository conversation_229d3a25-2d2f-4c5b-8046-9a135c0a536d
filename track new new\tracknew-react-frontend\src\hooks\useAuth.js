import { useSelector, useDispatch } from 'react-redux';
import { useCallback } from 'react';
import { 
  selectUser, 
  selectAuthLoading, 
  selectAuthError,
  login,
  register,
  logout,
  clearAuthError
} from '../store/slices/authSlice';

export const useAuth = () => {
  const dispatch = useDispatch();
  
  const user = useSelector(selectUser);
  const loading = useSelector(selectAuthLoading);
  const error = useSelector(selectAuthError);
  
  const isAuthenticated = !!user;
  
  const handleLogin = useCallback((credentials) => {
    return dispatch(login(credentials));
  }, [dispatch]);
  
  const handleRegister = useCallback((userData) => {
    return dispatch(register(userData));
  }, [dispatch]);
  
  const handleLogout = useCallback(() => {
    return dispatch(logout());
  }, [dispatch]);
  
  const clearError = useCallback(() => {
    dispatch(clearAuthError());
  }, [dispatch]);
  
  return {
    user,
    loading,
    error,
    isAuthenticated,
    login: handleLogin,
    register: handleRegister,
    logout: handleLogout,
    clearError
  };
};
