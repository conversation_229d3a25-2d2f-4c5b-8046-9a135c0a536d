# 🚀 TrackNew Production Deployment Guide

## 📋 Overview

This guide provides step-by-step instructions for deploying the TrackNew Service Management System to production environments.

## 🎯 Prerequisites

### System Requirements
- **Server**: Ubuntu 20.04+ or CentOS 8+
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 50GB SSD
- **CPU**: Minimum 2 cores, Recommended 4+ cores
- **Network**: Static IP address, SSL certificate

### Software Requirements
- **Node.js**: 18.x or higher
- **PostgreSQL**: 14.x or higher
- **Redis**: 6.x or higher
- **Nginx**: 1.18+ (reverse proxy)
- **PM2**: Process manager
- **Docker**: Optional for containerized deployment

## 🔧 Pre-Deployment Setup

### 1. Server Preparation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# Install Redis
sudo apt install redis-server -y

# Install Nginx
sudo apt install nginx -y

# Install PM2 globally
sudo npm install -g pm2
```

### 2. Database Setup
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE tracknew_production;
CREATE USER tracknew_user WITH ENCRYPTED PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE tracknew_production TO tracknew_user;
ALTER USER tracknew_user CREATEDB;
\q
```

### 3. Redis Configuration
```bash
# Edit Redis configuration
sudo nano /etc/redis/redis.conf

# Set password (uncomment and modify)
requirepass your_redis_password

# Restart Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

## 🚀 Backend Deployment

### 1. Clone and Setup
```bash
# Create application directory
sudo mkdir -p /var/www/tracknew
sudo chown $USER:$USER /var/www/tracknew

# Clone repository
cd /var/www/tracknew
git clone <your-repository-url> .

# Navigate to backend
cd tracknew-nodejs-backend

# Install dependencies
npm install --production

# Copy production environment
cp .env.production .env

# Edit environment variables
nano .env
```

### 2. Environment Configuration
Update `.env` with production values:
```env
NODE_ENV=production
PORT=8000
DB_HOST=localhost
DB_NAME=tracknew_production
DB_USER=tracknew_user
DB_PASSWORD=your_secure_password
JWT_SECRET=your_super_secure_jwt_secret_minimum_32_characters
REDIS_PASSWORD=your_redis_password
```

### 3. Database Migration
```bash
# Run database migrations
npm run migrate

# Seed initial data (if needed)
npm run seed
```

### 4. PM2 Process Management
```bash
# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'tracknew-api',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 8000
    },
    error_file: '/var/log/tracknew/api-error.log',
    out_file: '/var/log/tracknew/api-out.log',
    log_file: '/var/log/tracknew/api.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF

# Create log directory
sudo mkdir -p /var/log/tracknew
sudo chown $USER:$USER /var/log/tracknew

# Start application with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME
```

## 🎨 Frontend Deployment

### 1. Build Frontend
```bash
# Navigate to frontend directory
cd /var/www/tracknew/tracknew-react-frontend

# Install dependencies
npm install

# Build for production
npm run build
```

### 2. Nginx Configuration
```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/tracknew

# Add configuration:
server {
    listen 80;
    server_name tracknew.com www.tracknew.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name tracknew.com www.tracknew.com;

    ssl_certificate /etc/ssl/certs/tracknew.crt;
    ssl_certificate_key /etc/ssl/private/tracknew.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    root /var/www/tracknew/tracknew-react-frontend/build;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # API proxy
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static files
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # React Router
    location / {
        try_files $uri $uri/ /index.html;
    }
}

# Enable site
sudo ln -s /etc/nginx/sites-available/tracknew /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 🔒 SSL Certificate Setup

### Using Let's Encrypt (Recommended)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d tracknew.com -d www.tracknew.com

# Test auto-renewal
sudo certbot renew --dry-run
```

### Using Custom Certificate
```bash
# Copy certificate files
sudo cp your-certificate.crt /etc/ssl/certs/tracknew.crt
sudo cp your-private-key.key /etc/ssl/private/tracknew.key

# Set permissions
sudo chmod 644 /etc/ssl/certs/tracknew.crt
sudo chmod 600 /etc/ssl/private/tracknew.key
```

## 📊 Monitoring Setup

### 1. Application Monitoring
```bash
# Install monitoring tools
npm install -g pm2-logrotate

# Configure log rotation
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
```

### 2. System Monitoring
```bash
# Install htop for system monitoring
sudo apt install htop -y

# Install fail2ban for security
sudo apt install fail2ban -y

# Configure fail2ban
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

## 🔄 Backup Strategy

### 1. Database Backup
```bash
# Create backup script
cat > /home/<USER>/backup-db.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/tracknew"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

# Database backup
pg_dump -h localhost -U tracknew_user tracknew_production > $BACKUP_DIR/db_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "Database backup completed: $DATE"
EOF

chmod +x /home/<USER>/backup-db.sh

# Add to crontab (daily at 2 AM)
(crontab -l 2>/dev/null; echo "0 2 * * * /home/<USER>/backup-db.sh") | crontab -
```

### 2. File Backup
```bash
# Create file backup script
cat > /home/<USER>/backup-files.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/tracknew"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/var/www/tracknew"

# Create backup
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz -C $APP_DIR .

# Remove backups older than 7 days
find $BACKUP_DIR -name "files_backup_*.tar.gz" -mtime +7 -delete

echo "Files backup completed: $DATE"
EOF

chmod +x /home/<USER>/backup-files.sh

# Add to crontab (weekly on Sunday at 3 AM)
(crontab -l 2>/dev/null; echo "0 3 * * 0 /home/<USER>/backup-files.sh") | crontab -
```

## 🚀 Deployment Checklist

### Pre-Deployment
- [ ] Server provisioned and configured
- [ ] Domain name configured with DNS
- [ ] SSL certificate obtained
- [ ] Database created and configured
- [ ] Redis installed and configured
- [ ] Environment variables set

### Deployment
- [ ] Code deployed to server
- [ ] Dependencies installed
- [ ] Database migrations run
- [ ] Frontend built and deployed
- [ ] Nginx configured
- [ ] PM2 processes started
- [ ] SSL certificate configured

### Post-Deployment
- [ ] Application accessible via HTTPS
- [ ] API endpoints responding
- [ ] Database connections working
- [ ] Monitoring configured
- [ ] Backups scheduled
- [ ] Security measures in place
- [ ] Performance optimized

## 🔧 Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check PM2 logs
pm2 logs tracknew-api

# Check system logs
sudo journalctl -u nginx
sudo journalctl -u postgresql
sudo journalctl -u redis
```

#### Database Connection Issues
```bash
# Test database connection
psql -h localhost -U tracknew_user -d tracknew_production

# Check PostgreSQL status
sudo systemctl status postgresql
```

#### SSL Certificate Issues
```bash
# Test SSL certificate
openssl s_client -connect tracknew.com:443

# Renew Let's Encrypt certificate
sudo certbot renew
```

## 📞 Support

For deployment support:
- **Email**: <EMAIL>
- **Documentation**: https://docs.tracknew.com
- **Issues**: Create an issue on GitHub

---

**Deployment completed successfully! 🎉**
