import React, { useState } from 'react';

// Styled React application with inline styles
function StyledApp() {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [user, setUser] = useState(null);

  // Simple login function
  const handleLogin = (email, password) => {
    if (email && password) {
      setUser({ name: 'Demo User', email: email });
      setCurrentPage('dashboard');
    }
  };

  // Simple logout function
  const handleLogout = () => {
    setUser(null);
    setCurrentPage('login');
  };

  // Styles object
  const styles = {
    container: {
      minHeight: '100vh',
      backgroundColor: '#f3f4f6',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    },
    loginContainer: {
      minHeight: '100vh',
      backgroundColor: '#f3f4f6',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    },
    loginCard: {
      maxWidth: '400px',
      width: '100%',
      backgroundColor: 'white',
      borderRadius: '8px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      padding: '32px'
    },
    header: {
      backgroundColor: 'white',
      borderBottom: '1px solid #e5e7eb',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    },
    headerContent: {
      maxWidth: '1200px',
      margin: '0 auto',
      padding: '0 24px',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      height: '64px'
    },
    sidebar: {
      width: '256px',
      backgroundColor: 'white',
      minHeight: '100vh',
      borderRight: '1px solid #e5e7eb',
      boxShadow: '2px 0 4px rgba(0, 0, 0, 0.05)'
    },
    mainContent: {
      flex: 1,
      padding: '32px'
    },
    button: {
      backgroundColor: '#3b82f6',
      color: 'white',
      border: 'none',
      borderRadius: '6px',
      padding: '12px 24px',
      fontSize: '14px',
      fontWeight: '600',
      cursor: 'pointer',
      transition: 'all 0.2s ease'
    },
    buttonHover: {
      backgroundColor: '#2563eb'
    },
    input: {
      width: '100%',
      padding: '12px 16px',
      border: '1px solid #d1d5db',
      borderRadius: '6px',
      fontSize: '14px',
      outline: 'none',
      transition: 'border-color 0.2s ease'
    },
    card: {
      backgroundColor: 'white',
      borderRadius: '8px',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      padding: '24px',
      marginBottom: '24px'
    },
    statCard: {
      backgroundColor: 'white',
      borderRadius: '8px',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      padding: '24px',
      textAlign: 'center'
    },
    navItem: {
      width: '100%',
      textAlign: 'left',
      padding: '12px 16px',
      border: 'none',
      backgroundColor: 'transparent',
      borderRadius: '6px',
      margin: '4px 0',
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      fontSize: '14px',
      fontWeight: '500',
      transition: 'all 0.2s ease'
    },
    navItemActive: {
      backgroundColor: '#dbeafe',
      color: '#1d4ed8'
    },
    navItemHover: {
      backgroundColor: '#f3f4f6'
    }
  };

  // Login Page Component
  const LoginPage = () => (
    <div style={styles.loginContainer}>
      <div style={styles.loginCard}>
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <h1 style={{ fontSize: '32px', fontWeight: 'bold', color: '#111827', margin: '0 0 8px 0' }}>
            Track New
          </h1>
          <p style={{ color: '#6b7280', margin: 0 }}>Service Management System</p>
        </div>
        
        <form onSubmit={(e) => {
          e.preventDefault();
          const formData = new FormData(e.target);
          handleLogin(formData.get('email'), formData.get('password'));
        }}>
          <div style={{ marginBottom: '16px' }}>
            <label style={{ display: 'block', color: '#374151', fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
              Email
            </label>
            <input
              type="email"
              name="email"
              style={styles.input}
              placeholder="Enter your email"
              defaultValue="<EMAIL>"
            />
          </div>
          
          <div style={{ marginBottom: '24px' }}>
            <label style={{ display: 'block', color: '#374151', fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
              Password
            </label>
            <input
              type="password"
              name="password"
              style={styles.input}
              placeholder="Enter your password"
              defaultValue="password123"
            />
          </div>
          
          <button
            type="submit"
            style={{ ...styles.button, width: '100%' }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#2563eb'}
            onMouseOut={(e) => e.target.style.backgroundColor = '#3b82f6'}
          >
            Sign In
          </button>
        </form>
        
        <div style={{ marginTop: '24px', textAlign: 'center' }}>
          <p style={{ fontSize: '14px', color: '#6b7280' }}>
            Demo credentials are pre-filled. Just click "Sign In"
          </p>
        </div>
      </div>
    </div>
  );

  // Dashboard Page Component
  const DashboardPage = () => (
    <div style={styles.container}>
      {/* Header */}
      <header style={styles.header}>
        <div style={styles.headerContent}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#111827', margin: 0 }}>
              Track New
            </h1>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <span style={{ color: '#374151' }}>Welcome, {user?.name}</span>
            <button
              onClick={handleLogout}
              style={{ ...styles.button, backgroundColor: '#ef4444' }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#dc2626'}
              onMouseOut={(e) => e.target.style.backgroundColor = '#ef4444'}
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      <div style={{ display: 'flex' }}>
        {/* Sidebar */}
        <nav style={styles.sidebar}>
          <div style={{ padding: '16px' }}>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              {[
                { id: 'dashboard', name: 'Dashboard', icon: '📊' },
                { id: 'customers', name: 'Customers', icon: '👥' },
                { id: 'services', name: 'Services', icon: '🛠️' },
                { id: 'products', name: 'Products', icon: '📦' },
                { id: 'sales', name: 'Sales', icon: '💰' },
                { id: 'reports', name: 'Reports', icon: '📈' }
              ].map((item) => (
                <li key={item.id}>
                  <button
                    onClick={() => setCurrentPage(item.id)}
                    style={{
                      ...styles.navItem,
                      ...(currentPage === item.id ? styles.navItemActive : {}),
                      color: currentPage === item.id ? '#1d4ed8' : '#374151'
                    }}
                    onMouseOver={(e) => {
                      if (currentPage !== item.id) {
                        e.target.style.backgroundColor = '#f3f4f6';
                      }
                    }}
                    onMouseOut={(e) => {
                      if (currentPage !== item.id) {
                        e.target.style.backgroundColor = 'transparent';
                      }
                    }}
                  >
                    <span style={{ marginRight: '12px', fontSize: '16px' }}>{item.icon}</span>
                    <span>{item.name}</span>
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </nav>

        {/* Main Content */}
        <main style={styles.mainContent}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <h2 style={{ fontSize: '32px', fontWeight: 'bold', color: '#111827', marginBottom: '32px' }}>
              {currentPage.charAt(0).toUpperCase() + currentPage.slice(1)}
            </h2>
            
            {currentPage === 'dashboard' && (
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '24px', marginBottom: '32px' }}>
                {[
                  { title: 'Total Customers', value: '150', color: '#3b82f6', icon: '👥' },
                  { title: 'Active Services', value: '45', color: '#10b981', icon: '🛠️' },
                  { title: 'Monthly Revenue', value: '$25,000', color: '#8b5cf6', icon: '💰' },
                  { title: 'Pending Tasks', value: '12', color: '#f59e0b', icon: '📋' }
                ].map((stat, index) => (
                  <div key={index} style={styles.statCard}>
                    <div style={{
                      width: '48px',
                      height: '48px',
                      backgroundColor: stat.color,
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      margin: '0 auto 16px auto'
                    }}>
                      <span style={{ fontSize: '20px' }}>{stat.icon}</span>
                    </div>
                    <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', margin: '0 0 8px 0' }}>
                      {stat.title}
                    </h3>
                    <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#111827', margin: 0 }}>
                      {stat.value}
                    </p>
                  </div>
                ))}
              </div>
            )}
            
            <div style={styles.card}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#111827', marginBottom: '16px' }}>
                {currentPage === 'dashboard' ? 'Recent Activity' : `${currentPage.charAt(0).toUpperCase() + currentPage.slice(1)} Management`}
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                {[1, 2, 3].map((item) => (
                  <div key={item} style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: '16px',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px'
                  }}>
                    <div>
                      <h4 style={{ fontSize: '16px', fontWeight: '600', color: '#111827', margin: '0 0 4px 0' }}>
                        {currentPage === 'dashboard' ? `Activity ${item}` : `${currentPage.slice(0, -1)} ${item}`}
                      </h4>
                      <p style={{ color: '#6b7280', margin: 0 }}>Sample description for item {item}</p>
                    </div>
                    <button
                      style={styles.button}
                      onMouseOver={(e) => e.target.style.backgroundColor = '#2563eb'}
                      onMouseOut={(e) => e.target.style.backgroundColor = '#3b82f6'}
                    >
                      View
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );

  // Render the appropriate page
  if (!user) {
    return <LoginPage />;
  }

  return <DashboardPage />;
}

export default StyledApp;
