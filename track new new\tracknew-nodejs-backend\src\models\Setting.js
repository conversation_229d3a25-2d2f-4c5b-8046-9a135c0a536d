const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Setting = sequelize.define('Setting', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'companies',
      key: 'id'
    },
    comment: 'NULL for global settings, company_id for company-specific settings'
  },
  key: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  value: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  type: {
    type: DataTypes.ENUM('string', 'number', 'boolean', 'json', 'array', 'object', 'date', 'time', 'datetime', 'email', 'url', 'file', 'image'),
    defaultValue: 'string'
  },
  category: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Setting category for grouping'
  },
  group: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Setting group within category'
  },
  label: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Human-readable label'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Setting description'
  },
  default_value: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Default value for this setting'
  },
  options: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of possible values for select/radio inputs'
  },
  validation_rules: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of validation rules'
  },
  input_type: {
    type: DataTypes.ENUM('text', 'textarea', 'number', 'email', 'url', 'password', 'select', 'radio', 'checkbox', 'switch', 'file', 'image', 'color', 'date', 'time', 'datetime'),
    defaultValue: 'text'
  },
  is_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_encrypted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the value should be encrypted'
  },
  is_system: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is a system setting (cannot be deleted)'
  },
  is_public: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this setting can be accessed publicly'
  },
  is_editable: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether this setting can be edited'
  },
  is_visible: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether this setting is visible in UI'
  },
  requires_restart: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether changing this setting requires system restart'
  },
  affects_cache: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether changing this setting affects cache'
  },
  scope: {
    type: DataTypes.ENUM('global', 'company', 'user', 'module'),
    defaultValue: 'global'
  },
  module: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Module this setting belongs to'
  },
  feature: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Feature this setting controls'
  },
  environment: {
    type: DataTypes.ENUM('all', 'development', 'staging', 'production'),
    defaultValue: 'all',
    comment: 'Environment where this setting applies'
  },
  min_value: {
    type: DataTypes.DECIMAL(15, 6),
    allowNull: true,
    comment: 'Minimum value for numeric settings'
  },
  max_value: {
    type: DataTypes.DECIMAL(15, 6),
    allowNull: true,
    comment: 'Maximum value for numeric settings'
  },
  step: {
    type: DataTypes.DECIMAL(15, 6),
    allowNull: true,
    comment: 'Step value for numeric inputs'
  },
  placeholder: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Placeholder text for input'
  },
  help_text: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Help text to display'
  },
  icon: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Icon for this setting'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  depends_on: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of setting keys this setting depends on'
  },
  conditional_logic: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object defining when this setting should be shown/enabled'
  },
  auto_load: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether to auto-load this setting on system startup'
  },
  cache_duration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Cache duration in seconds'
  },
  last_modified_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  last_modified_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  version: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: 'Setting version for change tracking'
  },
  change_log: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of change history'
  },
  tags: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Comma-separated tags'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'settings',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['key']
    },
    {
      fields: ['company_id', 'key'],
      unique: true
    },
    {
      fields: ['category']
    },
    {
      fields: ['group']
    },
    {
      fields: ['type']
    },
    {
      fields: ['scope']
    },
    {
      fields: ['module']
    },
    {
      fields: ['feature']
    },
    {
      fields: ['environment']
    },
    {
      fields: ['is_system']
    },
    {
      fields: ['is_public']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['auto_load']
    },
    {
      fields: ['sort_order']
    }
  ]
});

module.exports = Setting;
