// Minimal server test
require('dotenv').config();
const express = require('express');
const cors = require('cors');

console.log('🚀 Starting minimal server...');

const app = express();
const PORT = process.env.PORT || 8000;

// Basic middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/health', (req, res) => {
  res.json({
    status: 'success',
    message: 'Minimal server is running',
    timestamp: new Date().toISOString(),
    database: 'not tested'
  });
});

// Test database connection route
app.get('/test-db', async (req, res) => {
  try {
    const { Sequelize } = require('sequelize');
    const sequelize = new Sequelize(process.env.DATABASE_URL, {
      dialect: 'postgres',
      logging: false,
      dialectOptions: {
        ssl: false
      }
    });

    await sequelize.authenticate();
    const [result] = await sequelize.query('SELECT version()');
    await sequelize.close();

    res.json({
      status: 'success',
      message: 'Database connection successful',
      version: result[0].version
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Database connection failed',
      error: error.message
    });
  }
});

app.listen(PORT, () => {
  console.log(`✅ Minimal server running on http://localhost:${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`🔧 Database test: http://localhost:${PORT}/test-db`);
});

// Handle errors
process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Rejection:', err);
});
