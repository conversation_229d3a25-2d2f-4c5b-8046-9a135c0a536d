import React, { forwardRef } from 'react';
import { ExclamationCircleIcon } from '@heroicons/react/24/outline';
import { classNames } from '../../utils/helpers';

const Textarea = forwardRef(({
  label,
  placeholder,
  value,
  onChange,
  onBlur,
  onFocus,
  error,
  helperText,
  required = false,
  disabled = false,
  readOnly = false,
  autoFocus = false,
  maxLength,
  minLength,
  rows = 4,
  cols,
  resize = 'vertical',
  autoResize = false,
  showCharCount = false,
  size = 'md',
  variant = 'default',
  className = '',
  textareaClassName = '',
  labelClassName = '',
  errorClassName = '',
  helperClassName = '',
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = React.useState(false);
  const textareaRef = React.useRef(null);
  
  const inputId = React.useId();
  const errorId = React.useId();
  const helperId = React.useId();

  // Auto-resize functionality
  React.useEffect(() => {
    if (autoResize && textareaRef.current) {
      const textarea = textareaRef.current;
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, [value, autoResize]);

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const variantClasses = {
    default: 'border-gray-300 focus:border-blue-500 focus:ring-blue-500',
    filled: 'border-transparent bg-gray-100 focus:bg-white focus:border-blue-500 focus:ring-blue-500',
    outlined: 'border-2 border-gray-300 focus:border-blue-500 focus:ring-0',
  };

  const resizeClasses = {
    none: 'resize-none',
    vertical: 'resize-y',
    horizontal: 'resize-x',
    both: 'resize',
  };

  const baseTextareaClasses = classNames(
    'block w-full rounded-md shadow-sm transition-colors duration-200',
    'placeholder-gray-400 text-gray-900 dark:text-white',
    'dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400',
    'focus:outline-none focus:ring-1',
    sizeClasses[size],
    resizeClasses[resize],
    error 
      ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-500' 
      : variantClasses[variant],
    disabled && 'bg-gray-50 text-gray-500 cursor-not-allowed dark:bg-gray-800',
    readOnly && 'bg-gray-50 cursor-default dark:bg-gray-800',
    textareaClassName
  );

  const handleFocus = (e) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const handleChange = (e) => {
    onChange?.(e);
    
    // Auto-resize on change
    if (autoResize && textareaRef.current) {
      const textarea = textareaRef.current;
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  };

  const currentLength = value ? value.length : 0;
  const isOverLimit = maxLength && currentLength > maxLength;

  return (
    <div className={classNames('w-full', className)}>
      {/* Label */}
      {label && (
        <label 
          htmlFor={inputId}
          className={classNames(
            'block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1',
            required && "after:content-['*'] after:ml-0.5 after:text-red-500",
            labelClassName
          )}
        >
          {label}
        </label>
      )}

      {/* Textarea Container */}
      <div className="relative">
        <textarea
          ref={(node) => {
            textareaRef.current = node;
            if (typeof ref === 'function') {
              ref(node);
            } else if (ref) {
              ref.current = node;
            }
          }}
          id={inputId}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          readOnly={readOnly}
          autoFocus={autoFocus}
          maxLength={maxLength}
          minLength={minLength}
          rows={autoResize ? 1 : rows}
          cols={cols}
          className={baseTextareaClasses}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={classNames(
            error && errorId,
            helperText && helperId
          )}
          {...props}
        />

        {/* Error Icon */}
        {error && (
          <div className="absolute top-2 right-2">
            <ExclamationCircleIcon className="h-5 w-5 text-red-500" />
          </div>
        )}
      </div>

      {/* Character Count and Helper Text Row */}
      <div className="flex justify-between items-start mt-1">
        <div className="flex-1">
          {/* Error Message */}
          {error && (
            <p 
              id={errorId}
              className={classNames(
                'text-sm text-red-600 dark:text-red-400',
                errorClassName
              )}
            >
              {error}
            </p>
          )}

          {/* Helper Text */}
          {helperText && !error && (
            <p 
              id={helperId}
              className={classNames(
                'text-sm text-gray-500 dark:text-gray-400',
                helperClassName
              )}
            >
              {helperText}
            </p>
          )}
        </div>

        {/* Character Count */}
        {showCharCount && (maxLength || currentLength > 0) && (
          <div className="flex-shrink-0 ml-2">
            <span className={classNames(
              'text-xs',
              isOverLimit 
                ? 'text-red-600 dark:text-red-400' 
                : 'text-gray-500 dark:text-gray-400'
            )}>
              {currentLength}
              {maxLength && `/${maxLength}`}
            </span>
          </div>
        )}
      </div>
    </div>
  );
});

Textarea.displayName = 'Textarea';

export default Textarea;
