var e=Object.defineProperty,t=(t,r,n)=>((t,r,n)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[r]=n)(t,"symbol"!=typeof r?r+"":r,n);import{r}from"./router-BMeu0lFb.js";import{r as n}from"./vendor-BRaCMJ4j.js";var o,i,c={exports:{}},u={};i||(i=1,c.exports=function(){if(o)return u;o=1;var e=n(),t="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},r=e.useSyncExternalStore,i=e.useRef,c=e.useEffect,a=e.useMemo,s=e.useDebugValue;return u.useSyncExternalStoreWithSelector=function(e,n,o,u,l){var f=i(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;f=a((function(){function e(e){if(!c){if(c=!0,r=e,e=u(e),void 0!==l&&d.hasValue){var n=d.value;if(l(n,e))return i=n}return i=e}if(n=i,t(r,e))return n;var o=u(e);return void 0!==l&&l(n,o)?(r=e,n):(r=e,i=o)}var r,i,c=!1,a=void 0===o?null:o;return[function(){return e(n())},null===a?void 0:function(){return e(a())}]}),[n,o,u,l]);var p=r(e,f[0],f[1]);return c((function(){d.hasValue=!0,d.value=p}),[p]),s(p),p},u}()),c.exports;var a={notify(){},get:()=>[]};var s=(()=>!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement))(),l=(()=>"undefined"!=typeof navigator&&"ReactNative"===navigator.product)(),f=(()=>s||l?r.useLayoutEffect:r.useEffect)(),d=Symbol.for("react-redux-context"),p="undefined"!=typeof globalThis?globalThis:{};function y(){if(!r.createContext)return{};const e=p[d]??(p[d]=new Map);let t=e.get(r.createContext);return t||(t=r.createContext(null),e.set(r.createContext,t)),t}var h=y();var b=function(e){const{children:t,context:n,serverState:o,store:i}=e,c=r.useMemo((()=>{const e=function(e){let t,r=a,n=0,o=!1;function i(){s.onStateChange&&s.onStateChange()}function c(){n++,t||(t=e.subscribe(i),r=function(){let e=null,t=null;return{clear(){e=null,t=null},notify(){(()=>{let t=e;for(;t;)t.callback(),t=t.next})()},get(){const t=[];let r=e;for(;r;)t.push(r),r=r.next;return t},subscribe(r){let n=!0;const o=t={callback:r,next:null,prev:t};return o.prev?o.prev.next=o:e=o,function(){n&&null!==e&&(n=!1,o.next?o.next.prev=o.prev:t=o.prev,o.prev?o.prev.next=o.next:e=o.next)}}}}())}function u(){n--,t&&0===n&&(t(),t=void 0,r.clear(),r=a)}const s={addNestedSub:function(e){c();const t=r.subscribe(e);let n=!1;return()=>{n||(n=!0,t(),u())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:i,isSubscribed:function(){return o},trySubscribe:function(){o||(o=!0,c())},tryUnsubscribe:function(){o&&(o=!1,u())},getListeners:()=>r};return s}(i);return{store:i,subscription:e,getServerState:o?()=>o:void 0}}),[i,o]),u=r.useMemo((()=>i.getState()),[i]);f((()=>{const{subscription:e}=c;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),u!==i.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}}),[c,u]);const s=n||h;return r.createElement(s.Provider,{value:c},t)};function v(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var _=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")(),m=()=>Math.random().toString(36).substring(7).split("").join("."),w={INIT:`@@redux/INIT${m()}`,REPLACE:`@@redux/REPLACE${m()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${m()}`};function g(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function S(e,t,r){if("function"!=typeof e)throw new Error(v(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(v(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(v(1));return r(S)(e,t)}let n=e,o=t,i=new Map,c=i,u=0,a=!1;function s(){c===i&&(c=new Map,i.forEach(((e,t)=>{c.set(t,e)})))}function l(){if(a)throw new Error(v(3));return o}function f(e){if("function"!=typeof e)throw new Error(v(4));if(a)throw new Error(v(5));let t=!0;s();const r=u++;return c.set(r,e),function(){if(t){if(a)throw new Error(v(6));t=!1,s(),c.delete(r),i=null}}}function d(e){if(!g(e))throw new Error(v(7));if(void 0===e.type)throw new Error(v(8));if("string"!=typeof e.type)throw new Error(v(17));if(a)throw new Error(v(9));try{a=!0,o=n(o,e)}finally{a=!1}return(i=c).forEach((e=>{e()})),e}d({type:w.INIT});return{dispatch:d,subscribe:f,getState:l,replaceReducer:function(e){if("function"!=typeof e)throw new Error(v(10));n=e,d({type:w.REPLACE})},[_]:function(){const e=f;return{subscribe(t){if("object"!=typeof t||null===t)throw new Error(v(11));function r(){const e=t;e.next&&e.next(l())}r();return{unsubscribe:e(r)}},[_](){return this}}}}}function E(e){const t=Object.keys(e),r={};for(let c=0;c<t.length;c++){const n=t[c];"function"==typeof e[n]&&(r[n]=e[n])}const n=Object.keys(r);let o;try{!function(e){Object.keys(e).forEach((t=>{const r=e[t];if(void 0===r(void 0,{type:w.INIT}))throw new Error(v(12));if(void 0===r(void 0,{type:w.PROBE_UNKNOWN_ACTION()}))throw new Error(v(13))}))}(r)}catch(i){o=i}return function(e={},t){if(o)throw o;let i=!1;const c={};for(let o=0;o<n.length;o++){const u=n[o],a=r[u],s=e[u],l=a(s,t);if(void 0===l)throw t&&t.type,new Error(v(14));c[u]=l,i=i||l!==s}return i=i||n.length!==Object.keys(e).length,i?c:e}}function O(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce(((e,t)=>(...r)=>e(t(...r))))}var j=Symbol.for("immer-nothing"),C=Symbol.for("immer-draftable"),P=Symbol.for("immer-state");function x(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var R=Object.getPrototypeOf;function A(e){return!!e&&!!e[P]}function N(e){var t;return!!e&&(k(e)||Array.isArray(e)||!!e[C]||!!(null==(t=e.constructor)?void 0:t[C])||F(e)||W(e))}var M=Object.prototype.constructor.toString();function k(e){if(!e||"object"!=typeof e)return!1;const t=R(e);if(null===t)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===M}function T(e,t){0===z(e)?Reflect.ownKeys(e).forEach((r=>{t(r,e[r],e)})):e.forEach(((r,n)=>t(n,r,e)))}function z(e){const t=e[P];return t?t.type_:Array.isArray(e)?1:F(e)?2:W(e)?3:0}function D(e,t){return 2===z(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function I(e,t,r){const n=z(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function F(e){return e instanceof Map}function W(e){return e instanceof Set}function q(e){return e.copy_||e.base_}function $(e,t){if(F(e))return new Map(e);if(W(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=k(e);if(!0===t||"class_only"===t&&!r){const t=Object.getOwnPropertyDescriptors(e);delete t[P];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){const o=r[n],i=t[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[o]})}return Object.create(R(e),t)}{const t=R(e);if(null!==t&&r)return{...e};const n=Object.create(t);return Object.assign(n,e)}}function B(e,t=!1){return U(e)||A(e)||!N(e)||(z(e)>1&&(e.set=e.add=e.clear=e.delete=L),Object.freeze(e),t&&Object.entries(e).forEach((([e,t])=>B(t,!0)))),e}function L(){x(2)}function U(e){return Object.isFrozen(e)}var V,K={};function X(e){const t=K[e];return t||x(0),t}function G(){return V}function H(e,t){t&&(X("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function J(e){Q(e),e.drafts_.forEach(Z),e.drafts_=null}function Q(e){e===V&&(V=e.parent_)}function Y(e){return V={drafts_:[],parent_:V,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function Z(e){const t=e[P];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function ee(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return void 0!==e&&e!==r?(r[P].modified_&&(J(t),x(4)),N(e)&&(e=te(t,e),t.parent_||ne(t,e)),t.patches_&&X("Patches").generateReplacementPatches_(r[P].base_,e,t.patches_,t.inversePatches_)):e=te(t,r,[]),J(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==j?e:void 0}function te(e,t,r){if(U(t))return t;const n=t[P];if(!n)return T(t,((o,i)=>re(e,n,t,o,i,r))),t;if(n.scope_!==e)return t;if(!n.modified_)return ne(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const t=n.copy_;let o=t,i=!1;3===n.type_&&(o=new Set(t),t.clear(),i=!0),T(o,((o,c)=>re(e,n,t,o,c,r,i))),ne(e,t,!1),r&&e.patches_&&X("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function re(e,t,r,n,o,i,c){if(A(o)){const c=te(e,o,i&&t&&3!==t.type_&&!D(t.assigned_,n)?i.concat(n):void 0);if(I(r,n,c),!A(c))return;e.canAutoFreeze_=!1}else c&&r.add(o);if(N(o)&&!U(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;te(e,o),t&&t.scope_.parent_||"symbol"==typeof n||!Object.prototype.propertyIsEnumerable.call(r,n)||ne(e,o)}}function ne(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&B(t,r)}var oe={get(e,t){if(t===P)return e;const r=q(e);if(!D(r,t))return function(e,t,r){var n;const o=ue(t,r);return o?"value"in o?o.value:null==(n=o.get)?void 0:n.call(e.draft_):void 0}(e,r,t);const n=r[t];return e.finalized_||!N(n)?n:n===ce(e.base_,t)?(se(e),e.copy_[t]=le(n,e)):n},has:(e,t)=>t in q(e),ownKeys:e=>Reflect.ownKeys(q(e)),set(e,t,r){const n=ue(q(e),t);if(null==n?void 0:n.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const n=ce(q(e),t),c=null==n?void 0:n[P];if(c&&c.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(((o=r)===(i=n)?0!==o||1/o==1/i:o!=o&&i!=i)&&(void 0!==r||D(e.base_,t)))return!0;se(e),ae(e)}var o,i;return e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==ce(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,se(e),ae(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const r=q(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){x(11)},getPrototypeOf:e=>R(e.base_),setPrototypeOf(){x(12)}},ie={};function ce(e,t){const r=e[P];return(r?q(r):e)[t]}function ue(e,t){if(!(t in e))return;let r=R(e);for(;r;){const e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=R(r)}}function ae(e){e.modified_||(e.modified_=!0,e.parent_&&ae(e.parent_))}function se(e){e.copy_||(e.copy_=$(e.base_,e.scope_.immer_.useStrictShallowCopy_))}T(oe,((e,t)=>{ie[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),ie.deleteProperty=function(e,t){return ie.set.call(this,e,t,void 0)},ie.set=function(e,t,r){return oe.set.call(this,e[0],t,r,e[0])};function le(e,t){const r=F(e)?X("MapSet").proxyMap_(e,t):W(e)?X("MapSet").proxySet_(e,t):function(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:G(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let o=n,i=oe;r&&(o=[n],i=ie);const{revoke:c,proxy:u}=Proxy.revocable(o,i);return n.draft_=u,n.revoke_=c,u}(e,t);return(t?t.scope_:G()).drafts_.push(r),r}function fe(e){if(!N(e)||U(e))return e;const t=e[P];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=$(e,t.scope_.immer_.useStrictShallowCopy_)}else r=$(e,!0);return T(r,((e,t)=>{I(r,e,fe(t))})),t&&(t.finalized_=!1),r}var de=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{if("function"==typeof e&&"function"!=typeof t){const r=t;t=e;const n=this;return function(e=r,...o){return n.produce(e,(e=>t.call(this,e,...o)))}}let n;if("function"!=typeof t&&x(6),void 0!==r&&"function"!=typeof r&&x(7),N(e)){const o=Y(this),i=le(e,void 0);let c=!0;try{n=t(i),c=!1}finally{c?J(o):Q(o)}return H(o,r),ee(n,o)}if(!e||"object"!=typeof e){if(n=t(e),void 0===n&&(n=e),n===j&&(n=void 0),this.autoFreeze_&&B(n,!0),r){const t=[],o=[];X("Patches").generateReplacementPatches_(e,n,t,o),r(t,o)}return n}x(1)},this.produceWithPatches=(e,t)=>{if("function"==typeof e)return(t,...r)=>this.produceWithPatches(t,(t=>e(t,...r)));let r,n;return[this.produce(e,t,((e,t)=>{r=e,n=t})),r,n]},"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof(null==e?void 0:e.useStrictShallowCopy)&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){N(e)||x(8),A(e)&&(e=function(e){A(e)||x(10);return fe(e)}(e));const t=Y(this),r=le(e,void 0);return r[P].isManual_=!0,Q(t),r}finishDraft(e,t){const r=e&&e[P];r&&r.isManual_||x(9);const{scope_:n}=r;return H(n,t),ee(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));const n=X("Patches").applyPatches_;return A(e)?n(e,t):this.produce(e,(e=>n(e,t)))}},pe=de.produce;de.produceWithPatches.bind(de),de.setAutoFreeze.bind(de),de.setUseStrictShallowCopy.bind(de),de.applyPatches.bind(de),de.createDraft.bind(de),de.finishDraft.bind(de);var ye=e=>Array.isArray(e)?e:[e];function he(e){const t=Array.isArray(e[0])?e[0]:e;return function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every((e=>"function"==typeof e))){const r=e.map((e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e)).join(", ");throw new TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}var be="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function ve(e,t={}){let r={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:n}=t;let o,i=0;function c(){var t;let c=r;const{length:u}=arguments;for(let e=0,r=u;e<r;e++){const t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=c.o;null===e&&(c.o=e=new WeakMap);const r=e.get(t);void 0===r?(c={s:0,v:void 0,o:null,p:null},e.set(t,c)):c=r}else{let e=c.p;null===e&&(c.p=e=new Map);const r=e.get(t);void 0===r?(c={s:0,v:void 0,o:null,p:null},e.set(t,c)):c=r}}const a=c;let s;if(1===c.s)s=c.v;else if(s=e.apply(null,arguments),i++,n){const e=(null==(t=null==o?void 0:o.deref)?void 0:t.call(o))??o;null!=e&&n(e,s)&&(s=e,0!==i&&i--);o="object"==typeof s&&null!==s||"function"==typeof s?new be(s):s}return a.s=1,a.v=s,s}return c.clearCache=()=>{r={s:0,v:void 0,o:null,p:null},c.resetResultsCount()},c.resultsCount=()=>i,c.resetResultsCount=()=>{i=0},c}function _e(e,...t){const r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,o=0,i={},c=e.pop();"object"==typeof c&&(i=c,c=e.pop()),function(e,t="expected a function, instead received "+typeof e){if("function"!=typeof e)throw new TypeError(t)}(c,`createSelector expects an output function after the inputs, but received: [${typeof c}]`);const u={...r,...i},{memoize:a,memoizeOptions:s=[],argsMemoize:l=ve,argsMemoizeOptions:f=[]}=u,d=ye(s),p=ye(f),y=he(e),h=a((function(){return n++,c.apply(null,arguments)}),...d),b=l((function(){o++;const e=function(e,t){const r=[],{length:n}=e;for(let o=0;o<n;o++)r.push(e[o].apply(null,t));return r}(y,arguments);return t=h.apply(null,e),t}),...p);return Object.assign(b,{resultFunc:c,memoizedResultFunc:h,dependencies:y,dependencyRecomputations:()=>o,resetDependencyRecomputations:()=>{o=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:a,argsMemoize:l})};return Object.assign(n,{withTypes:()=>n}),n}var me=_e(ve),we=Object.assign(((e,t=me)=>{!function(e,t="expected an object, instead received "+typeof e){if("object"!=typeof e)throw new TypeError(t)}(e,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof e);const r=Object.keys(e);return t(r.map((t=>e[t])),((...e)=>e.reduce(((e,t,n)=>(e[r[n]]=t,e)),{})))}),{withTypes:()=>we});function ge(e){return({dispatch:t,getState:r})=>n=>o=>"function"==typeof o?o(t,r,e):n(o)}var Se=ge(),Ee=ge,Oe="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?O:O.apply(null,arguments)};function je(e,t){function r(...r){if(t){let n=t(...r);if(!n)throw new Error(Xe(0));return{type:e,payload:n.payload,..."meta"in n&&{meta:n.meta},..."error"in n&&{error:n.error}}}return{type:e,payload:r[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>function(e){return g(e)&&"type"in e&&"string"==typeof e.type}(t)&&t.type===e,r}var Ce=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function Pe(e){return N(e)?pe(e,(()=>{})):e}function xe(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var Re=e=>t=>{setTimeout(t,e)},Ae=e=>function(t){const{autoBatch:r=!0}=t??{};let n=new Ce(e);return r&&n.push(((e={type:"raf"})=>t=>(...r)=>{const n=t(...r);let o=!0,i=!1,c=!1;const u=new Set,a="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:Re(10):"callback"===e.type?e.queueNotification:Re(e.timeout),s=()=>{c=!1,i&&(i=!1,u.forEach((e=>e())))};return Object.assign({},n,{subscribe(e){const t=n.subscribe((()=>o&&e()));return u.add(e),()=>{t(),u.delete(e)}},dispatch(e){var t;try{return o=!(null==(t=null==e?void 0:e.meta)?void 0:t.RTK_autoBatch),i=!o,i&&(c||(c=!0,a(s))),n.dispatch(e)}finally{o=!0}}})})("object"==typeof r?r:void 0)),n};function Ne(e){const t=function(e){const{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{};let i=new Ce;return t&&("boolean"==typeof t?i.push(Se):i.push(Ee(t.extraArgument))),i},{reducer:r,middleware:n,devTools:o=!0,preloadedState:i,enhancers:c}=e||{};let u,a;if("function"==typeof r)u=r;else{if(!g(r))throw new Error(Xe(1));u=E(r)}a="function"==typeof n?n(t):t();let s=O;o&&(s=Oe({trace:!1,..."object"==typeof o&&o}));const l=function(...e){return t=>(r,n)=>{const o=t(r,n);let i=()=>{throw new Error(v(15))};const c={getState:o.getState,dispatch:(e,...t)=>i(e,...t)},u=e.map((e=>e(c)));return i=O(...u)(o.dispatch),{...o,dispatch:i}}}(...a),f=Ae(l);return S(u,i,s(..."function"==typeof c?c(f):f()))}function Me(e){const t={},r=[];let n;const o={addCase(e,r){const n="string"==typeof e?e:e.type;if(!n)throw new Error(Xe(28));if(n in t)throw new Error(Xe(29));return t[n]=r,o},addMatcher:(e,t)=>(r.push({matcher:e,reducer:t}),o),addDefaultCase:e=>(n=e,o)};return e(o),[t,r,n]}function ke(...e){return t=>e.some((e=>((e,t)=>{return(r=e)&&"function"==typeof r.match?e.match(t):e(t);var r})(e,t)))}var Te=["name","message","stack","code"],ze=class{constructor(e,r){t(this,"_type"),this.payload=e,this.meta=r}},De=class{constructor(e,r){t(this,"_type"),this.payload=e,this.meta=r}},Ie=e=>{if("object"==typeof e&&null!==e){const t={};for(const r of Te)"string"==typeof e[r]&&(t[r]=e[r]);return t}return{message:String(e)}},Fe="External signal was aborted",We=(()=>{function e(e,t,r){const n=je(e+"/fulfilled",((e,t,r,n)=>({payload:e,meta:{...n||{},arg:r,requestId:t,requestStatus:"fulfilled"}}))),o=je(e+"/pending",((e,t,r)=>({payload:void 0,meta:{...r||{},arg:t,requestId:e,requestStatus:"pending"}}))),i=je(e+"/rejected",((e,t,n,o,i)=>({payload:o,error:(r&&r.serializeError||Ie)(e||"Rejected"),meta:{...i||{},arg:n,requestId:t,rejectedWithValue:!!o,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)}})));return Object.assign((function(e,{signal:c}={}){return(u,a,s)=>{const l=(null==r?void 0:r.idGenerator)?r.idGenerator(e):((e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t})(),f=new AbortController;let d,p;function y(e){p=e,f.abort()}c&&(c.aborted?y(Fe):c.addEventListener("abort",(()=>y(Fe)),{once:!0}));const h=async function(){var c,h;let b;try{let i=null==(c=null==r?void 0:r.condition)?void 0:c.call(r,e,{getState:a,extra:s});if(null!==(v=i)&&"object"==typeof v&&"function"==typeof v.then&&(i=await i),!1===i||f.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};const _=new Promise(((e,t)=>{d=()=>{t({name:"AbortError",message:p||"Aborted"})},f.signal.addEventListener("abort",d)}));u(o(l,e,null==(h=null==r?void 0:r.getPendingMeta)?void 0:h.call(r,{requestId:l,arg:e},{getState:a,extra:s}))),b=await Promise.race([_,Promise.resolve(t(e,{dispatch:u,getState:a,extra:s,requestId:l,signal:f.signal,abort:y,rejectWithValue:(e,t)=>new ze(e,t),fulfillWithValue:(e,t)=>new De(e,t)})).then((t=>{if(t instanceof ze)throw t;return t instanceof De?n(t.payload,l,e,t.meta):n(t,l,e)}))])}catch(_){b=_ instanceof ze?i(null,l,e,_.payload,_.meta):i(_,l,e)}finally{d&&f.signal.removeEventListener("abort",d)}var v;return r&&!r.dispatchConditionRejection&&i.match(b)&&b.meta.condition||u(b),b}();return Object.assign(h,{abort:y,requestId:l,arg:e,unwrap:()=>h.then(qe)})}}),{pending:o,rejected:i,fulfilled:n,settled:ke(i,n),typePrefix:e})}return e.withTypes=()=>e,e})();function qe(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var $e=Symbol.for("rtk-slice-createasyncthunk");function Be(e,t){return`${e}/${t}`}function Le({creators:e}={}){var t;const r=null==(t=null==e?void 0:e.asyncThunk)?void 0:t[$e];return function(e){const{name:t,reducerPath:n=t}=e;if(!t)throw new Error(Xe(11));const o=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},i=Object.keys(o),c={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},u={addCase(e,t){const r="string"==typeof e?e:e.type;if(!r)throw new Error(Xe(12));if(r in c.sliceCaseReducersByType)throw new Error(Xe(13));return c.sliceCaseReducersByType[r]=t,u},addMatcher:(e,t)=>(c.sliceMatchers.push({matcher:e,reducer:t}),u),exposeAction:(e,t)=>(c.actionCreators[e]=t,u),exposeCaseReducer:(e,t)=>(c.sliceCaseReducersByName[e]=t,u)};function a(){const[t={},r=[],n]="function"==typeof e.extraReducers?Me(e.extraReducers):[e.extraReducers],o={...t,...c.sliceCaseReducersByType};return function(e,t){let r,[n,o,i]=Me(t);if("function"==typeof e)r=()=>Pe(e());else{const t=Pe(e);r=()=>t}function c(e=r(),t){let c=[n[t.type],...o.filter((({matcher:e})=>e(t))).map((({reducer:e})=>e))];return 0===c.filter((e=>!!e)).length&&(c=[i]),c.reduce(((e,r)=>{if(r){if(A(e)){const n=r(e,t);return void 0===n?e:n}if(N(e))return pe(e,(e=>r(e,t)));{const n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}}return e}),e)}return c.getInitialState=r,c}(e.initialState,(e=>{for(let t in o)e.addCase(t,o[t]);for(let t of c.sliceMatchers)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)}))}i.forEach((n=>{const i=o[n],c={reducerName:n,type:Be(t,n),createNotation:"function"==typeof e.reducers};!function(e){return"asyncThunk"===e._reducerDefinitionType}(i)?function({type:e,reducerName:t,createNotation:r},n,o){let i,c;if("reducer"in n){if(r&&!function(e){return"reducerWithPrepare"===e._reducerDefinitionType}(n))throw new Error(Xe(17));i=n.reducer,c=n.prepare}else i=n;o.addCase(e,i).exposeCaseReducer(t,i).exposeAction(t,c?je(e,c):je(e))}(c,i,u):function({type:e,reducerName:t},r,n,o){if(!o)throw new Error(Xe(18));const{payloadCreator:i,fulfilled:c,pending:u,rejected:a,settled:s,options:l}=r,f=o(e,i,l);n.exposeAction(t,f),c&&n.addCase(f.fulfilled,c);u&&n.addCase(f.pending,u);a&&n.addCase(f.rejected,a);s&&n.addMatcher(f.settled,s);n.exposeCaseReducer(t,{fulfilled:c||Ke,pending:u||Ke,rejected:a||Ke,settled:s||Ke})}(c,i,u,r)}));const s=e=>e,l=new Map,f=new WeakMap;let d;function p(e,t){return d||(d=a()),d(e,t)}function y(){return d||(d=a()),d.getInitialState()}function h(t,r=!1){function n(e){let o=e[t];return void 0===o&&r&&(o=xe(f,n,y)),o}function o(t=s){const n=xe(l,r,(()=>new WeakMap));return xe(n,t,(()=>{const n={};for(const[o,i]of Object.entries(e.selectors??{}))n[o]=Ue(i,t,(()=>xe(f,t,y)),r);return n}))}return{reducerPath:t,getSelectors:o,get selectors(){return o(n)},selectSlice:n}}const b={name:t,reducer:p,actions:c.actionCreators,caseReducers:c.sliceCaseReducersByName,getInitialState:y,...h(n),injectInto(e,{reducerPath:t,...r}={}){const o=t??n;return e.inject({reducerPath:o,reducer:p},r),{...b,...h(o,!0)}}};return b}}function Ue(e,t,r,n){function o(o,...i){let c=t(o);return void 0===c&&n&&(c=r()),e(c,...i)}return o.unwrapped=e,o}var Ve=Le();function Ke(){}function Xe(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}export{b as P,We as a,Ve as b,S as c,Ne as d,E as e};
