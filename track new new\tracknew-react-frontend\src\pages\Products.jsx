import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  PlusIcon,
  CubeIcon,
  ExclamationTriangleIcon,
  PhotoIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  QrCodeIcon,
  TagIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

// Redux
import {
  fetchProducts,
  deleteProduct,
  updateProductStock,
  selectProducts,
  selectProductPagination,
  selectProductLoading,
  selectProductError,
  selectSelectedProducts,
  selectProductFilters,
  selectProductSearchQuery,
  setSearchQuery,
  setFilter,
  clearFilters,
  selectProduct,
  deselectProduct,
  selectAllProducts,
  deselectAllProducts
} from '../store/slices/productSlice';

// Components
import { Button, DataTable, Pagination, ConfirmModal } from '../components/ui';
import { Input, Select } from '../components/forms';
import { formatCurrency, classNames } from '../utils/helpers';

const Products = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  // Local state
  const [showFilters, setShowFilters] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState(null);
  
  // Selectors
  const products = useSelector(selectProducts);
  const pagination = useSelector(selectProductPagination);
  const loading = useSelector(selectProductLoading);
  const error = useSelector(selectProductError);
  const selectedProducts = useSelector(selectSelectedProducts);
  const filters = useSelector(selectProductFilters);
  const searchQuery = useSelector(selectProductSearchQuery);

  // Fetch products on mount and when filters change
  useEffect(() => {
    dispatch(fetchProducts({
      page: pagination.current_page,
      limit: pagination.items_per_page,
      search: searchQuery,
      ...filters
    }));
  }, [dispatch, pagination.current_page, pagination.items_per_page, searchQuery, filters]);

  // Get stock status
  const getStockStatus = (product) => {
    const stock = product.current_stock || 0;
    const minStock = product.min_stock || 0;
    
    if (stock === 0) return { status: 'out_of_stock', label: 'Out of Stock', color: 'red' };
    if (stock <= minStock) return { status: 'low_stock', label: 'Low Stock', color: 'yellow' };
    return { status: 'in_stock', label: 'In Stock', color: 'green' };
  };

  // Table columns
  const columns = [
    {
      key: 'name',
      title: 'Product',
      sortable: true,
      render: (value, product) => (
        <div className="flex items-center space-x-3">
          {product.image ? (
            <img
              className="h-10 w-10 rounded-lg object-cover"
              src={product.image}
              alt={product.name}
            />
          ) : (
            <div className="h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
              <PhotoIcon className="h-6 w-6 text-gray-400" />
            </div>
          )}
          <div>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {product.name}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              SKU: {product.sku || 'N/A'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      title: 'Category',
      render: (value, product) => (
        <div className="flex items-center space-x-1">
          <TagIcon className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {product.category?.name || 'Uncategorized'}
          </span>
        </div>
      )
    },
    {
      key: 'brand',
      title: 'Brand',
      render: (value, product) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {product.brand?.name || '-'}
        </span>
      )
    },
    {
      key: 'current_stock',
      title: 'Stock',
      sortable: true,
      render: (value, product) => {
        const stockInfo = getStockStatus(product);
        return (
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {value || 0}
            </span>
            <span className={classNames(
              'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
              stockInfo.color === 'red' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
              stockInfo.color === 'yellow' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
              'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            )}>
              {stockInfo.label}
            </span>
          </div>
        );
      }
    },
    {
      key: 'selling_price',
      title: 'Price',
      sortable: true,
      render: (value, product) => (
        <div className="flex items-center space-x-1">
          <CurrencyDollarIcon className="h-4 w-4 text-gray-400" />
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {formatCurrency(value || 0)}
          </span>
        </div>
      )
    },
    {
      key: 'cost_price',
      title: 'Cost',
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {formatCurrency(value || 0)}
        </span>
      )
    },
    {
      key: 'barcode',
      title: 'Barcode',
      render: (value) => (
        <div className="flex items-center space-x-1">
          <QrCodeIcon className="h-4 w-4 text-gray-400" />
          <span className="text-xs font-mono text-gray-600 dark:text-gray-400">
            {value || '-'}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (value) => (
        <span className={classNames(
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          value === 'active' 
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
        )}>
          {value || 'active'}
        </span>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: 'View',
      icon: EyeIcon,
      onClick: (product) => navigate(`/products/${product.id}`)
    },
    {
      label: 'Edit',
      icon: PencilIcon,
      onClick: (product) => navigate(`/products/${product.id}/edit`)
    },
    {
      label: 'Update Stock',
      icon: CubeIcon,
      onClick: (product) => {
        const newStock = prompt(`Update stock for ${product.name}:`, product.current_stock);
        if (newStock !== null && !isNaN(newStock)) {
          dispatch(updateProductStock({ id: product.id, stock: parseInt(newStock) }));
        }
      }
    },
    {
      label: 'Delete',
      icon: TrashIcon,
      onClick: (product) => {
        setProductToDelete(product);
        setDeleteModalOpen(true);
      },
      disabled: (product) => product.current_stock > 0
    }
  ];

  // Filter options
  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' }
  ];

  const stockOptions = [
    { value: '', label: 'All Stock Levels' },
    { value: 'in_stock', label: 'In Stock' },
    { value: 'low_stock', label: 'Low Stock' },
    { value: 'out_of_stock', label: 'Out of Stock' }
  ];

  // Handlers
  const handleSearch = (query) => {
    dispatch(setSearchQuery(query));
  };

  const handleFilterChange = (key, value) => {
    dispatch(setFilter({ key, value }));
  };

  const handleClearFilters = () => {
    dispatch(clearFilters());
    dispatch(setSearchQuery(''));
  };

  const handlePageChange = (page) => {
    dispatch(fetchProducts({
      page,
      limit: pagination.items_per_page,
      search: searchQuery,
      ...filters
    }));
  };

  const handleRowSelect = (productId, checked) => {
    if (checked) {
      dispatch(selectProduct(productId));
    } else {
      dispatch(deselectProduct(productId));
    }
  };

  const handleSelectAll = (productIds) => {
    if (productIds.length > 0) {
      dispatch(selectAllProducts());
    } else {
      dispatch(deselectAllProducts());
    }
  };

  const handleDeleteProduct = async () => {
    if (productToDelete) {
      await dispatch(deleteProduct(productToDelete.id));
      setDeleteModalOpen(false);
      setProductToDelete(null);
      // Refresh the list
      dispatch(fetchProducts({
        page: pagination.current_page,
        limit: pagination.items_per_page,
        search: searchQuery,
        ...filters
      }));
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Products
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Manage your product catalog and inventory
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            leftIcon={ExclamationTriangleIcon}
            onClick={() => navigate('/products/alerts')}
            color="yellow"
          >
            Stock Alerts
          </Button>
          
          <Button
            variant="outline"
            leftIcon={CubeIcon}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </Button>
          
          <Button
            leftIcon={PlusIcon}
            onClick={() => navigate('/products/new')}
          >
            Add Product
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              label="Status"
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
              options={statusOptions}
            />
            
            <Select
              label="Stock Level"
              value={filters.stock_level}
              onChange={(value) => handleFilterChange('stock_level', value)}
              options={stockOptions}
            />
            
            <Input
              label="Category"
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              placeholder="Filter by category"
            />
            
            <div className="flex items-end space-x-2">
              <Button
                variant="outline"
                onClick={handleClearFilters}
                className="flex-1"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <DataTable
          data={products}
          columns={columns}
          loading={loading}
          error={error}
          searchQuery={searchQuery}
          onSearch={handleSearch}
          selectedRows={selectedProducts}
          onSelectRow={handleRowSelect}
          onSelectAll={handleSelectAll}
          selectable={true}
          actions={actions}
          onRowClick={(product) => navigate(`/products/${product.id}`)}
          emptyMessage="No products found"
        />
        
        {/* Pagination */}
        <Pagination
          currentPage={pagination.current_page}
          totalPages={pagination.total_pages}
          totalItems={pagination.total_items}
          itemsPerPage={pagination.items_per_page}
          onPageChange={handlePageChange}
        />
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteProduct}
        title="Delete Product"
        message={`Are you sure you want to delete "${productToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        confirmColor="red"
      />
    </div>
  );
};

export default Products;
