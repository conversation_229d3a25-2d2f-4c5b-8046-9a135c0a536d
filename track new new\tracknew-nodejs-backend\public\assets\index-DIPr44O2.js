import{r as e,a,g as t}from"./vendor-BRaCMJ4j.js";import{r,R as s,a as o,b as n,B as i}from"./router-BMeu0lFb.js";import{c as d,a as l,b as c,d as u,e as p,P as y}from"./redux-BLRds4RX.js";import{a as g}from"./utils-rSMZb3Ae.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))a(e);new MutationObserver((e=>{for(const t of e)if("childList"===t.type)for(const e of t.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&a(e)})).observe(document,{childList:!0,subtree:!0})}function a(e){if(e.ep)return;e.ep=!0;const a=function(e){const a={};return e.integrity&&(a.integrity=e.integrity),e.referrerPolicy&&(a.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?a.credentials="include":"anonymous"===e.crossOrigin?a.credentials="omit":a.credentials="same-origin",a}(e);fetch(e.href,a)}}();var m,f,h={exports:{}},v={};var E,C=(f||(f=1,h.exports=function(){if(m)return v;m=1;var a=e(),t=Symbol.for("react.element"),r=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,o=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,n={key:!0,ref:!0,__self:!0,__source:!0};function i(e,a,r){var i,d={},l=null,c=null;for(i in void 0!==r&&(l=""+r),void 0!==a.key&&(l=""+a.key),void 0!==a.ref&&(c=a.ref),a)s.call(a,i)&&!n.hasOwnProperty(i)&&(d[i]=a[i]);if(e&&e.defaultProps)for(i in a=e.defaultProps)void 0===d[i]&&(d[i]=a[i]);return{$$typeof:t,type:e,key:l,ref:c,props:d,_owner:o.current}}return v.Fragment=r,v.jsx=i,v.jsxs=i,v}()),h.exports),S={};const L=t(function(){if(E)return S;E=1;var e=a();return S.createRoot=e.createRoot,S.hydrateRoot=e.hydrateRoot,S}());var b="persist:",w="persist/FLUSH",j="persist/REHYDRATE",_="persist/PAUSE",x="persist/PERSIST",A="persist/PURGE",O="persist/REGISTER";function P(e){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function k(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter((function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable}))),t.push.apply(t,r)}return t}function I(e,a,t){return a in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}function V(e,a,t,r){r.debug;var s=function(e){for(var a=1;a<arguments.length;a++){var t=null!=arguments[a]?arguments[a]:{};a%2?k(t,!0).forEach((function(a){I(e,a,t[a])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):k(t).forEach((function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))}))}return e}({},t);return e&&"object"===P(e)&&Object.keys(e).forEach((function(r){"_persist"!==r&&a[r]===t[r]&&(s[r]=e[r])})),s}function W(e){var a,t=e.blacklist||null,r=e.whitelist||null,s=e.transforms||[],o=e.throttle||0,n="".concat(void 0!==e.keyPrefix?e.keyPrefix:b).concat(e.key),i=e.storage;a=!1===e.serialize?function(e){return e}:"function"==typeof e.serialize?e.serialize:R;var d=e.writeFailHandler||null,l={},c={},u=[],p=null,y=null;function g(){if(0===u.length)return p&&clearInterval(p),void(p=null);var e=u.shift(),t=s.reduce((function(a,t){return t.in(a,e,l)}),l[e]);if(void 0!==t)try{c[e]=a(t)}catch(r){}else delete c[e];0===u.length&&(Object.keys(c).forEach((function(e){void 0===l[e]&&delete c[e]})),y=i.setItem(n,a(c)).catch(f))}function m(e){return(!r||-1!==r.indexOf(e)||"_persist"===e)&&(!t||-1===t.indexOf(e))}function f(e){d&&d(e)}return{update:function(e){Object.keys(e).forEach((function(a){m(a)&&l[a]!==e[a]&&-1===u.indexOf(a)&&u.push(a)})),Object.keys(l).forEach((function(a){void 0===e[a]&&m(a)&&-1===u.indexOf(a)&&void 0!==l[a]&&u.push(a)})),null===p&&(p=setInterval(g,o)),l=e},flush:function(){for(;0!==u.length;)g();return y||Promise.resolve()}}}function R(e){return JSON.stringify(e)}function N(e){var a,t=e.transforms||[],r="".concat(void 0!==e.keyPrefix?e.keyPrefix:b).concat(e.key),s=e.storage;return e.debug,a=!1===e.deserialize?function(e){return e}:"function"==typeof e.deserialize?e.deserialize:T,s.getItem(r).then((function(e){if(e)try{var r={},s=a(e);return Object.keys(s).forEach((function(e){r[e]=t.reduceRight((function(a,t){return t.out(a,e,s)}),a(s[e]))})),r}catch(o){throw o}}))}function T(e){return JSON.parse(e)}function M(e){}function B(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter((function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable}))),t.push.apply(t,r)}return t}function F(e){for(var a=1;a<arguments.length;a++){var t=null!=arguments[a]?arguments[a]:{};a%2?B(t,!0).forEach((function(a){D(e,a,t[a])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):B(t).forEach((function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))}))}return e}function D(e,a,t){return a in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}function Q(e,a){if(null==e)return{};var t,r,s=function(e,a){if(null==e)return{};var t,r,s={},o=Object.keys(e);for(r=0;r<o.length;r++)t=o[r],a.indexOf(t)>=0||(s[t]=e[t]);return s}(e,a);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)t=o[r],a.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(e,t)&&(s[t]=e[t])}return s}function $(e,a){var t=void 0!==e.version?e.version:-1;e.debug;var r=void 0===e.stateReconciler?V:e.stateReconciler,s=e.getStoredState||N,o=void 0!==e.timeout?e.timeout:5e3,n=null,i=!1,d=!0,l=function(e){return e._persist.rehydrated&&n&&!d&&n.update(e),e};return function(c,u){var p=c||{},y=p._persist,g=Q(p,["_persist"]);if(u.type===x){var m=!1,f=function(a,t){m||(u.rehydrate(e.key,a,t),m=!0)};if(o&&setTimeout((function(){!m&&f(void 0,new Error('redux-persist: persist timed out for persist key "'.concat(e.key,'"')))}),o),d=!1,n||(n=W(e)),y)return F({},a(g,u),{_persist:y});if("function"!=typeof u.rehydrate||"function"!=typeof u.register)throw new Error("redux-persist: either rehydrate or register is not a function on the PERSIST action. This can happen if the action is being replayed. This is an unexplored use case, please open an issue and we will figure out a resolution.");return u.register(e.key),s(e).then((function(a){(e.migrate||function(e,a){return Promise.resolve(e)})(a,t).then((function(e){f(e)}),(function(e){f(void 0,e)}))}),(function(e){f(void 0,e)})),F({},a(g,u),{_persist:{version:t,rehydrated:!1}})}if(u.type===A)return i=!0,u.result(function(e){var a=e.storage,t="".concat(void 0!==e.keyPrefix?e.keyPrefix:b).concat(e.key);return a.removeItem(t,M)}(e)),F({},a(g,u),{_persist:y});if(u.type===w)return u.result(n&&n.flush()),F({},a(g,u),{_persist:y});if(u.type===_)d=!0;else if(u.type===j){if(i)return F({},g,{_persist:F({},y,{rehydrated:!0})});if(u.key===e.key){var h=a(g,u),v=u.payload,E=F({},!1!==r&&void 0!==v?r(v,c,h,e):h,{_persist:F({},y,{rehydrated:!0})});return l(E)}}if(!y)return a(c,u);var C=a(g,u);return C===g?c:l(F({},C,{_persist:y}))}}function U(e){return function(e){if(Array.isArray(e)){for(var a=0,t=new Array(e.length);a<e.length;a++)t[a]=e[a];return t}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function q(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter((function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable}))),t.push.apply(t,r)}return t}function z(e){for(var a=1;a<arguments.length;a++){var t=null!=arguments[a]?arguments[a]:{};a%2?q(t,!0).forEach((function(a){H(e,a,t[a])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):q(t).forEach((function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))}))}return e}function H(e,a,t){return a in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}var Y={registry:[],bootstrapped:!1},G=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Y,a=arguments.length>1?arguments[1]:void 0;switch(a.type){case O:return z({},e,{registry:[].concat(U(e.registry),[a.key])});case j:var t=e.registry.indexOf(a.key),r=U(e.registry);return r.splice(t,1),z({},e,{registry:r,bootstrapped:0===r.length});default:return e}};var J,K,Z,X={},ee={},ae={};function te(){if(J)return ae;function e(a){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(a)}function a(){}J=1,ae.__esModule=!0,ae.default=function(a){var r="".concat(a,"Storage");return function(a){if("object"!==("undefined"==typeof self?"undefined":e(self))||!(a in self))return!1;try{var t=self[a],r="redux-persist ".concat(a," test");t.setItem(r,"test"),t.getItem(r),t.removeItem(r)}catch(_e){return!1}return!0}(r)?self[r]:t};var t={getItem:a,setItem:a,removeItem:a};return ae}const re=t(function(){if(Z)return X;var e;Z=1,X.__esModule=!0,X.default=void 0;var a=(0,((e=function(){if(K)return ee;K=1,ee.__esModule=!0,ee.default=function(e){var t=(0,a.default)(e);return{getItem:function(e){return new Promise((function(a,r){a(t.getItem(e))}))},setItem:function(e,a){return new Promise((function(r,s){r(t.setItem(e,a))}))},removeItem:function(e){return new Promise((function(a,r){a(t.removeItem(e))}))}}};var e,a=(e=te())&&e.__esModule?e:{default:e};return ee}())&&e.__esModule?e:{default:e}).default)("local");return X.default=a,X}());let se,oe,ne,ie={data:""},de=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,le=/\/\*[^]*?\*\/|  +/g,ce=/\n+/g,ue=(e,a)=>{let t="",r="",s="";for(let o in e){let n=e[o];"@"==o[0]?"i"==o[1]?t=o+" "+n+";":r+="f"==o[1]?ue(n,o):o+"{"+ue(n,"k"==o[1]?"":a)+"}":"object"==typeof n?r+=ue(n,a?a.replace(/([^,])+/g,(e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(a=>/&/.test(a)?a.replace(/&/g,e):e?e+" "+a:a)))):o):null!=n&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=ue.p?ue.p(o,n):o+":"+n+";")}return t+(a&&s?a+"{"+s+"}":s)+r},pe={},ye=e=>{if("object"==typeof e){let a="";for(let t in e)a+=t+ye(e[t]);return a}return e};function ge(e){let a=this||{},t=e.call?e(a.p):e;return((e,a,t,r,s)=>{let o=ye(e),n=pe[o]||(pe[o]=(e=>{let a=0,t=11;for(;a<e.length;)t=101*t+e.charCodeAt(a++)>>>0;return"go"+t})(o));if(!pe[n]){let a=o!==e?e:(e=>{let a,t,r=[{}];for(;a=de.exec(e.replace(le,""));)a[4]?r.shift():a[3]?(t=a[3].replace(ce," ").trim(),r.unshift(r[0][t]=r[0][t]||{})):r[0][a[1]]=a[2].replace(ce," ").trim();return r[0]})(e);pe[n]=ue(s?{["@keyframes "+n]:a}:a,t?"":"."+n)}let i=t&&pe.g?pe.g:null;return t&&(pe.g=pe[n]),d=pe[n],l=a,c=r,(u=i)?l.data=l.data.replace(u,d):-1===l.data.indexOf(d)&&(l.data=c?d+l.data:l.data+d),n;var d,l,c,u})(t.unshift?t.raw?((e,a,t)=>e.reduce(((e,r,s)=>{let o=a[s];if(o&&o.call){let e=o(t),a=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=a?"."+a:e&&"object"==typeof e?e.props?"":ue(e,""):!1===e?"":e}return e+r+(null==o?"":o)}),""))(t,[].slice.call(arguments,1),a.p):t.reduce(((e,t)=>Object.assign(e,t&&t.call?t(a.p):t)),{}):t,(r=a.target,"object"==typeof window?((r?r.querySelector("#_goober"):window._goober)||Object.assign((r||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:r||ie),a.g,a.o,a.k);var r}ge.bind({g:1});let me=ge.bind({k:1});function fe(e,a){let t=this||{};return function(){let a=arguments;return function r(s,o){let n=Object.assign({},s),i=n.className||r.className;t.p=Object.assign({theme:oe&&oe()},n),t.o=/ *go\d+/.test(i),n.className=ge.apply(t,a)+(i?" "+i:"");let d=e;return e[0]&&(d=n.as||e,delete n.as),ne&&d[0]&&ne(n),se(d,n)}}}var he=(e,a)=>(e=>"function"==typeof e)(e)?e(a):e,ve=(()=>{let e=0;return()=>(++e).toString()})(),Ee=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let a=matchMedia("(prefers-reduced-motion: reduce)");e=!a||a.matches}return e}})(),Ce=(e,a)=>{switch(a.type){case 0:return{...e,toasts:[a.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map((e=>e.id===a.toast.id?{...e,...a.toast}:e))};case 2:let{toast:t}=a;return Ce(e,{type:e.toasts.find((e=>e.id===t.id))?1:0,toast:t});case 3:let{toastId:r}=a;return{...e,toasts:e.toasts.map((e=>e.id===r||void 0===r?{...e,dismissed:!0,visible:!1}:e))};case 4:return void 0===a.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==a.toastId))};case 5:return{...e,pausedAt:a.time};case 6:let s=a.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map((e=>({...e,pauseDuration:e.pauseDuration+s})))}}},Se=[],Le={toasts:[],pausedAt:void 0},be=e=>{Le=Ce(Le,e),Se.forEach((e=>{e(Le)}))},we=e=>(a,t)=>{let r=((e,a="blank",t)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:a,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...t,id:(null==t?void 0:t.id)||ve()}))(a,e,t);return be({type:2,toast:r}),r.id},je=(e,a)=>we("blank")(e,a);je.error=we("error"),je.success=we("success"),je.loading=we("loading"),je.custom=we("custom"),je.dismiss=e=>{be({type:3,toastId:e})},je.remove=e=>be({type:4,toastId:e}),je.promise=(e,a,t)=>{let r=je.loading(a.loading,{...t,...null==t?void 0:t.loading});return"function"==typeof e&&(e=e()),e.then((e=>{let s=a.success?he(a.success,e):void 0;return s?je.success(s,{id:r,...t,...null==t?void 0:t.success}):je.dismiss(r),e})).catch((e=>{let s=a.error?he(a.error,e):void 0;s?je.error(s,{id:r,...t,...null==t?void 0:t.error}):je.dismiss(r)})),e};var _e,xe,Ae,Oe,Pe=me`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,ke=me`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Ie=me`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Ve=fe("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Pe} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${ke} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Ie} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,We=me`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Re=fe("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${We} 1s linear infinite;
`,Ne=me`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Te=me`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Me=fe("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Ne} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Te} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Be=fe("div")`
  position: absolute;
`,Fe=fe("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,De=me`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Qe=fe("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${De} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,$e=({toast:e})=>{let{icon:a,type:t,iconTheme:s}=e;return void 0!==a?"string"==typeof a?r.createElement(Qe,null,a):a:"blank"===t?null:r.createElement(Fe,null,r.createElement(Re,{...s}),"loading"!==t&&r.createElement(Be,null,"error"===t?r.createElement(Ve,{...s}):r.createElement(Me,{...s})))},Ue=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,qe=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,ze=fe("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,He=fe("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`;r.memo((({toast:e,position:a,style:t,children:s})=>{let o=e.height?((e,a)=>{let t=e.includes("top")?1:-1,[r,s]=Ee()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[Ue(t),qe(t)];return{animation:a?`${me(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${me(s)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(e.position||a||"top-center",e.visible):{opacity:0},n=r.createElement($e,{toast:e}),i=r.createElement(He,{...e.ariaProps},he(e.message,e));return r.createElement(ze,{className:e.className,style:{...o,...t,...e.style}},"function"==typeof s?s({icon:n,message:i}):r.createElement(r.Fragment,null,n,i))})),_e=r.createElement,ue.p=xe,se=_e,oe=Ae,ne=Oe,ge`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`;const Ye={development:{API_BASE_URL:"http://localhost:8000/api",APP_URL:"http://localhost:3000"},production:{API_BASE_URL:"/api",APP_URL:window.location.origin},test:{API_BASE_URL:"http://localhost:8000/api",APP_URL:"http://localhost:3000"}}.production;const Ge=g.create({baseURL:{}.REACT_APP_API_URL||Ye.API_BASE_URL,timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0});Ge.interceptors.request.use((e=>{const a=Ei.getState().auth.token;return a&&(e.headers.Authorization=`Bearer ${a}`),e}),(e=>Promise.reject(e))),Ge.interceptors.response.use((e=>e),(async e=>{var a,t,r,s,o,n;const i=e.config;if(401===(null==(a=e.response)?void 0:a.status)&&!i._retry){i._retry=!0;try{const e=Ei.getState();if(e.auth.refreshToken){await Ei.dispatch(oa()).unwrap();const e=Ei.getState().auth.token;if(e)return i.headers.Authorization=`Bearer ${e}`,Ge(i)}}catch(d){return Ei.dispatch(pa()),window.location.href="/login",Promise.reject(d)}}if(403===(null==(t=e.response)?void 0:t.status))je.error("You do not have permission to perform this action");else if(404===(null==(r=e.response)?void 0:r.status))je.error("Resource not found");else if(422===(null==(s=e.response)?void 0:s.status)){const a=e.response.data.errors;a&&"object"==typeof a?Object.values(a).forEach((e=>{Array.isArray(e)&&e.forEach((e=>je.error(e)))})):je.error(e.response.data.message||"Validation error")}else 429===(null==(o=e.response)?void 0:o.status)?je.error("Too many requests. Please try again later."):(null==(n=e.response)?void 0:n.status)>=500?je.error("Server error. Please try again later."):"ECONNABORTED"===e.code?je.error("Request timeout. Please check your connection."):e.response||je.error("Network error. Please check your connection.");return Promise.reject(e)}));const Je=(e,a={})=>Ge.get(e,a),Ke=(e,a={},t={})=>Ge.post(e,a,t),Ze=(e,a={},t={})=>Ge.put(e,a,t),Xe=(e,a={},t={})=>Ge.patch(e,a,t),ea=(e,a={})=>Ge.delete(e,a),aa={login:e=>Ge.post("/auth/login",e),register:e=>Ge.post("/auth/register",e),logout:()=>Ge.post("/auth/logout"),refreshToken:()=>Ge.post("/auth/refresh"),forgotPassword:e=>Ge.post("/auth/forgot-password",{email:e}),resetPassword:e=>Ge.post("/auth/reset-password",e),verifyEmail:e=>Ge.post("/auth/verify-email",{token:e}),getProfile:()=>Ge.get("/auth/profile"),updateProfile:e=>Ge.put("/auth/profile",e)},ta=l("auth/login",(async({login:e,password:a},{rejectWithValue:t})=>{var r,s;try{const t=await aa.login({login:e,password:a});return je.success("Login successful!"),t.data}catch(o){const e=(null==(s=null==(r=o.response)?void 0:r.data)?void 0:s.message)||"Login failed";return je.error(e),t(e)}})),ra=l("auth/register",(async(e,{rejectWithValue:a})=>{var t,r;try{const a=await aa.register(e);return je.success("Registration successful!"),a.data}catch(s){const e=(null==(r=null==(t=s.response)?void 0:t.data)?void 0:r.message)||"Registration failed";return je.error(e),a(e)}})),sa=l("auth/logout",(async(e,{rejectWithValue:a})=>{try{return await aa.logout(),je.success("Logged out successfully"),{}}catch(t){return{}}})),oa=l("auth/refreshToken",(async(e,{getState:a,rejectWithValue:t})=>{var r,s;try{const{auth:e}=a();return(await aa.refreshToken(e.refreshToken)).data}catch(o){return t((null==(s=null==(r=o.response)?void 0:r.data)?void 0:s.message)||"Token refresh failed")}})),na=l("auth/getCurrentUser",(async(e,{rejectWithValue:a})=>{var t,r;try{return(await aa.getCurrentUser()).data}catch(s){return a((null==(r=null==(t=s.response)?void 0:t.data)?void 0:r.message)||"Failed to get user data")}})),ia=l("auth/forgotPassword",(async({email:e},{rejectWithValue:a})=>{var t,r;try{const a=await aa.forgotPassword({email:e});return je.success("Password reset email sent!"),a.data}catch(s){const e=(null==(r=null==(t=s.response)?void 0:t.data)?void 0:r.message)||"Failed to send reset email";return je.error(e),a(e)}})),da=l("auth/resetPassword",(async({token:e,password:a},{rejectWithValue:t})=>{var r,s;try{const t=await aa.resetPassword({token:e,password:a});return je.success("Password reset successful!"),t.data}catch(o){const e=(null==(s=null==(r=o.response)?void 0:r.data)?void 0:s.message)||"Password reset failed";return je.error(e),t(e)}})),la=l("auth/updatePassword",(async({currentPassword:e,newPassword:a},{rejectWithValue:t})=>{var r,s;try{const t=await aa.updatePassword({passwordCurrent:e,password:a});return je.success("Password updated successfully!"),t.data}catch(o){const e=(null==(s=null==(r=o.response)?void 0:r.data)?void 0:s.message)||"Password update failed";return je.error(e),t(e)}})),ca=c({name:"auth",initialState:{user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null,loginAttempts:0,isAccountLocked:!1},reducers:{clearError:e=>{e.error=null},clearAuth:e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.error=null,e.loginAttempts=0,e.isAccountLocked=!1},setCredentials:(e,a)=>{const{user:t,token:r,refreshToken:s}=a.payload;e.user=t,e.token=r,e.refreshToken=s,e.isAuthenticated=!0,e.error=null,e.loginAttempts=0,e.isAccountLocked=!1},updateUser:(e,a)=>{e.user={...e.user,...a.payload}},incrementLoginAttempts:e=>{e.loginAttempts+=1,e.loginAttempts>=5&&(e.isAccountLocked=!0)},resetLoginAttempts:e=>{e.loginAttempts=0,e.isAccountLocked=!1}},extraReducers:e=>{e.addCase(ta.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(ta.fulfilled,((e,a)=>{e.isLoading=!1,e.user=a.payload.data.user,e.token=a.payload.token,e.refreshToken=a.payload.refreshToken,e.isAuthenticated=!0,e.error=null,e.loginAttempts=0,e.isAccountLocked=!1})).addCase(ta.rejected,((e,a)=>{e.isLoading=!1,e.error=a.payload,e.loginAttempts+=1,e.loginAttempts>=5&&(e.isAccountLocked=!0)})).addCase(ra.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(ra.fulfilled,((e,a)=>{e.isLoading=!1,e.user=a.payload.data.user,e.token=a.payload.token,e.refreshToken=a.payload.refreshToken,e.isAuthenticated=!0,e.error=null})).addCase(ra.rejected,((e,a)=>{e.isLoading=!1,e.error=a.payload})).addCase(sa.fulfilled,(e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.error=null,e.loginAttempts=0,e.isAccountLocked=!1})).addCase(oa.fulfilled,((e,a)=>{e.token=a.payload.token,e.refreshToken=a.payload.refreshToken,e.user=a.payload.data.user})).addCase(oa.rejected,(e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1})).addCase(na.pending,(e=>{e.isLoading=!0})).addCase(na.fulfilled,((e,a)=>{e.isLoading=!1,e.user=a.payload.data.user,e.isAuthenticated=!0})).addCase(na.rejected,(e=>{e.isLoading=!1,e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1})).addCase(ia.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(ia.fulfilled,(e=>{e.isLoading=!1,e.error=null})).addCase(ia.rejected,((e,a)=>{e.isLoading=!1,e.error=a.payload})).addCase(da.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(da.fulfilled,((e,a)=>{e.isLoading=!1,e.user=a.payload.data.user,e.token=a.payload.token,e.refreshToken=a.payload.refreshToken,e.isAuthenticated=!0,e.error=null})).addCase(da.rejected,((e,a)=>{e.isLoading=!1,e.error=a.payload})).addCase(la.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(la.fulfilled,((e,a)=>{e.isLoading=!1,e.token=a.payload.token,e.refreshToken=a.payload.refreshToken,e.error=null})).addCase(la.rejected,((e,a)=>{e.isLoading=!1,e.error=a.payload}))}}),{clearError:ua,clearAuth:pa,setCredentials:ya,updateUser:ga,incrementLoginAttempts:ma,resetLoginAttempts:fa}=ca.actions,ha=ca.reducer,va={sidebarOpen:!1,sidebarCollapsed:!1,theme:"light",globalLoading:!1,modals:{},notifications:{desktop:!0,email:!0,push:!0},layout:{density:"comfortable",tablePageSize:15,cardView:!1},filters:{},searchQueries:{},recentItems:[],breadcrumbs:[],pageTitle:"Dashboard",errors:{},successMessages:[]},Ea=c({name:"ui",initialState:va,reducers:{toggleSidebar:e=>{e.sidebarOpen=!e.sidebarOpen},setSidebarOpen:(e,a)=>{e.sidebarOpen=a.payload},toggleSidebarCollapsed:e=>{e.sidebarCollapsed=!e.sidebarCollapsed},setSidebarCollapsed:(e,a)=>{e.sidebarCollapsed=a.payload},setTheme:(e,a)=>{e.theme=a.payload},toggleTheme:e=>{e.theme="light"===e.theme?"dark":"light"},setGlobalLoading:(e,a)=>{e.globalLoading=a.payload},openModal:(e,a)=>{const{modalId:t,props:r={}}=a.payload;e.modals[t]={isOpen:!0,props:r}},closeModal:(e,a)=>{const t=a.payload;e.modals[t]&&(e.modals[t].isOpen=!1)},closeAllModals:e=>{Object.keys(e.modals).forEach((a=>{e.modals[a].isOpen=!1}))},setNotificationPreference:(e,a)=>{const{type:t,enabled:r}=a.payload;e.notifications[t]=r},setLayoutDensity:(e,a)=>{e.layout.density=a.payload},setTablePageSize:(e,a)=>{e.layout.tablePageSize=a.payload},toggleCardView:e=>{e.layout.cardView=!e.layout.cardView},setCardView:(e,a)=>{e.layout.cardView=a.payload},setFilter:(e,a)=>{const{page:t,filters:r}=a.payload;e.filters[t]={...e.filters[t],...r}},clearFilters:(e,a)=>{const t=a.payload;e.filters[t]={}},setSearchQuery:(e,a)=>{const{page:t,query:r}=a.payload;e.searchQueries[t]=r},clearSearchQuery:(e,a)=>{const t=a.payload;e.searchQueries[t]=""},addRecentItem:(e,a)=>{const t=a.payload,r=e.recentItems.findIndex((e=>e.id===t.id&&e.type===t.type));-1!==r&&e.recentItems.splice(r,1),e.recentItems.unshift(t),e.recentItems.length>10&&(e.recentItems=e.recentItems.slice(0,10))},clearRecentItems:e=>{e.recentItems=[]},setBreadcrumbs:(e,a)=>{e.breadcrumbs=a.payload},addBreadcrumb:(e,a)=>{e.breadcrumbs.push(a.payload)},clearBreadcrumbs:e=>{e.breadcrumbs=[]},setPageTitle:(e,a)=>{e.pageTitle=a.payload},setError:(e,a)=>{const{key:t,error:r}=a.payload;e.errors[t]=r},clearError:(e,a)=>{const t=a.payload;delete e.errors[t]},clearAllErrors:e=>{e.errors={}},addSuccessMessage:(e,a)=>{e.successMessages.push({id:Date.now(),message:a.payload,timestamp:(new Date).toISOString()})},removeSuccessMessage:(e,a)=>{const t=a.payload;e.successMessages=e.successMessages.filter((e=>e.id!==t))},clearSuccessMessages:e=>{e.successMessages=[]},resetUIState:e=>({...va,theme:e.theme,notifications:e.notifications,layout:e.layout})}}),{toggleSidebar:Ca,setSidebarOpen:Sa,toggleSidebarCollapsed:La,setSidebarCollapsed:ba,setTheme:wa,toggleTheme:ja,setGlobalLoading:_a,openModal:xa,closeModal:Aa,closeAllModals:Oa,setNotificationPreference:Pa,setLayoutDensity:ka,setTablePageSize:Ia,toggleCardView:Va,setCardView:Wa,setFilter:Ra,clearFilters:Na,setSearchQuery:Ta,clearSearchQuery:Ma,addRecentItem:Ba,clearRecentItems:Fa,setBreadcrumbs:Da,addBreadcrumb:Qa,clearBreadcrumbs:$a,setPageTitle:Ua,setError:qa,clearError:za,clearAllErrors:Ha,addSuccessMessage:Ya,removeSuccessMessage:Ga,clearSuccessMessages:Ja,resetUIState:Ka}=Ea.actions,Za=Ea.reducer,Xa=l("service/fetchServices",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/services",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),et=l("service/fetchService",(async(e,{rejectWithValue:a})=>{var t;try{return(await Je(`/services/${e}`)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),at=l("service/createService",(async(e,{rejectWithValue:a})=>{var t;try{return(await Ke("/services",e)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),tt=l("service/updateService",(async({id:e,data:a},{rejectWithValue:t})=>{var r;try{return(await Ze(`/services/${e}`,a)).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),rt=l("service/deleteService",(async(e,{rejectWithValue:a})=>{var t;try{return await ea(`/services/${e}`),e}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),st=l("service/fetchServiceStats",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/services/stats",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),ot=l("service/assignTechnician",(async({serviceId:e,technicianId:a},{rejectWithValue:t})=>{var r;try{return(await Ke(`/services/${e}/assign`,{technician_id:a})).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),nt=l("service/updateServiceStatus",(async({serviceId:e,status:a,notes:t},{rejectWithValue:r})=>{var s;try{return(await Xe(`/services/${e}/status`,{status:a,notes:t})).data}catch(o){return r((null==(s=o.response)?void 0:s.data)||o.message)}})),it={services:[],currentService:null,stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,serviceLoading:!1,statsLoading:!1,actionLoading:!1,error:null,serviceError:null,statsError:null,actionError:null,selectedServices:[],filters:{status:"",priority:"",technician_id:"",customer_id:"",service_type:"",date_from:"",date_to:""},sortBy:"created_at",sortOrder:"desc",searchQuery:""},dt=c({name:"service",initialState:it,reducers:{selectService:(e,a)=>{const t=a.payload;e.selectedServices.includes(t)||e.selectedServices.push(t)},deselectService:(e,a)=>{const t=a.payload;e.selectedServices=e.selectedServices.filter((e=>e!==t))},selectAllServices:e=>{e.selectedServices=e.services.map((e=>e.id))},deselectAllServices:e=>{e.selectedServices=[]},setFilter:(e,a)=>{const{key:t,value:r}=a.payload;e.filters[t]=r},setFilters:(e,a)=>{e.filters={...e.filters,...a.payload}},clearFilters:e=>{e.filters=it.filters},setSortBy:(e,a)=>{e.sortBy=a.payload},setSortOrder:(e,a)=>{e.sortOrder=a.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,a)=>{e.searchQuery=a.payload},clearSearchQuery:e=>{e.searchQuery=""},clearCurrentService:e=>{e.currentService=null,e.serviceError=null},clearError:e=>{e.error=null},clearServiceError:e=>{e.serviceError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetServiceState:e=>it},extraReducers:e=>{e.addCase(Xa.pending,(e=>{e.loading=!0,e.error=null})).addCase(Xa.fulfilled,((e,a)=>{e.loading=!1,e.services=a.payload.data.services,e.pagination=a.payload.data.pagination})).addCase(Xa.rejected,((e,a)=>{e.loading=!1,e.error=a.payload})),e.addCase(et.pending,(e=>{e.serviceLoading=!0,e.serviceError=null})).addCase(et.fulfilled,((e,a)=>{e.serviceLoading=!1,e.currentService=a.payload.data.service})).addCase(et.rejected,((e,a)=>{e.serviceLoading=!1,e.serviceError=a.payload})),e.addCase(at.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(at.fulfilled,((e,a)=>{e.actionLoading=!1,e.services.unshift(a.payload.data.service)})).addCase(at.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(tt.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(tt.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload.data.service,s=e.services.findIndex((e=>e.id===r.id));-1!==s&&(e.services[s]=r),(null==(t=e.currentService)?void 0:t.id)===r.id&&(e.currentService=r)})).addCase(tt.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(rt.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(rt.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload;e.services=e.services.filter((e=>e.id!==r)),e.selectedServices=e.selectedServices.filter((e=>e!==r)),(null==(t=e.currentService)?void 0:t.id)===r&&(e.currentService=null)})).addCase(rt.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(st.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(st.fulfilled,((e,a)=>{e.statsLoading=!1,e.stats=a.payload.data})).addCase(st.rejected,((e,a)=>{e.statsLoading=!1,e.statsError=a.payload})),e.addCase(ot.fulfilled,((e,a)=>{var t;const r=a.payload.data.service,s=e.services.findIndex((e=>e.id===r.id));-1!==s&&(e.services[s]=r),(null==(t=e.currentService)?void 0:t.id)===r.id&&(e.currentService=r)})),e.addCase(nt.fulfilled,((e,a)=>{var t;const r=a.payload.data.service,s=e.services.findIndex((e=>e.id===r.id));-1!==s&&(e.services[s]=r),(null==(t=e.currentService)?void 0:t.id)===r.id&&(e.currentService=r)}))}}),{selectService:lt,deselectService:ct,selectAllServices:ut,deselectAllServices:pt,setFilter:yt,setFilters:gt,clearFilters:mt,setSortBy:ft,setSortOrder:ht,toggleSortOrder:vt,setSearchQuery:Et,clearSearchQuery:Ct,clearCurrentService:St,clearError:Lt,clearServiceError:bt,clearStatsError:wt,clearActionError:jt,resetServiceState:_t}=dt.actions,xt=dt.reducer,At=l("customer/fetchCustomers",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/customers",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Ot=l("customer/fetchCustomer",(async(e,{rejectWithValue:a})=>{var t;try{return(await Je(`/customers/${e}`)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Pt=l("customer/createCustomer",(async(e,{rejectWithValue:a})=>{var t;try{return(await Ke("/customers",e)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),kt=l("customer/updateCustomer",(async({id:e,data:a},{rejectWithValue:t})=>{var r;try{return(await Ze(`/customers/${e}`,a)).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),It=l("customer/deleteCustomer",(async(e,{rejectWithValue:a})=>{var t;try{return await ea(`/customers/${e}`),e}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Vt=l("customer/fetchCustomerStats",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/customers/stats",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Wt=l("customer/fetchCustomerHistory",(async(e,{rejectWithValue:a})=>{var t;try{return(await Je(`/customers/${e}/history`)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Rt=l("customer/bulkDeleteCustomers",(async(e,{rejectWithValue:a})=>{var t;try{return await ea("/customers/bulk",{data:{customer_ids:e}}),e}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Nt={customers:[],currentCustomer:null,customerHistory:null,stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,customerLoading:!1,historyLoading:!1,statsLoading:!1,actionLoading:!1,error:null,customerError:null,historyError:null,statsError:null,actionError:null,selectedCustomers:[],filters:{status:"",customer_type:"",category_id:"",city:"",state:"",date_from:"",date_to:""},sortBy:"created_at",sortOrder:"desc",searchQuery:""},Tt=c({name:"customer",initialState:Nt,reducers:{selectCustomer:(e,a)=>{const t=a.payload;e.selectedCustomers.includes(t)||e.selectedCustomers.push(t)},deselectCustomer:(e,a)=>{const t=a.payload;e.selectedCustomers=e.selectedCustomers.filter((e=>e!==t))},selectAllCustomers:e=>{e.selectedCustomers=e.customers.map((e=>e.id))},deselectAllCustomers:e=>{e.selectedCustomers=[]},setFilter:(e,a)=>{const{key:t,value:r}=a.payload;e.filters[t]=r},setFilters:(e,a)=>{e.filters={...e.filters,...a.payload}},clearFilters:e=>{e.filters=Nt.filters},setSortBy:(e,a)=>{e.sortBy=a.payload},setSortOrder:(e,a)=>{e.sortOrder=a.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,a)=>{e.searchQuery=a.payload},clearSearchQuery:e=>{e.searchQuery=""},clearCurrentCustomer:e=>{e.currentCustomer=null,e.customerError=null},clearCustomerHistory:e=>{e.customerHistory=null,e.historyError=null},clearError:e=>{e.error=null},clearCustomerError:e=>{e.customerError=null},clearHistoryError:e=>{e.historyError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetCustomerState:e=>Nt},extraReducers:e=>{e.addCase(At.pending,(e=>{e.loading=!0,e.error=null})).addCase(At.fulfilled,((e,a)=>{e.loading=!1,e.customers=a.payload.data.customers,e.pagination=a.payload.data.pagination})).addCase(At.rejected,((e,a)=>{e.loading=!1,e.error=a.payload})),e.addCase(Ot.pending,(e=>{e.customerLoading=!0,e.customerError=null})).addCase(Ot.fulfilled,((e,a)=>{e.customerLoading=!1,e.currentCustomer=a.payload.data.customer})).addCase(Ot.rejected,((e,a)=>{e.customerLoading=!1,e.customerError=a.payload})),e.addCase(Pt.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Pt.fulfilled,((e,a)=>{e.actionLoading=!1,e.customers.unshift(a.payload.data.customer)})).addCase(Pt.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(kt.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(kt.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload.data.customer,s=e.customers.findIndex((e=>e.id===r.id));-1!==s&&(e.customers[s]=r),(null==(t=e.currentCustomer)?void 0:t.id)===r.id&&(e.currentCustomer=r)})).addCase(kt.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(It.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(It.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload;e.customers=e.customers.filter((e=>e.id!==r)),e.selectedCustomers=e.selectedCustomers.filter((e=>e!==r)),(null==(t=e.currentCustomer)?void 0:t.id)===r&&(e.currentCustomer=null)})).addCase(It.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Wt.pending,(e=>{e.historyLoading=!0,e.historyError=null})).addCase(Wt.fulfilled,((e,a)=>{e.historyLoading=!1,e.customerHistory=a.payload.data})).addCase(Wt.rejected,((e,a)=>{e.historyLoading=!1,e.historyError=a.payload})),e.addCase(Vt.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(Vt.fulfilled,((e,a)=>{e.statsLoading=!1,e.stats=a.payload.data})).addCase(Vt.rejected,((e,a)=>{e.statsLoading=!1,e.statsError=a.payload})),e.addCase(Rt.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Rt.fulfilled,((e,a)=>{e.actionLoading=!1;const t=a.payload;e.customers=e.customers.filter((e=>!t.includes(e.id))),e.selectedCustomers=e.selectedCustomers.filter((e=>!t.includes(e)))})).addCase(Rt.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload}))}}),{selectCustomer:Mt,deselectCustomer:Bt,selectAllCustomers:Ft,deselectAllCustomers:Dt,setFilter:Qt,setFilters:$t,clearFilters:Ut,setSortBy:qt,setSortOrder:zt,toggleSortOrder:Ht,setSearchQuery:Yt,clearSearchQuery:Gt,clearCurrentCustomer:Jt,clearCustomerHistory:Kt,clearError:Zt,clearCustomerError:Xt,clearHistoryError:er,clearStatsError:ar,clearActionError:tr,resetCustomerState:rr}=Tt.actions,sr=Tt.reducer,or=l("lead/fetchLeads",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/leads",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),nr=l("lead/fetchLead",(async(e,{rejectWithValue:a})=>{var t;try{return(await Je(`/leads/${e}`)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),ir=l("lead/createLead",(async(e,{rejectWithValue:a})=>{var t;try{return(await Ke("/leads",e)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),dr=l("lead/updateLead",(async({id:e,data:a},{rejectWithValue:t})=>{var r;try{return(await Ze(`/leads/${e}`,a)).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),lr=l("lead/deleteLead",(async(e,{rejectWithValue:a})=>{var t;try{return await ea(`/leads/${e}`),e}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),cr=l("lead/convertLeadToCustomer",(async(e,{rejectWithValue:a})=>{var t;try{return(await Ke(`/leads/${e}/convert`)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),ur=l("lead/updateLeadStatus",(async({leadId:e,status:a,notes:t},{rejectWithValue:r})=>{var s;try{return(await Xe(`/leads/${e}/status`,{status:a,notes:t})).data}catch(o){return r((null==(s=o.response)?void 0:s.data)||o.message)}})),pr=l("lead/fetchLeadStats",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/leads/stats",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),yr={leads:[],currentLead:null,stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,leadLoading:!1,statsLoading:!1,actionLoading:!1,error:null,leadError:null,statsError:null,actionError:null,selectedLeads:[],filters:{status:"",priority:"",source:"",assigned_to:"",date_from:"",date_to:""},sortBy:"created_at",sortOrder:"desc",searchQuery:"",pipeline:{new:0,contacted:0,qualified:0,proposal:0,negotiation:0,won:0,lost:0}},gr=c({name:"lead",initialState:yr,reducers:{selectLead:(e,a)=>{const t=a.payload;e.selectedLeads.includes(t)||e.selectedLeads.push(t)},deselectLead:(e,a)=>{const t=a.payload;e.selectedLeads=e.selectedLeads.filter((e=>e!==t))},selectAllLeads:e=>{e.selectedLeads=e.leads.map((e=>e.id))},deselectAllLeads:e=>{e.selectedLeads=[]},setFilter:(e,a)=>{const{key:t,value:r}=a.payload;e.filters[t]=r},setFilters:(e,a)=>{e.filters={...e.filters,...a.payload}},clearFilters:e=>{e.filters=yr.filters},setSortBy:(e,a)=>{e.sortBy=a.payload},setSortOrder:(e,a)=>{e.sortOrder=a.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,a)=>{e.searchQuery=a.payload},clearSearchQuery:e=>{e.searchQuery=""},clearCurrentLead:e=>{e.currentLead=null,e.leadError=null},updatePipeline:(e,a)=>{e.pipeline={...e.pipeline,...a.payload}},clearError:e=>{e.error=null},clearLeadError:e=>{e.leadError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetLeadState:e=>yr},extraReducers:e=>{e.addCase(or.pending,(e=>{e.loading=!0,e.error=null})).addCase(or.fulfilled,((e,a)=>{e.loading=!1,e.leads=a.payload.data.leads,e.pagination=a.payload.data.pagination,a.payload.data.pipeline&&(e.pipeline=a.payload.data.pipeline)})).addCase(or.rejected,((e,a)=>{e.loading=!1,e.error=a.payload})),e.addCase(nr.pending,(e=>{e.leadLoading=!0,e.leadError=null})).addCase(nr.fulfilled,((e,a)=>{e.leadLoading=!1,e.currentLead=a.payload.data.lead})).addCase(nr.rejected,((e,a)=>{e.leadLoading=!1,e.leadError=a.payload})),e.addCase(ir.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(ir.fulfilled,((e,a)=>{e.actionLoading=!1,e.leads.unshift(a.payload.data.lead)})).addCase(ir.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(dr.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(dr.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload.data.lead,s=e.leads.findIndex((e=>e.id===r.id));-1!==s&&(e.leads[s]=r),(null==(t=e.currentLead)?void 0:t.id)===r.id&&(e.currentLead=r)})).addCase(dr.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(lr.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(lr.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload;e.leads=e.leads.filter((e=>e.id!==r)),e.selectedLeads=e.selectedLeads.filter((e=>e!==r)),(null==(t=e.currentLead)?void 0:t.id)===r&&(e.currentLead=null)})).addCase(lr.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(cr.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(cr.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload.data.lead,s=e.leads.findIndex((e=>e.id===r.id));-1!==s&&(e.leads[s]=r),(null==(t=e.currentLead)?void 0:t.id)===r.id&&(e.currentLead=r)})).addCase(cr.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(ur.fulfilled,((e,a)=>{var t;const r=a.payload.data.lead,s=e.leads.findIndex((e=>e.id===r.id));-1!==s&&(e.leads[s]=r),(null==(t=e.currentLead)?void 0:t.id)===r.id&&(e.currentLead=r)})),e.addCase(pr.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(pr.fulfilled,((e,a)=>{e.statsLoading=!1,e.stats=a.payload.data,a.payload.data.pipeline&&(e.pipeline=a.payload.data.pipeline)})).addCase(pr.rejected,((e,a)=>{e.statsLoading=!1,e.statsError=a.payload}))}}),{selectLead:mr,deselectLead:fr,selectAllLeads:hr,deselectAllLeads:vr,setFilter:Er,setFilters:Cr,clearFilters:Sr,setSortBy:Lr,setSortOrder:br,toggleSortOrder:wr,setSearchQuery:jr,clearSearchQuery:_r,clearCurrentLead:xr,updatePipeline:Ar,clearError:Or,clearLeadError:Pr,clearStatsError:kr,clearActionError:Ir,resetLeadState:Vr}=gr.actions,Wr=gr.reducer,Rr=l("amc/fetchAMCs",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/amcs",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Nr=l("amc/fetchAMC",(async(e,{rejectWithValue:a})=>{var t;try{return(await Je(`/amcs/${e}`)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Tr=l("amc/createAMC",(async(e,{rejectWithValue:a})=>{var t;try{return(await Ke("/amcs",e)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Mr=l("amc/updateAMC",(async({id:e,data:a},{rejectWithValue:t})=>{var r;try{return(await Ze(`/amcs/${e}`,a)).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),Br=l("amc/deleteAMC",(async(e,{rejectWithValue:a})=>{var t;try{return await ea(`/amcs/${e}`),e}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Fr=l("amc/renewAMC",(async({amcId:e,renewalData:a},{rejectWithValue:t})=>{var r;try{return(await Ke(`/amcs/${e}/renew`,a)).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),Dr=l("amc/fetchAMCStats",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/amcs/stats",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Qr=l("amc/fetchExpiringAMCs",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/amcs/expiring",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),$r={amcs:[],currentAMC:null,expiringAMCs:[],stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,amcLoading:!1,expiringLoading:!1,statsLoading:!1,actionLoading:!1,error:null,amcError:null,expiringError:null,statsError:null,actionError:null,selectedAMCs:[],filters:{status:"",customer_id:"",start_date:"",end_date:"",expiry_from:"",expiry_to:""},sortBy:"created_at",sortOrder:"desc",searchQuery:""},Ur=c({name:"amc",initialState:$r,reducers:{selectAMC:(e,a)=>{const t=a.payload;e.selectedAMCs.includes(t)||e.selectedAMCs.push(t)},deselectAMC:(e,a)=>{const t=a.payload;e.selectedAMCs=e.selectedAMCs.filter((e=>e!==t))},selectAllAMCs:e=>{e.selectedAMCs=e.amcs.map((e=>e.id))},deselectAllAMCs:e=>{e.selectedAMCs=[]},setFilter:(e,a)=>{const{key:t,value:r}=a.payload;e.filters[t]=r},setFilters:(e,a)=>{e.filters={...e.filters,...a.payload}},clearFilters:e=>{e.filters=$r.filters},setSortBy:(e,a)=>{e.sortBy=a.payload},setSortOrder:(e,a)=>{e.sortOrder=a.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,a)=>{e.searchQuery=a.payload},clearSearchQuery:e=>{e.searchQuery=""},clearCurrentAMC:e=>{e.currentAMC=null,e.amcError=null},clearError:e=>{e.error=null},clearAMCError:e=>{e.amcError=null},clearExpiringError:e=>{e.expiringError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetAMCState:e=>$r},extraReducers:e=>{e.addCase(Rr.pending,(e=>{e.loading=!0,e.error=null})).addCase(Rr.fulfilled,((e,a)=>{e.loading=!1,e.amcs=a.payload.data.amcs,e.pagination=a.payload.data.pagination})).addCase(Rr.rejected,((e,a)=>{e.loading=!1,e.error=a.payload})),e.addCase(Nr.pending,(e=>{e.amcLoading=!0,e.amcError=null})).addCase(Nr.fulfilled,((e,a)=>{e.amcLoading=!1,e.currentAMC=a.payload.data.amc})).addCase(Nr.rejected,((e,a)=>{e.amcLoading=!1,e.amcError=a.payload})),e.addCase(Tr.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Tr.fulfilled,((e,a)=>{e.actionLoading=!1,e.amcs.unshift(a.payload.data.amc)})).addCase(Tr.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Mr.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Mr.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload.data.amc,s=e.amcs.findIndex((e=>e.id===r.id));-1!==s&&(e.amcs[s]=r),(null==(t=e.currentAMC)?void 0:t.id)===r.id&&(e.currentAMC=r)})).addCase(Mr.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Br.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Br.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload;e.amcs=e.amcs.filter((e=>e.id!==r)),e.selectedAMCs=e.selectedAMCs.filter((e=>e!==r)),(null==(t=e.currentAMC)?void 0:t.id)===r&&(e.currentAMC=null)})).addCase(Br.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Fr.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Fr.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload.data.amc,s=e.amcs.findIndex((e=>e.id===r.id));-1!==s&&(e.amcs[s]=r),(null==(t=e.currentAMC)?void 0:t.id)===r.id&&(e.currentAMC=r)})).addCase(Fr.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Qr.pending,(e=>{e.expiringLoading=!0,e.expiringError=null})).addCase(Qr.fulfilled,((e,a)=>{e.expiringLoading=!1,e.expiringAMCs=a.payload.data.amcs||a.payload.data.expiring_amcs})).addCase(Qr.rejected,((e,a)=>{e.expiringLoading=!1,e.expiringError=a.payload})),e.addCase(Dr.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(Dr.fulfilled,((e,a)=>{e.statsLoading=!1,e.stats=a.payload.data})).addCase(Dr.rejected,((e,a)=>{e.statsLoading=!1,e.statsError=a.payload}))}}),{selectAMC:qr,deselectAMC:zr,selectAllAMCs:Hr,deselectAllAMCs:Yr,setFilter:Gr,setFilters:Jr,clearFilters:Kr,setSortBy:Zr,setSortOrder:Xr,toggleSortOrder:es,setSearchQuery:as,clearSearchQuery:ts,clearCurrentAMC:rs,clearError:ss,clearAMCError:os,clearExpiringError:ns,clearStatsError:is,clearActionError:ds,resetAMCState:ls}=Ur.actions,cs=Ur.reducer,us=l("sales/fetchSales",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/sales",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),ps=l("sales/fetchSale",(async(e,{rejectWithValue:a})=>{var t;try{return(await Je(`/sales/${e}`)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),ys=l("sales/createSale",(async(e,{rejectWithValue:a})=>{var t;try{return(await Ke("/sales",e)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),gs=l("sales/updateSale",(async({id:e,data:a},{rejectWithValue:t})=>{var r;try{return(await Ze(`/sales/${e}`,a)).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),ms=l("sales/deleteSale",(async(e,{rejectWithValue:a})=>{var t;try{return await ea(`/sales/${e}`),e}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),fs=l("sales/fetchSalesStats",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/sales/stats",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),hs=l("sales/generateInvoice",(async(e,{rejectWithValue:a})=>{var t;try{return(await Ke(`/sales/${e}/generate-invoice`)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),vs=l("sales/addPayment",(async({saleId:e,paymentData:a},{rejectWithValue:t})=>{var r;try{return(await Ke(`/sales/${e}/payments`,a)).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),Es={sales:[],currentSale:null,stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,saleLoading:!1,statsLoading:!1,actionLoading:!1,error:null,saleError:null,statsError:null,actionError:null,selectedSales:[],filters:{status:"",payment_status:"",customer_id:"",sales_person:"",date_from:"",date_to:"",amount_from:"",amount_to:""},sortBy:"sales_date",sortOrder:"desc",searchQuery:"",cart:{items:[],customer:null,discount:0,tax_rate:0,notes:"",payment_terms:"immediate"}},Cs=c({name:"sales",initialState:Es,reducers:{selectSale:(e,a)=>{const t=a.payload;e.selectedSales.includes(t)||e.selectedSales.push(t)},deselectSale:(e,a)=>{const t=a.payload;e.selectedSales=e.selectedSales.filter((e=>e!==t))},selectAllSales:e=>{e.selectedSales=e.sales.map((e=>e.id))},deselectAllSales:e=>{e.selectedSales=[]},setFilter:(e,a)=>{const{key:t,value:r}=a.payload;e.filters[t]=r},setFilters:(e,a)=>{e.filters={...e.filters,...a.payload}},clearFilters:e=>{e.filters=Es.filters},setSortBy:(e,a)=>{e.sortBy=a.payload},setSortOrder:(e,a)=>{e.sortOrder=a.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,a)=>{e.searchQuery=a.payload},clearSearchQuery:e=>{e.searchQuery=""},addToCart:(e,a)=>{const t=a.payload,r=e.cart.items.find((e=>e.product_id===t.product_id));r?(r.quantity+=t.quantity||1,r.total_amount=r.quantity*r.unit_price):e.cart.items.push({...t,quantity:t.quantity||1,total_amount:(t.quantity||1)*t.unit_price})},removeFromCart:(e,a)=>{const t=a.payload;e.cart.items=e.cart.items.filter((e=>e.product_id!==t))},updateCartItem:(e,a)=>{const{productId:t,updates:r}=a.payload,s=e.cart.items.find((e=>e.product_id===t));s&&(Object.assign(s,r),s.total_amount=s.quantity*s.unit_price)},setCartCustomer:(e,a)=>{e.cart.customer=a.payload},setCartDiscount:(e,a)=>{e.cart.discount=a.payload},setCartTaxRate:(e,a)=>{e.cart.tax_rate=a.payload},setCartNotes:(e,a)=>{e.cart.notes=a.payload},setCartPaymentTerms:(e,a)=>{e.cart.payment_terms=a.payload},clearCart:e=>{e.cart=Es.cart},clearCurrentSale:e=>{e.currentSale=null,e.saleError=null},clearError:e=>{e.error=null},clearSaleError:e=>{e.saleError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetSalesState:e=>Es},extraReducers:e=>{e.addCase(us.pending,(e=>{e.loading=!0,e.error=null})).addCase(us.fulfilled,((e,a)=>{e.loading=!1,e.sales=a.payload.data.sales,e.pagination=a.payload.data.pagination})).addCase(us.rejected,((e,a)=>{e.loading=!1,e.error=a.payload})),e.addCase(ps.pending,(e=>{e.saleLoading=!0,e.saleError=null})).addCase(ps.fulfilled,((e,a)=>{e.saleLoading=!1,e.currentSale=a.payload.data.sale})).addCase(ps.rejected,((e,a)=>{e.saleLoading=!1,e.saleError=a.payload})),e.addCase(ys.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(ys.fulfilled,((e,a)=>{e.actionLoading=!1,e.sales.unshift(a.payload.data.sale),e.cart=Es.cart})).addCase(ys.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(gs.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(gs.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload.data.sale,s=e.sales.findIndex((e=>e.id===r.id));-1!==s&&(e.sales[s]=r),(null==(t=e.currentSale)?void 0:t.id)===r.id&&(e.currentSale=r)})).addCase(gs.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(ms.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(ms.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload;e.sales=e.sales.filter((e=>e.id!==r)),e.selectedSales=e.selectedSales.filter((e=>e!==r)),(null==(t=e.currentSale)?void 0:t.id)===r&&(e.currentSale=null)})).addCase(ms.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(fs.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(fs.fulfilled,((e,a)=>{e.statsLoading=!1,e.stats=a.payload.data})).addCase(fs.rejected,((e,a)=>{e.statsLoading=!1,e.statsError=a.payload})),e.addCase(hs.fulfilled,((e,a)=>{var t;const r=a.payload.data.sale,s=e.sales.findIndex((e=>e.id===r.id));-1!==s&&(e.sales[s]=r),(null==(t=e.currentSale)?void 0:t.id)===r.id&&(e.currentSale=r)})),e.addCase(vs.fulfilled,((e,a)=>{var t;const r=a.payload.data.sale,s=e.sales.findIndex((e=>e.id===r.id));-1!==s&&(e.sales[s]=r),(null==(t=e.currentSale)?void 0:t.id)===r.id&&(e.currentSale=r)}))}}),{selectSale:Ss,deselectSale:Ls,selectAllSales:bs,deselectAllSales:ws,setFilter:js,setFilters:_s,clearFilters:xs,setSortBy:As,setSortOrder:Os,toggleSortOrder:Ps,setSearchQuery:ks,clearSearchQuery:Is,addToCart:Vs,removeFromCart:Ws,updateCartItem:Rs,setCartCustomer:Ns,setCartDiscount:Ts,setCartTaxRate:Ms,setCartNotes:Bs,setCartPaymentTerms:Fs,clearCart:Ds,clearCurrentSale:Qs,clearError:$s,clearSaleError:Us,clearStatsError:qs,clearActionError:zs,resetSalesState:Hs}=Cs.actions,Ys=Cs.reducer,Gs=l("product/fetchProducts",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/products",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Js=l("product/fetchProduct",(async(e,{rejectWithValue:a})=>{var t;try{return(await Je(`/products/${e}`)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Ks=l("product/createProduct",(async(e,{rejectWithValue:a})=>{var t;try{return(await Ke("/products",e)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Zs=l("product/updateProduct",(async({id:e,data:a},{rejectWithValue:t})=>{var r;try{return(await Ze(`/products/${e}`,a)).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),Xs=l("product/deleteProduct",(async(e,{rejectWithValue:a})=>{var t;try{return await ea(`/products/${e}`),e}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),eo=l("product/fetchProductStats",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/products/stats",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),ao=l("product/fetchLowStockProducts",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/products/low-stock",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),to=l("product/updateStock",(async({productId:e,stockData:a},{rejectWithValue:t})=>{var r;try{return(await Ke(`/products/${e}/stock`,a)).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),ro=l("product/bulkUpdatePrices",(async({productIds:e,priceData:a},{rejectWithValue:t})=>{var r;try{return(await Ke("/products/bulk-update-prices",{product_ids:e,...a})).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),so={products:[],currentProduct:null,lowStockProducts:[],stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,productLoading:!1,lowStockLoading:!1,statsLoading:!1,actionLoading:!1,error:null,productError:null,lowStockError:null,statsError:null,actionError:null,selectedProducts:[],filters:{category_id:"",brand_id:"",status:"",stock_status:"",price_from:"",price_to:"",warehouse_id:""},sortBy:"product_name",sortOrder:"asc",searchQuery:"",viewMode:"list"},oo=c({name:"product",initialState:so,reducers:{selectProduct:(e,a)=>{const t=a.payload;e.selectedProducts.includes(t)||e.selectedProducts.push(t)},deselectProduct:(e,a)=>{const t=a.payload;e.selectedProducts=e.selectedProducts.filter((e=>e!==t))},selectAllProducts:e=>{e.selectedProducts=e.products.map((e=>e.id))},deselectAllProducts:e=>{e.selectedProducts=[]},setFilter:(e,a)=>{const{key:t,value:r}=a.payload;e.filters[t]=r},setFilters:(e,a)=>{e.filters={...e.filters,...a.payload}},clearFilters:e=>{e.filters=so.filters},setSortBy:(e,a)=>{e.sortBy=a.payload},setSortOrder:(e,a)=>{e.sortOrder=a.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,a)=>{e.searchQuery=a.payload},clearSearchQuery:e=>{e.searchQuery=""},setViewMode:(e,a)=>{e.viewMode=a.payload},toggleViewMode:e=>{e.viewMode="list"===e.viewMode?"grid":"list"},clearCurrentProduct:e=>{e.currentProduct=null,e.productError=null},clearError:e=>{e.error=null},clearProductError:e=>{e.productError=null},clearLowStockError:e=>{e.lowStockError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetProductState:e=>so},extraReducers:e=>{e.addCase(Gs.pending,(e=>{e.loading=!0,e.error=null})).addCase(Gs.fulfilled,((e,a)=>{e.loading=!1,e.products=a.payload.data.products,e.pagination=a.payload.data.pagination})).addCase(Gs.rejected,((e,a)=>{e.loading=!1,e.error=a.payload})),e.addCase(Js.pending,(e=>{e.productLoading=!0,e.productError=null})).addCase(Js.fulfilled,((e,a)=>{e.productLoading=!1,e.currentProduct=a.payload.data.product})).addCase(Js.rejected,((e,a)=>{e.productLoading=!1,e.productError=a.payload})),e.addCase(Ks.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Ks.fulfilled,((e,a)=>{e.actionLoading=!1,e.products.unshift(a.payload.data.product)})).addCase(Ks.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Zs.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Zs.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload.data.product,s=e.products.findIndex((e=>e.id===r.id));-1!==s&&(e.products[s]=r),(null==(t=e.currentProduct)?void 0:t.id)===r.id&&(e.currentProduct=r)})).addCase(Zs.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Xs.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Xs.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload;e.products=e.products.filter((e=>e.id!==r)),e.selectedProducts=e.selectedProducts.filter((e=>e!==r)),(null==(t=e.currentProduct)?void 0:t.id)===r&&(e.currentProduct=null)})).addCase(Xs.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(ao.pending,(e=>{e.lowStockLoading=!0,e.lowStockError=null})).addCase(ao.fulfilled,((e,a)=>{e.lowStockLoading=!1,e.lowStockProducts=a.payload.data.products||a.payload.data.low_stock_products})).addCase(ao.rejected,((e,a)=>{e.lowStockLoading=!1,e.lowStockError=a.payload})),e.addCase(eo.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(eo.fulfilled,((e,a)=>{e.statsLoading=!1,e.stats=a.payload.data})).addCase(eo.rejected,((e,a)=>{e.statsLoading=!1,e.statsError=a.payload})),e.addCase(to.fulfilled,((e,a)=>{var t;const r=a.payload.data.product,s=e.products.findIndex((e=>e.id===r.id));-1!==s&&(e.products[s]=r),(null==(t=e.currentProduct)?void 0:t.id)===r.id&&(e.currentProduct=r)})),e.addCase(ro.fulfilled,((e,a)=>{a.payload.data.products.forEach((a=>{const t=e.products.findIndex((e=>e.id===a.id));-1!==t&&(e.products[t]=a)}))}))}}),{selectProduct:no,deselectProduct:io,selectAllProducts:lo,deselectAllProducts:co,setFilter:uo,setFilters:po,clearFilters:yo,setSortBy:go,setSortOrder:mo,toggleSortOrder:fo,setSearchQuery:ho,clearSearchQuery:vo,setViewMode:Eo,toggleViewMode:Co,clearCurrentProduct:So,clearError:Lo,clearProductError:bo,clearLowStockError:wo,clearStatsError:jo,clearActionError:_o,resetProductState:xo}=oo.actions,Ao=oo.reducer,Oo=l("estimation/fetchEstimations",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/estimations",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Po=l("estimation/fetchEstimation",(async(e,{rejectWithValue:a})=>{var t;try{return(await Je(`/estimations/${e}`)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),ko=l("estimation/createEstimation",(async(e,{rejectWithValue:a})=>{var t;try{return(await Ke("/estimations",e)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Io=l("estimation/updateEstimation",(async({id:e,data:a},{rejectWithValue:t})=>{var r;try{return(await Ze(`/estimations/${e}`,a)).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),Vo=l("estimation/deleteEstimation",(async(e,{rejectWithValue:a})=>{var t;try{return await ea(`/estimations/${e}`),e}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Wo=l("estimation/convertToSale",(async(e,{rejectWithValue:a})=>{var t;try{return(await Ke(`/estimations/${e}/convert-to-sale`)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Ro=l("estimation/sendEstimation",(async({estimationId:e,sendData:a},{rejectWithValue:t})=>{var r;try{return(await Ke(`/estimations/${e}/send`,a)).data}catch(s){return t((null==(r=s.response)?void 0:r.data)||s.message)}})),No=l("estimation/fetchEstimationStats",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/estimations/stats",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),To={estimations:[],currentEstimation:null,stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,estimationLoading:!1,statsLoading:!1,actionLoading:!1,error:null,estimationError:null,statsError:null,actionError:null,selectedEstimations:[],filters:{status:"",customer_id:"",date_from:"",date_to:"",amount_from:"",amount_to:"",valid_until_from:"",valid_until_to:""},sortBy:"created_at",sortOrder:"desc",searchQuery:"",builder:{customer:null,items:[],discount:0,tax_rate:0,notes:"",terms_conditions:"",valid_until:null}},Mo=c({name:"estimation",initialState:To,reducers:{selectEstimation:(e,a)=>{const t=a.payload;e.selectedEstimations.includes(t)||e.selectedEstimations.push(t)},deselectEstimation:(e,a)=>{const t=a.payload;e.selectedEstimations=e.selectedEstimations.filter((e=>e!==t))},selectAllEstimations:e=>{e.selectedEstimations=e.estimations.map((e=>e.id))},deselectAllEstimations:e=>{e.selectedEstimations=[]},setFilter:(e,a)=>{const{key:t,value:r}=a.payload;e.filters[t]=r},setFilters:(e,a)=>{e.filters={...e.filters,...a.payload}},clearFilters:e=>{e.filters=To.filters},setSortBy:(e,a)=>{e.sortBy=a.payload},setSortOrder:(e,a)=>{e.sortOrder=a.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,a)=>{e.searchQuery=a.payload},clearSearchQuery:e=>{e.searchQuery=""},setBuilderCustomer:(e,a)=>{e.builder.customer=a.payload},addBuilderItem:(e,a)=>{const t=a.payload,r=e.builder.items.find((e=>e.product_id===t.product_id));r?(r.quantity+=t.quantity||1,r.total_amount=r.quantity*r.unit_price):e.builder.items.push({...t,quantity:t.quantity||1,total_amount:(t.quantity||1)*t.unit_price})},removeBuilderItem:(e,a)=>{const t=a.payload;e.builder.items=e.builder.items.filter((e=>e.product_id!==t))},updateBuilderItem:(e,a)=>{const{productId:t,updates:r}=a.payload,s=e.builder.items.find((e=>e.product_id===t));s&&(Object.assign(s,r),s.total_amount=s.quantity*s.unit_price)},setBuilderDiscount:(e,a)=>{e.builder.discount=a.payload},setBuilderTaxRate:(e,a)=>{e.builder.tax_rate=a.payload},setBuilderNotes:(e,a)=>{e.builder.notes=a.payload},setBuilderTermsConditions:(e,a)=>{e.builder.terms_conditions=a.payload},setBuilderValidUntil:(e,a)=>{e.builder.valid_until=a.payload},clearBuilder:e=>{e.builder=To.builder},clearCurrentEstimation:e=>{e.currentEstimation=null,e.estimationError=null},clearError:e=>{e.error=null},clearEstimationError:e=>{e.estimationError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetEstimationState:e=>To},extraReducers:e=>{e.addCase(Oo.pending,(e=>{e.loading=!0,e.error=null})).addCase(Oo.fulfilled,((e,a)=>{e.loading=!1,e.estimations=a.payload.data.estimations,e.pagination=a.payload.data.pagination})).addCase(Oo.rejected,((e,a)=>{e.loading=!1,e.error=a.payload})),e.addCase(Po.pending,(e=>{e.estimationLoading=!0,e.estimationError=null})).addCase(Po.fulfilled,((e,a)=>{e.estimationLoading=!1,e.currentEstimation=a.payload.data.estimation})).addCase(Po.rejected,((e,a)=>{e.estimationLoading=!1,e.estimationError=a.payload})),e.addCase(ko.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(ko.fulfilled,((e,a)=>{e.actionLoading=!1,e.estimations.unshift(a.payload.data.estimation),e.builder=To.builder})).addCase(ko.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Io.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Io.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload.data.estimation,s=e.estimations.findIndex((e=>e.id===r.id));-1!==s&&(e.estimations[s]=r),(null==(t=e.currentEstimation)?void 0:t.id)===r.id&&(e.currentEstimation=r)})).addCase(Io.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Vo.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Vo.fulfilled,((e,a)=>{var t;e.actionLoading=!1;const r=a.payload;e.estimations=e.estimations.filter((e=>e.id!==r)),e.selectedEstimations=e.selectedEstimations.filter((e=>e!==r)),(null==(t=e.currentEstimation)?void 0:t.id)===r&&(e.currentEstimation=null)})).addCase(Vo.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Wo.fulfilled,((e,a)=>{var t;const r=a.payload.data.estimation,s=e.estimations.findIndex((e=>e.id===r.id));-1!==s&&(e.estimations[s]=r),(null==(t=e.currentEstimation)?void 0:t.id)===r.id&&(e.currentEstimation=r)})),e.addCase(Ro.fulfilled,((e,a)=>{var t;const r=a.payload.data.estimation,s=e.estimations.findIndex((e=>e.id===r.id));-1!==s&&(e.estimations[s]=r),(null==(t=e.currentEstimation)?void 0:t.id)===r.id&&(e.currentEstimation=r)})),e.addCase(No.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(No.fulfilled,((e,a)=>{e.statsLoading=!1,e.stats=a.payload.data})).addCase(No.rejected,((e,a)=>{e.statsLoading=!1,e.statsError=a.payload}))}}),{selectEstimation:Bo,deselectEstimation:Fo,selectAllEstimations:Do,deselectAllEstimations:Qo,setFilter:$o,setFilters:Uo,clearFilters:qo,setSortBy:zo,setSortOrder:Ho,toggleSortOrder:Yo,setSearchQuery:Go,clearSearchQuery:Jo,setBuilderCustomer:Ko,addBuilderItem:Zo,removeBuilderItem:Xo,updateBuilderItem:en,setBuilderDiscount:an,setBuilderTaxRate:tn,setBuilderNotes:rn,setBuilderTermsConditions:sn,setBuilderValidUntil:on,clearBuilder:nn,clearCurrentEstimation:dn,clearError:ln,clearEstimationError:cn,clearStatsError:un,clearActionError:pn,resetEstimationState:yn}=Mo.actions,gn=Mo.reducer,mn=l("dashboard/fetchDashboardOverview",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/dashboard/overview",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),fn=l("dashboard/fetchRecentActivities",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/dashboard/activities",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),hn=l("dashboard/fetchSalesDashboard",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/dashboard/sales",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),vn=l("dashboard/fetchFinancialDashboard",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/dashboard/financial",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),En=l("dashboard/fetchInventoryDashboard",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/dashboard/inventory",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Cn=l("dashboard/fetchTopCustomers",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/dashboard/top-customers",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Sn=l("dashboard/fetchPaymentDashboard",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/dashboard/payments",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Ln=l("dashboard/fetchServiceStatusDistribution",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/dashboard/status-distribution",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),bn=l("dashboard/fetchServiceTrends",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/dashboard/trends",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),wn={overview:null,recentActivities:[],salesData:null,financialData:null,inventoryData:null,topCustomers:[],paymentData:null,serviceStatusDistribution:null,serviceTrends:null,overviewLoading:!1,activitiesLoading:!1,salesLoading:!1,financialLoading:!1,inventoryLoading:!1,customersLoading:!1,paymentsLoading:!1,statusLoading:!1,trendsLoading:!1,overviewError:null,activitiesError:null,salesError:null,financialError:null,inventoryError:null,customersError:null,paymentsError:null,statusError:null,trendsError:null,selectedDateRange:{start:null,end:null},refreshInterval:3e5,autoRefresh:!0,widgetLayout:[{id:"overview",enabled:!0,order:1},{id:"sales",enabled:!0,order:2},{id:"financial",enabled:!0,order:3},{id:"inventory",enabled:!0,order:4},{id:"customers",enabled:!0,order:5},{id:"payments",enabled:!0,order:6},{id:"activities",enabled:!0,order:7},{id:"status",enabled:!0,order:8},{id:"trends",enabled:!0,order:9}],lastRefresh:null},jn=c({name:"dashboard",initialState:wn,reducers:{setDateRange:(e,a)=>{e.selectedDateRange=a.payload},clearDateRange:e=>{e.selectedDateRange={start:null,end:null}},setAutoRefresh:(e,a)=>{e.autoRefresh=a.payload},setRefreshInterval:(e,a)=>{e.refreshInterval=a.payload},updateLastRefresh:e=>{e.lastRefresh=(new Date).toISOString()},updateWidgetLayout:(e,a)=>{e.widgetLayout=a.payload},toggleWidget:(e,a)=>{const t=a.payload,r=e.widgetLayout.find((e=>e.id===t));r&&(r.enabled=!r.enabled)},reorderWidgets:(e,a)=>{const{sourceIndex:t,destinationIndex:r}=a.payload,[s]=e.widgetLayout.splice(t,1);e.widgetLayout.splice(r,0,s),e.widgetLayout.forEach(((e,a)=>{e.order=a+1}))},clearOverviewError:e=>{e.overviewError=null},clearActivitiesError:e=>{e.activitiesError=null},clearSalesError:e=>{e.salesError=null},clearFinancialError:e=>{e.financialError=null},clearInventoryError:e=>{e.inventoryError=null},clearCustomersError:e=>{e.customersError=null},clearPaymentsError:e=>{e.paymentsError=null},clearStatusError:e=>{e.statusError=null},clearTrendsError:e=>{e.trendsError=null},clearAllErrors:e=>{e.overviewError=null,e.activitiesError=null,e.salesError=null,e.financialError=null,e.inventoryError=null,e.customersError=null,e.paymentsError=null,e.statusError=null,e.trendsError=null},resetDashboardState:e=>({...wn,widgetLayout:e.widgetLayout,autoRefresh:e.autoRefresh,refreshInterval:e.refreshInterval})},extraReducers:e=>{e.addCase(mn.pending,(e=>{e.overviewLoading=!0,e.overviewError=null})).addCase(mn.fulfilled,((e,a)=>{e.overviewLoading=!1,e.overview=a.payload.data,e.lastRefresh=(new Date).toISOString()})).addCase(mn.rejected,((e,a)=>{e.overviewLoading=!1,e.overviewError=a.payload})),e.addCase(fn.pending,(e=>{e.activitiesLoading=!0,e.activitiesError=null})).addCase(fn.fulfilled,((e,a)=>{e.activitiesLoading=!1,e.recentActivities=a.payload.data.activities||a.payload.data.recent_services||[]})).addCase(fn.rejected,((e,a)=>{e.activitiesLoading=!1,e.activitiesError=a.payload})),e.addCase(hn.pending,(e=>{e.salesLoading=!0,e.salesError=null})).addCase(hn.fulfilled,((e,a)=>{e.salesLoading=!1,e.salesData=a.payload.data})).addCase(hn.rejected,((e,a)=>{e.salesLoading=!1,e.salesError=a.payload})),e.addCase(vn.pending,(e=>{e.financialLoading=!0,e.financialError=null})).addCase(vn.fulfilled,((e,a)=>{e.financialLoading=!1,e.financialData=a.payload.data})).addCase(vn.rejected,((e,a)=>{e.financialLoading=!1,e.financialError=a.payload})),e.addCase(En.pending,(e=>{e.inventoryLoading=!0,e.inventoryError=null})).addCase(En.fulfilled,((e,a)=>{e.inventoryLoading=!1,e.inventoryData=a.payload.data})).addCase(En.rejected,((e,a)=>{e.inventoryLoading=!1,e.inventoryError=a.payload})),e.addCase(Cn.pending,(e=>{e.customersLoading=!0,e.customersError=null})).addCase(Cn.fulfilled,((e,a)=>{e.customersLoading=!1,e.topCustomers=a.payload.data.top_customers||[]})).addCase(Cn.rejected,((e,a)=>{e.customersLoading=!1,e.customersError=a.payload})),e.addCase(Sn.pending,(e=>{e.paymentsLoading=!0,e.paymentsError=null})).addCase(Sn.fulfilled,((e,a)=>{e.paymentsLoading=!1,e.paymentData=a.payload.data})).addCase(Sn.rejected,((e,a)=>{e.paymentsLoading=!1,e.paymentsError=a.payload})),e.addCase(Ln.pending,(e=>{e.statusLoading=!0,e.statusError=null})).addCase(Ln.fulfilled,((e,a)=>{e.statusLoading=!1,e.serviceStatusDistribution=a.payload.data})).addCase(Ln.rejected,((e,a)=>{e.statusLoading=!1,e.statusError=a.payload})),e.addCase(bn.pending,(e=>{e.trendsLoading=!0,e.trendsError=null})).addCase(bn.fulfilled,((e,a)=>{e.trendsLoading=!1,e.serviceTrends=a.payload.data})).addCase(bn.rejected,((e,a)=>{e.trendsLoading=!1,e.trendsError=a.payload}))}}),{setDateRange:_n,clearDateRange:xn,setAutoRefresh:An,setRefreshInterval:On,updateLastRefresh:Pn,updateWidgetLayout:kn,toggleWidget:In,reorderWidgets:Vn,clearOverviewError:Wn,clearActivitiesError:Rn,clearSalesError:Nn,clearFinancialError:Tn,clearInventoryError:Mn,clearCustomersError:Bn,clearPaymentsError:Fn,clearStatusError:Dn,clearTrendsError:Qn,clearAllErrors:$n,resetDashboardState:Un}=jn.actions,qn=jn.reducer,zn=l("notification/fetchNotifications",(async(e={},{rejectWithValue:a})=>{var t;try{return(await Je("/notifications",{params:e})).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Hn=l("notification/markAsRead",(async(e,{rejectWithValue:a})=>{var t;try{return(await Xe(`/notifications/${e}/read`)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Yn=l("notification/markAllAsRead",(async(e,{rejectWithValue:a})=>{var t;try{return(await Xe("/notifications/mark-all-read")).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Gn=l("notification/deleteNotification",(async(e,{rejectWithValue:a})=>{var t;try{return await ea(`/notifications/${e}`),e}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Jn=l("notification/fetchNotificationSettings",(async(e,{rejectWithValue:a})=>{var t;try{return(await Je("/notifications/settings")).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Kn=l("notification/updateNotificationSettings",(async(e,{rejectWithValue:a})=>{var t;try{return(await Ze("/notifications/settings",e)).data}catch(r){return a((null==(t=r.response)?void 0:t.data)||r.message)}})),Zn={notifications:[],settings:{email_notifications:!0,push_notifications:!0,sms_notifications:!1,desktop_notifications:!0,notification_types:{service_updates:!0,payment_reminders:!0,system_alerts:!0,marketing:!1}},pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},unreadCount:0,totalCount:0,loading:!1,settingsLoading:!1,actionLoading:!1,error:null,settingsError:null,actionError:null,showNotificationPanel:!1,filters:{type:"",read_status:"",date_from:"",date_to:""},sortBy:"created_at",sortOrder:"desc"},Xn=c({name:"notification",initialState:Zn,reducers:{toggleNotificationPanel:e=>{e.showNotificationPanel=!e.showNotificationPanel},setNotificationPanel:(e,a)=>{e.showNotificationPanel=a.payload},setFilter:(e,a)=>{const{key:t,value:r}=a.payload;e.filters[t]=r},setFilters:(e,a)=>{e.filters={...e.filters,...a.payload}},clearFilters:e=>{e.filters=Zn.filters},setSortBy:(e,a)=>{e.sortBy=a.payload},setSortOrder:(e,a)=>{e.sortOrder=a.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},addNotification:(e,a)=>{const t={id:Date.now(),...a.payload,created_at:(new Date).toISOString(),is_read:!1};e.notifications.unshift(t),e.unreadCount+=1,e.totalCount+=1},removeNotification:(e,a)=>{const t=a.payload,r=e.notifications.find((e=>e.id===t));r&&!r.is_read&&(e.unreadCount-=1),e.notifications=e.notifications.filter((e=>e.id!==t)),e.totalCount-=1},markNotificationAsRead:(e,a)=>{const t=a.payload,r=e.notifications.find((e=>e.id===t));r&&!r.is_read&&(r.is_read=!0,e.unreadCount-=1)},updateLocalSettings:(e,a)=>{e.settings={...e.settings,...a.payload}},clearError:e=>{e.error=null},clearSettingsError:e=>{e.settingsError=null},clearActionError:e=>{e.actionError=null},resetNotificationState:e=>Zn},extraReducers:e=>{e.addCase(zn.pending,(e=>{e.loading=!0,e.error=null})).addCase(zn.fulfilled,((e,a)=>{e.loading=!1,e.notifications=a.payload.data.notifications,e.pagination=a.payload.data.pagination,e.unreadCount=a.payload.data.unread_count||0,e.totalCount=a.payload.data.total_count||0})).addCase(zn.rejected,((e,a)=>{e.loading=!1,e.error=a.payload})),e.addCase(Hn.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Hn.fulfilled,((e,a)=>{e.actionLoading=!1;const t=a.meta.arg,r=e.notifications.find((e=>e.id===t));r&&!r.is_read&&(r.is_read=!0,e.unreadCount-=1)})).addCase(Hn.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Yn.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Yn.fulfilled,(e=>{e.actionLoading=!1,e.notifications.forEach((e=>{e.is_read=!0})),e.unreadCount=0})).addCase(Yn.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Gn.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Gn.fulfilled,((e,a)=>{e.actionLoading=!1;const t=a.payload,r=e.notifications.find((e=>e.id===t));r&&!r.is_read&&(e.unreadCount-=1),e.notifications=e.notifications.filter((e=>e.id!==t)),e.totalCount-=1})).addCase(Gn.rejected,((e,a)=>{e.actionLoading=!1,e.actionError=a.payload})),e.addCase(Jn.pending,(e=>{e.settingsLoading=!0,e.settingsError=null})).addCase(Jn.fulfilled,((e,a)=>{e.settingsLoading=!1,e.settings=a.payload.data.settings})).addCase(Jn.rejected,((e,a)=>{e.settingsLoading=!1,e.settingsError=a.payload})),e.addCase(Kn.pending,(e=>{e.settingsLoading=!0,e.settingsError=null})).addCase(Kn.fulfilled,((e,a)=>{e.settingsLoading=!1,e.settings=a.payload.data.settings})).addCase(Kn.rejected,((e,a)=>{e.settingsLoading=!1,e.settingsError=a.payload}))}}),{toggleNotificationPanel:ei,setNotificationPanel:ai,setFilter:ti,setFilters:ri,clearFilters:si,setSortBy:oi,setSortOrder:ni,toggleSortOrder:ii,addNotification:di,removeNotification:li,markNotificationAsRead:ci,updateLocalSettings:ui,clearError:pi,clearSettingsError:yi,clearActionError:gi,resetNotificationState:mi}=Xn.actions,fi=Xn.reducer,hi={key:"root",storage:re,whitelist:["auth","ui"],blacklist:["service","customer","lead","amc","sales","product","estimation","dashboard","notification"]},vi=p({auth:$({key:"auth",storage:re,whitelist:["user","token","isAuthenticated"]},ha),ui:Za,service:xt,customer:sr,lead:Wr,amc:cs,sales:Ys,product:Ao,estimation:gn,dashboard:qn,notification:fi}),Ei=u({reducer:$(hi,vi),middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE"],ignoredPaths:["register"]}}),devTools:!1});var Ci,Si,Li,bi;Ci=Ei,Si=d(G,Y,void 0),Li=function(e){Si.dispatch({type:O,key:e})},bi=function(e,a,t){var r={type:j,payload:a,err:t,key:e};Ci.dispatch(r),Si.dispatch(r)},z({},Si,{purge:function(){var e=[];return Ci.dispatch({type:A,result:function(a){e.push(a)}}),Promise.all(e)},flush:function(){var e=[];return Ci.dispatch({type:w,result:function(a){e.push(a)}}),Promise.all(e)},pause:function(){Ci.dispatch({type:_})},persist:function(){Ci.dispatch({type:x,register:Li,rehydrate:bi})}}).persist();const wi=()=>C.jsx("div",{className:"min-h-screen bg-gray-100 p-8",children:C.jsxs("div",{className:"max-w-4xl mx-auto",children:[C.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-8",children:"🎉 Track New Application is Working!"}),C.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[C.jsx("h2",{className:"text-2xl font-semibold text-gray-800 mb-4",children:"✅ System Status"}),C.jsxs("div",{className:"space-y-2",children:[C.jsxs("div",{className:"flex items-center",children:[C.jsx("span",{className:"w-3 h-3 bg-green-500 rounded-full mr-3"}),C.jsx("span",{children:"Frontend: React.js application loaded successfully"})]}),C.jsxs("div",{className:"flex items-center",children:[C.jsx("span",{className:"w-3 h-3 bg-green-500 rounded-full mr-3"}),C.jsx("span",{children:"Backend: Node.js server running on port 8000"})]}),C.jsxs("div",{className:"flex items-center",children:[C.jsx("span",{className:"w-3 h-3 bg-green-500 rounded-full mr-3"}),C.jsx("span",{children:"Routing: React Router working correctly"})]}),C.jsxs("div",{className:"flex items-center",children:[C.jsx("span",{className:"w-3 h-3 bg-green-500 rounded-full mr-3"}),C.jsx("span",{children:"Styling: Tailwind CSS loaded and working"})]})]})]}),C.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[C.jsx("h3",{className:"text-lg font-semibold text-blue-800 mb-2",children:"🚀 Next Steps"}),C.jsx("p",{className:"text-blue-700",children:"The application is now running successfully! You can now proceed to test the full application features."})]}),C.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[C.jsxs("div",{className:"bg-white rounded-lg shadow p-4",children:[C.jsx("h4",{className:"font-semibold text-gray-800",children:"Dashboard"}),C.jsx("p",{className:"text-gray-600 text-sm",children:"Overview and analytics"})]}),C.jsxs("div",{className:"bg-white rounded-lg shadow p-4",children:[C.jsx("h4",{className:"font-semibold text-gray-800",children:"Customers"}),C.jsx("p",{className:"text-gray-600 text-sm",children:"Customer management"})]}),C.jsxs("div",{className:"bg-white rounded-lg shadow p-4",children:[C.jsx("h4",{className:"font-semibold text-gray-800",children:"Services"}),C.jsx("p",{className:"text-gray-600 text-sm",children:"Service tracking"})]}),C.jsxs("div",{className:"bg-white rounded-lg shadow p-4",children:[C.jsx("h4",{className:"font-semibold text-gray-800",children:"Products"}),C.jsx("p",{className:"text-gray-600 text-sm",children:"Inventory management"})]}),C.jsxs("div",{className:"bg-white rounded-lg shadow p-4",children:[C.jsx("h4",{className:"font-semibold text-gray-800",children:"Sales"}),C.jsx("p",{className:"text-gray-600 text-sm",children:"Sales processing"})]}),C.jsxs("div",{className:"bg-white rounded-lg shadow p-4",children:[C.jsx("h4",{className:"font-semibold text-gray-800",children:"Reports"}),C.jsx("p",{className:"text-gray-600 text-sm",children:"Analytics and reports"})]})]})]})}),ji=()=>C.jsx("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:C.jsxs("div",{className:"bg-white p-8 rounded-lg shadow-md",children:[C.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Test Login Page"}),C.jsx("p",{children:"This is a test login page to verify routing is working."})]})});function _i(){return C.jsxs(s,{children:[C.jsx(o,{path:"/",element:C.jsx(wi,{})}),C.jsx(o,{path:"/dashboard",element:C.jsx(wi,{})}),C.jsx(o,{path:"/login",element:C.jsx(ji,{})}),C.jsx(o,{path:"*",element:C.jsx(wi,{})})]})}L.createRoot(document.getElementById("root")).render(C.jsx(n.StrictMode,{children:C.jsx(y,{store:Ei,children:C.jsx(i,{children:C.jsx(_i,{})})})}));
