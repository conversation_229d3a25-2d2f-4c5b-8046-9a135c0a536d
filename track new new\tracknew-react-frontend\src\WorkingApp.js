import React, { useState } from 'react';

// Simple working React application
function WorkingApp() {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [user, setUser] = useState(null);

  // Simple login function
  const handleLogin = (email, password) => {
    if (email && password) {
      setUser({ name: 'Demo User', email: email });
      setCurrentPage('dashboard');
    }
  };

  // Simple logout function
  const handleLogout = () => {
    setUser(null);
    setCurrentPage('login');
  };

  // Login Page Component
  const LoginPage = () => (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Track New</h1>
          <p className="text-gray-600 mt-2">Service Management System</p>
        </div>
        
        <form onSubmit={(e) => {
          e.preventDefault();
          const formData = new FormData(e.target);
          handleLogin(formData.get('email'), formData.get('password'));
        }}>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Email
            </label>
            <input
              type="email"
              name="email"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your email"
              defaultValue="<EMAIL>"
            />
          </div>
          
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Password
            </label>
            <input
              type="password"
              name="password"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your password"
              defaultValue="password123"
            />
          </div>
          
          <button
            type="submit"
            className="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          >
            Sign In
          </button>
        </form>
        
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Demo credentials are pre-filled. Just click "Sign In"
          </p>
        </div>
      </div>
    </div>
  );

  // Dashboard Page Component
  const DashboardPage = () => (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Track New</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">Welcome, {user?.name}</span>
              <button
                onClick={handleLogout}
                className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <nav className="w-64 bg-white shadow-sm min-h-screen">
          <div className="p-4">
            <ul className="space-y-2">
              {[
                { id: 'dashboard', name: 'Dashboard', icon: '📊' },
                { id: 'customers', name: 'Customers', icon: '👥' },
                { id: 'services', name: 'Services', icon: '🛠️' },
                { id: 'products', name: 'Products', icon: '📦' },
                { id: 'sales', name: 'Sales', icon: '💰' },
                { id: 'reports', name: 'Reports', icon: '📈' }
              ].map((item) => (
                <li key={item.id}>
                  <button
                    onClick={() => setCurrentPage(item.id)}
                    className={`w-full text-left px-4 py-2 rounded-md flex items-center space-x-3 ${
                      currentPage === item.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <span>{item.icon}</span>
                    <span>{item.name}</span>
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-8">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">
              {currentPage.charAt(0).toUpperCase() + currentPage.slice(1)}
            </h2>
            
            {currentPage === 'dashboard' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {[
                  { title: 'Total Customers', value: '150', color: 'bg-blue-500' },
                  { title: 'Active Services', value: '45', color: 'bg-green-500' },
                  { title: 'Monthly Revenue', value: '$25,000', color: 'bg-purple-500' },
                  { title: 'Pending Tasks', value: '12', color: 'bg-orange-500' }
                ].map((stat, index) => (
                  <div key={index} className="bg-white rounded-lg shadow p-6">
                    <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center mb-4`}>
                      <span className="text-white text-xl">📊</span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{stat.title}</h3>
                    <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
                  </div>
                ))}
              </div>
            )}
            
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                {currentPage === 'dashboard' ? 'Recent Activity' : `${currentPage.charAt(0).toUpperCase() + currentPage.slice(1)} Management`}
              </h3>
              <div className="space-y-4">
                {[1, 2, 3].map((item) => (
                  <div key={item} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {currentPage === 'dashboard' ? `Activity ${item}` : `${currentPage.slice(0, -1)} ${item}`}
                      </h4>
                      <p className="text-gray-600">Sample description for item {item}</p>
                    </div>
                    <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                      View
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );

  // Render the appropriate page
  if (!user) {
    return <LoginPage />;
  }

  return <DashboardPage />;
}

export default WorkingApp;
