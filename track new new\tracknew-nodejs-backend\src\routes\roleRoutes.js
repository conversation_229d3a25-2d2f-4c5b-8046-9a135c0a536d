const express = require('express');
const { body } = require('express-validator');
const roleController = require('../controllers/roleController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating role
const createRoleValidation = [
  body('name')
    .notEmpty()
    .withMessage('Role name is required')
    .isLength({ min: 2, max: 255 })
    .withMessage('Role name must be between 2 and 255 characters'),
  
  body('display_name')
    .notEmpty()
    .withMessage('Display name is required')
    .isLength({ min: 2, max: 255 })
    .withMessage('Display name must be between 2 and 255 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('guard_name')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Guard name must be maximum 255 characters'),
  
  body('role_type')
    .optional()
    .isIn(['system', 'company', 'custom'])
    .withMessage('Invalid role type'),
  
  body('level')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Level must be between 1 and 100'),
  
  body('is_admin')
    .optional()
    .isBoolean()
    .withMessage('is_admin must be a boolean'),
  
  body('is_super_admin')
    .optional()
    .isBoolean()
    .withMessage('is_super_admin must be a boolean'),
  
  body('is_default')
    .optional()
    .isBoolean()
    .withMessage('is_default must be a boolean'),
  
  body('is_system_role')
    .optional()
    .isBoolean()
    .withMessage('is_system_role must be a boolean'),
  
  body('inherits_from')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Inherits from must be a positive integer'),
  
  body('landing_page')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Landing page must be maximum 255 characters'),
  
  body('menu_access')
    .optional()
    .isArray()
    .withMessage('Menu access must be an array'),
  
  body('feature_access')
    .optional()
    .isObject()
    .withMessage('Feature access must be a valid JSON object'),
  
  body('data_access_level')
    .optional()
    .isIn(['none', 'own', 'team', 'department', 'company', 'all'])
    .withMessage('Invalid data access level'),
  
  body('can_view_reports')
    .optional()
    .isBoolean()
    .withMessage('can_view_reports must be a boolean'),
  
  body('can_export_data')
    .optional()
    .isBoolean()
    .withMessage('can_export_data must be a boolean'),
  
  body('can_import_data')
    .optional()
    .isBoolean()
    .withMessage('can_import_data must be a boolean'),
  
  body('can_manage_users')
    .optional()
    .isBoolean()
    .withMessage('can_manage_users must be a boolean'),
  
  body('can_manage_roles')
    .optional()
    .isBoolean()
    .withMessage('can_manage_roles must be a boolean'),
  
  body('can_manage_settings')
    .optional()
    .isBoolean()
    .withMessage('can_manage_settings must be a boolean'),
  
  body('can_access_api')
    .optional()
    .isBoolean()
    .withMessage('can_access_api must be a boolean'),
  
  body('api_rate_limit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('API rate limit must be a positive integer'),
  
  body('session_timeout')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Session timeout must be a positive integer'),
  
  body('ip_restrictions')
    .optional()
    .isArray()
    .withMessage('IP restrictions must be an array'),
  
  body('time_restrictions')
    .optional()
    .isObject()
    .withMessage('Time restrictions must be a valid JSON object'),
  
  body('two_factor_required')
    .optional()
    .isBoolean()
    .withMessage('two_factor_required must be a boolean'),
  
  body('password_policy')
    .optional()
    .isObject()
    .withMessage('Password policy must be a valid JSON object'),
  
  body('notification_settings')
    .optional()
    .isObject()
    .withMessage('Notification settings must be a valid JSON object'),
  
  body('workflow_permissions')
    .optional()
    .isObject()
    .withMessage('Workflow permissions must be a valid JSON object'),
  
  body('approval_limits')
    .optional()
    .isObject()
    .withMessage('Approval limits must be a valid JSON object'),
  
  body('custom_permissions')
    .optional()
    .isObject()
    .withMessage('Custom permissions must be a valid JSON object'),
  
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  
  body('permission_ids')
    .optional()
    .isArray()
    .withMessage('Permission IDs must be an array'),
  
  body('permission_ids.*')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Each permission ID must be a positive integer')
];

// Validation rules for updating role
const updateRoleValidation = [
  body('name')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Role name must be between 2 and 255 characters'),
  
  body('display_name')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Display name must be between 2 and 255 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('role_type')
    .optional()
    .isIn(['system', 'company', 'custom'])
    .withMessage('Invalid role type'),
  
  body('level')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Level must be between 1 and 100'),
  
  body('is_admin')
    .optional()
    .isBoolean()
    .withMessage('is_admin must be a boolean'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  
  body('permission_ids')
    .optional()
    .isArray()
    .withMessage('Permission IDs must be an array')
];

// Validation for role assignment
const assignRoleValidation = [
  body('role_id')
    .notEmpty()
    .withMessage('Role ID is required')
    .isInt({ min: 1 })
    .withMessage('Role ID must be a positive integer'),
  
  body('user_id')
    .notEmpty()
    .withMessage('User ID is required')
    .isInt({ min: 1 })
    .withMessage('User ID must be a positive integer')
];

// Routes
router
  .route('/')
  .get(roleController.getRoles)
  .post(createRoleValidation, validateRequest, restrictTo('admin', 'sub_admin'), roleController.createRole);

router
  .route('/stats')
  .get(roleController.getRoleStats);

router
  .route('/active')
  .get(roleController.getActiveRoles);

router
  .route('/by-type/:role_type')
  .get(roleController.getRolesByType);

router
  .route('/assign')
  .post(assignRoleValidation, validateRequest, restrictTo('admin', 'sub_admin'), roleController.assignRoleToUser);

router
  .route('/remove')
  .post(assignRoleValidation, validateRequest, restrictTo('admin', 'sub_admin'), roleController.removeRoleFromUser);

router
  .route('/:id')
  .get(roleController.getRole)
  .put(updateRoleValidation, validateRequest, restrictTo('admin', 'sub_admin'), roleController.updateRole)
  .delete(restrictTo('admin'), roleController.deleteRole);

module.exports = router;
