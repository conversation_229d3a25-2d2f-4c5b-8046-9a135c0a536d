# TrackNew React Frontend

A modern, responsive React frontend for the TrackNew Service Management System.

## 🚀 Features

### ✅ **Complete Business Logic**
- **Dashboard** - Comprehensive business intelligence with widgets
- **Customer Management** - Full CRM with advanced filtering
- **Service Management** - Complete workflow tracking
- **Product Management** - Inventory control with stock alerts
- **Sales Management** - Transaction processing and invoicing
- **Authentication** - Secure login with role-based access

### 🎨 **Modern UI/UX**
- **Responsive Design** - Mobile-first approach
- **Dark Mode Support** - Toggle between light and dark themes
- **Professional Components** - 20+ reusable UI components
- **Accessibility** - WCAG compliant with ARIA labels
- **Loading States** - Professional loading indicators
- **Error Handling** - Comprehensive error boundaries

### 🛠 **Technical Stack**
- **React 18** - Latest React with concurrent features
- **TypeScript** - Type-safe development
- **Redux Toolkit** - State management with RTK Query
- **React Router v6** - Client-side routing
- **Tailwind CSS** - Utility-first CSS framework
- **Headless UI** - Unstyled, accessible UI components
- **Heroicons** - Beautiful hand-crafted SVG icons

## 📦 Installation

### Prerequisites
- Node.js 16+ 
- npm or yarn

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd tracknew-react-frontend

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Start development server
npm start
```

## 🔧 Configuration

### Environment Variables
Copy `.env.example` to `.env` and configure:

```env
# API Configuration
REACT_APP_API_URL=http://localhost:3001/api

# Application Settings
REACT_APP_NAME=TrackNew
REACT_APP_VERSION=1.0.0

# Features
REACT_APP_ENABLE_REGISTRATION=true
REACT_APP_ENABLE_DARK_MODE=true
```

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── auth/            # Authentication components
│   ├── dashboard/       # Dashboard widgets
│   ├── forms/           # Form components
│   ├── layout/          # Layout components
│   ├── notifications/   # Notification components
│   └── ui/              # Base UI components
├── pages/               # Page components
│   ├── auth/            # Authentication pages
│   ├── Dashboard.jsx    # Main dashboard
│   ├── Customers.jsx    # Customer management
│   ├── Services.jsx     # Service management
│   ├── Products.jsx     # Product management
│   └── Sales.jsx        # Sales management
├── store/               # Redux store
│   ├── slices/          # Redux slices
│   └── store.js         # Store configuration
├── services/            # API services
├── utils/               # Utility functions
└── App.js               # Main app component
```

## 🎯 Available Scripts

```bash
# Development
npm start              # Start development server
npm run build          # Build for production
npm test               # Run tests
npm run eject          # Eject from Create React App

# Code Quality
npm run lint           # Run ESLint
npm run lint:fix       # Fix ESLint issues
npm run format         # Format with Prettier
```

## 🔐 Authentication

The app includes a complete authentication system:

### Demo Credentials
- **Email**: <EMAIL>
- **Password**: admin123

### Features
- JWT-based authentication
- Role-based access control
- Protected routes
- Automatic token refresh
- Remember me functionality

## 📱 Components

### Form Components
- **Input** - Text input with validation and icons
- **Select** - Dropdown with search and multi-select
- **Textarea** - Auto-resizing text area
- **Checkbox** - Custom styled checkboxes
- **DatePicker** - Date/time picker with formatting

### UI Components
- **Button** - Multiple variants and sizes
- **Modal** - Confirmation and alert modals
- **DataTable** - Advanced table with sorting/filtering
- **Pagination** - Complete pagination controls
- **LoadingSpinner** - Configurable loading states

### Layout Components
- **Header** - Navigation with search and user menu
- **Sidebar** - Collapsible navigation menu
- **Layout** - Main layout wrapper

## 🎨 Styling

### Tailwind CSS
The project uses Tailwind CSS for styling:
- Utility-first approach
- Responsive design
- Dark mode support
- Custom color palette

### Theme Configuration
```javascript
// tailwind.config.js
module.exports = {
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {...},
        secondary: {...}
      }
    }
  }
}
```

## 🔄 State Management

### Redux Toolkit
- **11 Business Logic Slices**
- Async thunks for API calls
- Normalized state structure
- Persistent storage

### Key Slices
- `authSlice` - Authentication state
- `customerSlice` - Customer management
- `serviceSlice` - Service workflow
- `productSlice` - Inventory management
- `salesSlice` - Sales transactions
- `dashboardSlice` - Dashboard data
- `uiSlice` - UI preferences

## 🌐 API Integration

### HTTP Client
- Axios with interceptors
- Automatic token attachment
- Request/response transformation
- Error handling

### API Services
```javascript
// Example API call
import { customerAPI } from '../services/api';

const customers = await customerAPI.getCustomers({
  page: 1,
  limit: 15,
  search: 'john'
});
```

## 🧪 Testing

### Test Structure
```bash
src/
├── __tests__/           # Test files
├── components/
│   └── __tests__/       # Component tests
└── utils/
    └── __tests__/       # Utility tests
```

### Running Tests
```bash
npm test                 # Run all tests
npm test -- --watch     # Watch mode
npm test -- --coverage  # Coverage report
```

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Environment-specific Builds
```bash
# Staging
REACT_APP_ENV=staging npm run build

# Production
REACT_APP_ENV=production npm run build
```

## 📊 Performance

### Optimization Features
- Code splitting with React.lazy()
- Memoization with React.memo()
- Virtual scrolling for large lists
- Image optimization
- Bundle analysis

### Bundle Analysis
```bash
npm install -g webpack-bundle-analyzer
npm run build
npx webpack-bundle-analyzer build/static/js/*.js
```

## 🔧 Development

### Code Standards
- ESLint configuration
- Prettier formatting
- Husky pre-commit hooks
- Conventional commits

### VS Code Extensions
- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- Auto Rename Tag
- Bracket Pair Colorizer

## 🐛 Troubleshooting

### Common Issues

**Build Errors**
```bash
# Clear cache
npm start -- --reset-cache

# Delete node_modules
rm -rf node_modules package-lock.json
npm install
```

**API Connection Issues**
- Check REACT_APP_API_URL in .env
- Verify backend server is running
- Check CORS configuration

## 📝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Email**: <EMAIL>
- **Documentation**: https://docs.tracknew.com
- **Issues**: Create an issue on GitHub

---

Built with ❤️ by the TrackNew Team
