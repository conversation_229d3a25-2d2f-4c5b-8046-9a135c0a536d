const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const ExpenseCategory = sequelize.define('ExpenseCategory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  category_code: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  parent_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'expense_categories',
      key: 'id'
    }
  },
  level: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: 'Hierarchy level (1 = root, 2 = sub-category, etc.)'
  },
  path: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Full category path'
  },
  icon: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Icon class or image path'
  },
  color_code: {
    type: DataTypes.STRING(7),
    allowNull: true,
    comment: 'Hex color code for category'
  },
  expense_type: {
    type: DataTypes.ENUM('operational', 'administrative', 'travel', 'marketing', 'equipment', 'utilities', 'professional', 'other'),
    defaultValue: 'operational'
  },
  is_billable: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether expenses in this category can be billed to customers'
  },
  default_markup_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    comment: 'Default markup for billable expenses'
  },
  requires_receipt: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether receipt is mandatory for this category'
  },
  requires_approval: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether expenses require approval'
  },
  approval_limit: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Amount above which approval is required'
  },
  auto_approve_limit: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Amount below which auto-approval is allowed'
  },
  default_tax_rate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    comment: 'Default tax rate for this category'
  },
  is_tax_deductible: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether expenses are tax deductible'
  },
  account_code: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Accounting system account code'
  },
  gl_account: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'General ledger account'
  },
  cost_center_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether cost center is required'
  },
  project_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether project assignment is required'
  },
  mileage_category: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is a mileage-based category'
  },
  default_mileage_rate: {
    type: DataTypes.DECIMAL(6, 2),
    allowNull: true,
    comment: 'Default rate per mile/km'
  },
  per_diem_category: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is a per diem category'
  },
  default_per_diem_rate: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    comment: 'Default per diem rate'
  },
  attendee_tracking: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether to track attendees for this category'
  },
  location_tracking: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether to track location for this category'
  },
  purpose_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether business purpose is required'
  },
  recurring_allowed: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether recurring expenses are allowed'
  },
  budget_category: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Budget category for tracking'
  },
  monthly_limit: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Monthly spending limit for this category'
  },
  yearly_limit: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: true,
    comment: 'Yearly spending limit for this category'
  },
  notification_threshold: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Percentage of limit to trigger notification'
  },
  workflow_template: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object defining approval workflow'
  },
  custom_fields: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of custom fields for this category'
  },
  validation_rules: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of validation rules'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_system_category: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is a system-defined category'
  },
  expense_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of expenses in this category'
  },
  total_amount: {
    type: DataTypes.DECIMAL(12, 2),
    defaultValue: 0.00,
    comment: 'Total amount spent in this category'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'expense_categories',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['parent_id']
    },
    {
      fields: ['company_id', 'name'],
      unique: true
    },
    {
      fields: ['category_code']
    },
    {
      fields: ['expense_type']
    },
    {
      fields: ['level']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_system_category']
    },
    {
      fields: ['is_billable']
    },
    {
      fields: ['requires_approval']
    },
    {
      fields: ['sort_order']
    }
  ]
});

module.exports = ExpenseCategory;
