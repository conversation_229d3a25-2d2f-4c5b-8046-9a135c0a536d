import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiMethods } from '../../services/api';

// Async thunks
export const fetchSales = createAsyncThunk(
  'sales/fetchSales',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/sales', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchSale = createAsyncThunk(
  'sales/fetchSale',
  async (id, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get(`/sales/${id}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const createSale = createAsyncThunk(
  'sales/createSale',
  async (saleData, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post('/sales', saleData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateSale = createAsyncThunk(
  'sales/updateSale',
  async ({ id, data }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.put(`/sales/${id}`, data);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const deleteSale = createAsyncThunk(
  'sales/deleteSale',
  async (id, { rejectWithValue }) => {
    try {
      await apiMethods.delete(`/sales/${id}`);
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchSalesStats = createAsyncThunk(
  'sales/fetchSalesStats',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/sales/stats', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const generateInvoice = createAsyncThunk(
  'sales/generateInvoice',
  async (saleId, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post(`/sales/${saleId}/generate-invoice`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const addPayment = createAsyncThunk(
  'sales/addPayment',
  async ({ saleId, paymentData }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post(`/sales/${saleId}/payments`, paymentData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  // Data
  sales: [],
  currentSale: null,
  stats: null,
  
  // Pagination
  pagination: {
    current_page: 1,
    total_pages: 1,
    total_items: 0,
    items_per_page: 15,
    has_next_page: false,
    has_prev_page: false,
  },
  
  // Loading states
  loading: false,
  saleLoading: false,
  statsLoading: false,
  actionLoading: false,
  
  // Error states
  error: null,
  saleError: null,
  statsError: null,
  actionError: null,
  
  // UI states
  selectedSales: [],
  filters: {
    status: '',
    payment_status: '',
    customer_id: '',
    sales_person: '',
    date_from: '',
    date_to: '',
    amount_from: '',
    amount_to: '',
  },
  sortBy: 'sales_date',
  sortOrder: 'desc',
  searchQuery: '',
  
  // Cart for new sale
  cart: {
    items: [],
    customer: null,
    discount: 0,
    tax_rate: 0,
    notes: '',
    payment_terms: 'immediate',
  },
};

const salesSlice = createSlice({
  name: 'sales',
  initialState,
  reducers: {
    // Selection actions
    selectSale: (state, action) => {
      const saleId = action.payload;
      if (!state.selectedSales.includes(saleId)) {
        state.selectedSales.push(saleId);
      }
    },
    deselectSale: (state, action) => {
      const saleId = action.payload;
      state.selectedSales = state.selectedSales.filter(id => id !== saleId);
    },
    selectAllSales: (state) => {
      state.selectedSales = state.sales.map(sale => sale.id);
    },
    deselectAllSales: (state) => {
      state.selectedSales = [];
    },
    
    // Filter actions
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    // Sort actions
    setSortBy: (state, action) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action) => {
      state.sortOrder = action.payload;
    },
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    // Search actions
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    clearSearchQuery: (state) => {
      state.searchQuery = '';
    },
    
    // Cart actions
    addToCart: (state, action) => {
      const item = action.payload;
      const existingItem = state.cart.items.find(i => i.product_id === item.product_id);
      
      if (existingItem) {
        existingItem.quantity += item.quantity || 1;
        existingItem.total_amount = existingItem.quantity * existingItem.unit_price;
      } else {
        state.cart.items.push({
          ...item,
          quantity: item.quantity || 1,
          total_amount: (item.quantity || 1) * item.unit_price,
        });
      }
    },
    removeFromCart: (state, action) => {
      const productId = action.payload;
      state.cart.items = state.cart.items.filter(item => item.product_id !== productId);
    },
    updateCartItem: (state, action) => {
      const { productId, updates } = action.payload;
      const item = state.cart.items.find(i => i.product_id === productId);
      if (item) {
        Object.assign(item, updates);
        item.total_amount = item.quantity * item.unit_price;
      }
    },
    setCartCustomer: (state, action) => {
      state.cart.customer = action.payload;
    },
    setCartDiscount: (state, action) => {
      state.cart.discount = action.payload;
    },
    setCartTaxRate: (state, action) => {
      state.cart.tax_rate = action.payload;
    },
    setCartNotes: (state, action) => {
      state.cart.notes = action.payload;
    },
    setCartPaymentTerms: (state, action) => {
      state.cart.payment_terms = action.payload;
    },
    clearCart: (state) => {
      state.cart = initialState.cart;
    },
    
    // Clear current sale
    clearCurrentSale: (state) => {
      state.currentSale = null;
      state.saleError = null;
    },
    
    // Clear errors
    clearError: (state) => {
      state.error = null;
    },
    clearSaleError: (state) => {
      state.saleError = null;
    },
    clearStatsError: (state) => {
      state.statsError = null;
    },
    clearActionError: (state) => {
      state.actionError = null;
    },
    
    // Reset state
    resetSalesState: (state) => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    // Fetch sales
    builder
      .addCase(fetchSales.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSales.fulfilled, (state, action) => {
        state.loading = false;
        state.sales = action.payload.data.sales;
        state.pagination = action.payload.data.pagination;
      })
      .addCase(fetchSales.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
    
    // Fetch single sale
    builder
      .addCase(fetchSale.pending, (state) => {
        state.saleLoading = true;
        state.saleError = null;
      })
      .addCase(fetchSale.fulfilled, (state, action) => {
        state.saleLoading = false;
        state.currentSale = action.payload.data.sale;
      })
      .addCase(fetchSale.rejected, (state, action) => {
        state.saleLoading = false;
        state.saleError = action.payload;
      });
    
    // Create sale
    builder
      .addCase(createSale.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(createSale.fulfilled, (state, action) => {
        state.actionLoading = false;
        state.sales.unshift(action.payload.data.sale);
        // Clear cart after successful sale creation
        state.cart = initialState.cart;
      })
      .addCase(createSale.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Update sale
    builder
      .addCase(updateSale.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(updateSale.fulfilled, (state, action) => {
        state.actionLoading = false;
        const updatedSale = action.payload.data.sale;
        const index = state.sales.findIndex(sale => sale.id === updatedSale.id);
        if (index !== -1) {
          state.sales[index] = updatedSale;
        }
        if (state.currentSale?.id === updatedSale.id) {
          state.currentSale = updatedSale;
        }
      })
      .addCase(updateSale.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Delete sale
    builder
      .addCase(deleteSale.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(deleteSale.fulfilled, (state, action) => {
        state.actionLoading = false;
        const saleId = action.payload;
        state.sales = state.sales.filter(sale => sale.id !== saleId);
        state.selectedSales = state.selectedSales.filter(id => id !== saleId);
        if (state.currentSale?.id === saleId) {
          state.currentSale = null;
        }
      })
      .addCase(deleteSale.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Fetch stats
    builder
      .addCase(fetchSalesStats.pending, (state) => {
        state.statsLoading = true;
        state.statsError = null;
      })
      .addCase(fetchSalesStats.fulfilled, (state, action) => {
        state.statsLoading = false;
        state.stats = action.payload.data;
      })
      .addCase(fetchSalesStats.rejected, (state, action) => {
        state.statsLoading = false;
        state.statsError = action.payload;
      });
    
    // Generate invoice
    builder
      .addCase(generateInvoice.fulfilled, (state, action) => {
        const updatedSale = action.payload.data.sale;
        const index = state.sales.findIndex(sale => sale.id === updatedSale.id);
        if (index !== -1) {
          state.sales[index] = updatedSale;
        }
        if (state.currentSale?.id === updatedSale.id) {
          state.currentSale = updatedSale;
        }
      });
    
    // Add payment
    builder
      .addCase(addPayment.fulfilled, (state, action) => {
        const updatedSale = action.payload.data.sale;
        const index = state.sales.findIndex(sale => sale.id === updatedSale.id);
        if (index !== -1) {
          state.sales[index] = updatedSale;
        }
        if (state.currentSale?.id === updatedSale.id) {
          state.currentSale = updatedSale;
        }
      });
  },
});

export const {
  selectSale,
  deselectSale,
  selectAllSales,
  deselectAllSales,
  setFilter,
  setFilters,
  clearFilters,
  setSortBy,
  setSortOrder,
  toggleSortOrder,
  setSearchQuery,
  clearSearchQuery,
  addToCart,
  removeFromCart,
  updateCartItem,
  setCartCustomer,
  setCartDiscount,
  setCartTaxRate,
  setCartNotes,
  setCartPaymentTerms,
  clearCart,
  clearCurrentSale,
  clearError,
  clearSaleError,
  clearStatsError,
  clearActionError,
  resetSalesState,
} = salesSlice.actions;

export default salesSlice.reducer;

// Selectors
export const selectSales = (state) => state.sales.sales;
export const selectCurrentSale = (state) => state.sales.currentSale;
export const selectSalesStats = (state) => state.sales.stats;
export const selectSalesPagination = (state) => state.sales.pagination;
export const selectSalesLoading = (state) => state.sales.loading;
export const selectSalesError = (state) => state.sales.error;
export const selectSelectedSales = (state) => state.sales.selectedSales;
export const selectSalesFilters = (state) => state.sales.filters;
export const selectSalesSort = (state) => ({
  sortBy: state.sales.sortBy,
  sortOrder: state.sales.sortOrder,
});
export const selectSalesSearchQuery = (state) => state.sales.searchQuery;
export const selectCart = (state) => state.sales.cart;
export const selectCartItems = (state) => state.sales.cart.items;
export const selectCartTotal = (state) => {
  const { items, discount, tax_rate } = state.sales.cart;
  const subtotal = items.reduce((sum, item) => sum + item.total_amount, 0);
  const discountAmount = (subtotal * discount) / 100;
  const taxableAmount = subtotal - discountAmount;
  const taxAmount = (taxableAmount * tax_rate) / 100;
  return {
    subtotal,
    discount: discountAmount,
    tax: taxAmount,
    total: taxableAmount + taxAmount,
  };
};
