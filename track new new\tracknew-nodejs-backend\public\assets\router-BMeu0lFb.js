import{r as e,g as t,a as r}from"./vendor-BRaCMJ4j.js";function n(e,t){for(var r=0;r<t.length;r++){const n=t[r];if("string"!=typeof n&&!Array.isArray(n))for(const t in n)if("default"!==t&&!(t in e)){const r=Object.getOwnPropertyDescriptor(n,t);r&&Object.defineProperty(e,t,r.get?r:{enumerable:!0,get:()=>n[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var a=e();const o=t(a),l=n({__proto__:null,default:o},[a]);
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},i.apply(this,arguments)}var s,u;r(),(u=s||(s={})).Pop="POP",u.Push="PUSH",u.Replace="REPLACE";const c="popstate";function h(e){return void 0===e&&(e={}),function(e,t,r,n){void 0===n&&(n={});let{window:a=document.defaultView,v5Compat:o=!1}=n,l=a.history,u=s.Pop,h=null,d=g();null==d&&(d=0,l.replaceState(i({},l.state,{idx:d}),""));function g(){return(l.state||{idx:null}).idx}function y(){u=s.Pop;let e=g(),t=null==e?null:e-d;d=e,h&&h({action:u,location:w.location,delta:t})}function b(e,t){u=s.Push;let r=m(w.location,e,t);d=g()+1;let n=f(r,d),i=w.createHref(r);try{l.pushState(n,"",i)}catch(c){if(c instanceof DOMException&&"DataCloneError"===c.name)throw c;a.location.assign(i)}o&&h&&h({action:u,location:w.location,delta:1})}function E(e,t){u=s.Replace;let r=m(w.location,e,t);d=g();let n=f(r,d),a=w.createHref(r);l.replaceState(n,"",a),o&&h&&h({action:u,location:w.location,delta:0})}function x(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,r="string"==typeof e?e:v(e);return r=r.replace(/ $/,"%20"),p(t,"No window.location.(origin|href) available to create URL for href: "+r),new URL(r,t)}let w={get action(){return u},get location(){return e(a,l)},listen(e){if(h)throw new Error("A history only accepts one active listener");return a.addEventListener(c,y),h=e,()=>{a.removeEventListener(c,y),h=null}},createHref:e=>t(a,e),createURL:x,encodeLocation(e){let t=x(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:b,replace:E,go:e=>l.go(e)};return w}((function(e,t){let{pathname:r,search:n,hash:a}=e.location;return m("",{pathname:r,search:n,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:v(t)}),0,e)}function p(e,t){if(!1===e||null==e)throw new Error(t)}function d(e,t){if(!e)try{throw new Error(t)}catch(r){}}function f(e,t){return{usr:e.state,key:e.key,idx:t}}function m(e,t,r,n){return void 0===r&&(r=null),i({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?g(t):t,{state:r,key:t&&t.key||n||Math.random().toString(36).substr(2,8)})}function v(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&"?"!==r&&(t+="?"===r.charAt(0)?r:"?"+r),n&&"#"!==n&&(t+="#"===n.charAt(0)?n:"#"+n),t}function g(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}var y,b;function E(e,t,r){return void 0===r&&(r="/"),function(e,t,r){let n="string"==typeof t?g(t):t,a=$(n.pathname||"/",r);if(null==a)return null;let o=x(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let r=e.length===t.length&&e.slice(0,-1).every(((e,r)=>e===t[r]));return r?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let l=null;for(let i=0;null==l&&i<o.length;++i){let e=F(a);l=L(o[i],e)}return l}(e,t,r)}function x(e,t,r,n){void 0===t&&(t=[]),void 0===r&&(r=[]),void 0===n&&(n="");let a=(e,a,o)=>{let l={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};l.relativePath.startsWith("/")&&(p(l.relativePath.startsWith(n),'Absolute route path "'+l.relativePath+'" nested under path "'+n+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),l.relativePath=l.relativePath.slice(n.length));let i=D([n,l.relativePath]),s=r.concat(l);e.children&&e.children.length>0&&(p(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),x(e.children,t,s,i)),(null!=e.path||e.index)&&t.push({path:i,score:B(i,e.index),routesMeta:s})};return e.forEach(((e,t)=>{var r;if(""!==e.path&&null!=(r=e.path)&&r.includes("?"))for(let n of w(e.path))a(e,t,n);else a(e,t)})),t}function w(e){let t=e.split("/");if(0===t.length)return[];let[r,...n]=t,a=r.endsWith("?"),o=r.replace(/\?$/,"");if(0===n.length)return a?[o,""]:[o];let l=w(n.join("/")),i=[];return i.push(...l.map((e=>""===e?o:[o,e].join("/")))),a&&i.push(...l),i.map((t=>e.startsWith("/")&&""===t?"/":t))}(b=y||(y={})).data="data",b.deferred="deferred",b.redirect="redirect",b.error="error";const C=/^:[\w-]+$/,P=3,S=2,R=1,U=10,O=-2,j=e=>"*"===e;function B(e,t){let r=e.split("/"),n=r.length;return r.some(j)&&(n+=O),t&&(n+=S),r.filter((e=>!j(e))).reduce(((e,t)=>e+(C.test(t)?P:""===t?R:U)),n)}function L(e,t,r){let{routesMeta:n}=e,a={},o="/",l=[];for(let i=0;i<n.length;++i){let e=n[i],r=i===n.length-1,s="/"===o?t:t.slice(o.length)||"/",u=k({path:e.relativePath,caseSensitive:e.caseSensitive,end:r},s),c=e.route;if(!u)return null;Object.assign(a,u.params),l.push({params:a,pathname:D([o,u.pathname]),pathnameBase:T(D([o,u.pathnameBase])),route:c}),"/"!==u.pathnameBase&&(o=D([o,u.pathnameBase]))}return l}function k(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=function(e,t,r){void 0===t&&(t=!1);void 0===r&&(r=!0);d("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,r)=>(n.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(n.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,n]}(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:n.reduce(((e,t,r)=>{let{paramName:n,isOptional:a}=t;if("*"===n){let e=i[r]||"";l=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const s=i[r];return e[n]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:l,pattern:e}}function F(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return d(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function $(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&"/"!==n?null:e.slice(r)||"/"}const D=e=>e.join("/").replace(/\/\/+/g,"/"),T=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");const _=["post","put","patch","delete"];new Set(_);const A=["get",..._];
/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function M(){return M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},M.apply(this,arguments)}new Set(A);const W=a.createContext(null),I=a.createContext(null),N=a.createContext(null),H=a.createContext(null),z=a.createContext({outlet:null,matches:[],isDataRoute:!1}),V=a.createContext(null);function J(){return null!=a.useContext(H)}function q(e,t){return function(e,t,r,n){J()||p(!1);let{navigator:o}=a.useContext(N),{matches:l}=a.useContext(z),i=l[l.length-1],u=i?i.params:{};!i||i.pathname;let c=i?i.pathnameBase:"/";i&&i.route;let h,d=(J()||p(!1),a.useContext(H).location);if(t){var f;let e="string"==typeof t?g(t):t;"/"===c||(null==(f=e.pathname)?void 0:f.startsWith(c))||p(!1),h=e}else h=d;let m=h.pathname||"/",v=m;if("/"!==c){let e=c.replace(/^\//,"").split("/");v="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=E(e,{pathname:v}),b=function(e,t,r,n){var o;void 0===t&&(t=[]);void 0===r&&(r=null);void 0===n&&(n=null);if(null==e){var l;if(!r)return null;if(r.errors)e=r.matches;else{if(!(null!=(l=n)&&l.v7_partialHydration&&0===t.length&&!r.initialized&&r.matches.length>0))return null;e=r.matches}}let i=e,s=null==(o=r)?void 0:o.errors;if(null!=s){let e=i.findIndex((e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id])));e>=0||p(!1),i=i.slice(0,Math.min(i.length,e+1))}let u=!1,c=-1;if(r&&n&&n.v7_partialHydration)for(let a=0;a<i.length;a++){let e=i[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=a),e.route.id){let{loaderData:t,errors:n}=r,a=e.route.loader&&void 0===t[e.route.id]&&(!n||void 0===n[e.route.id]);if(e.route.lazy||a){u=!0,i=c>=0?i.slice(0,c+1):[i[0]];break}}}return i.reduceRight(((e,n,o)=>{let l,h=!1,p=null,d=null;var f;r&&(l=s&&n.route.id?s[n.route.id]:void 0,p=n.route.errorElement||K,u&&(c<0&&0===o?(Z[f="route-fallback"]||(Z[f]=!0),h=!0,d=null):c===o&&(h=!0,d=n.route.hydrateFallbackElement||null)));let m=t.concat(i.slice(0,o+1)),v=()=>{let t;return t=l?p:h?d:n.route.Component?a.createElement(n.route.Component,null):n.route.element?n.route.element:e,a.createElement(X,{match:n,routeContext:{outlet:e,matches:m,isDataRoute:null!=r},children:t})};return r&&(n.route.ErrorBoundary||n.route.errorElement||0===o)?a.createElement(Q,{location:r.location,revalidation:r.revalidation,component:p,error:l,children:v(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):v()}),null)}(y&&y.map((e=>Object.assign({},e,{params:Object.assign({},u,e.params),pathname:D([c,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:D([c,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),l,r,n);if(t&&b)return a.createElement(H.Provider,{value:{location:M({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:s.Pop}},b);return b}(e,t)}function G(){let e=function(){var e;let t=a.useContext(V),r=function(){let e=a.useContext(I);return e||p(!1),e}(Y.UseRouteError),n=function(){let e=function(){let e=a.useContext(z);return e||p(!1),e}(),t=e.matches[e.matches.length-1];return t.route.id||p(!1),t.route.id}();if(void 0!==t)return t;return null==(e=r.errors)?void 0:e[n]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),r?a.createElement("pre",{style:n},r):null,null)}const K=a.createElement(G,null);class Q extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){}render(){return void 0!==this.state.error?a.createElement(z.Provider,{value:this.props.routeContext},a.createElement(V.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function X(e){let{routeContext:t,match:r,children:n}=e,o=a.useContext(W);return o&&o.static&&o.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=r.route.id),a.createElement(z.Provider,{value:t},n)}var Y=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Y||{});const Z={};function ee(e){p(!1)}function te(e){let{basename:t="/",children:r=null,location:n,navigationType:o=s.Pop,navigator:l,static:i=!1,future:u}=e;J()&&p(!1);let c=t.replace(/^\/*/,"/"),h=a.useMemo((()=>({basename:c,navigator:l,static:i,future:M({v7_relativeSplatPath:!1},u)})),[c,u,l,i]);"string"==typeof n&&(n=g(n));let{pathname:d="/",search:f="",hash:m="",state:v=null,key:y="default"}=n,b=a.useMemo((()=>{let e=$(d,c);return null==e?null:{location:{pathname:e,search:f,hash:m,state:v,key:y},navigationType:o}}),[c,d,f,m,v,y,o]);return null==b?null:a.createElement(N.Provider,{value:h},a.createElement(H.Provider,{children:r,value:b}))}function re(e){let{children:t,location:r}=e;return q(ne(t),r)}function ne(e,t){void 0===t&&(t=[]);let r=[];return a.Children.forEach(e,((e,n)=>{if(!a.isValidElement(e))return;let o=[...t,n];if(e.type===a.Fragment)return void r.push.apply(r,ne(e.props.children,o));e.type!==ee&&p(!1),e.props.index&&e.props.children&&p(!1);let l={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(l.children=ne(e.props.children,o)),r.push(l)})),r}
/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */new Promise((()=>{}));try{window.__reactRouterVersion="6"}catch(ce){}const ae=l.startTransition;function oe(e){let{basename:t,children:r,future:n,window:o}=e,l=a.useRef();null==l.current&&(l.current=h({window:o,v5Compat:!0}));let i=l.current,[s,u]=a.useState({action:i.action,location:i.location}),{v7_startTransition:c}=n||{},p=a.useCallback((e=>{c&&ae?ae((()=>u(e))):u(e)}),[u,c]);return a.useLayoutEffect((()=>i.listen(p)),[i,p]),a.useEffect((()=>{return null==(e=n)||e.v7_startTransition,void(null==e||e.v7_relativeSplatPath);var e}),[n]),a.createElement(te,{basename:t,children:r,location:s.location,navigationType:s.action,navigator:i,future:n})}var le,ie,se,ue;(ie=le||(le={})).UseScrollRestoration="useScrollRestoration",ie.UseSubmit="useSubmit",ie.UseSubmitFetcher="useSubmitFetcher",ie.UseFetcher="useFetcher",ie.useViewTransitionState="useViewTransitionState",(ue=se||(se={})).UseFetcher="useFetcher",ue.UseFetchers="useFetchers",ue.UseScrollRestoration="useScrollRestoration";export{oe as B,re as R,ee as a,o as b,a as r};
