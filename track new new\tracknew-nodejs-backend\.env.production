# Production Environment Configuration
NODE_ENV=production
PORT=8000

# Database Configuration
DATABASE_URL=****************************************************/tracnew?sslmode=disable
DB_HOST=**************
DB_PORT=5432
DB_NAME=tracnew
DB_USER=postgres
DB_USERNAME=postgres
DB_PASSWORD=Cloud@2025
DB_DIALECT=postgres
DB_SSL=false

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_here_minimum_32_characters
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_here_minimum_32_characters
JWT_REFRESH_EXPIRE=30d

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here

# Email Configuration (Production SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=Track New System

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Firebase Configuration
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email
FIREBASE_CLIENT_ID=your_firebase_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=tracknew-uploads

# Application Configuration
APP_URL=https://api.tracknew.com
FRONTEND_URL=https://tracknew.com
CORS_ORIGIN=https://tracknew.com

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=/app/uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_here_minimum_32_characters

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=/app/logs
