const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const SalesPayment = sequelize.define('SalesPayment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  sales_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'sales',
      key: 'id'
    }
  },
  payment_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  payment_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  payment_method: {
    type: DataTypes.ENUM('cash', 'cheque', 'bank_transfer', 'credit_card', 'debit_card', 'upi', 'wallet', 'online', 'other'),
    defaultValue: 'cash'
  },
  payment_mode: {
    type: DataTypes.ENUM('full', 'partial', 'advance', 'refund'),
    defaultValue: 'full'
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'INR'
  },
  exchange_rate: {
    type: DataTypes.DECIMAL(10, 4),
    defaultValue: 1.0000
  },
  base_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Amount in base currency'
  },
  reference_number: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Cheque number, transaction ID, etc.'
  },
  bank_name: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  bank_account: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  cheque_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  cheque_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  cheque_status: {
    type: DataTypes.ENUM('pending', 'cleared', 'bounced', 'cancelled'),
    allowNull: true
  },
  transaction_id: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Online transaction ID'
  },
  gateway_response: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Payment gateway response data'
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'),
    defaultValue: 'completed'
  },
  payment_gateway: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Payment gateway used (razorpay, payu, etc.)'
  },
  gateway_transaction_id: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  gateway_fee: {
    type: DataTypes.DECIMAL(8, 2),
    defaultValue: 0.00,
    comment: 'Payment gateway charges'
  },
  net_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Amount after deducting gateway fees'
  },
  reconciliation_status: {
    type: DataTypes.ENUM('pending', 'matched', 'unmatched', 'disputed'),
    defaultValue: 'pending'
  },
  reconciliation_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  reconciliation_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  receipt_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  receipt_generated: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  receipt_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  receipt_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  internal_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Internal notes not visible to customer'
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of attachment file paths'
  },
  tds_applicable: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether TDS is applicable'
  },
  tds_rate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    comment: 'TDS rate percentage'
  },
  tds_amount: {
    type: DataTypes.DECIMAL(8, 2),
    defaultValue: 0.00
  },
  tds_certificate_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  advance_adjustment: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00,
    comment: 'Amount adjusted from advance payments'
  },
  discount_given: {
    type: DataTypes.DECIMAL(8, 2),
    defaultValue: 0.00,
    comment: 'Additional discount given during payment'
  },
  late_fee_charged: {
    type: DataTypes.DECIMAL(8, 2),
    defaultValue: 0.00,
    comment: 'Late payment fee charged'
  },
  is_advance: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is an advance payment'
  },
  advance_adjusted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether advance has been adjusted'
  },
  adjustment_sales_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'sales',
      key: 'id'
    },
    comment: 'Sales ID where advance was adjusted'
  },
  refund_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  refund_amount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00
  },
  refund_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  refund_reference: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  approved_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  approved_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'sales_payments',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['sales_id']
    },
    {
      fields: ['payment_number'],
      unique: true
    },
    {
      fields: ['payment_date']
    },
    {
      fields: ['payment_method']
    },
    {
      fields: ['status']
    },
    {
      fields: ['cheque_number']
    },
    {
      fields: ['transaction_id']
    },
    {
      fields: ['gateway_transaction_id']
    },
    {
      fields: ['reconciliation_status']
    },
    {
      fields: ['is_advance']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = SalesPayment;
