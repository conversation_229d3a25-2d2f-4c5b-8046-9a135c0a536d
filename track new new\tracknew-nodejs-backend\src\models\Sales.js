const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Sales = sequelize.define('Sales', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  sales_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  reference_number: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Customer reference or PO number'
  },
  sales_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  due_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('draft', 'pending', 'confirmed', 'processing', 'shipped', 'delivered', 'completed', 'cancelled', 'returned'),
    defaultValue: 'draft'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  sales_type: {
    type: DataTypes.ENUM('product', 'service', 'mixed'),
    defaultValue: 'product'
  },
  payment_status: {
    type: DataTypes.ENUM('pending', 'partial', 'paid', 'overdue', 'cancelled'),
    defaultValue: 'pending'
  },
  payment_terms: {
    type: DataTypes.ENUM('cash', 'credit_7', 'credit_15', 'credit_30', 'credit_45', 'credit_60', 'credit_90', 'custom'),
    defaultValue: 'cash'
  },
  payment_terms_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Custom payment terms in days'
  },
  subtotal: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  discount_type: {
    type: DataTypes.ENUM('percentage', 'fixed'),
    allowNull: true
  },
  discount_value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00
  },
  discount_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  tax_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  shipping_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  other_charges: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  total_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  paid_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  balance_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'INR'
  },
  exchange_rate: {
    type: DataTypes.DECIMAL(10, 4),
    defaultValue: 1.0000
  },
  billing_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  shipping_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  delivery_method: {
    type: DataTypes.ENUM('pickup', 'delivery', 'courier', 'post', 'email'),
    defaultValue: 'pickup'
  },
  delivery_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  delivery_time: {
    type: DataTypes.TIME,
    allowNull: true
  },
  delivery_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  tracking_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  courier_company: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  internal_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Internal notes not visible to customer'
  },
  terms_and_conditions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  warranty_terms: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  return_policy: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  source: {
    type: DataTypes.ENUM('direct', 'website', 'phone', 'email', 'referral', 'advertisement', 'social_media', 'other'),
    defaultValue: 'direct'
  },
  source_details: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  sales_person: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  commission_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Commission rate percentage'
  },
  commission_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  is_taxable: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  tax_inclusive: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  round_off: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of attachment file paths'
  },
  custom_fields: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object for custom field values'
  },
  is_recurring: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  recurring_frequency: {
    type: DataTypes.ENUM('weekly', 'monthly', 'quarterly', 'yearly'),
    allowNull: true
  },
  recurring_end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  parent_sales_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'sales',
      key: 'id'
    },
    comment: 'Reference to parent sales for recurring orders'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  approved_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  approved_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'sales',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['sales_number'],
      unique: true
    },
    {
      fields: ['status']
    },
    {
      fields: ['payment_status']
    },
    {
      fields: ['sales_date']
    },
    {
      fields: ['due_date']
    },
    {
      fields: ['sales_person']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['parent_sales_id']
    }
  ]
});

module.exports = Sales;
