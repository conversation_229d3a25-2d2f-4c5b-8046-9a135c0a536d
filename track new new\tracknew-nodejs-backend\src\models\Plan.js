const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Plan = sequelize.define('Plan', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  },
  display_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  plan_code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  plan_type: {
    type: DataTypes.ENUM('free', 'trial', 'basic', 'standard', 'premium', 'enterprise', 'custom'),
    defaultValue: 'basic'
  },
  billing_cycle: {
    type: DataTypes.ENUM('monthly', 'quarterly', 'half_yearly', 'yearly', 'one_time', 'custom'),
    defaultValue: 'monthly'
  },
  billing_cycle_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Custom billing cycle in days'
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  setup_fee: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'INR'
  },
  trial_period_days: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Free trial period in days'
  },
  grace_period_days: {
    type: DataTypes.INTEGER,
    defaultValue: 7,
    comment: 'Grace period after payment due'
  },
  max_users: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum number of users allowed'
  },
  max_customers: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum number of customers allowed'
  },
  max_products: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum number of products allowed'
  },
  max_services: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum number of services per month'
  },
  max_sales: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum number of sales per month'
  },
  max_invoices: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum number of invoices per month'
  },
  max_storage_gb: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum storage in GB'
  },
  max_api_calls: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum API calls per month'
  },
  max_sms: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum SMS per month'
  },
  max_emails: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum emails per month'
  },
  max_whatsapp: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum WhatsApp messages per month'
  },
  features: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of included features'
  },
  modules: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of included modules'
  },
  integrations: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of available integrations'
  },
  restrictions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of plan restrictions'
  },
  custom_limits: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of custom limits'
  },
  support_level: {
    type: DataTypes.ENUM('none', 'email', 'chat', 'phone', 'priority', 'dedicated'),
    defaultValue: 'email'
  },
  support_hours: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Support availability hours'
  },
  sla_response_time: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'SLA response time in hours'
  },
  backup_frequency: {
    type: DataTypes.ENUM('none', 'daily', 'weekly', 'monthly'),
    defaultValue: 'weekly'
  },
  backup_retention_days: {
    type: DataTypes.INTEGER,
    defaultValue: 30
  },
  uptime_guarantee: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Uptime guarantee percentage'
  },
  white_label: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether white labeling is allowed'
  },
  custom_domain: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether custom domain is allowed'
  },
  ssl_certificate: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether SSL certificate is included'
  },
  api_access: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether API access is included'
  },
  webhook_support: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether webhook support is included'
  },
  advanced_reporting: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether advanced reporting is included'
  },
  data_export: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether data export is allowed'
  },
  multi_company: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether multi-company support is included'
  },
  priority_support: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether priority support is included'
  },
  training_included: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether training is included'
  },
  onboarding_support: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether onboarding support is included'
  },
  discount_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    comment: 'Discount percentage for this plan'
  },
  promotional_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Promotional price if applicable'
  },
  promotion_valid_until: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Promotion validity end date'
  },
  is_popular: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this plan is marked as popular'
  },
  is_recommended: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this plan is recommended'
  },
  is_featured: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this plan is featured'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_public: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether this plan is publicly available'
  },
  available_from: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Plan availability start date'
  },
  available_until: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Plan availability end date'
  },
  subscriber_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of active subscribers'
  },
  max_subscribers: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum number of subscribers allowed'
  },
  terms_and_conditions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'plans',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['name'],
      unique: true
    },
    {
      fields: ['plan_code'],
      unique: true
    },
    {
      fields: ['plan_type']
    },
    {
      fields: ['billing_cycle']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_public']
    },
    {
      fields: ['is_popular']
    },
    {
      fields: ['is_recommended']
    },
    {
      fields: ['sort_order']
    },
    {
      fields: ['price']
    }
  ]
});

module.exports = Plan;
