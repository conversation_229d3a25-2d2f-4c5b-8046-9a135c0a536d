const express = require('express');
const { body, query } = require('express-validator');
const backupController = require('../controllers/backupController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating backup
const createBackupValidation = [
  body('backup_type')
    .optional()
    .isIn(['full', 'incremental', 'differential'])
    .withMessage('Invalid backup type'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('include_files')
    .optional()
    .isBoolean()
    .withMessage('Include files must be a boolean'),
  
  body('compression')
    .optional()
    .isBoolean()
    .withMessage('Compression must be a boolean')
];

// Validation for restore backup
const restoreBackupValidation = [
  body('confirm')
    .notEmpty()
    .withMessage('Confirmation is required')
    .isBoolean()
    .withMessage('Confirm must be a boolean')
    .custom((value) => {
      if (value !== true) {
        throw new Error('Restore confirmation must be true');
      }
      return true;
    })
];

// Validation for scheduling backup
const scheduleBackupValidation = [
  body('frequency')
    .optional()
    .isIn(['hourly', 'daily', 'weekly', 'monthly'])
    .withMessage('Invalid backup frequency'),
  
  body('time')
    .optional()
    .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Time must be in HH:MM format'),
  
  body('backup_type')
    .optional()
    .isIn(['full', 'incremental', 'differential'])
    .withMessage('Invalid backup type'),
  
  body('include_files')
    .optional()
    .isBoolean()
    .withMessage('Include files must be a boolean'),
  
  body('retention_days')
    .optional()
    .isInt({ min: 1, max: 2555 })
    .withMessage('Retention days must be between 1 and 2555 (7 years)')
];

// Validation for cleanup
const cleanupValidation = [
  body('retention_days')
    .optional()
    .isInt({ min: 1, max: 2555 })
    .withMessage('Retention days must be between 1 and 2555 (7 years)')
];

// Validation for query parameters
const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  query('backup_type')
    .optional()
    .isIn(['full', 'incremental', 'differential'])
    .withMessage('Invalid backup type'),
  
  query('status')
    .optional()
    .isIn(['pending', 'in_progress', 'completed', 'failed', 'cancelled'])
    .withMessage('Invalid backup status'),
  
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid date'),
  
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid date'),
  
  query('sort_by')
    .optional()
    .isIn(['created_at', 'completed_at', 'file_size', 'backup_type'])
    .withMessage('Invalid sort field'),
  
  query('sort_order')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC')
];

// Routes
router
  .route('/')
  .get(queryValidation, validateRequest, restrictTo('admin', 'sub_admin'), backupController.getBackups)
  .post(createBackupValidation, validateRequest, restrictTo('admin'), backupController.createBackup);

router
  .route('/stats')
  .get(restrictTo('admin', 'sub_admin'), backupController.getBackupStats);

router
  .route('/schedule')
  .get(restrictTo('admin', 'sub_admin'), backupController.getBackupSchedule)
  .put(scheduleBackupValidation, validateRequest, restrictTo('admin'), backupController.scheduleBackup);

router
  .route('/cleanup')
  .post(cleanupValidation, validateRequest, restrictTo('admin'), backupController.cleanupBackups);

router
  .route('/:id/download')
  .get(restrictTo('admin', 'sub_admin'), backupController.downloadBackup);

router
  .route('/:id/restore')
  .post(restoreBackupValidation, validateRequest, restrictTo('admin'), backupController.restoreBackup);

router
  .route('/:id')
  .delete(restrictTo('admin'), backupController.deleteBackup);

module.exports = router;
