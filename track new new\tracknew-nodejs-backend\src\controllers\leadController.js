const { Lead, LeadType, Lead<PERSON>tatus, Customer, User, Company } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all leads with filtering and pagination
const getLeads = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    status,
    priority,
    temperature,
    lead_type_id,
    assigned_to,
    source,
    search,
    sort_by = 'created_at',
    sort_order = 'DESC',
    date_from,
    date_to,
    is_converted
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { company_id: req.user.company_id };

  // Apply filters
  if (status) whereClause.leadstatus_id = status;
  if (priority) whereClause.priority = priority;
  if (temperature) whereClause.temperature = temperature;
  if (lead_type_id) whereClause.lead_type_id = lead_type_id;
  if (assigned_to) whereClause.assigned_to = assigned_to;
  if (source) whereClause.source = source;
  if (is_converted !== undefined) whereClause.is_converted = is_converted === 'true';

  if (search) {
    whereClause[Op.or] = [
      { title: { [Op.like]: `%${search}%` } },
      { lead_number: { [Op.like]: `%${search}%` } },
      { contact_person: { [Op.like]: `%${search}%` } },
      { contact_phone: { [Op.like]: `%${search}%` } },
      { contact_email: { [Op.like]: `%${search}%` } }
    ];
  }

  if (date_from || date_to) {
    whereClause.created_at = {};
    if (date_from) whereClause.created_at[Op.gte] = new Date(date_from);
    if (date_to) whereClause.created_at[Op.lte] = new Date(date_to);
  }

  const { count, rows: leads } = await Lead.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: LeadType,
        as: 'leadType',
        attributes: ['id', 'name', 'color_code']
      },
      {
        model: LeadStatus,
        as: 'leadStatus',
        attributes: ['id', 'name', 'color_code', 'is_closed', 'is_won']
      },
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'email', 'mobile_number']
      },
      {
        model: User,
        as: 'assignedTo',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      }
    ],
    order: [[sort_by, sort_order.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.status(200).json({
    status: 'success',
    data: {
      leads,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: count,
        total_pages: Math.ceil(count / limit)
      }
    }
  });
});

// Get lead by ID
const getLead = catchAsync(async (req, res, next) => {
  const lead = await Lead.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    },
    include: [
      {
        model: LeadType,
        as: 'leadType',
        attributes: ['id', 'name', 'description', 'color_code']
      },
      {
        model: LeadStatus,
        as: 'leadStatus',
        attributes: ['id', 'name', 'description', 'color_code', 'is_closed', 'is_won']
      },
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: User,
        as: 'assignedTo',
        attributes: ['id', 'name', 'email', 'phone']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!lead) {
    return next(new AppError('Lead not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: { lead }
  });
});

// Create new lead
const createLead = catchAsync(async (req, res, next) => {
  const {
    customer_id,
    lead_type_id,
    leadstatus_id,
    assigned_to,
    title,
    description,
    source = 'other',
    source_details,
    priority = 'medium',
    temperature = 'warm',
    estimated_value,
    probability,
    expected_close_date,
    contact_person,
    contact_phone,
    contact_email,
    contact_address,
    requirements,
    budget_range,
    decision_maker,
    competition,
    next_follow_up,
    follow_up_notes,
    tags
  } = req.body;

  // Generate lead number
  const leadCount = await Lead.count({
    where: { company_id: req.user.company_id }
  });
  const lead_number = `LEAD-${String(leadCount + 1).padStart(6, '0')}`;

  // Verify customer belongs to the same company if provided
  if (customer_id) {
    const customer = await Customer.findOne({
      where: { id: customer_id, company_id: req.user.company_id }
    });
    if (!customer) {
      return next(new AppError('Customer not found', 404));
    }
  }

  const lead = await Lead.create({
    company_id: req.user.company_id,
    customer_id,
    lead_type_id,
    leadstatus_id,
    assigned_to,
    lead_number,
    title,
    description,
    source,
    source_details,
    priority,
    temperature,
    estimated_value,
    probability,
    expected_close_date,
    contact_person,
    contact_phone,
    contact_email,
    contact_address,
    requirements,
    budget_range,
    decision_maker,
    competition,
    next_follow_up,
    follow_up_notes,
    tags,
    created_by: req.user.id
  });

  // Fetch created lead with associations
  const createdLead = await Lead.findByPk(lead.id, {
    include: [
      {
        model: LeadType,
        as: 'leadType',
        attributes: ['id', 'name', 'color_code']
      },
      {
        model: LeadStatus,
        as: 'leadStatus',
        attributes: ['id', 'name', 'color_code']
      },
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'email', 'mobile_number']
      },
      {
        model: User,
        as: 'assignedTo',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: { lead: createdLead }
  });
});

// Update lead
const updateLead = catchAsync(async (req, res, next) => {
  const lead = await Lead.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!lead) {
    return next(new AppError('Lead not found', 404));
  }

  const allowedUpdates = [
    'customer_id', 'lead_type_id', 'leadstatus_id', 'assigned_to', 'title',
    'description', 'source', 'source_details', 'priority', 'temperature',
    'estimated_value', 'probability', 'expected_close_date', 'actual_close_date',
    'contact_person', 'contact_phone', 'contact_email', 'contact_address',
    'requirements', 'budget_range', 'decision_maker', 'competition',
    'next_follow_up', 'follow_up_notes', 'tags', 'is_converted',
    'converted_to', 'converted_id', 'conversion_date', 'lost_reason'
  ];

  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  // Update last activity timestamp
  updates.last_activity_at = new Date();

  await lead.update(updates);

  // Fetch updated lead with associations
  const updatedLead = await Lead.findByPk(lead.id, {
    include: [
      {
        model: LeadType,
        as: 'leadType',
        attributes: ['id', 'name', 'color_code']
      },
      {
        model: LeadStatus,
        as: 'leadStatus',
        attributes: ['id', 'name', 'color_code']
      },
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'email', 'mobile_number']
      },
      {
        model: User,
        as: 'assignedTo',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: { lead: updatedLead }
  });
});

// Delete lead
const deleteLead = catchAsync(async (req, res, next) => {
  const lead = await Lead.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!lead) {
    return next(new AppError('Lead not found', 404));
  }

  await lead.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Convert lead to customer/service/sale
const convertLead = catchAsync(async (req, res, next) => {
  const { convert_to, conversion_data } = req.body;
  
  const lead = await Lead.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!lead) {
    return next(new AppError('Lead not found', 404));
  }

  if (lead.is_converted) {
    return next(new AppError('Lead is already converted', 400));
  }

  let convertedRecord;
  
  switch (convert_to) {
    case 'customer':
      convertedRecord = await Customer.create({
        company_id: req.user.company_id,
        customer_name: conversion_data.customer_name || lead.contact_person,
        email: conversion_data.email || lead.contact_email,
        mobile_number: conversion_data.mobile_number || lead.contact_phone,
        address: conversion_data.address || lead.contact_address,
        created_by: req.user.id,
        ...conversion_data
      });
      break;
    
    // Add other conversion types as needed
    default:
      return next(new AppError('Invalid conversion type', 400));
  }

  // Update lead as converted
  await lead.update({
    is_converted: true,
    converted_to: convert_to,
    converted_id: convertedRecord.id,
    conversion_date: new Date()
  });

  res.status(200).json({
    status: 'success',
    data: {
      lead,
      converted_record: convertedRecord
    }
  });
});

// Get lead statistics
const getLeadStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const totalLeads = await Lead.count({
    where: { company_id: companyId }
  });

  const convertedLeads = await Lead.count({
    where: { company_id: companyId, is_converted: true }
  });

  const statusStats = await Lead.findAll({
    where: { company_id: companyId },
    include: [{
      model: LeadStatus,
      as: 'leadStatus',
      attributes: ['name', 'color_code']
    }],
    attributes: [
      'leadstatus_id',
      [Lead.sequelize.fn('COUNT', Lead.sequelize.col('Lead.id')), 'count']
    ],
    group: ['leadstatus_id', 'leadStatus.id'],
    raw: false
  });

  const sourceStats = await Lead.findAll({
    where: { company_id: companyId },
    attributes: [
      'source',
      [Lead.sequelize.fn('COUNT', Lead.sequelize.col('id')), 'count']
    ],
    group: ['source'],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_leads: totalLeads,
      converted_leads: convertedLeads,
      conversion_rate: totalLeads > 0 ? ((convertedLeads / totalLeads) * 100).toFixed(2) : 0,
      status_stats: statusStats,
      source_stats: sourceStats
    }
  });
});

module.exports = {
  getLeads,
  getLead,
  createLead,
  updateLead,
  deleteLead,
  convertLead,
  getLeadStats
};
