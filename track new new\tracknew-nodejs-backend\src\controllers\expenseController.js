const { Expense, Expense<PERSON><PERSON><PERSON><PERSON>, Customer, User, Company } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all expenses with filtering and pagination
const getExpenses = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    status,
    approval_status,
    payment_status,
    expense_category_id,
    employee_id,
    customer_id,
    is_billable,
    search,
    sort_by = 'expense_date',
    sort_order = 'DESC',
    date_from,
    date_to,
    amount_from,
    amount_to
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { company_id: req.user.company_id };

  // Apply filters
  if (status) whereClause.status = status;
  if (approval_status) whereClause.approval_status = approval_status;
  if (payment_status) whereClause.payment_status = payment_status;
  if (expense_category_id) whereClause.expense_category_id = expense_category_id;
  if (employee_id) whereClause.employee_id = employee_id;
  if (customer_id) whereClause.customer_id = customer_id;
  if (is_billable !== undefined) whereClause.is_billable = is_billable === 'true';

  if (search) {
    whereClause[Op.or] = [
      { title: { [Op.like]: `%${search}%` } },
      { expense_number: { [Op.like]: `%${search}%` } },
      { vendor_name: { [Op.like]: `%${search}%` } },
      { description: { [Op.like]: `%${search}%` } }
    ];
  }

  if (date_from || date_to) {
    whereClause.expense_date = {};
    if (date_from) whereClause.expense_date[Op.gte] = new Date(date_from);
    if (date_to) whereClause.expense_date[Op.lte] = new Date(date_to);
  }

  if (amount_from || amount_to) {
    whereClause.amount = {};
    if (amount_from) whereClause.amount[Op.gte] = parseFloat(amount_from);
    if (amount_to) whereClause.amount[Op.lte] = parseFloat(amount_to);
  }

  const { count, rows: expenses } = await Expense.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: ExpenseCategory,
        as: 'expenseCategory',
        attributes: ['id', 'name', 'color_code', 'expense_type']
      },
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'email']
      },
      {
        model: User,
        as: 'employee',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'approvedBy',
        attributes: ['id', 'name', 'email']
      }
    ],
    order: [[sort_by, sort_order.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.status(200).json({
    status: 'success',
    data: {
      expenses,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: count,
        total_pages: Math.ceil(count / limit)
      }
    }
  });
});

// Get expense by ID
const getExpense = catchAsync(async (req, res, next) => {
  const expense = await Expense.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    },
    include: [
      {
        model: ExpenseCategory,
        as: 'expenseCategory'
      },
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: User,
        as: 'employee',
        attributes: ['id', 'name', 'email', 'phone']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'approvedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!expense) {
    return next(new AppError('Expense not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: { expense }
  });
});

// Create new expense
const createExpense = catchAsync(async (req, res, next) => {
  const {
    expense_category_id,
    title,
    description,
    expense_date = new Date(),
    amount,
    currency = 'INR',
    payment_method = 'cash',
    vendor_name,
    vendor_contact,
    vendor_address,
    invoice_number,
    invoice_date,
    due_date,
    reference_number,
    customer_id,
    is_billable = false,
    billable_amount,
    markup_percentage = 0,
    employee_id,
    department,
    cost_center,
    location,
    purpose,
    attendees,
    mileage,
    mileage_rate,
    receipts,
    attachments,
    notes,
    tags
  } = req.body;

  // Generate expense number
  const expenseCount = await Expense.count({
    where: { company_id: req.user.company_id }
  });
  const expense_number = `EXP-${String(expenseCount + 1).padStart(6, '0')}`;

  // Verify expense category belongs to the same company
  const category = await ExpenseCategory.findOne({
    where: { id: expense_category_id, company_id: req.user.company_id }
  });
  if (!category) {
    return next(new AppError('Expense category not found', 404));
  }

  // Calculate base amount and balance
  const exchange_rate = 1.0000; // TODO: Implement currency conversion
  const base_amount = amount * exchange_rate;
  const balance_amount = base_amount;

  // Calculate markup if billable
  let markup_amount = 0;
  if (is_billable && markup_percentage > 0) {
    markup_amount = (amount * markup_percentage) / 100;
  }

  const expense = await Expense.create({
    company_id: req.user.company_id,
    expense_category_id,
    expense_number,
    title,
    description,
    expense_date,
    amount,
    currency,
    exchange_rate,
    base_amount,
    payment_method,
    balance_amount,
    vendor_name,
    vendor_contact,
    vendor_address,
    invoice_number,
    invoice_date,
    due_date,
    reference_number,
    customer_id,
    is_billable,
    billable_amount: billable_amount || (is_billable ? amount + markup_amount : null),
    markup_percentage,
    markup_amount,
    employee_id: employee_id || req.user.id,
    department,
    cost_center,
    location,
    purpose,
    attendees,
    mileage,
    mileage_rate,
    receipts,
    attachments,
    notes,
    tags,
    created_by: req.user.id
  });

  // Fetch created expense with associations
  const createdExpense = await Expense.findByPk(expense.id, {
    include: [
      {
        model: ExpenseCategory,
        as: 'expenseCategory',
        attributes: ['id', 'name', 'color_code']
      },
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'email']
      },
      {
        model: User,
        as: 'employee',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: { expense: createdExpense }
  });
});

// Update expense
const updateExpense = catchAsync(async (req, res, next) => {
  const expense = await Expense.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!expense) {
    return next(new AppError('Expense not found', 404));
  }

  // Check if expense can be updated
  if (expense.status === 'paid') {
    return next(new AppError('Cannot update paid expense', 400));
  }

  const allowedUpdates = [
    'expense_category_id', 'title', 'description', 'expense_date', 'amount',
    'currency', 'payment_method', 'vendor_name', 'vendor_contact', 'vendor_address',
    'invoice_number', 'invoice_date', 'due_date', 'reference_number', 'customer_id',
    'is_billable', 'billable_amount', 'markup_percentage', 'employee_id',
    'department', 'cost_center', 'location', 'purpose', 'attendees', 'mileage',
    'mileage_rate', 'receipts', 'attachments', 'notes', 'tags', 'status',
    'approval_status', 'rejection_reason'
  ];

  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  // Recalculate amounts if amount or markup changed
  if (updates.amount || updates.markup_percentage) {
    const newAmount = updates.amount || expense.amount;
    const newMarkupPercentage = updates.markup_percentage || expense.markup_percentage;
    
    updates.base_amount = newAmount * (updates.exchange_rate || expense.exchange_rate);
    updates.balance_amount = updates.base_amount;
    
    if (expense.is_billable && newMarkupPercentage > 0) {
      updates.markup_amount = (newAmount * newMarkupPercentage) / 100;
      updates.billable_amount = newAmount + updates.markup_amount;
    }
  }

  await expense.update(updates);

  // Fetch updated expense with associations
  const updatedExpense = await Expense.findByPk(expense.id, {
    include: [
      {
        model: ExpenseCategory,
        as: 'expenseCategory',
        attributes: ['id', 'name', 'color_code']
      },
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'email']
      },
      {
        model: User,
        as: 'employee',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: { expense: updatedExpense }
  });
});

// Approve/Reject expense
const approveExpense = catchAsync(async (req, res, next) => {
  const { approval_status, approved_amount, rejection_reason } = req.body;
  
  const expense = await Expense.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!expense) {
    return next(new AppError('Expense not found', 404));
  }

  if (expense.approval_status !== 'pending') {
    return next(new AppError('Expense is already processed', 400));
  }

  const updates = {
    approval_status,
    approved_by: req.user.id,
    approved_at: new Date()
  };

  if (approval_status === 'approved') {
    updates.approved_amount = approved_amount || expense.amount;
    updates.status = 'approved';
  } else if (approval_status === 'rejected') {
    updates.rejection_reason = rejection_reason;
    updates.status = 'rejected';
  }

  await expense.update(updates);

  res.status(200).json({
    status: 'success',
    data: { expense }
  });
});

// Delete expense
const deleteExpense = catchAsync(async (req, res, next) => {
  const expense = await Expense.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!expense) {
    return next(new AppError('Expense not found', 404));
  }

  if (expense.status === 'paid') {
    return next(new AppError('Cannot delete paid expense', 400));
  }

  await expense.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Get expense statistics
const getExpenseStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const totalExpenses = await Expense.sum('amount', {
    where: { company_id: companyId }
  });

  const pendingApproval = await Expense.count({
    where: { company_id: companyId, approval_status: 'pending' }
  });

  const statusStats = await Expense.findAll({
    where: { company_id: companyId },
    attributes: [
      'status',
      [Expense.sequelize.fn('COUNT', Expense.sequelize.col('id')), 'count'],
      [Expense.sequelize.fn('SUM', Expense.sequelize.col('amount')), 'total_amount']
    ],
    group: ['status'],
    raw: true
  });

  const categoryStats = await Expense.findAll({
    where: { company_id: companyId },
    include: [{
      model: ExpenseCategory,
      as: 'expenseCategory',
      attributes: ['name', 'color_code']
    }],
    attributes: [
      'expense_category_id',
      [Expense.sequelize.fn('COUNT', Expense.sequelize.col('Expense.id')), 'count'],
      [Expense.sequelize.fn('SUM', Expense.sequelize.col('amount')), 'total_amount']
    ],
    group: ['expense_category_id', 'expenseCategory.id'],
    raw: false
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_expenses: totalExpenses || 0,
      pending_approval: pendingApproval,
      status_stats: statusStats,
      category_stats: categoryStats
    }
  });
});

module.exports = {
  getExpenses,
  getExpense,
  createExpense,
  updateExpense,
  approveExpense,
  deleteExpense,
  getExpenseStats
};
