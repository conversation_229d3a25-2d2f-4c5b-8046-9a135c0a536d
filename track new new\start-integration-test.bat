@echo off
echo 🚀 Starting Phase 4 Integration Testing
echo.

echo 📦 Installing backend dependencies...
cd "tracknew-nodejs-backend"
call npm install
if %errorlevel% neq 0 (
    echo ❌ Backend dependency installation failed
    pause
    exit /b 1
)

echo.
echo 🗄️ Setting up database...
echo Please ensure PostgreSQL is running and create database 'tracknew_development'
echo.

echo 🔄 Starting backend server...
start "Backend Server" cmd /k "npm run dev"

echo.
echo ⏳ Waiting for backend to start...
timeout /t 10 /nobreak > nul

echo.
echo 📦 Installing frontend dependencies...
cd "..\tracknew-react-frontend"
call npm install
if %errorlevel% neq 0 (
    echo ❌ Frontend dependency installation failed
    pause
    exit /b 1
)

echo.
echo 🎨 Starting frontend development server...
start "Frontend Server" cmd /k "npm start"

echo.
echo ✅ Both servers are starting!
echo.
echo 📋 Next steps:
echo 1. Wait for both servers to fully start
echo 2. Backend will be available at: http://localhost:8000
echo 3. Frontend will be available at: http://localhost:3000
echo 4. Navigate to: http://localhost:3000/integration-test
echo 5. Run the integration tests
echo.
echo 🔧 If you encounter issues:
echo - Ensure PostgreSQL is running
echo - Check that ports 8000 and 3000 are available
echo - Review the console output in both server windows
echo.
pause
