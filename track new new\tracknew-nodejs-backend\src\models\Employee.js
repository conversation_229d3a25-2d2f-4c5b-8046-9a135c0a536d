const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Employee = sequelize.define('Employee', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Associated user account (if employee has system access)'
  },
  employee_id: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Company employee ID'
  },
  first_name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  last_name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  full_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  mobile: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  emergency_contact: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  emergency_contact_name: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  emergency_contact_relation: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  date_of_birth: {
    type: DataTypes.DATE,
    allowNull: true
  },
  gender: {
    type: DataTypes.ENUM('male', 'female', 'other', 'prefer_not_to_say'),
    allowNull: true
  },
  marital_status: {
    type: DataTypes.ENUM('single', 'married', 'divorced', 'widowed', 'other'),
    allowNull: true
  },
  nationality: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  city: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  state: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  postal_code: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  country: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  department: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  designation: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  job_title: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  employment_type: {
    type: DataTypes.ENUM('full_time', 'part_time', 'contract', 'temporary', 'intern', 'consultant'),
    defaultValue: 'full_time'
  },
  employment_status: {
    type: DataTypes.ENUM('active', 'inactive', 'terminated', 'resigned', 'retired', 'on_leave'),
    defaultValue: 'active'
  },
  hire_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  probation_end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  confirmation_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  termination_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  termination_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  reporting_manager_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'employees',
      key: 'id'
    }
  },
  salary: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  salary_currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'INR'
  },
  salary_frequency: {
    type: DataTypes.ENUM('hourly', 'daily', 'weekly', 'monthly', 'yearly'),
    defaultValue: 'monthly'
  },
  bank_name: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  bank_account_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  bank_ifsc_code: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  pan_number: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  aadhar_number: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  passport_number: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  driving_license: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  skills: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of employee skills'
  },
  certifications: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of certifications'
  },
  education: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of education details'
  },
  experience: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of work experience'
  },
  languages: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of known languages'
  },
  profile_photo: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  documents: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of document file paths'
  },
  work_location: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  work_shift: {
    type: DataTypes.ENUM('day', 'night', 'rotating', 'flexible'),
    defaultValue: 'day'
  },
  work_hours_per_week: {
    type: DataTypes.INTEGER,
    defaultValue: 40
  },
  overtime_eligible: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  remote_work_allowed: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  travel_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  performance_rating: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    comment: 'Latest performance rating (1-5)'
  },
  last_review_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  next_review_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  leave_balance: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of leave balances by type'
  },
  benefits: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of employee benefits'
  },
  access_level: {
    type: DataTypes.ENUM('basic', 'standard', 'advanced', 'admin'),
    defaultValue: 'basic'
  },
  can_login: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether employee can login to system'
  },
  login_allowed_from: {
    type: DataTypes.DATE,
    allowNull: true
  },
  login_allowed_until: {
    type: DataTypes.DATE,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  internal_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Internal HR notes'
  },
  tags: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Comma-separated tags'
  },
  custom_fields: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object for custom field values'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'employees',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['company_id', 'employee_id'],
      unique: true
    },
    {
      fields: ['email']
    },
    {
      fields: ['phone']
    },
    {
      fields: ['mobile']
    },
    {
      fields: ['department']
    },
    {
      fields: ['designation']
    },
    {
      fields: ['employment_type']
    },
    {
      fields: ['employment_status']
    },
    {
      fields: ['reporting_manager_id']
    },
    {
      fields: ['hire_date']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['can_login']
    },
    {
      fields: ['full_name']
    }
  ]
});

module.exports = Employee;
