const express = require('express');
const { body, query } = require('express-validator');
const documentController = require('../controllers/documentController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation for document upload
const uploadDocumentValidation = [
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('category')
    .optional()
    .isIn([
      'general', 
      'invoices', 
      'contracts', 
      'certificates', 
      'reports', 
      'images', 
      'presentations', 
      'spreadsheets', 
      'legal', 
      'hr'
    ])
    .withMessage('Invalid document category'),
  
  body('tags')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Tags must be maximum 500 characters'),
  
  body('is_public')
    .optional()
    .isBoolean()
    .withMessage('Is public must be a boolean'),
  
  body('related_type')
    .optional()
    .isIn(['customer', 'supplier', 'product', 'service', 'sales', 'invoice', 'expense'])
    .withMessage('Invalid related type'),
  
  body('related_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Related ID must be a positive integer')
];

// Validation for bulk operations
const bulkDeleteValidation = [
  body('document_ids')
    .isArray({ min: 1 })
    .withMessage('Document IDs array is required and must not be empty'),
  
  body('document_ids.*')
    .isInt({ min: 1 })
    .withMessage('Each document ID must be a positive integer')
];

// Validation for query parameters
const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  query('category')
    .optional()
    .isIn([
      'general', 
      'invoices', 
      'contracts', 
      'certificates', 
      'reports', 
      'images', 
      'presentations', 
      'spreadsheets', 
      'legal', 
      'hr'
    ])
    .withMessage('Invalid document category'),
  
  query('file_type')
    .optional()
    .isLength({ max: 100 })
    .withMessage('File type must be maximum 100 characters'),
  
  query('uploaded_by')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Uploaded by must be a positive integer'),
  
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid date'),
  
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid date'),
  
  query('sort_by')
    .optional()
    .isIn(['created_at', 'original_name', 'file_size', 'category'])
    .withMessage('Invalid sort field'),
  
  query('sort_order')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC')
];

// Routes
router
  .route('/')
  .get(queryValidation, validateRequest, documentController.getDocuments)
  .post(uploadDocumentValidation, validateRequest, documentController.uploadDocument);

router
  .route('/categories')
  .get(documentController.getDocumentCategories);

router
  .route('/stats')
  .get(documentController.getDocumentStats);

router
  .route('/bulk-delete')
  .delete(bulkDeleteValidation, validateRequest, restrictTo('admin', 'sub_admin'), documentController.bulkDeleteDocuments);

router
  .route('/download/:filename')
  .get(documentController.downloadDocument);

router
  .route('/:id')
  .delete(restrictTo('admin', 'sub_admin', 'manager'), documentController.deleteDocument);

module.exports = router;
