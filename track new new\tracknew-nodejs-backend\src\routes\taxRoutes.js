const express = require('express');
const { body } = require('express-validator');
const taxController = require('../controllers/taxController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating tax
const createTaxValidation = [
  body('tax_name')
    .notEmpty()
    .withMessage('Tax name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Tax name must be between 2 and 100 characters'),
  
  body('tax_code')
    .notEmpty()
    .withMessage('Tax code is required')
    .isLength({ min: 1, max: 20 })
    .withMessage('Tax code must be between 1 and 20 characters'),
  
  body('tax_type')
    .notEmpty()
    .withMessage('Tax type is required')
    .isIn(['gst', 'vat', 'service_tax', 'excise', 'customs', 'cess', 'other'])
    .withMessage('Invalid tax type'),
  
  body('tax_rate')
    .notEmpty()
    .withMessage('Tax rate is required')
    .isFloat({ min: 0, max: 100 })
    .withMessage('Tax rate must be between 0 and 100'),
  
  body('cgst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('CGST rate must be between 0 and 100'),
  
  body('sgst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('SGST rate must be between 0 and 100'),
  
  body('igst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('IGST rate must be between 0 and 100'),
  
  body('cess_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('CESS rate must be between 0 and 100'),
  
  body('is_compound')
    .optional()
    .isBoolean()
    .withMessage('is_compound must be a boolean'),
  
  body('is_inclusive')
    .optional()
    .isBoolean()
    .withMessage('is_inclusive must be a boolean'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  
  body('effective_from')
    .optional()
    .isISO8601()
    .withMessage('Effective from must be a valid date'),
  
  body('effective_to')
    .optional()
    .isISO8601()
    .withMessage('Effective to must be a valid date'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters')
];

// Validation rules for updating tax
const updateTaxValidation = [
  body('tax_name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Tax name must be between 2 and 100 characters'),
  
  body('tax_code')
    .optional()
    .isLength({ min: 1, max: 20 })
    .withMessage('Tax code must be between 1 and 20 characters'),
  
  body('tax_type')
    .optional()
    .isIn(['gst', 'vat', 'service_tax', 'excise', 'customs', 'cess', 'other'])
    .withMessage('Invalid tax type'),
  
  body('tax_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Tax rate must be between 0 and 100'),
  
  body('cgst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('CGST rate must be between 0 and 100'),
  
  body('sgst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('SGST rate must be between 0 and 100'),
  
  body('igst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('IGST rate must be between 0 and 100'),
  
  body('cess_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('CESS rate must be between 0 and 100'),
  
  body('is_compound')
    .optional()
    .isBoolean()
    .withMessage('is_compound must be a boolean'),
  
  body('is_inclusive')
    .optional()
    .isBoolean()
    .withMessage('is_inclusive must be a boolean'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  
  body('effective_from')
    .optional()
    .isISO8601()
    .withMessage('Effective from must be a valid date'),
  
  body('effective_to')
    .optional()
    .isISO8601()
    .withMessage('Effective to must be a valid date'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters')
];

// Validation for tax calculation
const calculateTaxValidation = [
  body('tax_id')
    .notEmpty()
    .withMessage('Tax ID is required')
    .isInt({ min: 1 })
    .withMessage('Tax ID must be a positive integer'),
  
  body('amount')
    .notEmpty()
    .withMessage('Amount is required')
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number'),
  
  body('is_inclusive')
    .optional()
    .isBoolean()
    .withMessage('is_inclusive must be a boolean')
];

// Routes
router
  .route('/')
  .get(taxController.getTaxes)
  .post(createTaxValidation, validateRequest, taxController.createTax);

router
  .route('/stats')
  .get(taxController.getTaxStats);

router
  .route('/active')
  .get(taxController.getActiveTaxes);

router
  .route('/calculate')
  .post(calculateTaxValidation, validateRequest, taxController.calculateTax);

router
  .route('/by-type/:tax_type')
  .get(taxController.getTaxesByType);

router
  .route('/:id')
  .get(taxController.getTax)
  .put(updateTaxValidation, validateRequest, taxController.updateTax)
  .delete(restrictTo('admin', 'sub_admin'), taxController.deleteTax);

module.exports = router;
