const express = require('express');
const { body, query } = require('express-validator');
const auditLogController = require('../controllers/auditLogController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation for creating audit log
const createAuditLogValidation = [
  body('action')
    .notEmpty()
    .withMessage('Action is required')
    .isIn(['create', 'read', 'update', 'delete', 'login', 'logout', 'export', 'import', 'approve', 'reject'])
    .withMessage('Invalid action'),
  
  body('entity_type')
    .notEmpty()
    .withMessage('Entity type is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Entity type must be between 1 and 100 characters'),
  
  body('entity_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Entity ID must be a positive integer'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('old_values')
    .optional()
    .isObject()
    .withMessage('Old values must be a valid JSON object'),
  
  body('new_values')
    .optional()
    .isObject()
    .withMessage('New values must be a valid JSON object'),
  
  body('metadata')
    .optional()
    .isObject()
    .withMessage('Metadata must be a valid JSON object')
];

// Validation for query parameters
const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  query('action')
    .optional()
    .isIn(['create', 'read', 'update', 'delete', 'login', 'logout', 'export', 'import', 'approve', 'reject'])
    .withMessage('Invalid action'),
  
  query('entity_type')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Entity type must be maximum 100 characters'),
  
  query('user_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('User ID must be a positive integer'),
  
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid date'),
  
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid date'),
  
  query('sort_by')
    .optional()
    .isIn(['created_at', 'action', 'entity_type', 'user_id'])
    .withMessage('Invalid sort field'),
  
  query('sort_order')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC')
];

// Validation for export
const exportValidation = [
  query('format')
    .optional()
    .isIn(['csv', 'json', 'xlsx'])
    .withMessage('Export format must be csv, json, or xlsx'),
  
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid date'),
  
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid date')
];

// Validation for cleanup
const cleanupValidation = [
  body('retention_days')
    .optional()
    .isInt({ min: 30, max: 2555 })
    .withMessage('Retention days must be between 30 and 2555 (7 years)')
];

// Routes
router
  .route('/')
  .get(queryValidation, validateRequest, restrictTo('admin', 'sub_admin'), auditLogController.getAuditLogs)
  .post(createAuditLogValidation, validateRequest, restrictTo('admin', 'sub_admin'), auditLogController.createAuditLog);

router
  .route('/stats')
  .get(restrictTo('admin', 'sub_admin'), auditLogController.getAuditLogStats);

router
  .route('/export')
  .get(exportValidation, validateRequest, restrictTo('admin', 'sub_admin'), auditLogController.exportAuditLogs);

router
  .route('/cleanup')
  .post(cleanupValidation, validateRequest, restrictTo('admin'), auditLogController.cleanupAuditLogs);

router
  .route('/user/:user_id/activity')
  .get(restrictTo('admin', 'sub_admin'), auditLogController.getUserActivity);

router
  .route('/entity/:entity_type/:entity_id/history')
  .get(restrictTo('admin', 'sub_admin'), auditLogController.getEntityHistory);

router
  .route('/:id')
  .get(restrictTo('admin', 'sub_admin'), auditLogController.getAuditLog);

module.exports = router;
