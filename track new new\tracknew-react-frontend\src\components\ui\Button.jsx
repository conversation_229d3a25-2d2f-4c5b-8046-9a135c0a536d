import React, { forwardRef } from 'react';
import { classNames } from '../../utils/helpers';
import LoadingSpinner from './LoadingSpinner';

const Button = forwardRef(({
  children,
  type = 'button',
  variant = 'primary',
  size = 'md',
  color = 'blue',
  loading = false,
  disabled = false,
  fullWidth = false,
  leftIcon: LeftIcon,
  rightIcon: RightIcon,
  loadingText,
  onClick,
  className = '',
  ...props
}, ref) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  const sizeClasses = {
    xs: 'px-2.5 py-1.5 text-xs',
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-4 py-2 text-base',
    xl: 'px-6 py-3 text-base',
  };

  const variantClasses = {
    primary: {
      blue: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 dark:bg-blue-600 dark:hover:bg-blue-700',
      green: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 dark:bg-green-600 dark:hover:bg-green-700',
      red: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 dark:bg-red-600 dark:hover:bg-red-700',
      yellow: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 dark:bg-yellow-600 dark:hover:bg-yellow-700',
      purple: 'bg-purple-600 text-white hover:bg-purple-700 focus:ring-purple-500 dark:bg-purple-600 dark:hover:bg-purple-700',
      pink: 'bg-pink-600 text-white hover:bg-pink-700 focus:ring-pink-500 dark:bg-pink-600 dark:hover:bg-pink-700',
      indigo: 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500 dark:bg-indigo-600 dark:hover:bg-indigo-700',
      gray: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 dark:bg-gray-600 dark:hover:bg-gray-700',
    },
    secondary: {
      blue: 'bg-blue-100 text-blue-700 hover:bg-blue-200 focus:ring-blue-500 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800',
      green: 'bg-green-100 text-green-700 hover:bg-green-200 focus:ring-green-500 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800',
      red: 'bg-red-100 text-red-700 hover:bg-red-200 focus:ring-red-500 dark:bg-red-900 dark:text-red-300 dark:hover:bg-red-800',
      yellow: 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200 focus:ring-yellow-500 dark:bg-yellow-900 dark:text-yellow-300 dark:hover:bg-yellow-800',
      purple: 'bg-purple-100 text-purple-700 hover:bg-purple-200 focus:ring-purple-500 dark:bg-purple-900 dark:text-purple-300 dark:hover:bg-purple-800',
      pink: 'bg-pink-100 text-pink-700 hover:bg-pink-200 focus:ring-pink-500 dark:bg-pink-900 dark:text-pink-300 dark:hover:bg-pink-800',
      indigo: 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200 focus:ring-indigo-500 dark:bg-indigo-900 dark:text-indigo-300 dark:hover:bg-indigo-800',
      gray: 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600',
    },
    outline: {
      blue: 'border border-blue-300 text-blue-700 hover:bg-blue-50 focus:ring-blue-500 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900',
      green: 'border border-green-300 text-green-700 hover:bg-green-50 focus:ring-green-500 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900',
      red: 'border border-red-300 text-red-700 hover:bg-red-50 focus:ring-red-500 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900',
      yellow: 'border border-yellow-300 text-yellow-700 hover:bg-yellow-50 focus:ring-yellow-500 dark:border-yellow-600 dark:text-yellow-400 dark:hover:bg-yellow-900',
      purple: 'border border-purple-300 text-purple-700 hover:bg-purple-50 focus:ring-purple-500 dark:border-purple-600 dark:text-purple-400 dark:hover:bg-purple-900',
      pink: 'border border-pink-300 text-pink-700 hover:bg-pink-50 focus:ring-pink-500 dark:border-pink-600 dark:text-pink-400 dark:hover:bg-pink-900',
      indigo: 'border border-indigo-300 text-indigo-700 hover:bg-indigo-50 focus:ring-indigo-500 dark:border-indigo-600 dark:text-indigo-400 dark:hover:bg-indigo-900',
      gray: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700',
    },
    ghost: {
      blue: 'text-blue-700 hover:bg-blue-100 focus:ring-blue-500 dark:text-blue-400 dark:hover:bg-blue-900',
      green: 'text-green-700 hover:bg-green-100 focus:ring-green-500 dark:text-green-400 dark:hover:bg-green-900',
      red: 'text-red-700 hover:bg-red-100 focus:ring-red-500 dark:text-red-400 dark:hover:bg-red-900',
      yellow: 'text-yellow-700 hover:bg-yellow-100 focus:ring-yellow-500 dark:text-yellow-400 dark:hover:bg-yellow-900',
      purple: 'text-purple-700 hover:bg-purple-100 focus:ring-purple-500 dark:text-purple-400 dark:hover:bg-purple-900',
      pink: 'text-pink-700 hover:bg-pink-100 focus:ring-pink-500 dark:text-pink-400 dark:hover:bg-pink-900',
      indigo: 'text-indigo-700 hover:bg-indigo-100 focus:ring-indigo-500 dark:text-indigo-400 dark:hover:bg-indigo-900',
      gray: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-700',
    },
    link: {
      blue: 'text-blue-600 hover:text-blue-500 underline-offset-4 hover:underline focus:ring-blue-500 dark:text-blue-400 dark:hover:text-blue-300',
      green: 'text-green-600 hover:text-green-500 underline-offset-4 hover:underline focus:ring-green-500 dark:text-green-400 dark:hover:text-green-300',
      red: 'text-red-600 hover:text-red-500 underline-offset-4 hover:underline focus:ring-red-500 dark:text-red-400 dark:hover:text-red-300',
      yellow: 'text-yellow-600 hover:text-yellow-500 underline-offset-4 hover:underline focus:ring-yellow-500 dark:text-yellow-400 dark:hover:text-yellow-300',
      purple: 'text-purple-600 hover:text-purple-500 underline-offset-4 hover:underline focus:ring-purple-500 dark:text-purple-400 dark:hover:text-purple-300',
      pink: 'text-pink-600 hover:text-pink-500 underline-offset-4 hover:underline focus:ring-pink-500 dark:text-pink-400 dark:hover:text-pink-300',
      indigo: 'text-indigo-600 hover:text-indigo-500 underline-offset-4 hover:underline focus:ring-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300',
      gray: 'text-gray-600 hover:text-gray-500 underline-offset-4 hover:underline focus:ring-gray-500 dark:text-gray-400 dark:hover:text-gray-300',
    },
  };

  const iconSizeClasses = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
    xl: 'h-5 w-5',
  };

  const isDisabled = disabled || loading;

  const buttonClasses = classNames(
    baseClasses,
    sizeClasses[size],
    variantClasses[variant][color],
    fullWidth && 'w-full',
    className
  );

  const handleClick = (e) => {
    if (!isDisabled && onClick) {
      onClick(e);
    }
  };

  return (
    <button
      ref={ref}
      type={type}
      className={buttonClasses}
      disabled={isDisabled}
      onClick={handleClick}
      {...props}
    >
      {loading ? (
        <>
          <LoadingSpinner 
            size={size === 'xs' ? 'xs' : size === 'sm' ? 'sm' : 'sm'} 
            color="white" 
            className="mr-2" 
          />
          {loadingText || 'Loading...'}
        </>
      ) : (
        <>
          {LeftIcon && (
            <LeftIcon className={classNames(
              iconSizeClasses[size],
              children && 'mr-2'
            )} />
          )}
          {children}
          {RightIcon && (
            <RightIcon className={classNames(
              iconSizeClasses[size],
              children && 'ml-2'
            )} />
          )}
        </>
      )}
    </button>
  );
});

Button.displayName = 'Button';

export default Button;
