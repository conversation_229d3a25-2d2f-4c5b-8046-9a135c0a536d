const { ServiceCategory, User, Company, Service } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all service categories with filtering and pagination
const getServiceCategories = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    parent_id,
    is_active = true,
    requires_approval,
    auto_assign,
    sort_by = 'name',
    sort_order = 'ASC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add filters
  if (parent_id !== undefined) {
    whereConditions.parent_id = parent_id === 'null' ? null : parent_id;
  }

  if (is_active !== undefined) {
    whereConditions.is_active = is_active === 'true';
  }

  if (requires_approval !== undefined) {
    whereConditions.requires_approval = requires_approval === 'true';
  }

  if (auto_assign !== undefined) {
    whereConditions.auto_assign = auto_assign === 'true';
  }

  // Get service categories with associations
  const { count, rows: serviceCategories } = await ServiceCategory.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: ServiceCategory,
        as: 'parent',
        attributes: ['id', 'name']
      },
      {
        model: ServiceCategory,
        as: 'children',
        attributes: ['id', 'name', 'is_active'],
        required: false
      },
      {
        model: Service,
        as: 'services',
        attributes: [],
        required: false
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ],
    attributes: {
      include: [
        [ServiceCategory.sequelize.fn('COUNT', ServiceCategory.sequelize.col('services.id')), 'service_count']
      ]
    },
    limit: parseInt(limit),
    offset: offset,
    order: [[sort_by, sort_order.toUpperCase()]],
    group: ['ServiceCategory.id', 'parent.id', 'children.id', 'createdBy.id', 'updatedBy.id'],
    distinct: true
  });

  // Calculate pagination
  const totalPages = Math.ceil(count.length / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      service_categories: serviceCategories,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count.length,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single service category by ID
const getServiceCategory = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const serviceCategory = await ServiceCategory.findOne({
    where: {
      id,
      company_id: companyId
    },
    include: [
      {
        model: ServiceCategory,
        as: 'parent',
        attributes: ['id', 'name', 'description']
      },
      {
        model: ServiceCategory,
        as: 'children',
        attributes: ['id', 'name', 'description', 'is_active', 'sort_order'],
        order: [['sort_order', 'ASC'], ['name', 'ASC']]
      },
      {
        model: Service,
        as: 'services',
        attributes: ['id', 'service_name', 'service_status', 'created_at'],
        limit: 10,
        order: [['created_at', 'DESC']]
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!serviceCategory) {
    return next(new AppError('Service category not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      service_category: serviceCategory
    }
  });
});

// Create new service category
const createServiceCategory = catchAsync(async (req, res, next) => {
  const {
    name,
    description,
    parent_id,
    color_code,
    icon,
    estimated_duration,
    default_price,
    requires_approval = false,
    auto_assign = false,
    sla_hours,
    sort_order = 0,
    is_active = true
  } = req.body;

  const companyId = req.user.company_id;

  // Check for duplicate name within company
  const existingCategory = await ServiceCategory.findOne({
    where: {
      company_id: companyId,
      name: name
    }
  });

  if (existingCategory) {
    return next(new AppError('Service category with this name already exists', 400));
  }

  // Validate parent category if provided
  if (parent_id) {
    const parentCategory = await ServiceCategory.findOne({
      where: {
        id: parent_id,
        company_id: companyId,
        is_active: true
      }
    });

    if (!parentCategory) {
      return next(new AppError('Parent category not found or inactive', 400));
    }
  }

  const serviceCategory = await ServiceCategory.create({
    company_id: companyId,
    name,
    description,
    parent_id,
    color_code,
    icon,
    estimated_duration,
    default_price,
    requires_approval,
    auto_assign,
    sla_hours,
    sort_order,
    is_active,
    created_by: req.user.id
  });

  // Fetch the created service category with associations
  const createdServiceCategory = await ServiceCategory.findByPk(serviceCategory.id, {
    include: [
      {
        model: ServiceCategory,
        as: 'parent',
        attributes: ['id', 'name']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      service_category: createdServiceCategory
    }
  });
});

// Update service category
const updateServiceCategory = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const serviceCategory = await ServiceCategory.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!serviceCategory) {
    return next(new AppError('Service category not found', 404));
  }

  // Check for duplicate name (excluding current category)
  if (req.body.name) {
    const existingCategory = await ServiceCategory.findOne({
      where: {
        company_id: companyId,
        id: { [Op.ne]: id },
        name: req.body.name
      }
    });

    if (existingCategory) {
      return next(new AppError('Service category with this name already exists', 400));
    }
  }

  // Validate parent category if provided
  if (req.body.parent_id) {
    // Cannot set self as parent
    if (req.body.parent_id === parseInt(id)) {
      return next(new AppError('Category cannot be its own parent', 400));
    }

    const parentCategory = await ServiceCategory.findOne({
      where: {
        id: req.body.parent_id,
        company_id: companyId,
        is_active: true
      }
    });

    if (!parentCategory) {
      return next(new AppError('Parent category not found or inactive', 400));
    }
  }

  // Update service category
  await serviceCategory.update({
    ...req.body,
    updated_by: req.user.id
  });

  // Fetch updated service category with associations
  const updatedServiceCategory = await ServiceCategory.findByPk(id, {
    include: [
      {
        model: ServiceCategory,
        as: 'parent',
        attributes: ['id', 'name']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      service_category: updatedServiceCategory
    }
  });
});

// Delete service category
const deleteServiceCategory = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const serviceCategory = await ServiceCategory.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!serviceCategory) {
    return next(new AppError('Service category not found', 404));
  }

  // Check if category has child categories
  const childCount = await ServiceCategory.count({
    where: {
      parent_id: id,
      company_id: companyId
    }
  });

  if (childCount > 0) {
    return next(new AppError(`Cannot delete category. It has ${childCount} sub-categories. Please delete or reassign them first.`, 400));
  }

  // Check if category has services
  const serviceCount = await Service.count({
    where: {
      service_category_id: id,
      company_id: companyId
    }
  });

  if (serviceCount > 0) {
    return next(new AppError(`Cannot delete category. It has ${serviceCount} service(s). Please reassign them first.`, 400));
  }

  await serviceCategory.destroy();

  res.status(200).json({
    status: 'success',
    message: 'Service category deleted successfully'
  });
});

// Get service category statistics
const getServiceCategoryStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  // Total categories count
  const totalCategories = await ServiceCategory.count({
    where: { company_id: companyId }
  });

  // Active categories count
  const activeCategories = await ServiceCategory.count({
    where: {
      company_id: companyId,
      is_active: true
    }
  });

  // Categories with services
  const categoriesWithServices = await ServiceCategory.count({
    where: { company_id: companyId },
    include: [{
      model: Service,
      as: 'services',
      required: true
    }]
  });

  // Categories requiring approval
  const approvalCategories = await ServiceCategory.count({
    where: {
      company_id: companyId,
      requires_approval: true
    }
  });

  // Auto-assign categories
  const autoAssignCategories = await ServiceCategory.count({
    where: {
      company_id: companyId,
      auto_assign: true
    }
  });

  // Most used categories
  const mostUsedCategories = await ServiceCategory.findAll({
    where: { company_id: companyId },
    include: [{
      model: Service,
      as: 'services',
      attributes: []
    }],
    attributes: [
      'id',
      'name',
      'color_code',
      'icon',
      [ServiceCategory.sequelize.fn('COUNT', ServiceCategory.sequelize.col('services.id')), 'service_count']
    ],
    group: ['ServiceCategory.id'],
    order: [[ServiceCategory.sequelize.fn('COUNT', ServiceCategory.sequelize.col('services.id')), 'DESC']],
    limit: 5,
    raw: false
  });

  // Average estimated duration
  const avgDuration = await ServiceCategory.findOne({
    where: {
      company_id: companyId,
      estimated_duration: { [Op.ne]: null }
    },
    attributes: [
      [ServiceCategory.sequelize.fn('AVG', ServiceCategory.sequelize.col('estimated_duration')), 'avg_duration']
    ],
    raw: true
  });

  // Average default price
  const avgPrice = await ServiceCategory.findOne({
    where: {
      company_id: companyId,
      default_price: { [Op.ne]: null }
    },
    attributes: [
      [ServiceCategory.sequelize.fn('AVG', ServiceCategory.sequelize.col('default_price')), 'avg_price']
    ],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_categories: totalCategories,
      active_categories: activeCategories,
      inactive_categories: totalCategories - activeCategories,
      categories_with_services: categoriesWithServices,
      approval_categories: approvalCategories,
      auto_assign_categories: autoAssignCategories,
      most_used_categories: mostUsedCategories,
      average_duration: Math.round(avgDuration?.avg_duration || 0),
      average_price: parseFloat(avgPrice?.avg_price || 0).toFixed(2)
    }
  });
});

// Get service category tree
const getServiceCategoryTree = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const categories = await ServiceCategory.findAll({
    where: {
      company_id: companyId,
      parent_id: null,
      is_active: true
    },
    include: [
      {
        model: ServiceCategory,
        as: 'children',
        where: { is_active: true },
        required: false,
        include: [
          {
            model: ServiceCategory,
            as: 'children',
            where: { is_active: true },
            required: false
          }
        ]
      }
    ],
    order: [
      ['sort_order', 'ASC'],
      ['name', 'ASC'],
      [{ model: ServiceCategory, as: 'children' }, 'sort_order', 'ASC'],
      [{ model: ServiceCategory, as: 'children' }, 'name', 'ASC']
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      categories
    }
  });
});

// Get active service categories (for dropdowns)
const getActiveServiceCategories = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const categories = await ServiceCategory.findAll({
    where: {
      company_id: companyId,
      is_active: true
    },
    attributes: ['id', 'name', 'color_code', 'icon', 'estimated_duration', 'default_price', 'requires_approval', 'auto_assign'],
    order: [['sort_order', 'ASC'], ['name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      categories
    }
  });
});

// Get categories by parent
const getCategoriesByParent = catchAsync(async (req, res) => {
  const { parent_id } = req.params;
  const companyId = req.user.company_id;

  const categories = await ServiceCategory.findAll({
    where: {
      company_id: companyId,
      parent_id: parent_id === 'null' ? null : parent_id,
      is_active: true
    },
    attributes: ['id', 'name', 'description', 'color_code', 'icon', 'sort_order'],
    order: [['sort_order', 'ASC'], ['name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      categories,
      parent_id: parent_id === 'null' ? null : parent_id
    }
  });
});

// Bulk update sort order
const updateSortOrder = catchAsync(async (req, res, next) => {
  const { categories } = req.body;
  const companyId = req.user.company_id;

  if (!Array.isArray(categories) || categories.length === 0) {
    return next(new AppError('Categories array is required', 400));
  }

  // Validate all categories belong to the company
  const categoryIds = categories.map(cat => cat.id);
  const validCategories = await ServiceCategory.findAll({
    where: {
      id: { [Op.in]: categoryIds },
      company_id: companyId
    },
    attributes: ['id']
  });

  if (validCategories.length !== categoryIds.length) {
    return next(new AppError('Some categories not found or do not belong to your company', 400));
  }

  // Update sort orders
  const updatePromises = categories.map(cat =>
    ServiceCategory.update(
      {
        sort_order: cat.sort_order,
        updated_by: req.user.id
      },
      {
        where: {
          id: cat.id,
          company_id: companyId
        }
      }
    )
  );

  await Promise.all(updatePromises);

  res.status(200).json({
    status: 'success',
    message: 'Sort order updated successfully'
  });
});

module.exports = {
  getServiceCategories,
  getServiceCategory,
  createServiceCategory,
  updateServiceCategory,
  deleteServiceCategory,
  getServiceCategoryStats,
  getServiceCategoryTree,
  getActiveServiceCategories,
  getCategoriesByParent,
  updateSortOrder
};
