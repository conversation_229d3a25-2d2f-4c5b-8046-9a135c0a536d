const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Reminder = sequelize.define('Reminder', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  reminder_type: {
    type: DataTypes.ENUM('task', 'appointment', 'follow_up', 'payment', 'service', 'amc', 'birthday', 'anniversary', 'custom'),
    defaultValue: 'task'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  status: {
    type: DataTypes.ENUM('pending', 'sent', 'completed', 'cancelled', 'snoozed'),
    defaultValue: 'pending'
  },
  remind_at: {
    type: DataTypes.DATE,
    allowNull: false
  },
  completed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  snoozed_until: {
    type: DataTypes.DATE,
    allowNull: true
  },
  related_type: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Type of related entity (customer, service, sales, etc.)'
  },
  related_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'ID of related entity'
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  assigned_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User assigned to handle this reminder'
  },
  notification_methods: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of notification methods (email, sms, push, etc.)'
  },
  advance_notifications: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of advance notification settings'
  },
  is_recurring: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  recurrence_pattern: {
    type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom'),
    allowNull: true
  },
  recurrence_interval: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Interval for recurrence (e.g., every 2 weeks)'
  },
  recurrence_days: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of days for weekly recurrence (0=Sunday, 1=Monday, etc.)'
  },
  recurrence_end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  recurrence_count: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Number of times to repeat'
  },
  parent_reminder_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'reminders',
      key: 'id'
    },
    comment: 'Reference to parent reminder for recurring reminders'
  },
  location: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  action_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this reminder requires action'
  },
  action_url: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'URL to navigate for action'
  },
  action_data: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Data needed for the action'
  },
  email_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  email_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  sms_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  sms_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  push_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  push_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  whatsapp_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  whatsapp_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  notification_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of notifications sent for this reminder'
  },
  last_notification_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  snooze_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of times this reminder has been snoozed'
  },
  max_snooze_count: {
    type: DataTypes.INTEGER,
    defaultValue: 3,
    comment: 'Maximum number of times this reminder can be snoozed'
  },
  auto_complete: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether to auto-complete when related entity is updated'
  },
  completion_criteria: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object defining auto-completion criteria'
  },
  tags: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Comma-separated tags'
  },
  custom_fields: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object for custom field values'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'reminders',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['assigned_to']
    },
    {
      fields: ['reminder_type']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['status']
    },
    {
      fields: ['remind_at']
    },
    {
      fields: ['related_type', 'related_id']
    },
    {
      fields: ['is_recurring']
    },
    {
      fields: ['parent_reminder_id']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['user_id', 'status', 'remind_at']
    }
  ]
});

module.exports = Reminder;
