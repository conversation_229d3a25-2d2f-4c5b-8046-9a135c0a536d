const express = require('express');
const { body } = require('express-validator');
const expenseController = require('../controllers/expenseController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating expense
const createExpenseValidation = [
  body('expense_category_id')
    .notEmpty()
    .withMessage('Expense category ID is required')
    .isInt({ min: 1 })
    .withMessage('Expense category ID must be a positive integer'),
  
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ min: 3, max: 255 })
    .withMessage('Title must be between 3 and 255 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('expense_date')
    .optional()
    .isISO8601()
    .withMessage('Expense date must be a valid date'),
  
  body('amount')
    .notEmpty()
    .withMessage('Amount is required')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be greater than 0'),
  
  body('currency')
    .optional()
    .isLength({ min: 3, max: 3 })
    .withMessage('Currency must be 3 characters'),
  
  body('payment_method')
    .optional()
    .isIn(['cash', 'cheque', 'bank_transfer', 'credit_card', 'debit_card', 'upi', 'wallet', 'online', 'other'])
    .withMessage('Invalid payment method'),
  
  body('vendor_name')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Vendor name must be maximum 255 characters'),
  
  body('vendor_contact')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Vendor contact must be maximum 100 characters'),
  
  body('invoice_number')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Invoice number must be maximum 100 characters'),
  
  body('invoice_date')
    .optional()
    .isISO8601()
    .withMessage('Invoice date must be a valid date'),
  
  body('due_date')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid date'),
  
  body('reference_number')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Reference number must be maximum 100 characters'),
  
  body('customer_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Customer ID must be a positive integer'),
  
  body('is_billable')
    .optional()
    .isBoolean()
    .withMessage('Is billable must be a boolean'),
  
  body('billable_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Billable amount must be a positive number'),
  
  body('markup_percentage')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Markup percentage must be between 0 and 100'),
  
  body('employee_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Employee ID must be a positive integer'),
  
  body('department')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Department must be maximum 100 characters'),
  
  body('cost_center')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Cost center must be maximum 100 characters'),
  
  body('location')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Location must be maximum 255 characters'),
  
  body('purpose')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Purpose must be maximum 1000 characters'),
  
  body('attendees')
    .optional()
    .isArray()
    .withMessage('Attendees must be an array'),
  
  body('mileage')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Mileage must be a positive number'),
  
  body('mileage_rate')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Mileage rate must be a positive number'),
  
  body('receipts')
    .optional()
    .isArray()
    .withMessage('Receipts must be an array'),
  
  body('attachments')
    .optional()
    .isArray()
    .withMessage('Attachments must be an array'),
  
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must be maximum 1000 characters'),
  
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array')
];

// Validation rules for updating expense
const updateExpenseValidation = [
  body('title')
    .optional()
    .isLength({ min: 3, max: 255 })
    .withMessage('Title must be between 3 and 255 characters'),
  
  body('amount')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be greater than 0'),
  
  body('payment_method')
    .optional()
    .isIn(['cash', 'cheque', 'bank_transfer', 'credit_card', 'debit_card', 'upi', 'wallet', 'online', 'other'])
    .withMessage('Invalid payment method'),
  
  body('is_billable')
    .optional()
    .isBoolean()
    .withMessage('Is billable must be a boolean'),
  
  body('markup_percentage')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Markup percentage must be between 0 and 100')
];

// Validation for approval
const approveExpenseValidation = [
  body('approved_amount')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('Approved amount must be greater than 0'),
  
  body('approval_notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Approval notes must be maximum 1000 characters')
];

// Validation for rejection
const rejectExpenseValidation = [
  body('rejection_reason')
    .notEmpty()
    .withMessage('Rejection reason is required')
    .isLength({ min: 10, max: 1000 })
    .withMessage('Rejection reason must be between 10 and 1000 characters')
];

// Validation for payment
const markAsPaidValidation = [
  body('paid_amount')
    .notEmpty()
    .withMessage('Paid amount is required')
    .isFloat({ min: 0.01 })
    .withMessage('Paid amount must be greater than 0'),
  
  body('payment_method')
    .optional()
    .isIn(['cash', 'cheque', 'bank_transfer', 'credit_card', 'debit_card', 'upi', 'wallet', 'online', 'other'])
    .withMessage('Invalid payment method'),
  
  body('payment_date')
    .optional()
    .isISO8601()
    .withMessage('Payment date must be a valid date'),
  
  body('payment_reference')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Payment reference must be maximum 100 characters')
];

// Validation for bulk approve
const bulkApproveValidation = [
  body('expense_ids')
    .isArray({ min: 1 })
    .withMessage('Expense IDs array is required and must not be empty'),
  
  body('expense_ids.*')
    .isInt({ min: 1 })
    .withMessage('Each expense ID must be a positive integer'),
  
  body('approved_amounts')
    .optional()
    .isArray()
    .withMessage('Approved amounts must be an array'),
  
  body('approved_amounts.*')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('Each approved amount must be greater than 0')
];

// Routes
router
  .route('/')
  .get(expenseController.getExpenses)
  .post(createExpenseValidation, validateRequest, expenseController.createExpense);

router
  .route('/stats')
  .get(expenseController.getExpenseStats);

router
  .route('/reports')
  .get(expenseController.getExpenseReports);

router
  .route('/bulk-approve')
  .post(bulkApproveValidation, validateRequest, restrictTo('admin', 'sub_admin', 'manager'), expenseController.bulkApproveExpenses);

router
  .route('/:id/submit')
  .put(expenseController.submitExpense);

router
  .route('/:id/approve')
  .put(approveExpenseValidation, validateRequest, restrictTo('admin', 'sub_admin', 'manager'), expenseController.approveExpense);

router
  .route('/:id/reject')
  .put(rejectExpenseValidation, validateRequest, restrictTo('admin', 'sub_admin', 'manager'), expenseController.rejectExpense);

router
  .route('/:id/mark-paid')
  .put(markAsPaidValidation, validateRequest, restrictTo('admin', 'sub_admin', 'accountant'), expenseController.markExpenseAsPaid);

router
  .route('/:id')
  .get(expenseController.getExpense)
  .put(updateExpenseValidation, validateRequest, expenseController.updateExpense)
  .delete(restrictTo('admin', 'sub_admin'), expenseController.deleteExpense);

module.exports = router;
