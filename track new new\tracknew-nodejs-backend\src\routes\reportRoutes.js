const express = require('express');
const { query } = require('express-validator');
const reportController = require('../controllers/reportController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation for date range queries
const dateRangeValidation = [
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid date'),

  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid date'),

  query('group_by')
    .optional()
    .isIn(['day', 'week', 'month', 'quarter', 'year'])
    .withMessage('Invalid group by option')
];

// Validation for ID parameters
const idValidation = [
  query('customer_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Customer ID must be a positive integer'),

  query('product_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),

  query('warehouse_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Warehouse ID must be a positive integer'),

  query('category_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Category ID must be a positive integer')
];

// Routes
router
  .route('/dashboard')
  .get(reportController.getDashboardSummary);

router
  .route('/sales')
  .get(dateRangeValidation, idValidation, validateRequest, reportController.getSalesReport);

router
  .route('/services')
  .get(dateRangeValidation, idValidation, validateRequest, reportController.getServiceReport);

router
  .route('/financial')
  .get(dateRangeValidation, validateRequest, reportController.getFinancialReport);

router
  .route('/inventory')
  .get(idValidation, validateRequest, reportController.getInventoryReport);

router
  .route('/customers')
  .get(dateRangeValidation, idValidation, validateRequest, reportController.getCustomerReport);

router
  .route('/expenses')
  .get(dateRangeValidation, idValidation, validateRequest, reportController.getExpenseReport);

module.exports = router;
