import{r as e,R as t,e as n,a as r}from"./router-CH1RGDGB.js";var o=Object.defineProperty,l=(e,t,n)=>(((e,t,n)=>{t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n);let i=new class{constructor(){l(this,"current",this.detect()),l(this,"handoffState","pending"),l(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}},a=(t,n)=>{i.isServer?e.useEffect(t,n):e.useLayoutEffect(t,n)};function u(t){let n=e.useRef(t);return a((()=>{n.current=t}),[t]),n}function s(t,n){let[r,o]=e.useState(t),l=u(t);return a((()=>o(l.current)),[l,o,...n]),r}let c=function(e){let n=u(e);return t.useCallback(((...e)=>n.current(...e)),[n])};function d(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch((e=>setTimeout((()=>{throw e}))))}function f(){let e=[],t={addEventListener:(e,n,r,o)=>(e.addEventListener(n,r,o),t.add((()=>e.removeEventListener(n,r,o)))),requestAnimationFrame(...e){let n=requestAnimationFrame(...e);return t.add((()=>cancelAnimationFrame(n)))},nextFrame:(...e)=>t.requestAnimationFrame((()=>t.requestAnimationFrame(...e))),setTimeout(...e){let n=setTimeout(...e);return t.add((()=>clearTimeout(n)))},microTask(...e){let n={current:!0};return d((()=>{n.current&&e[0]()})),t.add((()=>{n.current=!1}))},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add((()=>{Object.assign(e.style,{[t]:r})}))},group(e){let t=f();return e(t),this.add((()=>t.dispose()))},add:t=>(e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}function p(){let[t]=e.useState(f);return e.useEffect((()=>()=>t.dispose()),[t]),t}function m(){let t=function(){let e="undefined"==typeof document;return"useSyncExternalStore"in n&&n.useSyncExternalStore((()=>()=>{}),(()=>!1),(()=>!e))}(),[r,o]=e.useState(i.isHandoffComplete);return r&&!1===i.isHandoffComplete&&o(!1),e.useEffect((()=>{!0!==r&&o(!0)}),[r]),e.useEffect((()=>i.handoff()),[]),!t&&r}var v;let g=null!=(v=t.useId)?v:function(){let e=m(),[n,r]=t.useState(e?()=>i.nextId():null);return a((()=>{null===n&&r(i.nextId())}),[n]),null!=n?""+n:void 0};function h(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map((e=>`"${e}"`)).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,h),r}function b(e){return i.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}let E=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map((e=>`${e}:not([tabindex='-1'])`)).join(",");var y,x,S,w=((S=w||{})[S.First=1]="First",S[S.Previous=2]="Previous",S[S.Next=4]="Next",S[S.Last=8]="Last",S[S.WrapAround=16]="WrapAround",S[S.NoScroll=32]="NoScroll",S),R=((x=R||{})[x.Error=0]="Error",x[x.Overflow=1]="Overflow",x[x.Success=2]="Success",x[x.Underflow=3]="Underflow",x),T=((y=T||{})[y.Previous=-1]="Previous",y[y.Next=1]="Next",y);function P(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(E)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}var O=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(O||{});function I(e,t=0){var n;return e!==(null==(n=b(e))?void 0:n.body)&&h(t,{0:()=>e.matches(E),1(){let t=e;for(;null!==t;){if(t.matches(E))return!0;t=t.parentElement}return!1}})}function L(e){let t=b(e);f().nextFrame((()=>{t&&!I(t.activeElement,0)&&D(e)}))}var C=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(C||{});function D(e){null==e||e.focus({preventScroll:!0})}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",(e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")}),!0),document.addEventListener("click",(e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")}),!0));let F=["textarea","input"].join(",");function k(e,t=e=>e){return e.slice().sort(((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let l=r.compareDocumentPosition(o);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0}))}function M(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){let l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,i=Array.isArray(e)?n?k(e):e:P(e);o.length>0&&i.length>1&&(i=i.filter((e=>!o.includes(e)))),r=null!=r?r:l.activeElement;let a,u=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),s=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,i.indexOf(r))-1;if(4&t)return Math.max(0,i.indexOf(r))+1;if(8&t)return i.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=32&t?{preventScroll:!0}:{},d=0,f=i.length;do{if(d>=f||d+f<=0)return 0;let e=s+d;if(16&t)e=(e+f)%f;else{if(e<0)return 3;if(e>=f)return 1}a=i[e],null==a||a.focus(c),d+=u}while(a!==l.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,F))&&n}(a)&&a.select(),2}function A(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function N(){return A()||/Android/gi.test(window.navigator.userAgent)}function H(t,n,r){let o=u(n);e.useEffect((()=>{function e(e){o.current(e)}return document.addEventListener(t,e,r),()=>document.removeEventListener(t,e,r)}),[t,r])}function j(t,n,r){let o=u(n);e.useEffect((()=>{function e(e){o.current(e)}return window.addEventListener(t,e,r),()=>window.removeEventListener(t,e,r)}),[t,r])}function B(t,n,r=!0){let o=e.useRef(!1);function l(e,r){if(!o.current||e.defaultPrevented)return;let l=r(e);if(null===l||!l.getRootNode().contains(l)||!l.isConnected)return;let i=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(t);for(let t of i){if(null===t)continue;let n=t instanceof HTMLElement?t:t.current;if(null!=n&&n.contains(l)||e.composed&&e.composedPath().includes(n))return}return!I(l,O.Loose)&&-1!==l.tabIndex&&e.preventDefault(),n(e,l)}e.useEffect((()=>{requestAnimationFrame((()=>{o.current=r}))}),[r]);let i=e.useRef(null);H("pointerdown",(e=>{var t,n;o.current&&(i.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)}),!0),H("mousedown",(e=>{var t,n;o.current&&(i.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)}),!0),H("click",(e=>{N()||i.current&&(l(e,(()=>i.current)),i.current=null)}),!0),H("touchend",(e=>l(e,(()=>e.target instanceof HTMLElement?e.target:null))),!0),j("blur",(e=>l(e,(()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null))),!0)}function U(...t){return e.useMemo((()=>b(...t)),[...t])}function $(e){var t;if(e.type)return e.type;let n=null!=(t=e.as)?t:"button";return"string"==typeof n&&"button"===n.toLowerCase()?"button":void 0}function _(t,n){let[r,o]=e.useState((()=>$(t)));return a((()=>{o($(t))}),[t.type,t.as]),a((()=>{r||n.current&&n.current instanceof HTMLButtonElement&&!n.current.hasAttribute("type")&&o("button")}),[r,n]),r}let V=Symbol();function W(e,t=!0){return Object.assign(e,{[V]:t})}function K(...t){let n=e.useRef(t);e.useEffect((()=>{n.current=t}),[t]);let r=c((e=>{for(let t of n.current)null!=t&&("function"==typeof t?t(e):t.current=e)}));return t.every((e=>null==e||(null==e?void 0:e[V])))?void 0:r}function Q(e){return[e.screenX,e.screenY]}function q(){let t=e.useRef([-1,-1]);return{wasMoved(e){let n=Q(e);return(t.current[0]!==n[0]||t.current[1]!==n[1])&&(t.current=n,!0)},update(e){t.current=Q(e)}}}function G(t,n){let r=e.useRef([]),o=c(t);e.useEffect((()=>{let e=[...r.current];for(let[t,l]of n.entries())if(r.current[t]!==l){let t=o(n,e);return r.current=n,t}}),[o,...n])}function Y(...e){return Array.from(new Set(e.flatMap((e=>"string"==typeof e?e.split(" "):[])))).filter(Boolean).join(" ")}var z,X=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(X||{}),J=((z=J||{})[z.Unmount=0]="Unmount",z[z.Hidden=1]="Hidden",z);function Z({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:l=!0,name:i,mergeRefs:a}){a=null!=a?a:ne;let u=re(t,e);if(l)return ee(u,n,r,i,a);let s=null!=o?o:0;if(2&s){let{static:e=!1,...t}=u;if(e)return ee(t,n,r,i,a)}if(1&s){let{unmount:e=!0,...t}=u;return h(e?0:1,{0:()=>null,1:()=>ee({...t,hidden:!0,style:{display:"none"}},n,r,i,a)})}return ee(u,n,r,i,a)}function ee(t,n={},r,o,l){let{as:i=r,children:a,refName:u="ref",...s}=ie(t,["unmount","static"]),c=void 0!==t.ref?{[u]:t.ref}:{},d="function"==typeof a?a(n):a;"className"in s&&s.className&&"function"==typeof s.className&&(s.className=s.className(n));let f={};if(n){let e=!1,t=[];for(let[r,o]of Object.entries(n))"boolean"==typeof o&&(e=!0),!0===o&&t.push(r);e&&(f["data-headlessui-state"]=t.join(" "))}if(i===e.Fragment&&Object.keys(le(s)).length>0){if(!e.isValidElement(d)||Array.isArray(d)&&d.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${o} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(s).map((e=>`  - ${e}`)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map((e=>`  - ${e}`)).join("\n")].join("\n"));let t=d.props,n="function"==typeof(null==t?void 0:t.className)?(...e)=>Y(null==t?void 0:t.className(...e),s.className):Y(null==t?void 0:t.className,s.className),r=n?{className:n}:{};return e.cloneElement(d,Object.assign({},re(d.props,le(ie(s,["ref"]))),f,c,{ref:l(d.ref,c.ref)},r))}return e.createElement(i,Object.assign({},ie(s,["ref"]),i!==e.Fragment&&c,i!==e.Fragment&&f),d)}function te(){let t=e.useRef([]),n=e.useCallback((e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)}),[]);return(...e)=>{if(!e.every((e=>null==e)))return t.current=e,n}}function ne(...e){return e.every((e=>null==e))?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function re(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map((e=>[e,void 0]))));for(let r in n)Object.assign(t,{[r](e,...t){let o=n[r];for(let n of o){if((e instanceof Event||(null==e?void 0:e.nativeEvent)instanceof Event)&&e.defaultPrevented)return;n(e,...t)}}});return t}function oe(t){var n;return Object.assign(e.forwardRef(t),{displayName:null!=(n=t.displayName)?n:t.name})}function le(e){let t=Object.assign({},e);for(let n in t)void 0===t[n]&&delete t[n];return t}function ie(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}var ae=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(ae||{});let ue=oe((function(e,t){var n;let{features:r=1,...o}=e;return Z({ourProps:{ref:t,"aria-hidden":!(2&~r)||(null!=(n=o["aria-hidden"])?n:void 0),hidden:!(4&~r)||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...!(4&~r)&&!!(2&~r)&&{display:"none"}}},theirProps:o,slot:{},defaultTag:"div",name:"Hidden"})})),se=e.createContext(null);se.displayName="OpenClosedContext";var ce=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(ce||{});function de(){return e.useContext(se)}function fe({value:e,children:n}){return t.createElement(se.Provider,{value:e},n)}let pe=[];function me(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=""===(null==t?void 0:t.getAttribute("disabled"));return(!r||!function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&r}!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}((()=>{function e(e){e.target instanceof HTMLElement&&e.target!==document.body&&pe[0]!==e.target&&(pe.unshift(e.target),pe=pe.filter((e=>null!=e&&e.isConnected)),pe.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})}));var ve,ge=((ve=ge||{})[ve.First=0]="First",ve[ve.Previous=1]="Previous",ve[ve.Next=2]="Next",ve[ve.Last=3]="Last",ve[ve.Specific=4]="Specific",ve[ve.Nothing=5]="Nothing",ve);function he(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),o=null!=r?r:-1;switch(e.focus){case 0:for(let e=0;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 1:for(let e=o-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 2:for(let e=o+1;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 3:for(let e=n.length-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 4:for(let r=0;r<n.length;++r)if(t.resolveId(n[r],r,n)===e.id)return r;return r;case 5:return null;default:!function(e){throw new Error("Unexpected object: "+e)}(e)}}function be(e={},t=null,n=[]){for(let[r,o]of Object.entries(e))ye(n,Ee(t,r),o);return n}function Ee(e,t){return e?e+"["+t+"]":t}function ye(e,t,n){if(Array.isArray(n))for(let[r,o]of n.entries())ye(e,Ee(t,r.toString()),o);else n instanceof Date?e.push([t,n.toISOString()]):"boolean"==typeof n?e.push([t,n?"1":"0"]):"string"==typeof n?e.push([t,n]):"number"==typeof n?e.push([t,`${n}`]):null==n?e.push([t,""]):be(n,t,e)}var xe,Se=((xe=Se||{}).Space=" ",xe.Enter="Enter",xe.Escape="Escape",xe.Backspace="Backspace",xe.Delete="Delete",xe.ArrowLeft="ArrowLeft",xe.ArrowUp="ArrowUp",xe.ArrowRight="ArrowRight",xe.ArrowDown="ArrowDown",xe.Home="Home",xe.End="End",xe.PageUp="PageUp",xe.PageDown="PageDown",xe.Tab="Tab",xe);function we(t,n,r,o){let l=u(r);e.useEffect((()=>{function e(e){l.current(e)}return(t=null!=t?t:window).addEventListener(n,e,o),()=>t.removeEventListener(n,e,o)}),[t,n,o])}function Re(){let t=e.useRef(!1);return a((()=>(t.current=!0,()=>{t.current=!1})),[]),t}function Te(t){let n=c(t),r=e.useRef(!1);e.useEffect((()=>(r.current=!1,()=>{r.current=!0,d((()=>{r.current&&n()}))})),[n])}var Pe=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(Pe||{});function Oe(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}var Ie=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(Ie||{});let Le=oe((function(n,r){let o=e.useRef(null),l=K(o,r),{initialFocus:i,containers:a,features:u=30,...s}=n;m()||(u=1);let f=U(o);!function({ownerDocument:t},n){let r=function(t=!0){let n=e.useRef(pe.slice());return G((([e],[t])=>{!0===t&&!1===e&&d((()=>{n.current.splice(0)})),!1===t&&!0===e&&(n.current=pe.slice())}),[t,pe,n]),c((()=>{var e;return null!=(e=n.current.find((e=>null!=e&&e.isConnected)))?e:null}))}(n);G((()=>{n||(null==t?void 0:t.activeElement)===(null==t?void 0:t.body)&&D(r())}),[n]),Te((()=>{n&&D(r())}))}({ownerDocument:f},Boolean(16&u));let v=function({ownerDocument:t,container:n,initialFocus:r},o){let l=e.useRef(null),i=Re();return G((()=>{if(!o)return;let e=n.current;e&&d((()=>{if(!i.current)return;let n=null==t?void 0:t.activeElement;if(null!=r&&r.current){if((null==r?void 0:r.current)===n)return void(l.current=n)}else if(e.contains(n))return void(l.current=n);null!=r&&r.current?D(r.current):(M(e,w.First),R.Error),l.current=null==t?void 0:t.activeElement}))}),[o]),l}({ownerDocument:f,container:o,initialFocus:i},Boolean(2&u));!function({ownerDocument:e,container:t,containers:n,previousActiveElement:r},o){let l=Re();we(null==e?void 0:e.defaultView,"focus",(e=>{if(!o||!l.current)return;let i=Oe(n);t.current instanceof HTMLElement&&i.add(t.current);let a=r.current;if(!a)return;let u=e.target;u&&u instanceof HTMLElement?De(i,u)?(r.current=u,D(u)):(e.preventDefault(),e.stopPropagation(),D(a)):D(r.current)}),!0)}({ownerDocument:f,container:o,containers:a,previousActiveElement:v},Boolean(8&u));let g=function(){let t=e.useRef(0);return j("keydown",(e=>{"Tab"===e.key&&(t.current=e.shiftKey?1:0)}),!0),t}(),b=c((e=>{let t=o.current;t&&h(g.current,{[Pe.Forwards]:()=>{M(t,w.First,{skipElements:[e.relatedTarget]})},[Pe.Backwards]:()=>{M(t,w.Last,{skipElements:[e.relatedTarget]})}})})),E=p(),y=e.useRef(!1),x={ref:l,onKeyDown(e){"Tab"==e.key&&(y.current=!0,E.requestAnimationFrame((()=>{y.current=!1})))},onBlur(e){let t=Oe(a);o.current instanceof HTMLElement&&t.add(o.current);let n=e.relatedTarget;n instanceof HTMLElement&&"true"!==n.dataset.headlessuiFocusGuard&&(De(t,n)||(y.current?M(o.current,h(g.current,{[Pe.Forwards]:()=>w.Next,[Pe.Backwards]:()=>w.Previous})|w.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&D(e.target)))}};return t.createElement(t.Fragment,null,Boolean(4&u)&&t.createElement(ue,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:b,features:ae.Focusable}),Z({ourProps:x,theirProps:s,defaultTag:"div",name:"FocusTrap"}),Boolean(4&u)&&t.createElement(ue,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:b,features:ae.Focusable}))})),Ce=Object.assign(Le,{features:Ie});function De(e,t){for(let n of e)if(n.contains(t))return!0;return!1}let Fe=e.createContext(!1);function ke(e){return t.createElement(Fe.Provider,{value:e.force},e.children)}function Me(t){let n=e.useContext(Fe),r=e.useContext(He),o=U(t),[l,a]=e.useState((()=>{if(!n&&null!==r||i.isServer)return null;let e=null==o?void 0:o.getElementById("headlessui-portal-root");if(e)return e;if(null===o)return null;let t=o.createElement("div");return t.setAttribute("id","headlessui-portal-root"),o.body.appendChild(t)}));return e.useEffect((()=>{null!==l&&(null!=o&&o.body.contains(l)||null==o||o.body.appendChild(l))}),[l,o]),e.useEffect((()=>{n||null!==r&&a(r.current)}),[r,a,n]),l}let Ae=e.Fragment;let Ne=e.Fragment,He=e.createContext(null);let je=e.createContext(null);let Be=oe((function(t,n){let o=t,l=e.useRef(null),u=K(W((e=>{l.current=e})),n),s=U(l),c=Me(l),[d]=e.useState((()=>{var e;return i.isServer?null:null!=(e=null==s?void 0:s.createElement("div"))?e:null})),f=e.useContext(je),p=m();return a((()=>{!c||!d||c.contains(d)||(d.setAttribute("data-headlessui-portal",""),c.appendChild(d))}),[c,d]),a((()=>{if(d&&f)return f.register(d)}),[f,d]),Te((()=>{var e;!c||!d||(d instanceof Node&&c.contains(d)&&c.removeChild(d),c.childNodes.length<=0&&(null==(e=c.parentElement)||e.removeChild(c)))})),p&&c&&d?r.createPortal(Z({ourProps:{ref:u},theirProps:o,defaultTag:Ae,name:"Portal"}),d):null})),Ue=oe((function(e,n){let{target:r,...o}=e,l={ref:K(n)};return t.createElement(He.Provider,{value:r},Z({ourProps:l,theirProps:o,defaultTag:Ne,name:"Popover.Group"}))})),$e=Object.assign(Be,{Group:Ue});const _e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},{useState:Ve,useEffect:We,useLayoutEffect:Ke,useDebugValue:Qe}=n;function qe(e){const t=e.getSnapshot,n=e.value;try{const e=t();return!_e(n,e)}catch{return!0}}const Ge=!("undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement)?function(e,t,n){return t()}:function(e,t,n){const r=t(),[{inst:o},l]=Ve({inst:{value:r,getSnapshot:t}});return Ke((()=>{o.value=r,o.getSnapshot=t,qe(o)&&l({inst:o})}),[e,r,t]),We((()=>(qe(o)&&l({inst:o}),e((()=>{qe(o)&&l({inst:o})})))),[e]),Qe(r),r},Ye="useSyncExternalStore"in n?n.useSyncExternalStore:Ge;function ze(){let e;return{before({doc:t}){var n;let r=t.documentElement;e=(null!=(n=t.defaultView)?n:window).innerWidth-r.clientWidth},after({doc:t,d:n}){let r=t.documentElement,o=r.clientWidth-r.offsetWidth,l=e-o;n.style(r,"paddingRight",`${l}px`)}}}function Xe(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let Je=function(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e,...o){let l=t[e].call(n,...o);l&&(n=l,r.forEach((e=>e())))}}}((()=>new Map),{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:f(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:Xe(n)},o=[A()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap((e=>e())).some((t=>t.contains(e)))}t.microTask((()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=f();n.style(e.documentElement,"scrollBehavior","auto"),t.add((()=>t.microTask((()=>n.dispose()))))}let o=null!=(n=window.scrollY)?n:window.pageYOffset,l=null;t.addEventListener(e,"click",(t=>{if(t.target instanceof HTMLElement)try{let n=t.target.closest("a");if(!n)return;let{hash:o}=new URL(n.href),i=e.querySelector(o);i&&!r(i)&&(l=i)}catch{}}),!0),t.addEventListener(e,"touchstart",(e=>{if(e.target instanceof HTMLElement)if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")})),t.addEventListener(e,"touchmove",(e=>{if(e.target instanceof HTMLElement)if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}),{passive:!1}),t.add((()=>{var e;let t=null!=(e=window.scrollY)?e:window.pageYOffset;o!==t&&window.scrollTo(0,o),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)}))}))}}:{},ze(),{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];o.forEach((({before:e})=>null==e?void 0:e(r))),o.forEach((({after:e})=>null==e?void 0:e(r)))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});function Ze(e,t,n){let r=function(e){return Ye(e.subscribe,e.getSnapshot,e.getSnapshot)}(Je),o=e?r.get(e):void 0,l=!!o&&o.count>0;return a((()=>{if(e&&t)return Je.dispatch("PUSH",e,n),()=>Je.dispatch("POP",e,n)}),[t,e]),l}Je.subscribe((()=>{let e=Je.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&Je.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&Je.dispatch("TEARDOWN",n)}}));let et=new Map,tt=new Map;function nt(e,t=!0){a((()=>{var n;if(!t)return;let r="function"==typeof e?e():e.current;if(!r)return;let o=null!=(n=tt.get(r))?n:0;return tt.set(r,o+1),0!==o||(et.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),function(){var e;if(!r)return;let t=null!=(e=tt.get(r))?e:1;if(1===t?tt.delete(r):tt.set(r,t-1),1!==t)return;let n=et.get(r);n&&(null===n["aria-hidden"]?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",n["aria-hidden"]),r.inert=n.inert,et.delete(r))}}),[e,t])}let rt=e.createContext((()=>{}));rt.displayName="StackContext";var ot=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(ot||{});function lt({children:n,onUpdate:r,type:o,element:l,enabled:i}){let u=e.useContext(rt),s=c(((...e)=>{null==r||r(...e),u(...e)}));return a((()=>{let e=void 0===i||!0===i;return e&&s(0,o,l),()=>{e&&s(1,o,l)}}),[s,o,l,i]),t.createElement(rt.Provider,{value:s},n)}let it=e.createContext(null);function at(){let t=e.useContext(it);if(null===t){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,at),e}return t}let ut=oe((function(e,t){let n=g(),{id:r=`headlessui-description-${n}`,...o}=e,l=at(),i=K(t);return a((()=>l.register(r)),[r,l.register]),Z({ourProps:{ref:i,...l.props,id:r},theirProps:o,slot:l.slot||{},defaultTag:"p",name:l.name||"Description"})})),st=Object.assign(ut,{});var ct=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(ct||{}),dt=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(dt||{});let ft={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},pt=e.createContext(null);function mt(t){let n=e.useContext(pt);if(null===n){let e=new Error(`<${t} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,mt),e}return n}function vt(e,t){return h(t.type,ft,e,t)}pt.displayName="DialogContext";let gt=X.RenderStrategy|X.Static;let ht=oe((function(n,r){let o=g(),{id:l=`headlessui-dialog-${o}`,open:i,onClose:a,initialFocus:u,role:s="dialog",__demoMode:d=!1,...f}=n,[p,v]=e.useState(0),b=e.useRef(!1);s="dialog"===s||"alertdialog"===s?s:(b.current||(b.current=!0),"dialog");let E=de();void 0===i&&null!==E&&(i=(E&ce.Open)===ce.Open);let y=e.useRef(null),x=K(y,r),S=U(y),w=n.hasOwnProperty("open")||null!==E,R=n.hasOwnProperty("onClose");if(!w&&!R)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!w)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!R)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if("boolean"!=typeof i)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${i}`);if("function"!=typeof a)throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${a}`);let T=i?0:1,[P,O]=e.useReducer(vt,{titleId:null,descriptionId:null,panelRef:e.createRef()}),I=c((()=>a(!1))),L=c((e=>O({type:0,id:e}))),C=!!m()&&(!d&&0===T),D=p>1,F=null!==e.useContext(pt),[k,M]=function(){let n=e.useContext(je),r=e.useRef([]),o=c((e=>(r.current.push(e),n&&n.register(e),()=>l(e)))),l=c((e=>{let t=r.current.indexOf(e);-1!==t&&r.current.splice(t,1),n&&n.unregister(e)})),i=e.useMemo((()=>({register:o,unregister:l,portals:r})),[o,l,r]);return[r,e.useMemo((()=>function({children:e}){return t.createElement(je.Provider,{value:i},e)}),[i])]}(),A={get current(){var e;return null!=(e=P.panelRef.current)?e:y.current}},{resolveContainers:N,mainTreeNodeRef:H,MainTreeNode:j}=function({defaultContainers:n=[],portals:r,mainTreeNodeRef:o}={}){var l;let i=e.useRef(null!=(l=null==o?void 0:o.current)?l:null),a=U(i),u=c((()=>{var e,t,o;let l=[];for(let r of n)null!==r&&(r instanceof HTMLElement?l.push(r):"current"in r&&r.current instanceof HTMLElement&&l.push(r.current));if(null!=r&&r.current)for(let n of r.current)l.push(n);for(let n of null!=(e=null==a?void 0:a.querySelectorAll("html > *, body > *"))?e:[])n!==document.body&&n!==document.head&&n instanceof HTMLElement&&"headlessui-portal-root"!==n.id&&(n.contains(i.current)||n.contains(null==(o=null==(t=i.current)?void 0:t.getRootNode())?void 0:o.host)||l.some((e=>n.contains(e)))||l.push(n));return l}));return{resolveContainers:u,contains:c((e=>u().some((t=>t.contains(e))))),mainTreeNodeRef:i,MainTreeNode:e.useMemo((()=>function(){return null!=o?null:t.createElement(ue,{features:ae.Hidden,ref:i})}),[i,o])}}({portals:k,defaultContainers:[A]}),$=D?"parent":"leaf",_=null!==E&&(E&ce.Closing)===ce.Closing,V=(()=>!F&&!_&&C)(),W=e.useCallback((()=>{var e,t;return null!=(t=Array.from(null!=(e=null==S?void 0:S.querySelectorAll("body > *"))?e:[]).find((e=>"headlessui-portal-root"!==e.id&&(e.contains(H.current)&&e instanceof HTMLElement))))?t:null}),[H]);nt(W,V);let Q=(()=>!!D||C)(),q=e.useCallback((()=>{var e,t;return null!=(t=Array.from(null!=(e=null==S?void 0:S.querySelectorAll("[data-headlessui-portal]"))?e:[]).find((e=>e.contains(H.current)&&e instanceof HTMLElement)))?t:null}),[H]);nt(q,Q),B(N,(e=>{e.preventDefault(),I()}),(()=>!(!C||D))());let G=(()=>!(D||0!==T))();we(null==S?void 0:S.defaultView,"keydown",(e=>{G&&(e.defaultPrevented||e.key===Se.Escape&&(e.preventDefault(),e.stopPropagation(),I()))})),function(e,t,n=()=>[document.body]){Ze(e,t,(e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}}))}(S,(()=>!(_||0!==T||F))(),N),e.useEffect((()=>{if(0!==T||!y.current)return;let e=new ResizeObserver((e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&I()}}));return e.observe(y.current),()=>e.disconnect()}),[T,y,I]);let[Y,z]=function(){let[n,r]=e.useState([]);return[n.length>0?n.join(" "):void 0,e.useMemo((()=>function(n){let o=c((e=>(r((t=>[...t,e])),()=>r((t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))))),l=e.useMemo((()=>({register:o,slot:n.slot,name:n.name,props:n.props})),[o,n.slot,n.name,n.props]);return t.createElement(it.Provider,{value:l},n.children)}),[r])]}(),X=e.useMemo((()=>[{dialogState:T,close:I,setTitleId:L},P]),[T,P,I,L]),J=e.useMemo((()=>({open:0===T})),[T]),ee={ref:x,id:l,role:s,"aria-modal":0===T||void 0,"aria-labelledby":P.titleId,"aria-describedby":Y};return t.createElement(lt,{type:"Dialog",enabled:0===T,element:y,onUpdate:c(((e,t)=>{"Dialog"===t&&h(e,{[ot.Add]:()=>v((e=>e+1)),[ot.Remove]:()=>v((e=>e-1))})}))},t.createElement(ke,{force:!0},t.createElement($e,null,t.createElement(pt.Provider,{value:X},t.createElement($e.Group,{target:y},t.createElement(ke,{force:!1},t.createElement(z,{slot:J,name:"Dialog.Description"},t.createElement(Ce,{initialFocus:u,containers:N,features:C?h($,{parent:Ce.features.RestoreFocus,leaf:Ce.features.All&~Ce.features.FocusLock}):Ce.features.None},t.createElement(M,null,Z({ourProps:ee,theirProps:f,slot:J,defaultTag:"div",features:gt,visible:0===T,name:"Dialog"}))))))))),t.createElement(j,null))})),bt=oe((function(n,r){let o=g(),{id:l=`headlessui-dialog-backdrop-${o}`,...i}=n,[{dialogState:a},u]=mt("Dialog.Backdrop"),s=K(r);e.useEffect((()=>{if(null===u.panelRef.current)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")}),[u.panelRef]);let c=e.useMemo((()=>({open:0===a})),[a]);return t.createElement(ke,{force:!0},t.createElement($e,null,Z({ourProps:{ref:s,id:l,"aria-hidden":!0},theirProps:i,slot:c,defaultTag:"div",name:"Dialog.Backdrop"})))})),Et=oe((function(t,n){let r=g(),{id:o=`headlessui-dialog-panel-${r}`,...l}=t,[{dialogState:i},a]=mt("Dialog.Panel"),u=K(n,a.panelRef),s=e.useMemo((()=>({open:0===i})),[i]);return Z({ourProps:{ref:u,id:o,onClick:c((e=>{e.stopPropagation()}))},theirProps:l,slot:s,defaultTag:"div",name:"Dialog.Panel"})})),yt=oe((function(t,n){let r=g(),{id:o=`headlessui-dialog-overlay-${r}`,...l}=t,[{dialogState:i,close:a}]=mt("Dialog.Overlay");return Z({ourProps:{ref:K(n),id:o,"aria-hidden":!0,onClick:c((e=>{if(e.target===e.currentTarget){if(me(e.currentTarget))return e.preventDefault();e.preventDefault(),e.stopPropagation(),a()}}))},theirProps:l,slot:e.useMemo((()=>({open:0===i})),[i]),defaultTag:"div",name:"Dialog.Overlay"})})),xt=oe((function(t,n){let r=g(),{id:o=`headlessui-dialog-title-${r}`,...l}=t,[{dialogState:i,setTitleId:a}]=mt("Dialog.Title"),u=K(n);e.useEffect((()=>(a(o),()=>a(null))),[o,a]);let s=e.useMemo((()=>({open:0===i})),[i]);return Z({ourProps:{ref:u,id:o},theirProps:l,slot:s,defaultTag:"h2",name:"Dialog.Title"})})),St=Object.assign(ht,{Backdrop:bt,Panel:Et,Overlay:yt,Title:xt,Description:st});var wt;let Rt=null!=(wt=t.startTransition)?wt:function(e){e()};var Tt=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Tt||{}),Pt=(e=>(e[e.ToggleDisclosure=0]="ToggleDisclosure",e[e.CloseDisclosure=1]="CloseDisclosure",e[e.SetButtonId=2]="SetButtonId",e[e.SetPanelId=3]="SetPanelId",e[e.LinkPanel=4]="LinkPanel",e[e.UnlinkPanel=5]="UnlinkPanel",e))(Pt||{});let Ot={0:e=>({...e,disclosureState:h(e.disclosureState,{0:1,1:0})}),1:e=>1===e.disclosureState?e:{...e,disclosureState:1},4:e=>!0===e.linkedPanel?e:{...e,linkedPanel:!0},5:e=>!1===e.linkedPanel?e:{...e,linkedPanel:!1},2:(e,t)=>e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId},3:(e,t)=>e.panelId===t.panelId?e:{...e,panelId:t.panelId}},It=e.createContext(null);function Lt(t){let n=e.useContext(It);if(null===n){let e=new Error(`<${t} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,Lt),e}return n}It.displayName="DisclosureContext";let Ct=e.createContext(null);function Dt(t){let n=e.useContext(Ct);if(null===n){let e=new Error(`<${t} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,Dt),e}return n}Ct.displayName="DisclosureAPIContext";let Ft=e.createContext(null);function kt(e,t){return h(t.type,Ot,e,t)}Ft.displayName="DisclosurePanelContext";let Mt=e.Fragment;let At=X.RenderStrategy|X.Static;let Nt=oe((function(n,r){let{defaultOpen:o=!1,...l}=n,i=e.useRef(null),a=K(r,W((e=>{i.current=e}),void 0===n.as||n.as===e.Fragment)),u=e.useRef(null),s=e.useRef(null),d=e.useReducer(kt,{disclosureState:o?0:1,linkedPanel:!1,buttonRef:s,panelRef:u,buttonId:null,panelId:null}),[{disclosureState:f,buttonId:p},m]=d,v=c((e=>{m({type:1});let t=b(i);if(!t||!p)return;let n=e?e instanceof HTMLElement?e:e.current instanceof HTMLElement?e.current:t.getElementById(p):t.getElementById(p);null==n||n.focus()})),g=e.useMemo((()=>({close:v})),[v]),E=e.useMemo((()=>({open:0===f,close:v})),[f,v]),y={ref:a};return t.createElement(It.Provider,{value:d},t.createElement(Ct.Provider,{value:g},t.createElement(fe,{value:h(f,{0:ce.Open,1:ce.Closed})},Z({ourProps:y,theirProps:l,slot:E,defaultTag:Mt,name:"Disclosure"}))))})),Ht=oe((function(t,n){let r=g(),{id:o=`headlessui-disclosure-button-${r}`,...l}=t,[i,a]=Lt("Disclosure.Button"),u=e.useContext(Ft),s=null!==u&&u===i.panelId,d=e.useRef(null),f=K(d,n,s?null:i.buttonRef),p=te();e.useEffect((()=>{if(!s)return a({type:2,buttonId:o}),()=>{a({type:2,buttonId:null})}}),[o,a,s]);let m=c((e=>{var t;if(s){if(1===i.disclosureState)return;switch(e.key){case Se.Space:case Se.Enter:e.preventDefault(),e.stopPropagation(),a({type:0}),null==(t=i.buttonRef.current)||t.focus()}}else switch(e.key){case Se.Space:case Se.Enter:e.preventDefault(),e.stopPropagation(),a({type:0})}})),v=c((e=>{if(e.key===Se.Space)e.preventDefault()})),h=c((e=>{var n;me(e.currentTarget)||t.disabled||(s?(a({type:0}),null==(n=i.buttonRef.current)||n.focus()):a({type:0}))})),b=e.useMemo((()=>({open:0===i.disclosureState})),[i]),E=_(t,d);return Z({mergeRefs:p,ourProps:s?{ref:f,type:E,onKeyDown:m,onClick:h}:{ref:f,id:o,type:E,"aria-expanded":0===i.disclosureState,"aria-controls":i.linkedPanel?i.panelId:void 0,onKeyDown:m,onKeyUp:v,onClick:h},theirProps:l,slot:b,defaultTag:"button",name:"Disclosure.Button"})})),jt=oe((function(n,r){let o=g(),{id:l=`headlessui-disclosure-panel-${o}`,...i}=n,[a,u]=Lt("Disclosure.Panel"),{close:s}=Dt("Disclosure.Panel"),c=te(),d=K(r,a.panelRef,(e=>{Rt((()=>u({type:e?4:5})))}));e.useEffect((()=>(u({type:3,panelId:l}),()=>{u({type:3,panelId:null})})),[l,u]);let f=de(),p=null!==f?(f&ce.Open)===ce.Open:0===a.disclosureState,m=e.useMemo((()=>({open:0===a.disclosureState,close:s})),[a,s]),v={ref:d,id:l};return t.createElement(Ft.Provider,{value:a.panelId},Z({mergeRefs:c,ourProps:v,theirProps:i,slot:m,defaultTag:"div",features:At,visible:p,name:"Disclosure.Panel"}))})),Bt=Object.assign(Nt,{Button:Ht,Panel:jt}),Ut=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function $t(e){var t,n;let r=null!=(t=e.innerText)?t:"",o=e.cloneNode(!0);if(!(o instanceof HTMLElement))return r;let l=!1;for(let a of o.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))a.remove(),l=!0;let i=l?null!=(n=o.innerText)?n:"":r;return Ut.test(i)&&(i=i.replace(Ut,"")),i}function _t(t){let n=e.useRef(""),r=e.useRef("");return c((()=>{let e=t.current;if(!e)return"";let o=e.innerText;if(n.current===o)return r.current;let l=function(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let n=e.getAttribute("aria-labelledby");if(n){let e=n.split(" ").map((e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():$t(t).trim()}return null})).filter(Boolean);if(e.length>0)return e.join(", ")}return $t(e).trim()}(e).trim().toLowerCase();return n.current=o,r.current=l,l}))}var Vt=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Vt||{}),Wt=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(Wt||{}),Kt=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(Kt||{}),Qt=(e=>(e[e.OpenListbox=0]="OpenListbox",e[e.CloseListbox=1]="CloseListbox",e[e.GoToOption=2]="GoToOption",e[e.Search=3]="Search",e[e.ClearSearch=4]="ClearSearch",e[e.RegisterOption=5]="RegisterOption",e[e.UnregisterOption=6]="UnregisterOption",e[e.RegisterLabel=7]="RegisterLabel",e))(Qt||{});function qt(e,t=e=>e){let n=null!==e.activeOptionIndex?e.options[e.activeOptionIndex]:null,r=k(t(e.options.slice()),(e=>e.dataRef.current.domRef.current)),o=n?r.indexOf(n):null;return-1===o&&(o=null),{options:r,activeOptionIndex:o}}let Gt={1:e=>e.dataRef.current.disabled||1===e.listboxState?e:{...e,activeOptionIndex:null,listboxState:1},0(e){if(e.dataRef.current.disabled||0===e.listboxState)return e;let t=e.activeOptionIndex,{isSelected:n}=e.dataRef.current,r=e.options.findIndex((e=>n(e.dataRef.current.value)));return-1!==r&&(t=r),{...e,listboxState:0,activeOptionIndex:t}},2(e,t){var n;if(e.dataRef.current.disabled||1===e.listboxState)return e;let r=qt(e),o=he(t,{resolveItems:()=>r.options,resolveActiveIndex:()=>r.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...r,searchQuery:"",activeOptionIndex:o,activationTrigger:null!=(n=t.trigger)?n:1}},3:(e,t)=>{if(e.dataRef.current.disabled||1===e.listboxState)return e;let n=""!==e.searchQuery?0:1,r=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeOptionIndex?e.options.slice(e.activeOptionIndex+n).concat(e.options.slice(0,e.activeOptionIndex+n)):e.options).find((e=>{var t;return!e.dataRef.current.disabled&&(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(r))})),l=o?e.options.indexOf(o):-1;return-1===l||l===e.activeOptionIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeOptionIndex:l,activationTrigger:1}},4:e=>e.dataRef.current.disabled||1===e.listboxState||""===e.searchQuery?e:{...e,searchQuery:""},5:(e,t)=>{let n={id:t.id,dataRef:t.dataRef},r=qt(e,(e=>[...e,n]));return null===e.activeOptionIndex&&e.dataRef.current.isSelected(t.dataRef.current.value)&&(r.activeOptionIndex=r.options.indexOf(n)),{...e,...r}},6:(e,t)=>{let n=qt(e,(e=>{let n=e.findIndex((e=>e.id===t.id));return-1!==n&&e.splice(n,1),e}));return{...e,...n,activationTrigger:1}},7:(e,t)=>({...e,labelId:t.id})},Yt=e.createContext(null);function zt(t){let n=e.useContext(Yt);if(null===n){let e=new Error(`<${t} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,zt),e}return n}Yt.displayName="ListboxActionsContext";let Xt=e.createContext(null);function Jt(t){let n=e.useContext(Xt);if(null===n){let e=new Error(`<${t} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,Jt),e}return n}function Zt(e,t){return h(t.type,Gt,e,t)}Xt.displayName="ListboxDataContext";let en=e.Fragment;let tn=X.RenderStrategy|X.Static;let nn=oe((function(n,r){let{value:o,defaultValue:l,form:i,name:u,onChange:s,by:d=(e,t)=>e===t,disabled:f=!1,horizontal:m=!1,multiple:v=!1,...g}=n;const b=m?"horizontal":"vertical";let E=K(r),[y=(v?[]:void 0),x]=function(t,n,r){let[o,l]=e.useState(r),i=void 0!==t,a=e.useRef(i),u=e.useRef(!1),s=e.useRef(!1);return!i||a.current||u.current?!i&&a.current&&!s.current&&(s.current=!0,a.current=i):(u.current=!0,a.current=i),[i?t:o,c((e=>(i||l(e),null==n?void 0:n(e))))]}(o,s,l),[S,w]=e.useReducer(Zt,{dataRef:e.createRef(),listboxState:1,options:[],searchQuery:"",labelId:null,activeOptionIndex:null,activationTrigger:1}),R=e.useRef({static:!1,hold:!1}),T=e.useRef(null),P=e.useRef(null),L=e.useRef(null),C=c("string"==typeof d?(e,t)=>{let n=d;return(null==e?void 0:e[n])===(null==t?void 0:t[n])}:d),D=e.useCallback((e=>h(F.mode,{1:()=>y.some((t=>C(t,e))),0:()=>C(y,e)})),[y]),F=e.useMemo((()=>({...S,value:y,disabled:f,mode:v?1:0,orientation:b,compare:C,isSelected:D,optionsPropsRef:R,labelRef:T,buttonRef:P,optionsRef:L})),[y,f,v,S]);a((()=>{S.dataRef.current=F}),[F]),B([F.buttonRef,F.optionsRef],((e,t)=>{var n;w({type:1}),I(t,O.Loose)||(e.preventDefault(),null==(n=F.buttonRef.current)||n.focus())}),0===F.listboxState);let k=e.useMemo((()=>({open:0===F.listboxState,disabled:f,value:y})),[F,f,y]),M=c((e=>{let t=F.options.find((t=>t.id===e));t&&_(t.dataRef.current.value)})),A=c((()=>{if(null!==F.activeOptionIndex){let{dataRef:e,id:t}=F.options[F.activeOptionIndex];_(e.current.value),w({type:2,focus:ge.Specific,id:t})}})),N=c((()=>w({type:0}))),H=c((()=>w({type:1}))),j=c(((e,t,n)=>e===ge.Specific?w({type:2,focus:ge.Specific,id:t,trigger:n}):w({type:2,focus:e,trigger:n}))),U=c(((e,t)=>(w({type:5,id:e,dataRef:t}),()=>w({type:6,id:e})))),$=c((e=>(w({type:7,id:e}),()=>w({type:7,id:null})))),_=c((e=>h(F.mode,{0:()=>null==x?void 0:x(e),1(){let t=F.value.slice(),n=t.findIndex((t=>C(t,e)));return-1===n?t.push(e):t.splice(n,1),null==x?void 0:x(t)}}))),V=c((e=>w({type:3,value:e}))),W=c((()=>w({type:4}))),Q=e.useMemo((()=>({onChange:_,registerOption:U,registerLabel:$,goToOption:j,closeListbox:H,openListbox:N,selectActiveOption:A,selectOption:M,search:V,clearSearch:W})),[]),q={ref:E},G=e.useRef(null),Y=p();return e.useEffect((()=>{G.current&&void 0!==l&&Y.addEventListener(G.current,"reset",(()=>{null==x||x(l)}))}),[G,x]),t.createElement(Yt.Provider,{value:Q},t.createElement(Xt.Provider,{value:F},t.createElement(fe,{value:h(F.listboxState,{0:ce.Open,1:ce.Closed})},null!=u&&null!=y&&be({[u]:y}).map((([e,n],r)=>t.createElement(ue,{features:ae.Hidden,ref:0===r?e=>{var t;G.current=null!=(t=null==e?void 0:e.closest("form"))?t:null}:void 0,...le({key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:i,disabled:f,name:e,value:n})}))),Z({ourProps:q,theirProps:g,slot:k,defaultTag:en,name:"Listbox"}))))})),rn=oe((function(t,n){var r;let o=g(),{id:l=`headlessui-listbox-button-${o}`,...i}=t,a=Jt("Listbox.Button"),u=zt("Listbox.Button"),d=K(a.buttonRef,n),f=p(),m=c((e=>{switch(e.key){case Se.Space:case Se.Enter:case Se.ArrowDown:e.preventDefault(),u.openListbox(),f.nextFrame((()=>{a.value||u.goToOption(ge.First)}));break;case Se.ArrowUp:e.preventDefault(),u.openListbox(),f.nextFrame((()=>{a.value||u.goToOption(ge.Last)}))}})),v=c((e=>{if(e.key===Se.Space)e.preventDefault()})),h=c((e=>{if(me(e.currentTarget))return e.preventDefault();0===a.listboxState?(u.closeListbox(),f.nextFrame((()=>{var e;return null==(e=a.buttonRef.current)?void 0:e.focus({preventScroll:!0})}))):(e.preventDefault(),u.openListbox())})),b=s((()=>{if(a.labelId)return[a.labelId,l].join(" ")}),[a.labelId,l]),E=e.useMemo((()=>({open:0===a.listboxState,disabled:a.disabled,value:a.value})),[a]);return Z({ourProps:{ref:d,id:l,type:_(t,a.buttonRef),"aria-haspopup":"listbox","aria-controls":null==(r=a.optionsRef.current)?void 0:r.id,"aria-expanded":0===a.listboxState,"aria-labelledby":b,disabled:a.disabled,onKeyDown:m,onKeyUp:v,onClick:h},theirProps:i,slot:E,defaultTag:"button",name:"Listbox.Button"})})),on=oe((function(t,n){let r=g(),{id:o=`headlessui-listbox-label-${r}`,...l}=t,i=Jt("Listbox.Label"),u=zt("Listbox.Label"),s=K(i.labelRef,n);a((()=>u.registerLabel(o)),[o]);let d=c((()=>{var e;return null==(e=i.buttonRef.current)?void 0:e.focus({preventScroll:!0})})),f=e.useMemo((()=>({open:0===i.listboxState,disabled:i.disabled})),[i]);return Z({ourProps:{ref:s,id:o,onClick:d},theirProps:l,slot:f,defaultTag:"label",name:"Listbox.Label"})})),ln=oe((function(t,n){var r;let o=g(),{id:l=`headlessui-listbox-options-${o}`,...i}=t,a=Jt("Listbox.Options"),u=zt("Listbox.Options"),d=K(a.optionsRef,n),m=p(),v=p(),E=de(),y=null!==E?(E&ce.Open)===ce.Open:0===a.listboxState;e.useEffect((()=>{var e;let t=a.optionsRef.current;t&&0===a.listboxState&&t!==(null==(e=b(t))?void 0:e.activeElement)&&t.focus({preventScroll:!0})}),[a.listboxState,a.optionsRef]);let x=c((e=>{switch(v.dispose(),e.key){case Se.Space:if(""!==a.searchQuery)return e.preventDefault(),e.stopPropagation(),u.search(e.key);case Se.Enter:if(e.preventDefault(),e.stopPropagation(),null!==a.activeOptionIndex){let{dataRef:e}=a.options[a.activeOptionIndex];u.onChange(e.current.value)}0===a.mode&&(u.closeListbox(),f().nextFrame((()=>{var e;return null==(e=a.buttonRef.current)?void 0:e.focus({preventScroll:!0})})));break;case h(a.orientation,{vertical:Se.ArrowDown,horizontal:Se.ArrowRight}):return e.preventDefault(),e.stopPropagation(),u.goToOption(ge.Next);case h(a.orientation,{vertical:Se.ArrowUp,horizontal:Se.ArrowLeft}):return e.preventDefault(),e.stopPropagation(),u.goToOption(ge.Previous);case Se.Home:case Se.PageUp:return e.preventDefault(),e.stopPropagation(),u.goToOption(ge.First);case Se.End:case Se.PageDown:return e.preventDefault(),e.stopPropagation(),u.goToOption(ge.Last);case Se.Escape:return e.preventDefault(),e.stopPropagation(),u.closeListbox(),m.nextFrame((()=>{var e;return null==(e=a.buttonRef.current)?void 0:e.focus({preventScroll:!0})}));case Se.Tab:e.preventDefault(),e.stopPropagation();break;default:1===e.key.length&&(u.search(e.key),v.setTimeout((()=>u.clearSearch()),350))}})),S=s((()=>{var e;return null==(e=a.buttonRef.current)?void 0:e.id}),[a.buttonRef.current]),w=e.useMemo((()=>({open:0===a.listboxState})),[a]);return Z({ourProps:{"aria-activedescendant":null===a.activeOptionIndex||null==(r=a.options[a.activeOptionIndex])?void 0:r.id,"aria-multiselectable":1===a.mode||void 0,"aria-labelledby":S,"aria-orientation":a.orientation,id:l,onKeyDown:x,role:"listbox",tabIndex:0,ref:d},theirProps:i,slot:w,defaultTag:"ul",features:tn,visible:y,name:"Listbox.Options"})})),an=oe((function(t,n){let r=g(),{id:o=`headlessui-listbox-option-${r}`,disabled:l=!1,value:i,...s}=t,d=Jt("Listbox.Option"),p=zt("Listbox.Option"),m=null!==d.activeOptionIndex&&d.options[d.activeOptionIndex].id===o,v=d.isSelected(i),h=e.useRef(null),b=_t(h),E=u({disabled:l,value:i,domRef:h,get textValue(){return b()}}),y=K(n,h);a((()=>{if(0!==d.listboxState||!m||0===d.activationTrigger)return;let e=f();return e.requestAnimationFrame((()=>{var e,t;null==(t=null==(e=h.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})),e.dispose}),[h,m,d.listboxState,d.activationTrigger,d.activeOptionIndex]),a((()=>p.registerOption(o,E)),[E,o]);let x=c((e=>{if(l)return e.preventDefault();p.onChange(i),0===d.mode&&(p.closeListbox(),f().nextFrame((()=>{var e;return null==(e=d.buttonRef.current)?void 0:e.focus({preventScroll:!0})})))})),S=c((()=>{if(l)return p.goToOption(ge.Nothing);p.goToOption(ge.Specific,o)})),w=q(),R=c((e=>w.update(e))),T=c((e=>{w.wasMoved(e)&&(l||m||p.goToOption(ge.Specific,o,0))})),P=c((e=>{w.wasMoved(e)&&(l||m&&p.goToOption(ge.Nothing))})),O=e.useMemo((()=>({active:m,selected:v,disabled:l})),[m,v,l]);return Z({ourProps:{id:o,ref:y,role:"option",tabIndex:!0===l?void 0:-1,"aria-disabled":!0===l||void 0,"aria-selected":v,disabled:void 0,onClick:x,onFocus:S,onPointerEnter:R,onMouseEnter:R,onPointerMove:T,onMouseMove:T,onPointerLeave:P,onMouseLeave:P},theirProps:s,slot:O,defaultTag:"li",name:"Listbox.Option"})})),un=Object.assign(nn,{Button:rn,Label:on,Options:ln,Option:an});var sn,cn=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(cn||{}),dn=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(dn||{}),fn=((sn=fn||{})[sn.OpenMenu=0]="OpenMenu",sn[sn.CloseMenu=1]="CloseMenu",sn[sn.GoToItem=2]="GoToItem",sn[sn.Search=3]="Search",sn[sn.ClearSearch=4]="ClearSearch",sn[sn.RegisterItem=5]="RegisterItem",sn[sn.UnregisterItem=6]="UnregisterItem",sn);function pn(e,t=e=>e){let n=null!==e.activeItemIndex?e.items[e.activeItemIndex]:null,r=k(t(e.items.slice()),(e=>e.dataRef.current.domRef.current)),o=n?r.indexOf(n):null;return-1===o&&(o=null),{items:r,activeItemIndex:o}}let mn={1:e=>1===e.menuState?e:{...e,activeItemIndex:null,menuState:1},0:e=>0===e.menuState?e:{...e,__demoMode:!1,menuState:0},2:(e,t)=>{var n;let r=pn(e),o=he(t,{resolveItems:()=>r.items,resolveActiveIndex:()=>r.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...r,searchQuery:"",activeItemIndex:o,activationTrigger:null!=(n=t.trigger)?n:1}},3:(e,t)=>{let n=""!==e.searchQuery?0:1,r=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeItemIndex?e.items.slice(e.activeItemIndex+n).concat(e.items.slice(0,e.activeItemIndex+n)):e.items).find((e=>{var t;return(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(r))&&!e.dataRef.current.disabled})),l=o?e.items.indexOf(o):-1;return-1===l||l===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:l,activationTrigger:1}},4:e=>""===e.searchQuery?e:{...e,searchQuery:"",searchActiveItemIndex:null},5:(e,t)=>{let n=pn(e,(e=>[...e,{id:t.id,dataRef:t.dataRef}]));return{...e,...n}},6:(e,t)=>{let n=pn(e,(e=>{let n=e.findIndex((e=>e.id===t.id));return-1!==n&&e.splice(n,1),e}));return{...e,...n,activationTrigger:1}}},vn=e.createContext(null);function gn(t){let n=e.useContext(vn);if(null===n){let e=new Error(`<${t} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,gn),e}return n}function hn(e,t){return h(t.type,mn,e,t)}vn.displayName="MenuContext";let bn=e.Fragment;let En=X.RenderStrategy|X.Static;let yn=e.Fragment;let xn=oe((function(n,r){let{__demoMode:o=!1,...l}=n,i=e.useReducer(hn,{__demoMode:o,menuState:o?0:1,buttonRef:e.createRef(),itemsRef:e.createRef(),items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:a,itemsRef:u,buttonRef:s},d]=i,f=K(r);B([s,u],((e,t)=>{var n;d({type:1}),I(t,O.Loose)||(e.preventDefault(),null==(n=s.current)||n.focus())}),0===a);let p=c((()=>{d({type:1})})),m=e.useMemo((()=>({open:0===a,close:p})),[a,p]),v={ref:f};return t.createElement(vn.Provider,{value:i},t.createElement(fe,{value:h(a,{0:ce.Open,1:ce.Closed})},Z({ourProps:v,theirProps:l,slot:m,defaultTag:bn,name:"Menu"})))})),Sn=oe((function(t,n){var r;let o=g(),{id:l=`headlessui-menu-button-${o}`,...i}=t,[a,u]=gn("Menu.Button"),s=K(a.buttonRef,n),d=p(),f=c((e=>{switch(e.key){case Se.Space:case Se.Enter:case Se.ArrowDown:e.preventDefault(),e.stopPropagation(),u({type:0}),d.nextFrame((()=>u({type:2,focus:ge.First})));break;case Se.ArrowUp:e.preventDefault(),e.stopPropagation(),u({type:0}),d.nextFrame((()=>u({type:2,focus:ge.Last})))}})),m=c((e=>{if(e.key===Se.Space)e.preventDefault()})),v=c((e=>{if(me(e.currentTarget))return e.preventDefault();t.disabled||(0===a.menuState?(u({type:1}),d.nextFrame((()=>{var e;return null==(e=a.buttonRef.current)?void 0:e.focus({preventScroll:!0})}))):(e.preventDefault(),u({type:0})))})),h=e.useMemo((()=>({open:0===a.menuState})),[a]);return Z({ourProps:{ref:s,id:l,type:_(t,a.buttonRef),"aria-haspopup":"menu","aria-controls":null==(r=a.itemsRef.current)?void 0:r.id,"aria-expanded":0===a.menuState,onKeyDown:f,onKeyUp:m,onClick:v},theirProps:i,slot:h,defaultTag:"button",name:"Menu.Button"})})),wn=oe((function(t,n){var r,o;let l=g(),{id:i=`headlessui-menu-items-${l}`,...u}=t,[s,d]=gn("Menu.Items"),m=K(s.itemsRef,n),v=U(s.itemsRef),h=p(),E=de(),y=null!==E?(E&ce.Open)===ce.Open:0===s.menuState;e.useEffect((()=>{let e=s.itemsRef.current;e&&0===s.menuState&&e!==(null==v?void 0:v.activeElement)&&e.focus({preventScroll:!0})}),[s.menuState,s.itemsRef,v]),function({container:t,accept:n,walk:r,enabled:o=!0}){let l=e.useRef(n),i=e.useRef(r);e.useEffect((()=>{l.current=n,i.current=r}),[n,r]),a((()=>{if(!t||!o)return;let e=b(t);if(!e)return;let n=l.current,r=i.current,a=Object.assign((e=>n(e)),{acceptNode:n}),u=e.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,a,!1);for(;u.nextNode();)r(u.currentNode)}),[t,o,l,i])}({container:s.itemsRef.current,enabled:0===s.menuState,accept:e=>"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let x=c((e=>{var t,n;switch(h.dispose(),e.key){case Se.Space:if(""!==s.searchQuery)return e.preventDefault(),e.stopPropagation(),d({type:3,value:e.key});case Se.Enter:if(e.preventDefault(),e.stopPropagation(),d({type:1}),null!==s.activeItemIndex){let{dataRef:e}=s.items[s.activeItemIndex];null==(n=null==(t=e.current)?void 0:t.domRef.current)||n.click()}L(s.buttonRef.current);break;case Se.ArrowDown:return e.preventDefault(),e.stopPropagation(),d({type:2,focus:ge.Next});case Se.ArrowUp:return e.preventDefault(),e.stopPropagation(),d({type:2,focus:ge.Previous});case Se.Home:case Se.PageUp:return e.preventDefault(),e.stopPropagation(),d({type:2,focus:ge.First});case Se.End:case Se.PageDown:return e.preventDefault(),e.stopPropagation(),d({type:2,focus:ge.Last});case Se.Escape:e.preventDefault(),e.stopPropagation(),d({type:1}),f().nextFrame((()=>{var e;return null==(e=s.buttonRef.current)?void 0:e.focus({preventScroll:!0})}));break;case Se.Tab:e.preventDefault(),e.stopPropagation(),d({type:1}),f().nextFrame((()=>{!function(e,t){M(P(),t,{relativeTo:e})}(s.buttonRef.current,e.shiftKey?w.Previous:w.Next)}));break;default:1===e.key.length&&(d({type:3,value:e.key}),h.setTimeout((()=>d({type:4})),350))}})),S=c((e=>{if(e.key===Se.Space)e.preventDefault()})),R=e.useMemo((()=>({open:0===s.menuState})),[s]);return Z({ourProps:{"aria-activedescendant":null===s.activeItemIndex||null==(r=s.items[s.activeItemIndex])?void 0:r.id,"aria-labelledby":null==(o=s.buttonRef.current)?void 0:o.id,id:i,onKeyDown:x,onKeyUp:S,role:"menu",tabIndex:0,ref:m},theirProps:u,slot:R,defaultTag:"div",features:En,visible:y,name:"Menu.Items"})})),Rn=oe((function(t,n){let r=g(),{id:o=`headlessui-menu-item-${r}`,disabled:l=!1,...i}=t,[u,s]=gn("Menu.Item"),d=null!==u.activeItemIndex&&u.items[u.activeItemIndex].id===o,p=e.useRef(null),m=K(n,p);a((()=>{if(u.__demoMode||0!==u.menuState||!d||0===u.activationTrigger)return;let e=f();return e.requestAnimationFrame((()=>{var e,t;null==(t=null==(e=p.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})),e.dispose}),[u.__demoMode,p,d,u.menuState,u.activationTrigger,u.activeItemIndex]);let v=_t(p),h=e.useRef({disabled:l,domRef:p,get textValue(){return v()}});a((()=>{h.current.disabled=l}),[h,l]),a((()=>(s({type:5,id:o,dataRef:h}),()=>s({type:6,id:o}))),[h,o]);let b=c((()=>{s({type:1})})),E=c((e=>{if(l)return e.preventDefault();s({type:1}),L(u.buttonRef.current)})),y=c((()=>{if(l)return s({type:2,focus:ge.Nothing});s({type:2,focus:ge.Specific,id:o})})),x=q(),S=c((e=>x.update(e))),w=c((e=>{x.wasMoved(e)&&(l||d||s({type:2,focus:ge.Specific,id:o,trigger:0}))})),R=c((e=>{x.wasMoved(e)&&(l||d&&s({type:2,focus:ge.Nothing}))})),T=e.useMemo((()=>({active:d,disabled:l,close:b})),[d,l,b]);return Z({ourProps:{id:o,ref:m,role:"menuitem",tabIndex:!0===l?void 0:-1,"aria-disabled":!0===l||void 0,disabled:void 0,onClick:E,onFocus:y,onPointerEnter:S,onMouseEnter:S,onPointerMove:w,onMouseMove:w,onPointerLeave:R,onMouseLeave:R},theirProps:i,slot:T,defaultTag:yn,name:"Menu.Item"})})),Tn=Object.assign(xn,{Button:Sn,Items:wn,Item:Rn});function Pn(e,...t){e&&t.length>0&&e.classList.add(...t)}function On(e,...t){e&&t.length>0&&e.classList.remove(...t)}function In(e,t,n,r){let o=n?"enter":"leave",l=f(),i=void 0!==r?function(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}(r):()=>{};"enter"===o&&(e.removeAttribute("hidden"),e.style.display="");let a=h(o,{enter:()=>t.enter,leave:()=>t.leave}),u=h(o,{enter:()=>t.enterTo,leave:()=>t.leaveTo}),s=h(o,{enter:()=>t.enterFrom,leave:()=>t.leaveFrom});return On(e,...t.base,...t.enter,...t.enterTo,...t.enterFrom,...t.leave,...t.leaveFrom,...t.leaveTo,...t.entered),Pn(e,...t.base,...a,...s),l.nextFrame((()=>{On(e,...t.base,...a,...s),Pn(e,...t.base,...a,...u),function(e,t){let n=f();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:o}=getComputedStyle(e),[l,i]=[r,o].map((e=>{let[t=0]=e.split(",").filter(Boolean).map((e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e))).sort(((e,t)=>t-e));return t})),a=l+i;if(0!==a){n.group((n=>{n.setTimeout((()=>{t(),n.dispose()}),a),n.addEventListener(e,"transitionrun",(e=>{e.target===e.currentTarget&&n.dispose()}))}));let r=n.addEventListener(e,"transitionend",(e=>{e.target===e.currentTarget&&(t(),r())}))}else t();n.add((()=>t())),n.dispose}(e,(()=>(On(e,...t.base,...a),Pn(e,...t.base,...t.entered),i())))})),l.dispose}function Ln(e=""){return e.split(/\s+/).filter((e=>e.length>1))}let Cn=e.createContext(null);Cn.displayName="TransitionContext";var Dn=(e=>(e.Visible="visible",e.Hidden="hidden",e))(Dn||{});let Fn=e.createContext(null);function kn(e){return"children"in e?kn(e.children):e.current.filter((({el:e})=>null!==e.current)).filter((({state:e})=>"visible"===e)).length>0}function Mn(t,n){let r=u(t),o=e.useRef([]),l=Re(),i=p(),a=c(((e,t=J.Hidden)=>{let n=o.current.findIndex((({el:t})=>t===e));-1!==n&&(h(t,{[J.Unmount](){o.current.splice(n,1)},[J.Hidden](){o.current[n].state="hidden"}}),i.microTask((()=>{var e;!kn(o)&&l.current&&(null==(e=r.current)||e.call(r))})))})),s=c((e=>{let t=o.current.find((({el:t})=>t===e));return t?"visible"!==t.state&&(t.state="visible"):o.current.push({el:e,state:"visible"}),()=>a(e,J.Unmount)})),d=e.useRef([]),f=e.useRef(Promise.resolve()),m=e.useRef({enter:[],leave:[],idle:[]}),v=c(((e,t,r)=>{d.current.splice(0),n&&(n.chains.current[t]=n.chains.current[t].filter((([t])=>t!==e))),null==n||n.chains.current[t].push([e,new Promise((e=>{d.current.push(e)}))]),null==n||n.chains.current[t].push([e,new Promise((e=>{Promise.all(m.current[t].map((([e,t])=>t))).then((()=>e()))}))]),"enter"===t?f.current=f.current.then((()=>null==n?void 0:n.wait.current)).then((()=>r(t))):r(t)})),g=c(((e,t,n)=>{Promise.all(m.current[t].splice(0).map((([e,t])=>t))).then((()=>{var e;null==(e=d.current.shift())||e()})).then((()=>n(t)))}));return e.useMemo((()=>({children:o,register:s,unregister:a,onStart:v,onStop:g,wait:f,chains:m})),[s,a,o,v,g,m,f])}function An(){}Fn.displayName="NestingContext";let Nn=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function Hn(e){var t;let n={};for(let r of Nn)n[r]=null!=(t=e[r])?t:An;return n}let jn=X.RenderStrategy;let Bn=oe((function(n,r){let{show:o,appear:l=!1,unmount:i=!0,...u}=n,s=e.useRef(null),d=K(s,r);m();let f=de();if(void 0===o&&null!==f&&(o=(f&ce.Open)===ce.Open),![!0,!1].includes(o))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[p,v]=e.useState(o?"visible":"hidden"),g=Mn((()=>{v("hidden")})),[h,b]=e.useState(!0),E=e.useRef([o]);a((()=>{!1!==h&&E.current[E.current.length-1]!==o&&(E.current.push(o),b(!1))}),[E,o]);let y=e.useMemo((()=>({show:o,appear:l,initial:h})),[o,l,h]);e.useEffect((()=>{if(o)v("visible");else if(kn(g)){let e=s.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&v("hidden")}else v("hidden")}),[o,g]);let x={unmount:i},S=c((()=>{var e;h&&b(!1),null==(e=n.beforeEnter)||e.call(n)})),w=c((()=>{var e;h&&b(!1),null==(e=n.beforeLeave)||e.call(n)}));return t.createElement(Fn.Provider,{value:g},t.createElement(Cn.Provider,{value:y},Z({ourProps:{...x,as:e.Fragment,children:t.createElement(Un,{ref:d,...x,...u,beforeEnter:S,beforeLeave:w})},theirProps:{},defaultTag:e.Fragment,features:jn,visible:"visible"===p,name:"Transition"})))})),Un=oe((function(n,r){var o,l;let{beforeEnter:i,afterEnter:s,beforeLeave:d,afterLeave:v,enter:g,enterFrom:b,enterTo:E,entered:y,leave:x,leaveFrom:S,leaveTo:w,...R}=n,T=e.useRef(null),P=K(T,r),O=null==(o=R.unmount)||o?J.Unmount:J.Hidden,{show:I,appear:L,initial:C}=function(){let t=e.useContext(Cn);if(null===t)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return t}(),[D,F]=e.useState(I?"visible":"hidden"),k=function(){let t=e.useContext(Fn);if(null===t)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return t}(),{register:M,unregister:A}=k;e.useEffect((()=>M(T)),[M,T]),e.useEffect((()=>{if(O===J.Hidden&&T.current)return I&&"visible"!==D?void F("visible"):h(D,{hidden:()=>A(T),visible:()=>M(T)})}),[D,T,M,A,I,O]);let N=u({base:Ln(R.className),enter:Ln(g),enterFrom:Ln(b),enterTo:Ln(E),entered:Ln(y),leave:Ln(x),leaveFrom:Ln(S),leaveTo:Ln(w)}),H=function(t){let n=e.useRef(Hn(t));return e.useEffect((()=>{n.current=Hn(t)}),[t]),n}({beforeEnter:i,afterEnter:s,beforeLeave:d,afterLeave:v}),j=m();e.useEffect((()=>{if(j&&"visible"===D&&null===T.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}),[T,D,j]);let B=C&&!L,U=L&&I&&C,$=(()=>!j||B?"idle":I?"enter":"leave")(),_=function(t=0){let[n,r]=e.useState(t),o=Re(),l=e.useCallback((e=>{o.current&&r((t=>t|e))}),[n,o]),i=e.useCallback((e=>Boolean(n&e)),[n]),a=e.useCallback((e=>{o.current&&r((t=>t&~e))}),[r,o]),u=e.useCallback((e=>{o.current&&r((t=>t^e))}),[r]);return{flags:n,addFlag:l,hasFlag:i,removeFlag:a,toggleFlag:u}}(0),V=c((e=>h(e,{enter:()=>{_.addFlag(ce.Opening),H.current.beforeEnter()},leave:()=>{_.addFlag(ce.Closing),H.current.beforeLeave()},idle:()=>{}}))),W=c((e=>h(e,{enter:()=>{_.removeFlag(ce.Opening),H.current.afterEnter()},leave:()=>{_.removeFlag(ce.Closing),H.current.afterLeave()},idle:()=>{}}))),Q=Mn((()=>{F("hidden"),A(T)}),k),q=e.useRef(!1);!function({immediate:e,container:t,direction:n,classes:r,onStart:o,onStop:l}){let i=Re(),s=p(),c=u(n);a((()=>{e&&(c.current="enter")}),[e]),a((()=>{let e=f();s.add(e.dispose);let n=t.current;if(n&&"idle"!==c.current&&i.current)return e.dispose(),o.current(c.current),e.add(In(n,r.current,"enter"===c.current,(()=>{e.dispose(),l.current(c.current)}))),e.dispose}),[n])}({immediate:U,container:T,classes:N,direction:$,onStart:u((e=>{q.current=!0,Q.onStart(T,e,V)})),onStop:u((e=>{q.current=!1,Q.onStop(T,e,W),"leave"===e&&!kn(Q)&&(F("hidden"),A(T))}))});let G=R,z={ref:P};return U?G={...G,className:Y(R.className,...N.current.enter,...N.current.enterFrom)}:q.current&&(G.className=Y(R.className,null==(l=T.current)?void 0:l.className),""===G.className&&delete G.className),t.createElement(Fn.Provider,{value:Q},t.createElement(fe,{value:h(D,{visible:ce.Open,hidden:ce.Closed})|_.flags},Z({ourProps:z,theirProps:G,defaultTag:"div",features:jn,visible:"visible"===D,name:"Transition.Child"})))})),$n=oe((function(n,r){let o=null!==e.useContext(Cn),l=null!==de();return t.createElement(t.Fragment,null,!o&&l?t.createElement(Bn,{ref:r,...n}):t.createElement(Un,{ref:r,...n}))})),_n=Object.assign(Bn,{Child:$n,Root:Bn});export{Bt as A,un as I,St as _,_n as a,Tn as q};
