const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const ServiceAssign = sequelize.define('ServiceAssign', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  service_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'services',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  assigned_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  assigned_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  status: {
    type: DataTypes.ENUM('assigned', 'accepted', 'rejected', 'in_progress', 'completed', 'cancelled'),
    defaultValue: 'assigned'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  estimated_start_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  estimated_end_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  actual_start_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  actual_end_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Assignment notes or instructions'
  },
  rejection_reason: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Reason for rejection if status is rejected'
  },
  completion_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Notes added upon completion'
  },
  rating: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 5
    },
    comment: 'Customer rating for the service (1-5)'
  },
  feedback: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Customer feedback'
  },
  is_primary: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is the primary assignee'
  },
  notification_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether assignment notification was sent'
  },
  reminder_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of reminders sent'
  },
  last_reminder_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Last reminder sent timestamp'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'service_assigns',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['service_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['assigned_by']
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['assigned_at']
    },
    {
      fields: ['is_primary']
    },
    {
      fields: ['service_id', 'user_id'],
      unique: false // Allow multiple assignments per service-user pair with different statuses
    }
  ]
});

module.exports = ServiceAssign;
