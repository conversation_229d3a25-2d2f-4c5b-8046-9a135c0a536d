import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiMethods } from '../../services/api';

// Async thunks
export const fetchNotifications = createAsyncThunk(
  'notification/fetchNotifications',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/notifications', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const markAsRead = createAsyncThunk(
  'notification/markAsRead',
  async (notificationId, { rejectWithValue }) => {
    try {
      const response = await apiMethods.patch(`/notifications/${notificationId}/read`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const markAllAsRead = createAsyncThunk(
  'notification/markAllAsRead',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiMethods.patch('/notifications/mark-all-read');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const deleteNotification = createAsyncThunk(
  'notification/deleteNotification',
  async (notificationId, { rejectWithValue }) => {
    try {
      await apiMethods.delete(`/notifications/${notificationId}`);
      return notificationId;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchNotificationSettings = createAsyncThunk(
  'notification/fetchNotificationSettings',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/notifications/settings');
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateNotificationSettings = createAsyncThunk(
  'notification/updateNotificationSettings',
  async (settings, { rejectWithValue }) => {
    try {
      const response = await apiMethods.put('/notifications/settings', settings);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  // Data
  notifications: [],
  settings: {
    email_notifications: true,
    push_notifications: true,
    sms_notifications: false,
    desktop_notifications: true,
    notification_types: {
      service_updates: true,
      payment_reminders: true,
      system_alerts: true,
      marketing: false,
    },
  },
  
  // Pagination
  pagination: {
    current_page: 1,
    total_pages: 1,
    total_items: 0,
    items_per_page: 15,
    has_next_page: false,
    has_prev_page: false,
  },
  
  // Counts
  unreadCount: 0,
  totalCount: 0,
  
  // Loading states
  loading: false,
  settingsLoading: false,
  actionLoading: false,
  
  // Error states
  error: null,
  settingsError: null,
  actionError: null,
  
  // UI states
  showNotificationPanel: false,
  filters: {
    type: '',
    read_status: '',
    date_from: '',
    date_to: '',
  },
  sortBy: 'created_at',
  sortOrder: 'desc',
};

const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    // UI actions
    toggleNotificationPanel: (state) => {
      state.showNotificationPanel = !state.showNotificationPanel;
    },
    setNotificationPanel: (state, action) => {
      state.showNotificationPanel = action.payload;
    },
    
    // Filter actions
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    // Sort actions
    setSortBy: (state, action) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action) => {
      state.sortOrder = action.payload;
    },
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    // Local notification actions
    addNotification: (state, action) => {
      const notification = {
        id: Date.now(),
        ...action.payload,
        created_at: new Date().toISOString(),
        is_read: false,
      };
      state.notifications.unshift(notification);
      state.unreadCount += 1;
      state.totalCount += 1;
    },
    
    removeNotification: (state, action) => {
      const notificationId = action.payload;
      const notification = state.notifications.find(n => n.id === notificationId);
      if (notification && !notification.is_read) {
        state.unreadCount -= 1;
      }
      state.notifications = state.notifications.filter(n => n.id !== notificationId);
      state.totalCount -= 1;
    },
    
    markNotificationAsRead: (state, action) => {
      const notificationId = action.payload;
      const notification = state.notifications.find(n => n.id === notificationId);
      if (notification && !notification.is_read) {
        notification.is_read = true;
        state.unreadCount -= 1;
      }
    },
    
    // Settings actions
    updateLocalSettings: (state, action) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    
    // Clear errors
    clearError: (state) => {
      state.error = null;
    },
    clearSettingsError: (state) => {
      state.settingsError = null;
    },
    clearActionError: (state) => {
      state.actionError = null;
    },
    
    // Reset state
    resetNotificationState: (state) => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    // Fetch notifications
    builder
      .addCase(fetchNotifications.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.loading = false;
        state.notifications = action.payload.data.notifications;
        state.pagination = action.payload.data.pagination;
        state.unreadCount = action.payload.data.unread_count || 0;
        state.totalCount = action.payload.data.total_count || 0;
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
    
    // Mark as read
    builder
      .addCase(markAsRead.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(markAsRead.fulfilled, (state, action) => {
        state.actionLoading = false;
        const notificationId = action.meta.arg;
        const notification = state.notifications.find(n => n.id === notificationId);
        if (notification && !notification.is_read) {
          notification.is_read = true;
          state.unreadCount -= 1;
        }
      })
      .addCase(markAsRead.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Mark all as read
    builder
      .addCase(markAllAsRead.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(markAllAsRead.fulfilled, (state) => {
        state.actionLoading = false;
        state.notifications.forEach(notification => {
          notification.is_read = true;
        });
        state.unreadCount = 0;
      })
      .addCase(markAllAsRead.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Delete notification
    builder
      .addCase(deleteNotification.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(deleteNotification.fulfilled, (state, action) => {
        state.actionLoading = false;
        const notificationId = action.payload;
        const notification = state.notifications.find(n => n.id === notificationId);
        if (notification && !notification.is_read) {
          state.unreadCount -= 1;
        }
        state.notifications = state.notifications.filter(n => n.id !== notificationId);
        state.totalCount -= 1;
      })
      .addCase(deleteNotification.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Fetch notification settings
    builder
      .addCase(fetchNotificationSettings.pending, (state) => {
        state.settingsLoading = true;
        state.settingsError = null;
      })
      .addCase(fetchNotificationSettings.fulfilled, (state, action) => {
        state.settingsLoading = false;
        state.settings = action.payload.data.settings;
      })
      .addCase(fetchNotificationSettings.rejected, (state, action) => {
        state.settingsLoading = false;
        state.settingsError = action.payload;
      });
    
    // Update notification settings
    builder
      .addCase(updateNotificationSettings.pending, (state) => {
        state.settingsLoading = true;
        state.settingsError = null;
      })
      .addCase(updateNotificationSettings.fulfilled, (state, action) => {
        state.settingsLoading = false;
        state.settings = action.payload.data.settings;
      })
      .addCase(updateNotificationSettings.rejected, (state, action) => {
        state.settingsLoading = false;
        state.settingsError = action.payload;
      });
  },
});

export const {
  toggleNotificationPanel,
  setNotificationPanel,
  setFilter,
  setFilters,
  clearFilters,
  setSortBy,
  setSortOrder,
  toggleSortOrder,
  addNotification,
  removeNotification,
  markNotificationAsRead,
  updateLocalSettings,
  clearError,
  clearSettingsError,
  clearActionError,
  resetNotificationState,
} = notificationSlice.actions;

export default notificationSlice.reducer;

// Selectors
export const selectNotifications = (state) => state.notification.notifications;
export const selectNotificationSettings = (state) => state.notification.settings;
export const selectNotificationPagination = (state) => state.notification.pagination;
export const selectUnreadCount = (state) => state.notification.unreadCount;
export const selectTotalCount = (state) => state.notification.totalCount;
export const selectNotificationLoading = (state) => state.notification.loading;
export const selectNotificationError = (state) => state.notification.error;
export const selectShowNotificationPanel = (state) => state.notification.showNotificationPanel;
export const selectNotificationFilters = (state) => state.notification.filters;
export const selectNotificationSort = (state) => ({
  sortBy: state.notification.sortBy,
  sortOrder: state.notification.sortOrder,
});
