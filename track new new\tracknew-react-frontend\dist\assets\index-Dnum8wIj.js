import{r as e,a as t,g as o}from"./vendor-BRaCMJ4j.js";import{r,R as n}from"./router-knuRb_GW.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var i,l,s={exports:{}},a={};var d,c=(l||(l=1,s.exports=function(){if(i)return a;i=1;var t=e(),o=Symbol.for("react.element"),r=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,l=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function d(e,t,r){var i,a={},d=null,c=null;for(i in void 0!==r&&(d=""+r),void 0!==t.key&&(d=""+t.key),void 0!==t.ref&&(c=t.ref),t)n.call(t,i)&&!s.hasOwnProperty(i)&&(a[i]=t[i]);if(e&&e.defaultProps)for(i in t=e.defaultProps)void 0===a[i]&&(a[i]=t[i]);return{$$typeof:o,type:e,key:d,ref:c,props:a,_owner:l.current}}return a.Fragment=r,a.jsx=d,a.jsxs=d,a}()),s.exports),p={};function x(){const[e,t]=r.useState("dashboard"),[o,n]=r.useState(null),i=()=>{n(null),t("login")},l={container:{minHeight:"100vh",backgroundColor:"#f3f4f6",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'},loginContainer:{minHeight:"100vh",backgroundColor:"#f3f4f6",display:"flex",alignItems:"center",justifyContent:"center",padding:"20px"},loginCard:{maxWidth:"400px",width:"100%",backgroundColor:"white",borderRadius:"8px",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)",padding:"32px"},header:{backgroundColor:"white",borderBottom:"1px solid #e5e7eb",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)"},headerContent:{maxWidth:"1200px",margin:"0 auto",padding:"0 24px",display:"flex",justifyContent:"space-between",alignItems:"center",height:"64px"},sidebar:{width:"256px",backgroundColor:"white",minHeight:"100vh",borderRight:"1px solid #e5e7eb",boxShadow:"2px 0 4px rgba(0, 0, 0, 0.05)"},mainContent:{flex:1,padding:"32px"},button:{backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"6px",padding:"12px 24px",fontSize:"14px",fontWeight:"600",cursor:"pointer",transition:"all 0.2s ease"},input:{width:"100%",padding:"12px 16px",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"14px",outline:"none",transition:"border-color 0.2s ease"},card:{backgroundColor:"white",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",padding:"24px",marginBottom:"24px"},statCard:{backgroundColor:"white",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.1)",padding:"24px",textAlign:"center"},navItem:{width:"100%",textAlign:"left",padding:"12px 16px",border:"none",backgroundColor:"transparent",borderRadius:"6px",margin:"4px 0",cursor:"pointer",display:"flex",alignItems:"center",fontSize:"14px",fontWeight:"500",transition:"all 0.2s ease"},navItemActive:{backgroundColor:"#dbeafe",color:"#1d4ed8"}},s=()=>c.jsx("div",{style:l.loginContainer,children:c.jsxs("div",{style:l.loginCard,children:[c.jsxs("div",{style:{textAlign:"center",marginBottom:"32px"},children:[c.jsx("h1",{style:{fontSize:"32px",fontWeight:"bold",color:"#111827",margin:"0 0 8px 0"},children:"Track New"}),c.jsx("p",{style:{color:"#6b7280",margin:0},children:"Service Management System"})]}),c.jsxs("form",{onSubmit:e=>{e.preventDefault();const o=new FormData(e.target);var r,i;r=o.get("email"),i=o.get("password"),r&&i&&(n({name:"Demo User",email:r}),t("dashboard"))},children:[c.jsxs("div",{style:{marginBottom:"16px"},children:[c.jsx("label",{style:{display:"block",color:"#374151",fontSize:"14px",fontWeight:"600",marginBottom:"8px"},children:"Email"}),c.jsx("input",{type:"email",name:"email",style:l.input,placeholder:"Enter your email",defaultValue:"<EMAIL>"})]}),c.jsxs("div",{style:{marginBottom:"24px"},children:[c.jsx("label",{style:{display:"block",color:"#374151",fontSize:"14px",fontWeight:"600",marginBottom:"8px"},children:"Password"}),c.jsx("input",{type:"password",name:"password",style:l.input,placeholder:"Enter your password",defaultValue:"password123"})]}),c.jsx("button",{type:"submit",style:{...l.button,width:"100%"},onMouseOver:e=>e.target.style.backgroundColor="#2563eb",onMouseOut:e=>e.target.style.backgroundColor="#3b82f6",children:"Sign In"})]}),c.jsx("div",{style:{marginTop:"24px",textAlign:"center"},children:c.jsx("p",{style:{fontSize:"14px",color:"#6b7280"},children:'Demo credentials are pre-filled. Just click "Sign In"'})})]})}),a=()=>c.jsxs("div",{style:l.container,children:[c.jsx("header",{style:l.header,children:c.jsxs("div",{style:l.headerContent,children:[c.jsx("div",{style:{display:"flex",alignItems:"center"},children:c.jsx("h1",{style:{fontSize:"24px",fontWeight:"bold",color:"#111827",margin:0},children:"Track New"})}),c.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[c.jsxs("span",{style:{color:"#374151"},children:["Welcome, ",null==o?void 0:o.name]}),c.jsx("button",{onClick:i,style:{...l.button,backgroundColor:"#ef4444"},onMouseOver:e=>e.target.style.backgroundColor="#dc2626",onMouseOut:e=>e.target.style.backgroundColor="#ef4444",children:"Logout"})]})]})}),c.jsxs("div",{style:{display:"flex"},children:[c.jsx("nav",{style:l.sidebar,children:c.jsx("div",{style:{padding:"16px"},children:c.jsx("ul",{style:{listStyle:"none",padding:0,margin:0},children:[{id:"dashboard",name:"Dashboard",icon:"📊"},{id:"customers",name:"Customers",icon:"👥"},{id:"services",name:"Services",icon:"🛠️"},{id:"products",name:"Products",icon:"📦"},{id:"sales",name:"Sales",icon:"💰"},{id:"reports",name:"Reports",icon:"📈"}].map((o=>c.jsx("li",{children:c.jsxs("button",{onClick:()=>t(o.id),style:{...l.navItem,...e===o.id?l.navItemActive:{},color:e===o.id?"#1d4ed8":"#374151"},onMouseOver:t=>{e!==o.id&&(t.target.style.backgroundColor="#f3f4f6")},onMouseOut:t=>{e!==o.id&&(t.target.style.backgroundColor="transparent")},children:[c.jsx("span",{style:{marginRight:"12px",fontSize:"16px"},children:o.icon}),c.jsx("span",{children:o.name})]})},o.id)))})})}),c.jsx("main",{style:l.mainContent,children:c.jsxs("div",{style:{maxWidth:"1200px",margin:"0 auto"},children:[c.jsx("h2",{style:{fontSize:"32px",fontWeight:"bold",color:"#111827",marginBottom:"32px"},children:e.charAt(0).toUpperCase()+e.slice(1)}),"dashboard"===e&&c.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))",gap:"24px",marginBottom:"32px"},children:[{title:"Total Customers",value:"150",color:"#3b82f6",icon:"👥"},{title:"Active Services",value:"45",color:"#10b981",icon:"🛠️"},{title:"Monthly Revenue",value:"$25,000",color:"#8b5cf6",icon:"💰"},{title:"Pending Tasks",value:"12",color:"#f59e0b",icon:"📋"}].map(((e,t)=>c.jsxs("div",{style:l.statCard,children:[c.jsx("div",{style:{width:"48px",height:"48px",backgroundColor:e.color,borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 16px auto"},children:c.jsx("span",{style:{fontSize:"20px"},children:e.icon})}),c.jsx("h3",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 8px 0"},children:e.title}),c.jsx("p",{style:{fontSize:"32px",fontWeight:"bold",color:"#111827",margin:0},children:e.value})]},t)))}),c.jsxs("div",{style:l.card,children:[c.jsx("h3",{style:{fontSize:"20px",fontWeight:"600",color:"#111827",marginBottom:"16px"},children:"dashboard"===e?"Recent Activity":`${e.charAt(0).toUpperCase()+e.slice(1)} Management`}),c.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[1,2,3].map((t=>c.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px",border:"1px solid #e5e7eb",borderRadius:"8px"},children:[c.jsxs("div",{children:[c.jsx("h4",{style:{fontSize:"16px",fontWeight:"600",color:"#111827",margin:"0 0 4px 0"},children:"dashboard"===e?`Activity ${t}`:`${e.slice(0,-1)} ${t}`}),c.jsxs("p",{style:{color:"#6b7280",margin:0},children:["Sample description for item ",t]})]}),c.jsx("button",{style:l.button,onMouseOver:e=>e.target.style.backgroundColor="#2563eb",onMouseOut:e=>e.target.style.backgroundColor="#3b82f6",children:"View"})]},t)))})]})]})})]})]});return o?c.jsx(a,{}):c.jsx(s,{})}o(function(){if(d)return p;d=1;var e=t();return p.createRoot=e.createRoot,p.hydrateRoot=e.hydrateRoot,p}()).createRoot(document.getElementById("root")).render(c.jsx(n.StrictMode,{children:c.jsx(x,{})}));
