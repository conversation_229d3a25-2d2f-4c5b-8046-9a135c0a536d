import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiMethods } from '../../services/api';

// Async thunks
export const fetchCustomers = createAsyncThunk(
  'customer/fetchCustomers',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/customers', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchCustomer = createAsyncThunk(
  'customer/fetchCustomer',
  async (id, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get(`/customers/${id}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const createCustomer = createAsyncThunk(
  'customer/createCustomer',
  async (customerData, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post('/customers', customerData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateCustomer = createAsyncThunk(
  'customer/updateCustomer',
  async ({ id, data }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.put(`/customers/${id}`, data);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const deleteCustomer = createAsyncThunk(
  'customer/deleteCustomer',
  async (id, { rejectWithValue }) => {
    try {
      await apiMethods.delete(`/customers/${id}`);
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchCustomerStats = createAsyncThunk(
  'customer/fetchCustomerStats',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/customers/stats', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchCustomerHistory = createAsyncThunk(
  'customer/fetchCustomerHistory',
  async (customerId, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get(`/customers/${customerId}/history`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const bulkDeleteCustomers = createAsyncThunk(
  'customer/bulkDeleteCustomers',
  async (customerIds, { rejectWithValue }) => {
    try {
      await apiMethods.delete('/customers/bulk', {
        data: { customer_ids: customerIds }
      });
      return customerIds;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  // Data
  customers: [],
  currentCustomer: null,
  customerHistory: null,
  stats: null,
  
  // Pagination
  pagination: {
    current_page: 1,
    total_pages: 1,
    total_items: 0,
    items_per_page: 15,
    has_next_page: false,
    has_prev_page: false,
  },
  
  // Loading states
  loading: false,
  customerLoading: false,
  historyLoading: false,
  statsLoading: false,
  actionLoading: false,
  
  // Error states
  error: null,
  customerError: null,
  historyError: null,
  statsError: null,
  actionError: null,
  
  // UI states
  selectedCustomers: [],
  filters: {
    status: '',
    customer_type: '',
    category_id: '',
    city: '',
    state: '',
    date_from: '',
    date_to: '',
  },
  sortBy: 'created_at',
  sortOrder: 'desc',
  searchQuery: '',
};

const customerSlice = createSlice({
  name: 'customer',
  initialState,
  reducers: {
    // Selection actions
    selectCustomer: (state, action) => {
      const customerId = action.payload;
      if (!state.selectedCustomers.includes(customerId)) {
        state.selectedCustomers.push(customerId);
      }
    },
    deselectCustomer: (state, action) => {
      const customerId = action.payload;
      state.selectedCustomers = state.selectedCustomers.filter(id => id !== customerId);
    },
    selectAllCustomers: (state) => {
      state.selectedCustomers = state.customers.map(customer => customer.id);
    },
    deselectAllCustomers: (state) => {
      state.selectedCustomers = [];
    },
    
    // Filter actions
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    // Sort actions
    setSortBy: (state, action) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action) => {
      state.sortOrder = action.payload;
    },
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    // Search actions
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    clearSearchQuery: (state) => {
      state.searchQuery = '';
    },
    
    // Clear current customer
    clearCurrentCustomer: (state) => {
      state.currentCustomer = null;
      state.customerError = null;
    },
    
    // Clear customer history
    clearCustomerHistory: (state) => {
      state.customerHistory = null;
      state.historyError = null;
    },
    
    // Clear errors
    clearError: (state) => {
      state.error = null;
    },
    clearCustomerError: (state) => {
      state.customerError = null;
    },
    clearHistoryError: (state) => {
      state.historyError = null;
    },
    clearStatsError: (state) => {
      state.statsError = null;
    },
    clearActionError: (state) => {
      state.actionError = null;
    },
    
    // Reset state
    resetCustomerState: (state) => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    // Fetch customers
    builder
      .addCase(fetchCustomers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCustomers.fulfilled, (state, action) => {
        state.loading = false;
        state.customers = action.payload.data.customers;
        state.pagination = action.payload.data.pagination;
      })
      .addCase(fetchCustomers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
    
    // Fetch single customer
    builder
      .addCase(fetchCustomer.pending, (state) => {
        state.customerLoading = true;
        state.customerError = null;
      })
      .addCase(fetchCustomer.fulfilled, (state, action) => {
        state.customerLoading = false;
        state.currentCustomer = action.payload.data.customer;
      })
      .addCase(fetchCustomer.rejected, (state, action) => {
        state.customerLoading = false;
        state.customerError = action.payload;
      });
    
    // Create customer
    builder
      .addCase(createCustomer.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(createCustomer.fulfilled, (state, action) => {
        state.actionLoading = false;
        state.customers.unshift(action.payload.data.customer);
      })
      .addCase(createCustomer.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Update customer
    builder
      .addCase(updateCustomer.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(updateCustomer.fulfilled, (state, action) => {
        state.actionLoading = false;
        const updatedCustomer = action.payload.data.customer;
        const index = state.customers.findIndex(customer => customer.id === updatedCustomer.id);
        if (index !== -1) {
          state.customers[index] = updatedCustomer;
        }
        if (state.currentCustomer?.id === updatedCustomer.id) {
          state.currentCustomer = updatedCustomer;
        }
      })
      .addCase(updateCustomer.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Delete customer
    builder
      .addCase(deleteCustomer.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(deleteCustomer.fulfilled, (state, action) => {
        state.actionLoading = false;
        const customerId = action.payload;
        state.customers = state.customers.filter(customer => customer.id !== customerId);
        state.selectedCustomers = state.selectedCustomers.filter(id => id !== customerId);
        if (state.currentCustomer?.id === customerId) {
          state.currentCustomer = null;
        }
      })
      .addCase(deleteCustomer.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Fetch customer history
    builder
      .addCase(fetchCustomerHistory.pending, (state) => {
        state.historyLoading = true;
        state.historyError = null;
      })
      .addCase(fetchCustomerHistory.fulfilled, (state, action) => {
        state.historyLoading = false;
        state.customerHistory = action.payload.data;
      })
      .addCase(fetchCustomerHistory.rejected, (state, action) => {
        state.historyLoading = false;
        state.historyError = action.payload;
      });
    
    // Fetch stats
    builder
      .addCase(fetchCustomerStats.pending, (state) => {
        state.statsLoading = true;
        state.statsError = null;
      })
      .addCase(fetchCustomerStats.fulfilled, (state, action) => {
        state.statsLoading = false;
        state.stats = action.payload.data;
      })
      .addCase(fetchCustomerStats.rejected, (state, action) => {
        state.statsLoading = false;
        state.statsError = action.payload;
      });
    
    // Bulk delete customers
    builder
      .addCase(bulkDeleteCustomers.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(bulkDeleteCustomers.fulfilled, (state, action) => {
        state.actionLoading = false;
        const deletedIds = action.payload;
        state.customers = state.customers.filter(customer => !deletedIds.includes(customer.id));
        state.selectedCustomers = state.selectedCustomers.filter(id => !deletedIds.includes(id));
      })
      .addCase(bulkDeleteCustomers.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
  },
});

export const {
  selectCustomer,
  deselectCustomer,
  selectAllCustomers,
  deselectAllCustomers,
  setFilter,
  setFilters,
  clearFilters,
  setSortBy,
  setSortOrder,
  toggleSortOrder,
  setSearchQuery,
  clearSearchQuery,
  clearCurrentCustomer,
  clearCustomerHistory,
  clearError,
  clearCustomerError,
  clearHistoryError,
  clearStatsError,
  clearActionError,
  resetCustomerState,
} = customerSlice.actions;

export default customerSlice.reducer;

// Selectors
export const selectCustomers = (state) => state.customer.customers;
export const selectCurrentCustomer = (state) => state.customer.currentCustomer;
export const selectCustomerHistory = (state) => state.customer.customerHistory;
export const selectCustomerStats = (state) => state.customer.stats;
export const selectCustomerPagination = (state) => state.customer.pagination;
export const selectCustomerLoading = (state) => state.customer.loading;
export const selectCustomerError = (state) => state.customer.error;
export const selectSelectedCustomers = (state) => state.customer.selectedCustomers;
export const selectCustomerFilters = (state) => state.customer.filters;
export const selectCustomerSort = (state) => ({
  sortBy: state.customer.sortBy,
  sortOrder: state.customer.sortOrder,
});
export const selectCustomerSearchQuery = (state) => state.customer.searchQuery;
