const express = require('express');
const { body } = require('express-validator');
const warehouseController = require('../controllers/warehouseController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating warehouse
const createWarehouseValidation = [
  body('warehouse_name')
    .notEmpty()
    .withMessage('Warehouse name is required')
    .isLength({ min: 2, max: 255 })
    .withMessage('Warehouse name must be between 2 and 255 characters'),
  
  body('warehouse_code')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Warehouse code must be between 1 and 50 characters'),
  
  body('warehouse_type')
    .optional()
    .isIn(['main', 'branch', 'virtual', 'consignment'])
    .withMessage('Invalid warehouse type'),
  
  body('address')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Address must be maximum 1000 characters'),
  
  body('city')
    .optional()
    .isLength({ max: 100 })
    .withMessage('City must be maximum 100 characters'),
  
  body('state')
    .optional()
    .isLength({ max: 100 })
    .withMessage('State must be maximum 100 characters'),
  
  body('country')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Country must be maximum 100 characters'),
  
  body('pincode')
    .optional()
    .isLength({ max: 10 })
    .withMessage('Pincode must be maximum 10 characters'),
  
  body('contact_person')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Contact person must be maximum 255 characters'),
  
  body('phone_number')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Phone number must be maximum 20 characters'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address'),
  
  body('manager_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Manager ID must be a positive integer'),
  
  body('capacity')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Capacity must be a positive number'),
  
  body('current_utilization')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Current utilization must be between 0 and 100'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  
  body('is_default')
    .optional()
    .isBoolean()
    .withMessage('is_default must be a boolean'),
  
  body('allow_negative_stock')
    .optional()
    .isBoolean()
    .withMessage('allow_negative_stock must be a boolean'),
  
  body('auto_reorder_enabled')
    .optional()
    .isBoolean()
    .withMessage('auto_reorder_enabled must be a boolean'),
  
  body('operating_hours')
    .optional()
    .isObject()
    .withMessage('Operating hours must be a valid JSON object'),
  
  body('facilities')
    .optional()
    .isArray()
    .withMessage('Facilities must be an array'),
  
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must be maximum 1000 characters')
];

// Validation rules for updating warehouse
const updateWarehouseValidation = [
  body('warehouse_name')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Warehouse name must be between 2 and 255 characters'),
  
  body('warehouse_code')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Warehouse code must be between 1 and 50 characters'),
  
  body('warehouse_type')
    .optional()
    .isIn(['main', 'branch', 'virtual', 'consignment'])
    .withMessage('Invalid warehouse type'),
  
  body('address')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Address must be maximum 1000 characters'),
  
  body('city')
    .optional()
    .isLength({ max: 100 })
    .withMessage('City must be maximum 100 characters'),
  
  body('state')
    .optional()
    .isLength({ max: 100 })
    .withMessage('State must be maximum 100 characters'),
  
  body('country')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Country must be maximum 100 characters'),
  
  body('pincode')
    .optional()
    .isLength({ max: 10 })
    .withMessage('Pincode must be maximum 10 characters'),
  
  body('contact_person')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Contact person must be maximum 255 characters'),
  
  body('phone_number')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Phone number must be maximum 20 characters'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address'),
  
  body('manager_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Manager ID must be a positive integer'),
  
  body('capacity')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Capacity must be a positive number'),
  
  body('current_utilization')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Current utilization must be between 0 and 100'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  
  body('is_default')
    .optional()
    .isBoolean()
    .withMessage('is_default must be a boolean'),
  
  body('allow_negative_stock')
    .optional()
    .isBoolean()
    .withMessage('allow_negative_stock must be a boolean'),
  
  body('auto_reorder_enabled')
    .optional()
    .isBoolean()
    .withMessage('auto_reorder_enabled must be a boolean'),
  
  body('operating_hours')
    .optional()
    .isObject()
    .withMessage('Operating hours must be a valid JSON object'),
  
  body('facilities')
    .optional()
    .isArray()
    .withMessage('Facilities must be an array'),
  
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must be maximum 1000 characters')
];

// Validation for utilization update
const updateUtilizationValidation = [
  body('current_utilization')
    .notEmpty()
    .withMessage('Current utilization is required')
    .isFloat({ min: 0, max: 100 })
    .withMessage('Current utilization must be between 0 and 100')
];

// Routes
router
  .route('/')
  .get(warehouseController.getWarehouses)
  .post(createWarehouseValidation, validateRequest, warehouseController.createWarehouse);

router
  .route('/stats')
  .get(warehouseController.getWarehouseStats);

router
  .route('/active')
  .get(warehouseController.getActiveWarehouses);

router
  .route('/default')
  .get(warehouseController.getDefaultWarehouse);

router
  .route('/by-type/:warehouse_type')
  .get(warehouseController.getWarehousesByType);

router
  .route('/:id/set-default')
  .put(restrictTo('admin', 'sub_admin'), warehouseController.setDefaultWarehouse);

router
  .route('/:id/utilization')
  .put(updateUtilizationValidation, validateRequest, warehouseController.updateUtilization);

router
  .route('/:id')
  .get(warehouseController.getWarehouse)
  .put(updateWarehouseValidation, validateRequest, warehouseController.updateWarehouse)
  .delete(restrictTo('admin', 'sub_admin'), warehouseController.deleteWarehouse);

module.exports = router;
