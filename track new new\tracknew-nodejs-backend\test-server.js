const express = require('express');
const cors = require('cors');

// Load environment variables
require('dotenv').config();

// Create Express app
const app = express();

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://************:3000'
    ];

    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
};

app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', async (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Test server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0',
    uptime: process.uptime(),
    memory: process.memoryUsage()
  });
});

// API health check endpoint
app.get('/api/health', async (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0',
    uptime: process.uptime(),
    memory: process.memoryUsage()
  });
});

// Test auth endpoints
app.post('/api/auth/login', (req, res) => {
  res.json({
    status: 'success',
    message: 'Login endpoint working',
    data: {
      token: 'test-token',
      user: {
        id: 1,
        name: 'Test User',
        email: '<EMAIL>'
      }
    }
  });
});

app.post('/api/auth/register', (req, res) => {
  res.json({
    status: 'success',
    message: 'Register endpoint working',
    data: {
      user: {
        id: 2,
        name: req.body.name || 'New User',
        email: req.body.email || '<EMAIL>'
      }
    }
  });
});

app.get('/api/auth/profile', (req, res) => {
  res.json({
    status: 'success',
    message: 'Profile endpoint working',
    data: {
      user: {
        id: 1,
        name: 'Test User',
        email: '<EMAIL>'
      }
    }
  });
});

// Test dashboard endpoints
app.get('/api/dashboard/overview', (req, res) => {
  res.json({
    status: 'success',
    data: {
      total_customers: 100,
      active_services: 25,
      pending_services: 5,
      total_revenue: 50000
    }
  });
});

app.get('/api/dashboard/sales', (req, res) => {
  res.json({
    status: 'success',
    data: {
      monthly_sales: 45,
      sales_change: 18
    }
  });
});

app.get('/api/dashboard/financial', (req, res) => {
  res.json({
    status: 'success',
    data: {
      total_revenue: 125000,
      net_profit: 80000
    }
  });
});

app.get('/api/dashboard/inventory', (req, res) => {
  res.json({
    status: 'success',
    data: {
      alerts: [],
      out_of_stock_count: 3,
      low_stock_count: 8
    }
  });
});

app.get('/api/dashboard/top-customers', (req, res) => {
  res.json({
    status: 'success',
    data: [
      {
        id: 1,
        name: 'John Smith',
        email: '<EMAIL>',
        total_spent: 15000
      }
    ]
  });
});

app.get('/api/dashboard/activities', (req, res) => {
  res.json({
    status: 'success',
    data: [
      {
        id: 1,
        type: 'customer_created',
        created_at: new Date().toISOString()
      }
    ]
  });
});

// Test CRUD endpoints
app.get('/api/customers', (req, res) => {
  res.json({
    status: 'success',
    data: {
      customers: [
        {
          id: 1,
          name: 'Test Customer',
          email: '<EMAIL>'
        }
      ],
      pagination: {
        current_page: 1,
        total_pages: 1,
        total_items: 1
      }
    }
  });
});

app.get('/api/services', (req, res) => {
  res.json({
    status: 'success',
    data: {
      services: [
        {
          id: 1,
          title: 'Test Service',
          status: 'active'
        }
      ]
    }
  });
});

app.get('/api/products', (req, res) => {
  res.json({
    status: 'success',
    data: {
      products: [
        {
          id: 1,
          name: 'Test Product',
          price: 100
        }
      ]
    }
  });
});

app.get('/api/sales', (req, res) => {
  res.json({
    status: 'success',
    data: {
      sales: [
        {
          id: 1,
          amount: 500,
          status: 'completed'
        }
      ]
    }
  });
});

// Handle undefined routes
app.all('*', (req, res) => {
  res.status(404).json({
    status: 'error',
    message: `Route ${req.originalUrl} not found`
  });
});

// Start server
const PORT = process.env.PORT || 8000;

app.listen(PORT, () => {
  console.log(`🚀 Test Server running on http://localhost:${PORT}`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log(`📡 API Health: http://localhost:${PORT}/api/health`);
});

module.exports = app;
