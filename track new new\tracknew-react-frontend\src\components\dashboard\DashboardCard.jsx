import React from 'react';
import { ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';
import { classNames } from '../../utils/helpers';
import { LoadingSpinner } from '../ui';

const DashboardCard = ({
  title,
  value,
  change,
  changeType = 'increase',
  icon: Icon,
  color = 'blue',
  loading = false,
  className = '',
  ...props
}) => {
  const colorClasses = {
    blue: {
      bg: 'bg-blue-50 dark:bg-blue-900/20',
      icon: 'text-blue-600 dark:text-blue-400',
      border: 'border-blue-200 dark:border-blue-800',
    },
    green: {
      bg: 'bg-green-50 dark:bg-green-900/20',
      icon: 'text-green-600 dark:text-green-400',
      border: 'border-green-200 dark:border-green-800',
    },
    purple: {
      bg: 'bg-purple-50 dark:bg-purple-900/20',
      icon: 'text-purple-600 dark:text-purple-400',
      border: 'border-purple-200 dark:border-purple-800',
    },
    orange: {
      bg: 'bg-orange-50 dark:bg-orange-900/20',
      icon: 'text-orange-600 dark:text-orange-400',
      border: 'border-orange-200 dark:border-orange-800',
    },
    red: {
      bg: 'bg-red-50 dark:bg-red-900/20',
      icon: 'text-red-600 dark:text-red-400',
      border: 'border-red-200 dark:border-red-800',
    },
    yellow: {
      bg: 'bg-yellow-50 dark:bg-yellow-900/20',
      icon: 'text-yellow-600 dark:text-yellow-400',
      border: 'border-yellow-200 dark:border-yellow-800',
    },
  };

  const changeColors = {
    increase: 'text-green-600 dark:text-green-400',
    decrease: 'text-red-600 dark:text-red-400',
    neutral: 'text-gray-600 dark:text-gray-400',
  };

  const colors = colorClasses[color] || colorClasses.blue;

  return (
    <div
      className={classNames(
        'bg-white dark:bg-gray-800 rounded-lg shadow border',
        colors.border,
        className
      )}
      {...props}
    >
      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center h-20">
            <LoadingSpinner size="md" />
          </div>
        ) : (
          <>
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={classNames(
                  'flex items-center justify-center w-12 h-12 rounded-lg',
                  colors.bg
                )}>
                  {Icon && (
                    <Icon className={classNames('w-6 h-6', colors.icon)} />
                  )}
                </div>
              </div>

              {/* Change Indicator */}
              {change !== undefined && change !== null && (
                <div className={classNames(
                  'flex items-center space-x-1 text-sm font-medium',
                  changeColors[changeType]
                )}>
                  {changeType === 'increase' ? (
                    <ArrowTrendingUpIcon className="w-4 h-4" />
                  ) : changeType === 'decrease' ? (
                    <ArrowTrendingDownIcon className="w-4 h-4" />
                  ) : null}
                  <span>
                    {Math.abs(change)}%
                  </span>
                </div>
              )}
            </div>

            {/* Content */}
            <div className="mt-4">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                {title}
              </h3>
              <p className="mt-2 text-3xl font-bold text-gray-900 dark:text-white">
                {value}
              </p>

              {/* Change Description */}
              {change !== undefined && change !== null && (
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  {changeType === 'increase' ? '+' : changeType === 'decrease' ? '-' : ''}
                  {Math.abs(change)}% from last month
                </p>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default DashboardCard;
