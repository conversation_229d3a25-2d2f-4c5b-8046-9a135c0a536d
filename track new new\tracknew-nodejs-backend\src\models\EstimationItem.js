const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const EstimationItem = sequelize.define('EstimationItem', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  estimation_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'estimations',
      key: 'id'
    }
  },
  product_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  item_type: {
    type: DataTypes.ENUM('product', 'service', 'labor', 'material', 'overhead', 'discount', 'other'),
    defaultValue: 'product'
  },
  item_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  item_description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  item_code: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  hsn_code: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: 'HSN/SAC code for tax purposes'
  },
  category: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Item category for grouping'
  },
  unit_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'units',
      key: 'id'
    }
  },
  unit_name: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  quantity: {
    type: DataTypes.DECIMAL(10, 3),
    allowNull: false,
    defaultValue: 1.000
  },
  unit_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  line_total: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'quantity * unit_price'
  },
  discount_type: {
    type: DataTypes.ENUM('percentage', 'fixed'),
    allowNull: true
  },
  discount_value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00
  },
  discount_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  taxable_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'line_total - discount_amount'
  },
  tax_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: 'Tax rate percentage'
  },
  tax_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  total_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'taxable_amount + tax_amount'
  },
  cost_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Cost price for margin calculation'
  },
  margin_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Profit margin amount'
  },
  margin_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Profit margin percentage'
  },
  specifications: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of item specifications'
  },
  features: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of item features'
  },
  delivery_time: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Estimated delivery time for this item'
  },
  warranty_period: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Warranty period in months'
  },
  warranty_terms: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  installation_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  installation_cost: {
    type: DataTypes.DECIMAL(8, 2),
    defaultValue: 0.00
  },
  training_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  training_cost: {
    type: DataTypes.DECIMAL(8, 2),
    defaultValue: 0.00
  },
  maintenance_cost: {
    type: DataTypes.DECIMAL(8, 2),
    defaultValue: 0.00,
    comment: 'Annual maintenance cost'
  },
  support_cost: {
    type: DataTypes.DECIMAL(8, 2),
    defaultValue: 0.00,
    comment: 'Annual support cost'
  },
  alternatives: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of alternative products/services'
  },
  dependencies: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of dependent items'
  },
  assumptions: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Assumptions for this item'
  },
  exclusions: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'What is excluded for this item'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  internal_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Internal notes not visible to customer'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  is_optional: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this item is optional'
  },
  is_alternative: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is an alternative option'
  },
  parent_item_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'estimation_items',
      key: 'id'
    },
    comment: 'Reference to parent item for alternatives/options'
  },
  group_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Group name for related items'
  },
  phase: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Project phase this item belongs to'
  },
  milestone: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Project milestone this item belongs to'
  },
  start_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Estimated start date for this item'
  },
  end_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Estimated completion date for this item'
  },
  duration_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Estimated duration in days'
  },
  resource_requirements: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of required resources'
  },
  risk_level: {
    type: DataTypes.ENUM('low', 'medium', 'high'),
    defaultValue: 'low'
  },
  confidence_level: {
    type: DataTypes.ENUM('low', 'medium', 'high'),
    defaultValue: 'medium',
    comment: 'Confidence in the estimate'
  },
  custom_fields: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object for custom field values'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'estimation_items',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['estimation_id']
    },
    {
      fields: ['product_id']
    },
    {
      fields: ['item_type']
    },
    {
      fields: ['category']
    },
    {
      fields: ['sort_order']
    },
    {
      fields: ['is_optional']
    },
    {
      fields: ['is_alternative']
    },
    {
      fields: ['parent_item_id']
    },
    {
      fields: ['group_name']
    },
    {
      fields: ['phase']
    },
    {
      fields: ['milestone']
    }
  ]
});

module.exports = EstimationItem;
