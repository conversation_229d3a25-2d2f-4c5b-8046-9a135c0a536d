const { Tax, User, Company, Product, SalesItem, InvoiceItem } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all taxes with filtering and pagination
const getTaxes = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    tax_type,
    is_active = true,
    sort_by = 'tax_name',
    sort_order = 'ASC',
    effective_date
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { tax_name: { [Op.iLike]: `%${search}%` } },
      { tax_code: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add filters
  if (tax_type) {
    whereConditions.tax_type = tax_type;
  }

  if (is_active !== undefined) {
    whereConditions.is_active = is_active === 'true';
  }

  // Filter by effective date
  if (effective_date) {
    const filterDate = new Date(effective_date);
    whereConditions[Op.and] = [
      {
        [Op.or]: [
          { effective_from: { [Op.lte]: filterDate } },
          { effective_from: { [Op.is]: null } }
        ]
      },
      {
        [Op.or]: [
          { effective_to: { [Op.gte]: filterDate } },
          { effective_to: { [Op.is]: null } }
        ]
      }
    ];
  }

  // Get taxes with associations
  const { count, rows: taxes } = await Tax.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ],
    limit: parseInt(limit),
    offset: offset,
    order: [[sort_by, sort_order.toUpperCase()]],
    distinct: true
  });

  // Calculate pagination
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      taxes,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single tax by ID
const getTax = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const tax = await Tax.findOne({
    where: {
      id,
      company_id: companyId
    },
    include: [
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!tax) {
    return next(new AppError('Tax not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      tax
    }
  });
});

// Create new tax
const createTax = catchAsync(async (req, res, next) => {
  const {
    tax_name,
    tax_code,
    tax_type,
    tax_rate,
    cgst_rate = 0,
    sgst_rate = 0,
    igst_rate = 0,
    cess_rate = 0,
    is_compound = false,
    is_inclusive = false,
    is_active = true,
    effective_from,
    effective_to,
    description
  } = req.body;

  const companyId = req.user.company_id;

  // Check for duplicate tax code within company
  const existingTax = await Tax.findOne({
    where: {
      company_id: companyId,
      tax_code: tax_code
    }
  });

  if (existingTax) {
    return next(new AppError('Tax with this code already exists', 400));
  }

  // Validate GST rates (CGST + SGST should equal IGST for GST type)
  if (tax_type === 'gst') {
    const totalGST = parseFloat(cgst_rate) + parseFloat(sgst_rate);
    const igstValue = parseFloat(igst_rate);

    if (Math.abs(totalGST - igstValue) > 0.01 && totalGST > 0 && igstValue > 0) {
      return next(new AppError('For GST: CGST + SGST should equal IGST', 400));
    }
  }

  // Validate effective dates
  if (effective_from && effective_to) {
    if (new Date(effective_from) >= new Date(effective_to)) {
      return next(new AppError('Effective from date must be before effective to date', 400));
    }
  }

  const tax = await Tax.create({
    company_id: companyId,
    tax_name,
    tax_code,
    tax_type,
    tax_rate,
    cgst_rate,
    sgst_rate,
    igst_rate,
    cess_rate,
    is_compound,
    is_inclusive,
    is_active,
    effective_from: effective_from ? new Date(effective_from) : null,
    effective_to: effective_to ? new Date(effective_to) : null,
    description,
    created_by: req.user.id
  });

  // Fetch the created tax with associations
  const createdTax = await Tax.findByPk(tax.id, {
    include: [
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      tax: createdTax
    }
  });
});

// Update tax
const updateTax = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const tax = await Tax.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!tax) {
    return next(new AppError('Tax not found', 404));
  }

  // Check if tax is being used in transactions
  if (req.body.is_active === false) {
    const salesItemCount = await SalesItem.count({
      where: { tax_id: id }
    });

    const invoiceItemCount = await InvoiceItem.count({
      where: { tax_id: id }
    });

    if (salesItemCount > 0 || invoiceItemCount > 0) {
      return next(new AppError(`Cannot deactivate tax. It is being used in ${salesItemCount + invoiceItemCount} transaction(s)`, 400));
    }
  }

  // Check for duplicate tax code (excluding current tax)
  if (req.body.tax_code) {
    const existingTax = await Tax.findOne({
      where: {
        company_id: companyId,
        id: { [Op.ne]: id },
        tax_code: req.body.tax_code
      }
    });

    if (existingTax) {
      return next(new AppError('Tax with this code already exists', 400));
    }
  }

  // Validate GST rates if updating GST type tax
  if (req.body.tax_type === 'gst' || (tax.tax_type === 'gst' && (req.body.cgst_rate !== undefined || req.body.sgst_rate !== undefined || req.body.igst_rate !== undefined))) {
    const cgst = req.body.cgst_rate !== undefined ? parseFloat(req.body.cgst_rate) : parseFloat(tax.cgst_rate);
    const sgst = req.body.sgst_rate !== undefined ? parseFloat(req.body.sgst_rate) : parseFloat(tax.sgst_rate);
    const igst = req.body.igst_rate !== undefined ? parseFloat(req.body.igst_rate) : parseFloat(tax.igst_rate);

    const totalGST = cgst + sgst;

    if (Math.abs(totalGST - igst) > 0.01 && totalGST > 0 && igst > 0) {
      return next(new AppError('For GST: CGST + SGST should equal IGST', 400));
    }
  }

  // Validate effective dates
  if (req.body.effective_from || req.body.effective_to) {
    const effectiveFrom = req.body.effective_from ? new Date(req.body.effective_from) : tax.effective_from;
    const effectiveTo = req.body.effective_to ? new Date(req.body.effective_to) : tax.effective_to;

    if (effectiveFrom && effectiveTo && effectiveFrom >= effectiveTo) {
      return next(new AppError('Effective from date must be before effective to date', 400));
    }
  }

  // Update tax
  await tax.update({
    ...req.body,
    effective_from: req.body.effective_from ? new Date(req.body.effective_from) : tax.effective_from,
    effective_to: req.body.effective_to ? new Date(req.body.effective_to) : tax.effective_to,
    updated_by: req.user.id
  });

  // Fetch updated tax with associations
  const updatedTax = await Tax.findByPk(id, {
    include: [
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      tax: updatedTax
    }
  });
});

// Delete tax
const deleteTax = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const tax = await Tax.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!tax) {
    return next(new AppError('Tax not found', 404));
  }

  // Check if tax is being used in transactions
  const salesItemCount = await SalesItem.count({
    where: { tax_id: id }
  });

  const invoiceItemCount = await InvoiceItem.count({
    where: { tax_id: id }
  });

  if (salesItemCount > 0 || invoiceItemCount > 0) {
    return next(new AppError(`Cannot delete tax. It is being used in ${salesItemCount + invoiceItemCount} transaction(s)`, 400));
  }

  await tax.destroy();

  res.status(200).json({
    status: 'success',
    message: 'Tax deleted successfully'
  });
});

// Get tax statistics
const getTaxStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  // Total taxes count
  const totalTaxes = await Tax.count({
    where: { company_id: companyId }
  });

  // Active taxes count
  const activeTaxes = await Tax.count({
    where: {
      company_id: companyId,
      is_active: true
    }
  });

  // Taxes by type
  const taxesByType = await Tax.findAll({
    where: { company_id: companyId },
    attributes: [
      'tax_type',
      [Tax.sequelize.fn('COUNT', Tax.sequelize.col('id')), 'count'],
      [Tax.sequelize.fn('AVG', Tax.sequelize.col('tax_rate')), 'avg_rate']
    ],
    group: ['tax_type'],
    raw: true
  });

  // GST taxes breakdown
  const gstTaxes = await Tax.findAll({
    where: {
      company_id: companyId,
      tax_type: 'gst',
      is_active: true
    },
    attributes: ['id', 'tax_name', 'tax_rate', 'cgst_rate', 'sgst_rate', 'igst_rate'],
    order: [['tax_rate', 'ASC']]
  });

  // Most used taxes (by transaction count)
  const mostUsedTaxes = await Tax.findAll({
    where: { company_id: companyId },
    include: [
      {
        model: SalesItem,
        as: 'salesItems',
        attributes: []
      },
      {
        model: InvoiceItem,
        as: 'invoiceItems',
        attributes: []
      }
    ],
    attributes: [
      'id',
      'tax_name',
      'tax_rate',
      [Tax.sequelize.fn('COUNT', Tax.sequelize.col('salesItems.id')), 'sales_usage'],
      [Tax.sequelize.fn('COUNT', Tax.sequelize.col('invoiceItems.id')), 'invoice_usage']
    ],
    group: ['Tax.id'],
    order: [
      [Tax.sequelize.literal('(COUNT("salesItems"."id") + COUNT("invoiceItems"."id"))'), 'DESC']
    ],
    limit: 5,
    raw: false
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_taxes: totalTaxes,
      active_taxes: activeTaxes,
      inactive_taxes: totalTaxes - activeTaxes,
      taxes_by_type: taxesByType,
      gst_taxes: gstTaxes,
      most_used_taxes: mostUsedTaxes
    }
  });
});

// Get taxes by type
const getTaxesByType = catchAsync(async (req, res) => {
  const { tax_type } = req.params;
  const companyId = req.user.company_id;
  const { effective_date } = req.query;

  const whereConditions = {
    company_id: companyId,
    tax_type: tax_type,
    is_active: true
  };

  // Filter by effective date
  if (effective_date) {
    const filterDate = new Date(effective_date);
    whereConditions[Op.and] = [
      {
        [Op.or]: [
          { effective_from: { [Op.lte]: filterDate } },
          { effective_from: { [Op.is]: null } }
        ]
      },
      {
        [Op.or]: [
          { effective_to: { [Op.gte]: filterDate } },
          { effective_to: { [Op.is]: null } }
        ]
      }
    ];
  }

  const taxes = await Tax.findAll({
    where: whereConditions,
    attributes: ['id', 'tax_name', 'tax_code', 'tax_rate', 'cgst_rate', 'sgst_rate', 'igst_rate', 'cess_rate', 'is_compound', 'is_inclusive'],
    order: [['tax_rate', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      taxes
    }
  });
});

// Get active taxes
const getActiveTaxes = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const { effective_date } = req.query;

  const whereConditions = {
    company_id: companyId,
    is_active: true
  };

  // Filter by effective date
  if (effective_date) {
    const filterDate = new Date(effective_date);
    whereConditions[Op.and] = [
      {
        [Op.or]: [
          { effective_from: { [Op.lte]: filterDate } },
          { effective_from: { [Op.is]: null } }
        ]
      },
      {
        [Op.or]: [
          { effective_to: { [Op.gte]: filterDate } },
          { effective_to: { [Op.is]: null } }
        ]
      }
    ];
  }

  const taxes = await Tax.findAll({
    where: whereConditions,
    attributes: ['id', 'tax_name', 'tax_code', 'tax_type', 'tax_rate', 'cgst_rate', 'sgst_rate', 'igst_rate', 'cess_rate', 'is_compound', 'is_inclusive'],
    order: [['tax_type', 'ASC'], ['tax_rate', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      taxes
    }
  });
});

// Calculate tax amount
const calculateTax = catchAsync(async (req, res, next) => {
  const { tax_id, amount, is_inclusive } = req.body;
  const companyId = req.user.company_id;

  if (!tax_id || !amount) {
    return next(new AppError('Tax ID and amount are required', 400));
  }

  const tax = await Tax.findOne({
    where: {
      id: tax_id,
      company_id: companyId,
      is_active: true
    }
  });

  if (!tax) {
    return next(new AppError('Tax not found or inactive', 404));
  }

  const baseAmount = parseFloat(amount);
  const taxRate = parseFloat(tax.tax_rate);
  const cgstRate = parseFloat(tax.cgst_rate);
  const sgstRate = parseFloat(tax.sgst_rate);
  const igstRate = parseFloat(tax.igst_rate);
  const cessRate = parseFloat(tax.cess_rate);

  let taxableAmount, totalTaxAmount, totalAmount;
  let cgstAmount = 0, sgstAmount = 0, igstAmount = 0, cessAmount = 0;

  const inclusive = is_inclusive !== undefined ? is_inclusive : tax.is_inclusive;

  if (inclusive) {
    // Tax is included in the amount
    taxableAmount = baseAmount / (1 + (taxRate / 100));
    totalTaxAmount = baseAmount - taxableAmount;
    totalAmount = baseAmount;
  } else {
    // Tax is additional to the amount
    taxableAmount = baseAmount;
    totalTaxAmount = (taxableAmount * taxRate) / 100;
    totalAmount = taxableAmount + totalTaxAmount;
  }

  // Calculate individual tax components for GST
  if (tax.tax_type === 'gst') {
    if (cgstRate > 0 && sgstRate > 0) {
      // Intra-state transaction (CGST + SGST)
      cgstAmount = (taxableAmount * cgstRate) / 100;
      sgstAmount = (taxableAmount * sgstRate) / 100;
    } else if (igstRate > 0) {
      // Inter-state transaction (IGST)
      igstAmount = (taxableAmount * igstRate) / 100;
    }
  }

  // Calculate CESS if applicable
  if (cessRate > 0) {
    cessAmount = (taxableAmount * cessRate) / 100;
  }

  res.status(200).json({
    status: 'success',
    data: {
      tax_details: {
        tax_id: tax.id,
        tax_name: tax.tax_name,
        tax_type: tax.tax_type,
        tax_rate: taxRate,
        is_inclusive: inclusive
      },
      calculation: {
        base_amount: baseAmount,
        taxable_amount: Math.round(taxableAmount * 100) / 100,
        total_tax_amount: Math.round(totalTaxAmount * 100) / 100,
        total_amount: Math.round(totalAmount * 100) / 100,
        cgst_amount: Math.round(cgstAmount * 100) / 100,
        sgst_amount: Math.round(sgstAmount * 100) / 100,
        igst_amount: Math.round(igstAmount * 100) / 100,
        cess_amount: Math.round(cessAmount * 100) / 100
      }
    }
  });
});

module.exports = {
  getTaxes,
  getTax,
  createTax,
  updateTax,
  deleteTax,
  getTaxStats,
  getTaxesByType,
  getActiveTaxes,
  calculateTax
};
