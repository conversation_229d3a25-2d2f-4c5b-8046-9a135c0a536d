{"name": "tracknew-nodejs-backend", "version": "1.0.0", "description": "Track New Service Management System - Node.js Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node dev-server.js", "production": "node production-server.js", "build": "npm run build:frontend && npm run copy:frontend", "build:frontend": "cd ../tracknew-react-frontend && npm run build", "copy:frontend": "powershell -Command \"Copy-Item -Path '../tracknew-react-frontend/dist/*' -Destination './public' -Recurse -Force\"", "test": "jest", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "seed:undo": "npx sequelize-cli db:seed:undo:all"}, "keywords": ["service-management", "nodejs", "express", "postgresql", "jwt", "api"], "author": "Track New Team", "license": "MIT", "dependencies": {"aws-sdk": "^2.1506.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "connect-redis": "^7.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-slow-down": "^2.0.1", "express-validator": "^7.0.1", "firebase-admin": "^12.0.0", "helmet": "^7.1.0", "hpp": "^0.2.3", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.8", "pdf-lib": "^1.17.1", "pg": "^8.16.0", "pg-hstore": "^2.3.4", "qrcode": "^1.5.3", "redis": "^4.6.12", "sequelize": "^6.35.2", "sequelize-cli": "^6.6.2", "twilio": "^4.19.3", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "xss-clean": "^0.1.4"}, "devDependencies": {"eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}