import React from 'react';
import { 
  ChevronLeftIcon, 
  ChevronRightIcon,
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon
} from '@heroicons/react/24/outline';
import { classNames } from '../../utils/helpers';
import Button from './Button';
import Select from '../forms/Select';

const Pagination = ({
  currentPage = 1,
  totalPages = 1,
  totalItems = 0,
  itemsPerPage = 15,
  onPageChange,
  onItemsPerPageChange,
  showItemsPerPage = true,
  showPageInfo = true,
  showFirstLast = true,
  maxVisiblePages = 5,
  itemsPerPageOptions = [10, 15, 25, 50, 100],
  size = 'md',
  className = '',
  ...props
}) => {
  const startItem = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange?.(page);
    }
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    onItemsPerPageChange?.(newItemsPerPage);
  };

  const getVisiblePages = () => {
    const pages = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    // Adjust start page if we're near the end
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  };

  const visiblePages = getVisiblePages();

  const sizeClasses = {
    sm: {
      button: 'px-2 py-1 text-xs',
      select: 'text-xs',
      text: 'text-xs',
    },
    md: {
      button: 'px-3 py-2 text-sm',
      select: 'text-sm',
      text: 'text-sm',
    },
    lg: {
      button: 'px-4 py-2 text-base',
      select: 'text-base',
      text: 'text-base',
    },
  };

  const buttonBaseClasses = classNames(
    'inline-flex items-center justify-center border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600',
    sizeClasses[size].button
  );

  const pageButtonClasses = (page) => classNames(
    buttonBaseClasses,
    page === currentPage 
      ? 'bg-blue-50 border-blue-500 text-blue-600 dark:bg-blue-900 dark:border-blue-500 dark:text-blue-300'
      : 'hover:text-gray-700 dark:hover:text-gray-200'
  );

  if (totalPages <= 1) {
    return null;
  }

  return (
    <div className={classNames(
      'flex items-center justify-between bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700',
      className
    )} {...props}>
      {/* Left side - Items per page and info */}
      <div className="flex items-center space-x-4">
        {showItemsPerPage && onItemsPerPageChange && (
          <div className="flex items-center space-x-2">
            <span className={classNames(
              'text-gray-700 dark:text-gray-300',
              sizeClasses[size].text
            )}>
              Show
            </span>
            <Select
              value={itemsPerPage}
              onChange={handleItemsPerPageChange}
              options={itemsPerPageOptions.map(option => ({
                value: option,
                label: option.toString()
              }))}
              size={size}
              className="w-20"
            />
            <span className={classNames(
              'text-gray-700 dark:text-gray-300',
              sizeClasses[size].text
            )}>
              per page
            </span>
          </div>
        )}

        {showPageInfo && (
          <div className={classNames(
            'text-gray-700 dark:text-gray-300',
            sizeClasses[size].text
          )}>
            Showing {startItem} to {endItem} of {totalItems} results
          </div>
        )}
      </div>

      {/* Right side - Pagination controls */}
      <div className="flex items-center space-x-1">
        {/* First page */}
        {showFirstLast && currentPage > 1 && (
          <button
            onClick={() => handlePageChange(1)}
            className={classNames(buttonBaseClasses, 'rounded-l-md')}
            title="First page"
          >
            <ChevronDoubleLeftIcon className="h-4 w-4" />
          </button>
        )}

        {/* Previous page */}
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage <= 1}
          className={classNames(
            buttonBaseClasses,
            !showFirstLast && 'rounded-l-md'
          )}
          title="Previous page"
        >
          <ChevronLeftIcon className="h-4 w-4" />
        </button>

        {/* Page numbers */}
        {visiblePages.map((page) => (
          <button
            key={page}
            onClick={() => handlePageChange(page)}
            className={pageButtonClasses(page)}
          >
            {page}
          </button>
        ))}

        {/* Next page */}
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage >= totalPages}
          className={classNames(
            buttonBaseClasses,
            !showFirstLast && 'rounded-r-md'
          )}
          title="Next page"
        >
          <ChevronRightIcon className="h-4 w-4" />
        </button>

        {/* Last page */}
        {showFirstLast && currentPage < totalPages && (
          <button
            onClick={() => handlePageChange(totalPages)}
            className={classNames(buttonBaseClasses, 'rounded-r-md')}
            title="Last page"
          >
            <ChevronDoubleRightIcon className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default Pagination;
