const sequelize = require('../config/database');

// Import all models
const User = require('./User');
const Company = require('./Company');
const Customer = require('./Customer');
const Service = require('./Service');
const ServiceCategory = require('./ServiceCategory');
const ServiceAssign = require('./ServiceAssign');
const Lead = require('./Lead');
const LeadType = require('./LeadType');
const LeadStatus = require('./LeadStatus');
const AMC = require('./AMC');
const AMCProduct = require('./AMCProduct');
const AMCDates = require('./AMCDates');
const AMCUsers = require('./AMCUsers');
const Sales = require('./Sales');
const SalesItem = require('./SalesItem');
const SalesPayment = require('./SalesPayment');
const Product = require('./Product');
const Brand = require('./Brand');
const Category = require('./Category');
const Unit = require('./Unit');
const Estimation = require('./Estimation');
const EstimationItem = require('./EstimationItem');
const Expense = require('./Expense');
const ExpenseCategory = require('./ExpenseCategory');
const Proforma = require('./Proforma');
const ProformaItem = require('./ProformaItem');
const Invoice = require('./Invoice');
const InvoiceItem = require('./InvoiceItem');
const Supplier = require('./Supplier');
const PurchaseOrder = require('./PurchaseOrder');
const PurchaseOrderItem = require('./PurchaseOrderItem');
const PurchaseOrderPayment = require('./PurchaseOrderPayment');
const Warehouse = require('./Warehouse');
const StockMovement = require('./StockMovement');
const Tax = require('./Tax');
const PaymentIn = require('./PaymentIn');
const PaymentOut = require('./PaymentOut');
const Enquiry = require('./Enquiry');
const RMA = require('./RMA');
const RMAItem = require('./RMAItem');
const RMAPayment = require('./RMAPayment');
const RMAUser = require('./RMAUser');
const RMAAccessory = require('./RMAAccessory');
const RMAAdditionalProduct = require('./RMAAdditionalProduct');
const RMAAssignedAccessory = require('./RMAAssignedAccessory');
const Notification = require('./Notification');
const Reminder = require('./Reminder');
const Plan = require('./Plan');
const Role = require('./Role');
const Permission = require('./Permission');
const UserRole = require('./UserRole');
const RolePermission = require('./RolePermission');
const CompanySettings = require('./CompanySettings');
const CompanySites = require('./CompanySites');
const WebsiteTemplates = require('./WebsiteTemplates');
const TemplateTags = require('./TemplateTags');
const InvoiceSettings = require('./InvoiceSettings');
const InvoiceTemplate = require('./InvoiceTemplate');
const HoldInvoices = require('./HoldInvoices');
const ServiceForms = require('./ServiceForms');
const Tickets = require('./Tickets');
const SmsPlans = require('./SmsPlans');
const SmsSetting = require('./SmsSetting');
const WhatsappSettings = require('./WhatsappSettings');
const MessageTransactions = require('./MessageTransactions');
const Gateways = require('./Gateways');
const Orders = require('./Orders');
const Packages = require('./Packages');
const Coupon = require('./Coupon');
const Country = require('./Country');
const Dashboard = require('./Dashboard');
const Module = require('./Module');
const Option = require('./Option');
const Setting = require('./Setting');
const CustomerCategory = require('./CustomerCategory');
const Employee = require('./Employee');
const ProductsBarcode = require('./ProductsBarcode');
const ProductsDetails = require('./ProductsDetails');
const EstimationUsers = require('./EstimationUsers');
const LeadFollows = require('./LeadFollows');

// Define associations
const defineAssociations = () => {
  // User associations
  User.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  User.belongsTo(Plan, { foreignKey: 'plan_id', as: 'plan' });
  User.belongsToMany(Role, { through: UserRole, foreignKey: 'user_id', as: 'roles' });

  // Company associations
  Company.hasMany(User, { foreignKey: 'company_id', as: 'users' });
  Company.hasMany(Customer, { foreignKey: 'company_id', as: 'customers' });
  Company.hasMany(Service, { foreignKey: 'company_id', as: 'services' });
  Company.hasMany(Lead, { foreignKey: 'company_id', as: 'leads' });
  Company.hasMany(AMC, { foreignKey: 'company_id', as: 'amcs' });
  Company.hasMany(Sales, { foreignKey: 'company_id', as: 'sales' });
  Company.hasMany(Product, { foreignKey: 'company_id', as: 'products' });
  Company.hasMany(Estimation, { foreignKey: 'company_id', as: 'estimations' });

  // Customer associations
  Customer.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Customer.hasMany(Service, { foreignKey: 'customer_id', as: 'services' });
  Customer.hasMany(Lead, { foreignKey: 'customer_id', as: 'leads' });
  Customer.hasMany(AMC, { foreignKey: 'customer_id', as: 'amcs' });
  Customer.hasMany(Sales, { foreignKey: 'customer_id', as: 'sales' });
  Customer.hasMany(Estimation, { foreignKey: 'customer_id', as: 'estimations' });

  // Service associations
  Service.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Service.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  Service.belongsTo(ServiceCategory, { foreignKey: 'service_category_id', as: 'category' });
  Service.hasMany(ServiceAssign, { foreignKey: 'service_id', as: 'assigns' });
  Service.belongsToMany(User, { through: ServiceAssign, foreignKey: 'service_id', as: 'assignedUsers' });

  // Service Category associations
  ServiceCategory.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  ServiceCategory.hasMany(Service, { foreignKey: 'service_category_id', as: 'services' });

  // Service Assign associations
  ServiceAssign.belongsTo(Service, { foreignKey: 'service_id', as: 'service' });
  ServiceAssign.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  ServiceAssign.belongsTo(User, { foreignKey: 'assigned_by', as: 'assignedBy' });

  // Lead associations
  Lead.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Lead.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  Lead.belongsTo(LeadType, { foreignKey: 'lead_type_id', as: 'leadType' });
  Lead.belongsTo(LeadStatus, { foreignKey: 'leadstatus_id', as: 'leadStatus' });
  Lead.belongsTo(User, { foreignKey: 'assigned_to', as: 'assignedTo' });

  // Lead Type associations
  LeadType.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  LeadType.hasMany(Lead, { foreignKey: 'lead_type_id', as: 'leads' });

  // Lead Status associations
  LeadStatus.hasMany(Lead, { foreignKey: 'leadstatus_id', as: 'leads' });

  // AMC associations
  AMC.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  AMC.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  AMC.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  AMC.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });
  AMC.hasMany(AMCProduct, { foreignKey: 'amc_id', as: 'products' });
  AMC.hasMany(AMCDates, { foreignKey: 'amc_id', as: 'dates' });
  AMC.belongsToMany(User, { through: AMCUsers, foreignKey: 'amc_id', as: 'assignedUsers' });

  // AMC Product associations
  AMCProduct.belongsTo(AMC, { foreignKey: 'amc_id', as: 'amc' });
  AMCProduct.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

  // AMC Dates associations
  AMCDates.belongsTo(AMC, { foreignKey: 'amc_id', as: 'amc' });
  AMCDates.belongsTo(User, { foreignKey: 'assigned_technician', as: 'technician' });
  AMCDates.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice' });
  AMCDates.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  AMCDates.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });

  // AMC Users associations
  AMCUsers.belongsTo(AMC, { foreignKey: 'amc_id', as: 'amc' });
  AMCUsers.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  AMCUsers.belongsTo(User, { foreignKey: 'assigned_by', as: 'assignedBy' });

  // Sales associations
  Sales.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Sales.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  Sales.belongsTo(User, { foreignKey: 'created_by', as: 'createdBy' });
  Sales.hasMany(SalesItem, { foreignKey: 'sales_id', as: 'items' });
  Sales.hasMany(SalesPayment, { foreignKey: 'sales_id', as: 'payments' });

  // Sales Item associations
  SalesItem.belongsTo(Sales, { foreignKey: 'sales_id', as: 'sales' });
  SalesItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

  // Sales Payment associations
  SalesPayment.belongsTo(Sales, { foreignKey: 'sales_id', as: 'sales' });
  SalesPayment.belongsTo(User, { foreignKey: 'created_by', as: 'createdBy' });

  // Product associations
  Product.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Product.belongsTo(Brand, { foreignKey: 'brand_id', as: 'brand' });
  Product.belongsTo(Category, { foreignKey: 'category_id', as: 'category' });
  Product.belongsTo(Unit, { foreignKey: 'unit_id', as: 'unit' });

  // Brand associations
  Brand.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Brand.hasMany(Product, { foreignKey: 'brand_id', as: 'products' });

  // Category associations
  Category.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Category.belongsTo(Category, { foreignKey: 'parent_id', as: 'parent' });
  Category.hasMany(Category, { foreignKey: 'parent_id', as: 'children' });
  Category.hasMany(Product, { foreignKey: 'category_id', as: 'products' });

  // Unit associations
  Unit.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Unit.belongsTo(User, { foreignKey: 'created_by', as: 'createdBy' });
  Unit.belongsTo(User, { foreignKey: 'updated_by', as: 'updatedBy' });
  Unit.belongsTo(Unit, { foreignKey: 'base_unit_id', as: 'baseUnit' });
  Unit.hasMany(Unit, { foreignKey: 'base_unit_id', as: 'derivedUnits' });
  Unit.hasMany(Product, { foreignKey: 'unit_id', as: 'products' });

  // Estimation associations
  Estimation.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Estimation.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  Estimation.belongsTo(Lead, { foreignKey: 'lead_id', as: 'lead' });
  Estimation.belongsTo(User, { foreignKey: 'created_by', as: 'createdBy' });
  Estimation.hasMany(EstimationItem, { foreignKey: 'estimation_id', as: 'items' });

  // Estimation Item associations
  EstimationItem.belongsTo(Estimation, { foreignKey: 'estimation_id', as: 'estimation' });
  EstimationItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

  // Expense associations
  Expense.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Expense.belongsTo(ExpenseCategory, { foreignKey: 'expense_category_id', as: 'category' });
  Expense.belongsTo(User, { foreignKey: 'created_by', as: 'createdBy' });

  // Expense Category associations
  ExpenseCategory.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  ExpenseCategory.hasMany(Expense, { foreignKey: 'expense_category_id', as: 'expenses' });

  // Notification associations
  Notification.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

  // Reminder associations
  Reminder.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Reminder.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

  // Proforma associations
  Proforma.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Proforma.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  Proforma.belongsTo(Estimation, { foreignKey: 'estimation_id', as: 'estimation' });
  Proforma.belongsTo(Sales, { foreignKey: 'sales_id', as: 'sales' });
  Proforma.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  Proforma.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });
  Proforma.hasMany(ProformaItem, { foreignKey: 'proforma_id', as: 'items' });

  // Proforma Item associations
  ProformaItem.belongsTo(Proforma, { foreignKey: 'proforma_id', as: 'proforma' });
  ProformaItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

  // Invoice associations
  Invoice.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Invoice.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  Invoice.belongsTo(Sales, { foreignKey: 'sales_id', as: 'sales' });
  Invoice.belongsTo(Service, { foreignKey: 'service_id', as: 'service' });
  Invoice.belongsTo(Proforma, { foreignKey: 'proforma_id', as: 'proforma' });
  Invoice.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  Invoice.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });
  Invoice.hasMany(InvoiceItem, { foreignKey: 'invoice_id', as: 'items' });

  // Invoice Item associations
  InvoiceItem.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice' });
  InvoiceItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });
  InvoiceItem.belongsTo(Warehouse, { foreignKey: 'warehouse_id', as: 'warehouse' });

  // Supplier associations
  Supplier.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Supplier.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  Supplier.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });
  Supplier.hasMany(PurchaseOrder, { foreignKey: 'supplier_id', as: 'purchaseOrders' });

  // Purchase Order associations
  PurchaseOrder.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  PurchaseOrder.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'supplier' });
  PurchaseOrder.belongsTo(Warehouse, { foreignKey: 'warehouse_id', as: 'warehouse' });
  PurchaseOrder.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  PurchaseOrder.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });
  PurchaseOrder.belongsTo(User, { foreignKey: 'approved_by', as: 'approver' });
  PurchaseOrder.hasMany(PurchaseOrderItem, { foreignKey: 'purchase_order_id', as: 'items' });
  PurchaseOrder.hasMany(PurchaseOrderPayment, { foreignKey: 'purchase_order_id', as: 'payments' });

  // Purchase Order Item associations
  PurchaseOrderItem.belongsTo(PurchaseOrder, { foreignKey: 'purchase_order_id', as: 'purchaseOrder' });
  PurchaseOrderItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

  // Purchase Order Payment associations
  PurchaseOrderPayment.belongsTo(PurchaseOrder, { foreignKey: 'purchase_order_id', as: 'purchaseOrder' });
  PurchaseOrderPayment.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });

  // Warehouse associations
  Warehouse.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Warehouse.belongsTo(User, { foreignKey: 'manager_id', as: 'manager' });
  Warehouse.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  Warehouse.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });
  Warehouse.hasMany(StockMovement, { foreignKey: 'warehouse_id', as: 'stockMovements' });

  // Stock Movement associations
  StockMovement.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  StockMovement.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });
  StockMovement.belongsTo(Warehouse, { foreignKey: 'warehouse_id', as: 'warehouse' });
  StockMovement.belongsTo(Warehouse, { foreignKey: 'from_warehouse_id', as: 'fromWarehouse' });
  StockMovement.belongsTo(Warehouse, { foreignKey: 'to_warehouse_id', as: 'toWarehouse' });
  StockMovement.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });

  // Tax associations
  Tax.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Tax.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  Tax.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });

  // Payment In associations
  PaymentIn.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  PaymentIn.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  PaymentIn.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  PaymentIn.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });

  // Payment Out associations
  PaymentOut.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  PaymentOut.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'supplier' });
  PaymentOut.belongsTo(User, { foreignKey: 'employee_id', as: 'employee' });
  PaymentOut.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  PaymentOut.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });

  // Enquiry associations
  Enquiry.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  Enquiry.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  Enquiry.belongsTo(User, { foreignKey: 'assigned_to', as: 'assignedTo' });
  Enquiry.belongsTo(Lead, { foreignKey: 'lead_id', as: 'lead' });
  Enquiry.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  Enquiry.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });

  // RMA associations
  RMA.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });
  RMA.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  RMA.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });
  RMA.belongsTo(Product, { foreignKey: 'replacement_product_id', as: 'replacementProduct' });
  RMA.belongsTo(Sales, { foreignKey: 'sales_id', as: 'sales' });
  RMA.belongsTo(Service, { foreignKey: 'service_id', as: 'service' });
  RMA.belongsTo(Warehouse, { foreignKey: 'warehouse_id', as: 'warehouse' });
  RMA.belongsTo(User, { foreignKey: 'assigned_to', as: 'assignedTo' });
  RMA.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  RMA.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });

  // Role and Permission associations
  Role.belongsToMany(User, { through: UserRole, foreignKey: 'role_id', as: 'users' });
  Role.belongsToMany(Permission, { through: RolePermission, foreignKey: 'role_id', as: 'permissions' });
  Permission.belongsToMany(Role, { through: RolePermission, foreignKey: 'permission_id', as: 'roles' });
};

// Initialize associations
defineAssociations();

// Export all models and sequelize instance
module.exports = {
  sequelize,
  User,
  Company,
  Customer,
  Service,
  ServiceCategory,
  ServiceAssign,
  Lead,
  LeadType,
  LeadStatus,
  AMC,
  AMCProduct,
  AMCDates,
  AMCUsers,
  Sales,
  SalesItem,
  SalesPayment,
  Product,
  Brand,
  Category,
  Unit,
  Estimation,
  EstimationItem,
  Expense,
  ExpenseCategory,
  Proforma,
  ProformaItem,
  Invoice,
  InvoiceItem,
  Supplier,
  PurchaseOrder,
  PurchaseOrderItem,
  PurchaseOrderPayment,
  Warehouse,
  StockMovement,
  Tax,
  PaymentIn,
  PaymentOut,
  Enquiry,
  RMA,
  RMAItem,
  RMAPayment,
  RMAUser,
  RMAAccessory,
  RMAAdditionalProduct,
  RMAAssignedAccessory,
  Notification,
  Reminder,
  Plan,
  Role,
  Permission,
  UserRole,
  RolePermission,
  CompanySettings,
  CompanySites,
  WebsiteTemplates,
  TemplateTags,
  InvoiceSettings,
  InvoiceTemplate,
  HoldInvoices,
  ServiceForms,
  Tickets,
  SmsPlans,
  SmsSetting,
  WhatsappSettings,
  MessageTransactions,
  Gateways,
  Orders,
  Packages,
  Coupon,
  Country,
  Dashboard,
  Module,
  Option,
  Setting,
  CustomerCategory,
  Employee,
  ProductsBarcode,
  ProductsDetails,
  EstimationUsers,
  LeadFollows
};
