const { PaymentIn, PaymentOut, Customer, Supplier, User, Company } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all payments (both in and out) with filtering and pagination
const getPayments = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    payment_type, // 'in' or 'out'
    payment_method,
    status,
    start_date,
    end_date,
    customer_id,
    supplier_id,
    is_reconciled,
    sort_by = 'payment_date',
    sort_order = 'DESC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  let payments = [];
  let totalCount = 0;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add date range filter
  if (start_date || end_date) {
    whereConditions.payment_date = {};
    if (start_date) {
      whereConditions.payment_date[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      whereConditions.payment_date[Op.lte] = new Date(end_date);
    }
  }

  // Add filters
  if (payment_method) {
    whereConditions.payment_method = payment_method;
  }

  if (status) {
    whereConditions.status = status;
  }

  if (is_reconciled !== undefined) {
    whereConditions.is_reconciled = is_reconciled === 'true';
  }

  if (customer_id) {
    whereConditions.customer_id = customer_id;
  }

  if (supplier_id) {
    whereConditions.supplier_id = supplier_id;
  }

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { payment_number: { [Op.iLike]: `%${search}%` } },
      { reference_number: { [Op.iLike]: `%${search}%` } },
      { transaction_id: { [Op.iLike]: `%${search}%` } },
      { notes: { [Op.iLike]: `%${search}%` } }
    ];
  }

  if (!payment_type || payment_type === 'in') {
    // Get payments in
    const paymentsIn = await PaymentIn.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'customer_name', 'email', 'mobile_number']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'updatedBy',
          attributes: ['id', 'name', 'email']
        }
      ],
      limit: payment_type === 'in' ? parseInt(limit) : undefined,
      offset: payment_type === 'in' ? offset : 0,
      order: [[sort_by, sort_order.toUpperCase()]],
      distinct: true
    });

    payments = paymentsIn.rows.map(payment => ({
      ...payment.toJSON(),
      payment_direction: 'in'
    }));

    if (payment_type === 'in') {
      totalCount = paymentsIn.count;
    }
  }

  if (!payment_type || payment_type === 'out') {
    // Get payments out
    const paymentsOut = await PaymentOut.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['id', 'supplier_name', 'email', 'phone']
        },
        {
          model: User,
          as: 'employee',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'updatedBy',
          attributes: ['id', 'name', 'email']
        }
      ],
      limit: payment_type === 'out' ? parseInt(limit) : undefined,
      offset: payment_type === 'out' ? offset : 0,
      order: [[sort_by, sort_order.toUpperCase()]],
      distinct: true
    });

    const paymentsOutMapped = paymentsOut.rows.map(payment => ({
      ...payment.toJSON(),
      payment_direction: 'out'
    }));

    if (payment_type === 'out') {
      payments = paymentsOutMapped;
      totalCount = paymentsOut.count;
    } else if (!payment_type) {
      payments = [...payments, ...paymentsOutMapped];
      totalCount = paymentsIn.count + paymentsOut.count;
    }
  }

  // Sort combined results if no specific payment_type
  if (!payment_type) {
    payments.sort((a, b) => {
      const aValue = a[sort_by];
      const bValue = b[sort_by];

      if (sort_order.toUpperCase() === 'DESC') {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });

    // Apply pagination for combined results
    payments = payments.slice(offset, offset + parseInt(limit));
  }

  // Calculate pagination
  const totalPages = Math.ceil(totalCount / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      payments,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: totalCount,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single payment by ID and type
const getPayment = catchAsync(async (req, res, next) => {
  const { id, type } = req.params; // type: 'in' or 'out'
  const companyId = req.user.company_id;

  let payment;

  if (type === 'in') {
    payment = await PaymentIn.findOne({
      where: {
        id,
        company_id: companyId
      },
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'customer_name', 'email', 'mobile_number', 'address']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'updatedBy',
          attributes: ['id', 'name', 'email']
        }
      ]
    });
  } else if (type === 'out') {
    payment = await PaymentOut.findOne({
      where: {
        id,
        company_id: companyId
      },
      include: [
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['id', 'supplier_name', 'email', 'phone', 'address']
        },
        {
          model: User,
          as: 'employee',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'updatedBy',
          attributes: ['id', 'name', 'email']
        }
      ]
    });
  } else {
    return next(new AppError('Invalid payment type. Must be "in" or "out"', 400));
  }

  if (!payment) {
    return next(new AppError('Payment not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      payment: {
        ...payment.toJSON(),
        payment_direction: type
      }
    }
  });
});

// Create new payment in
const createPaymentIn = catchAsync(async (req, res, next) => {
  const {
    customer_id,
    payment_date = new Date(),
    payment_method,
    amount,
    currency = 'INR',
    exchange_rate = 1,
    reference_number,
    bank_name,
    cheque_number,
    cheque_date,
    transaction_id,
    status = 'completed',
    payment_type = 'invoice_payment',
    notes,
    attachment
  } = req.body;

  const companyId = req.user.company_id;

  // Validate customer
  const customer = await Customer.findOne({
    where: {
      id: customer_id,
      company_id: companyId
    }
  });

  if (!customer) {
    return next(new AppError('Customer not found', 404));
  }

  const paymentIn = await PaymentIn.create({
    company_id: companyId,
    customer_id,
    payment_date: new Date(payment_date),
    payment_method,
    amount,
    currency,
    exchange_rate,
    reference_number,
    bank_name,
    cheque_number,
    cheque_date: cheque_date ? new Date(cheque_date) : null,
    transaction_id,
    status,
    payment_type,
    notes,
    attachment,
    created_by: req.user.id
  });

  // Fetch the created payment with associations
  const createdPayment = await PaymentIn.findByPk(paymentIn.id, {
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'email', 'mobile_number']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      payment: {
        ...createdPayment.toJSON(),
        payment_direction: 'in'
      }
    }
  });
});

// Create new payment out
const createPaymentOut = catchAsync(async (req, res, next) => {
  const {
    supplier_id,
    employee_id,
    payment_date = new Date(),
    payment_method,
    amount,
    currency = 'INR',
    exchange_rate = 1,
    reference_number,
    bank_name,
    cheque_number,
    cheque_date,
    transaction_id,
    status = 'completed',
    payment_type = 'purchase_payment',
    payee_name,
    purpose,
    notes,
    attachment
  } = req.body;

  const companyId = req.user.company_id;

  // Validate supplier if provided
  if (supplier_id) {
    const supplier = await Supplier.findOne({
      where: {
        id: supplier_id,
        company_id: companyId
      }
    });

    if (!supplier) {
      return next(new AppError('Supplier not found', 404));
    }
  }

  // Validate employee if provided
  if (employee_id) {
    const employee = await User.findOne({
      where: {
        id: employee_id,
        company_id: companyId,
        is_active: true
      }
    });

    if (!employee) {
      return next(new AppError('Employee not found or inactive', 404));
    }
  }

  const paymentOut = await PaymentOut.create({
    company_id: companyId,
    supplier_id,
    employee_id,
    payment_date: new Date(payment_date),
    payment_method,
    amount,
    currency,
    exchange_rate,
    reference_number,
    bank_name,
    cheque_number,
    cheque_date: cheque_date ? new Date(cheque_date) : null,
    transaction_id,
    status,
    payment_type,
    payee_name,
    purpose,
    notes,
    attachment,
    created_by: req.user.id
  });

  // Fetch the created payment with associations
  const createdPayment = await PaymentOut.findByPk(paymentOut.id, {
    include: [
      {
        model: Supplier,
        as: 'supplier',
        attributes: ['id', 'supplier_name', 'email', 'phone']
      },
      {
        model: User,
        as: 'employee',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      payment: {
        ...createdPayment.toJSON(),
        payment_direction: 'out'
      }
    }
  });
});

// Update payment
const updatePayment = catchAsync(async (req, res, next) => {
  const { id, type } = req.params;
  const companyId = req.user.company_id;

  let payment;
  let PaymentModel;

  if (type === 'in') {
    PaymentModel = PaymentIn;
    payment = await PaymentIn.findOne({
      where: {
        id,
        company_id: companyId
      }
    });
  } else if (type === 'out') {
    PaymentModel = PaymentOut;
    payment = await PaymentOut.findOne({
      where: {
        id,
        company_id: companyId
      }
    });
  } else {
    return next(new AppError('Invalid payment type. Must be "in" or "out"', 400));
  }

  if (!payment) {
    return next(new AppError('Payment not found', 404));
  }

  // Handle date fields
  const dateFields = ['payment_date', 'cheque_date', 'reconciled_date'];
  dateFields.forEach(field => {
    if (req.body[field]) {
      req.body[field] = new Date(req.body[field]);
    }
  });

  // Update payment
  await payment.update({
    ...req.body,
    updated_by: req.user.id
  });

  // Fetch updated payment with associations
  let updatedPayment;
  if (type === 'in') {
    updatedPayment = await PaymentIn.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'customer_name', 'email', 'mobile_number']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'updatedBy',
          attributes: ['id', 'name', 'email']
        }
      ]
    });
  } else {
    updatedPayment = await PaymentOut.findByPk(id, {
      include: [
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['id', 'supplier_name', 'email', 'phone']
        },
        {
          model: User,
          as: 'employee',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'updatedBy',
          attributes: ['id', 'name', 'email']
        }
      ]
    });
  }

  res.status(200).json({
    status: 'success',
    data: {
      payment: {
        ...updatedPayment.toJSON(),
        payment_direction: type
      }
    }
  });
});

// Delete payment
const deletePayment = catchAsync(async (req, res, next) => {
  const { id, type } = req.params;
  const companyId = req.user.company_id;

  let payment;

  if (type === 'in') {
    payment = await PaymentIn.findOne({
      where: {
        id,
        company_id: companyId
      }
    });
  } else if (type === 'out') {
    payment = await PaymentOut.findOne({
      where: {
        id,
        company_id: companyId
      }
    });
  } else {
    return next(new AppError('Invalid payment type. Must be "in" or "out"', 400));
  }

  if (!payment) {
    return next(new AppError('Payment not found', 404));
  }

  // Check if payment is reconciled
  if (payment.is_reconciled) {
    return next(new AppError('Cannot delete reconciled payment', 400));
  }

  await payment.destroy();

  res.status(200).json({
    status: 'success',
    message: 'Payment deleted successfully'
  });
});

// Get payment statistics
const getPaymentStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const { start_date, end_date } = req.query;

  // Build date filter
  const dateFilter = {};
  if (start_date || end_date) {
    dateFilter.payment_date = {};
    if (start_date) {
      dateFilter.payment_date[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      dateFilter.payment_date[Op.lte] = new Date(end_date);
    }
  }

  // Payments In Statistics
  const paymentsInStats = await PaymentIn.findAll({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      [PaymentIn.sequelize.fn('COUNT', PaymentIn.sequelize.col('id')), 'count'],
      [PaymentIn.sequelize.fn('SUM', PaymentIn.sequelize.col('amount')), 'total_amount'],
      [PaymentIn.sequelize.fn('AVG', PaymentIn.sequelize.col('amount')), 'avg_amount']
    ],
    raw: true
  });

  // Payments Out Statistics
  const paymentsOutStats = await PaymentOut.findAll({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      [PaymentOut.sequelize.fn('COUNT', PaymentOut.sequelize.col('id')), 'count'],
      [PaymentOut.sequelize.fn('SUM', PaymentOut.sequelize.col('amount')), 'total_amount'],
      [PaymentOut.sequelize.fn('AVG', PaymentOut.sequelize.col('amount')), 'avg_amount']
    ],
    raw: true
  });

  // Payments by method (In)
  const paymentsByMethodIn = await PaymentIn.findAll({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      'payment_method',
      [PaymentIn.sequelize.fn('COUNT', PaymentIn.sequelize.col('id')), 'count'],
      [PaymentIn.sequelize.fn('SUM', PaymentIn.sequelize.col('amount')), 'total_amount']
    ],
    group: ['payment_method'],
    raw: true
  });

  // Payments by method (Out)
  const paymentsByMethodOut = await PaymentOut.findAll({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      'payment_method',
      [PaymentOut.sequelize.fn('COUNT', PaymentOut.sequelize.col('id')), 'count'],
      [PaymentOut.sequelize.fn('SUM', PaymentOut.sequelize.col('amount')), 'total_amount']
    ],
    group: ['payment_method'],
    raw: true
  });

  // Reconciliation status
  const reconciliationStats = {
    in: await PaymentIn.findAll({
      where: {
        company_id: companyId,
        ...dateFilter
      },
      attributes: [
        'is_reconciled',
        [PaymentIn.sequelize.fn('COUNT', PaymentIn.sequelize.col('id')), 'count']
      ],
      group: ['is_reconciled'],
      raw: true
    }),
    out: await PaymentOut.findAll({
      where: {
        company_id: companyId,
        ...dateFilter
      },
      attributes: [
        'is_reconciled',
        [PaymentOut.sequelize.fn('COUNT', PaymentOut.sequelize.col('id')), 'count']
      ],
      group: ['is_reconciled'],
      raw: true
    })
  };

  res.status(200).json({
    status: 'success',
    data: {
      payments_in: {
        count: parseInt(paymentsInStats[0]?.count || 0),
        total_amount: parseFloat(paymentsInStats[0]?.total_amount || 0),
        avg_amount: parseFloat(paymentsInStats[0]?.avg_amount || 0),
        by_method: paymentsByMethodIn
      },
      payments_out: {
        count: parseInt(paymentsOutStats[0]?.count || 0),
        total_amount: parseFloat(paymentsOutStats[0]?.total_amount || 0),
        avg_amount: parseFloat(paymentsOutStats[0]?.avg_amount || 0),
        by_method: paymentsByMethodOut
      },
      net_flow: parseFloat(paymentsInStats[0]?.total_amount || 0) - parseFloat(paymentsOutStats[0]?.total_amount || 0),
      reconciliation_status: reconciliationStats
    }
  });
});

// Reconcile payment
const reconcilePayment = catchAsync(async (req, res, next) => {
  const { id, type } = req.params;
  const companyId = req.user.company_id;

  let payment;

  if (type === 'in') {
    payment = await PaymentIn.findOne({
      where: {
        id,
        company_id: companyId
      }
    });
  } else if (type === 'out') {
    payment = await PaymentOut.findOne({
      where: {
        id,
        company_id: companyId
      }
    });
  } else {
    return next(new AppError('Invalid payment type. Must be "in" or "out"', 400));
  }

  if (!payment) {
    return next(new AppError('Payment not found', 404));
  }

  if (payment.is_reconciled) {
    return next(new AppError('Payment is already reconciled', 400));
  }

  await payment.update({
    is_reconciled: true,
    reconciled_date: new Date(),
    updated_by: req.user.id
  });

  res.status(200).json({
    status: 'success',
    message: 'Payment reconciled successfully',
    data: {
      payment: {
        id: payment.id,
        payment_number: payment.payment_number,
        is_reconciled: true,
        reconciled_date: payment.reconciled_date
      }
    }
  });
});

// Bulk reconcile payments
const bulkReconcilePayments = catchAsync(async (req, res, next) => {
  const { payment_ids, type } = req.body;
  const companyId = req.user.company_id;

  if (!Array.isArray(payment_ids) || payment_ids.length === 0) {
    return next(new AppError('Payment IDs array is required', 400));
  }

  if (!['in', 'out'].includes(type)) {
    return next(new AppError('Invalid payment type. Must be "in" or "out"', 400));
  }

  const PaymentModel = type === 'in' ? PaymentIn : PaymentOut;

  // Update payments
  const [updatedCount] = await PaymentModel.update(
    {
      is_reconciled: true,
      reconciled_date: new Date(),
      updated_by: req.user.id
    },
    {
      where: {
        id: { [Op.in]: payment_ids },
        company_id: companyId,
        is_reconciled: false
      }
    }
  );

  res.status(200).json({
    status: 'success',
    message: `${updatedCount} payment(s) reconciled successfully`,
    data: {
      reconciled_count: updatedCount
    }
  });
});

// Get cash flow report
const getCashFlowReport = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const { start_date, end_date, group_by = 'month' } = req.query;

  // Build date filter
  const dateFilter = {};
  if (start_date || end_date) {
    dateFilter.payment_date = {};
    if (start_date) {
      dateFilter.payment_date[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      dateFilter.payment_date[Op.lte] = new Date(end_date);
    }
  }

  // Determine date grouping
  let dateFormat;
  switch (group_by) {
    case 'day':
      dateFormat = '%Y-%m-%d';
      break;
    case 'week':
      dateFormat = '%Y-%u';
      break;
    case 'month':
      dateFormat = '%Y-%m';
      break;
    case 'year':
      dateFormat = '%Y';
      break;
    default:
      dateFormat = '%Y-%m';
  }

  // Cash inflow
  const cashInflow = await PaymentIn.findAll({
    where: {
      company_id: companyId,
      status: 'completed',
      ...dateFilter
    },
    attributes: [
      [PaymentIn.sequelize.fn('DATE_FORMAT', PaymentIn.sequelize.col('payment_date'), dateFormat), 'period'],
      [PaymentIn.sequelize.fn('SUM', PaymentIn.sequelize.col('amount')), 'total_amount'],
      [PaymentIn.sequelize.fn('COUNT', PaymentIn.sequelize.col('id')), 'count']
    ],
    group: [PaymentIn.sequelize.fn('DATE_FORMAT', PaymentIn.sequelize.col('payment_date'), dateFormat)],
    order: [[PaymentIn.sequelize.fn('DATE_FORMAT', PaymentIn.sequelize.col('payment_date'), dateFormat), 'ASC']],
    raw: true
  });

  // Cash outflow
  const cashOutflow = await PaymentOut.findAll({
    where: {
      company_id: companyId,
      status: 'completed',
      ...dateFilter
    },
    attributes: [
      [PaymentOut.sequelize.fn('DATE_FORMAT', PaymentOut.sequelize.col('payment_date'), dateFormat), 'period'],
      [PaymentOut.sequelize.fn('SUM', PaymentOut.sequelize.col('amount')), 'total_amount'],
      [PaymentOut.sequelize.fn('COUNT', PaymentOut.sequelize.col('id')), 'count']
    ],
    group: [PaymentOut.sequelize.fn('DATE_FORMAT', PaymentOut.sequelize.col('payment_date'), dateFormat)],
    order: [[PaymentOut.sequelize.fn('DATE_FORMAT', PaymentOut.sequelize.col('payment_date'), dateFormat), 'ASC']],
    raw: true
  });

  // Combine and calculate net flow
  const periods = new Set([
    ...cashInflow.map(item => item.period),
    ...cashOutflow.map(item => item.period)
  ]);

  const cashFlowData = Array.from(periods).sort().map(period => {
    const inflow = cashInflow.find(item => item.period === period);
    const outflow = cashOutflow.find(item => item.period === period);

    const inflowAmount = parseFloat(inflow?.total_amount || 0);
    const outflowAmount = parseFloat(outflow?.total_amount || 0);

    return {
      period,
      inflow: inflowAmount,
      outflow: outflowAmount,
      net_flow: inflowAmount - outflowAmount,
      inflow_count: parseInt(inflow?.count || 0),
      outflow_count: parseInt(outflow?.count || 0)
    };
  });

  res.status(200).json({
    status: 'success',
    data: {
      cash_flow: cashFlowData,
      summary: {
        total_inflow: cashFlowData.reduce((sum, item) => sum + item.inflow, 0),
        total_outflow: cashFlowData.reduce((sum, item) => sum + item.outflow, 0),
        net_flow: cashFlowData.reduce((sum, item) => sum + item.net_flow, 0)
      }
    }
  });
});

module.exports = {
  getPayments,
  getPayment,
  createPaymentIn,
  createPaymentOut,
  updatePayment,
  deletePayment,
  getPaymentStats,
  reconcilePayment,
  bulkReconcilePayments,
  getCashFlowReport
};
