import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiMethods } from '../../services/api';

// Async thunks
export const fetchDashboardOverview = createAsyncThunk(
  'dashboard/fetchDashboardOverview',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/dashboard/overview', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchRecentActivities = createAsyncThunk(
  'dashboard/fetchRecentActivities',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/dashboard/activities', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchSalesDashboard = createAsyncThunk(
  'dashboard/fetchSalesDashboard',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/dashboard/sales', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchFinancialDashboard = createAsyncThunk(
  'dashboard/fetchFinancialDashboard',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/dashboard/financial', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchInventoryDashboard = createAsyncThunk(
  'dashboard/fetchInventoryDashboard',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/dashboard/inventory', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchTopCustomers = createAsyncThunk(
  'dashboard/fetchTopCustomers',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/dashboard/top-customers', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchPaymentDashboard = createAsyncThunk(
  'dashboard/fetchPaymentDashboard',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/dashboard/payments', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchServiceStatusDistribution = createAsyncThunk(
  'dashboard/fetchServiceStatusDistribution',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/dashboard/status-distribution', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchServiceTrends = createAsyncThunk(
  'dashboard/fetchServiceTrends',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/dashboard/trends', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  // Overview data
  overview: null,
  recentActivities: [],
  
  // Widget data
  salesData: null,
  financialData: null,
  inventoryData: null,
  topCustomers: [],
  paymentData: null,
  serviceStatusDistribution: null,
  serviceTrends: null,
  
  // Loading states
  overviewLoading: false,
  activitiesLoading: false,
  salesLoading: false,
  financialLoading: false,
  inventoryLoading: false,
  customersLoading: false,
  paymentsLoading: false,
  statusLoading: false,
  trendsLoading: false,
  
  // Error states
  overviewError: null,
  activitiesError: null,
  salesError: null,
  financialError: null,
  inventoryError: null,
  customersError: null,
  paymentsError: null,
  statusError: null,
  trendsError: null,
  
  // UI states
  selectedDateRange: {
    start: null,
    end: null,
  },
  refreshInterval: 300000, // 5 minutes
  autoRefresh: true,
  widgetLayout: [
    { id: 'overview', enabled: true, order: 1 },
    { id: 'sales', enabled: true, order: 2 },
    { id: 'financial', enabled: true, order: 3 },
    { id: 'inventory', enabled: true, order: 4 },
    { id: 'customers', enabled: true, order: 5 },
    { id: 'payments', enabled: true, order: 6 },
    { id: 'activities', enabled: true, order: 7 },
    { id: 'status', enabled: true, order: 8 },
    { id: 'trends', enabled: true, order: 9 },
  ],
  lastRefresh: null,
};

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    // Date range actions
    setDateRange: (state, action) => {
      state.selectedDateRange = action.payload;
    },
    clearDateRange: (state) => {
      state.selectedDateRange = { start: null, end: null };
    },
    
    // Auto refresh actions
    setAutoRefresh: (state, action) => {
      state.autoRefresh = action.payload;
    },
    setRefreshInterval: (state, action) => {
      state.refreshInterval = action.payload;
    },
    updateLastRefresh: (state) => {
      state.lastRefresh = new Date().toISOString();
    },
    
    // Widget layout actions
    updateWidgetLayout: (state, action) => {
      state.widgetLayout = action.payload;
    },
    toggleWidget: (state, action) => {
      const widgetId = action.payload;
      const widget = state.widgetLayout.find(w => w.id === widgetId);
      if (widget) {
        widget.enabled = !widget.enabled;
      }
    },
    reorderWidgets: (state, action) => {
      const { sourceIndex, destinationIndex } = action.payload;
      const [removed] = state.widgetLayout.splice(sourceIndex, 1);
      state.widgetLayout.splice(destinationIndex, 0, removed);
      
      // Update order numbers
      state.widgetLayout.forEach((widget, index) => {
        widget.order = index + 1;
      });
    },
    
    // Clear errors
    clearOverviewError: (state) => {
      state.overviewError = null;
    },
    clearActivitiesError: (state) => {
      state.activitiesError = null;
    },
    clearSalesError: (state) => {
      state.salesError = null;
    },
    clearFinancialError: (state) => {
      state.financialError = null;
    },
    clearInventoryError: (state) => {
      state.inventoryError = null;
    },
    clearCustomersError: (state) => {
      state.customersError = null;
    },
    clearPaymentsError: (state) => {
      state.paymentsError = null;
    },
    clearStatusError: (state) => {
      state.statusError = null;
    },
    clearTrendsError: (state) => {
      state.trendsError = null;
    },
    clearAllErrors: (state) => {
      state.overviewError = null;
      state.activitiesError = null;
      state.salesError = null;
      state.financialError = null;
      state.inventoryError = null;
      state.customersError = null;
      state.paymentsError = null;
      state.statusError = null;
      state.trendsError = null;
    },
    
    // Reset state
    resetDashboardState: (state) => {
      return {
        ...initialState,
        widgetLayout: state.widgetLayout,
        autoRefresh: state.autoRefresh,
        refreshInterval: state.refreshInterval,
      };
    },
  },
  extraReducers: (builder) => {
    // Fetch dashboard overview
    builder
      .addCase(fetchDashboardOverview.pending, (state) => {
        state.overviewLoading = true;
        state.overviewError = null;
      })
      .addCase(fetchDashboardOverview.fulfilled, (state, action) => {
        state.overviewLoading = false;
        state.overview = action.payload.data;
        state.lastRefresh = new Date().toISOString();
      })
      .addCase(fetchDashboardOverview.rejected, (state, action) => {
        state.overviewLoading = false;
        state.overviewError = action.payload;
      });
    
    // Fetch recent activities
    builder
      .addCase(fetchRecentActivities.pending, (state) => {
        state.activitiesLoading = true;
        state.activitiesError = null;
      })
      .addCase(fetchRecentActivities.fulfilled, (state, action) => {
        state.activitiesLoading = false;
        state.recentActivities = action.payload.data.activities || action.payload.data.recent_services || [];
      })
      .addCase(fetchRecentActivities.rejected, (state, action) => {
        state.activitiesLoading = false;
        state.activitiesError = action.payload;
      });
    
    // Fetch sales dashboard
    builder
      .addCase(fetchSalesDashboard.pending, (state) => {
        state.salesLoading = true;
        state.salesError = null;
      })
      .addCase(fetchSalesDashboard.fulfilled, (state, action) => {
        state.salesLoading = false;
        state.salesData = action.payload.data;
      })
      .addCase(fetchSalesDashboard.rejected, (state, action) => {
        state.salesLoading = false;
        state.salesError = action.payload;
      });
    
    // Fetch financial dashboard
    builder
      .addCase(fetchFinancialDashboard.pending, (state) => {
        state.financialLoading = true;
        state.financialError = null;
      })
      .addCase(fetchFinancialDashboard.fulfilled, (state, action) => {
        state.financialLoading = false;
        state.financialData = action.payload.data;
      })
      .addCase(fetchFinancialDashboard.rejected, (state, action) => {
        state.financialLoading = false;
        state.financialError = action.payload;
      });
    
    // Fetch inventory dashboard
    builder
      .addCase(fetchInventoryDashboard.pending, (state) => {
        state.inventoryLoading = true;
        state.inventoryError = null;
      })
      .addCase(fetchInventoryDashboard.fulfilled, (state, action) => {
        state.inventoryLoading = false;
        state.inventoryData = action.payload.data;
      })
      .addCase(fetchInventoryDashboard.rejected, (state, action) => {
        state.inventoryLoading = false;
        state.inventoryError = action.payload;
      });
    
    // Fetch top customers
    builder
      .addCase(fetchTopCustomers.pending, (state) => {
        state.customersLoading = true;
        state.customersError = null;
      })
      .addCase(fetchTopCustomers.fulfilled, (state, action) => {
        state.customersLoading = false;
        state.topCustomers = action.payload.data.top_customers || [];
      })
      .addCase(fetchTopCustomers.rejected, (state, action) => {
        state.customersLoading = false;
        state.customersError = action.payload;
      });
    
    // Fetch payment dashboard
    builder
      .addCase(fetchPaymentDashboard.pending, (state) => {
        state.paymentsLoading = true;
        state.paymentsError = null;
      })
      .addCase(fetchPaymentDashboard.fulfilled, (state, action) => {
        state.paymentsLoading = false;
        state.paymentData = action.payload.data;
      })
      .addCase(fetchPaymentDashboard.rejected, (state, action) => {
        state.paymentsLoading = false;
        state.paymentsError = action.payload;
      });
    
    // Fetch service status distribution
    builder
      .addCase(fetchServiceStatusDistribution.pending, (state) => {
        state.statusLoading = true;
        state.statusError = null;
      })
      .addCase(fetchServiceStatusDistribution.fulfilled, (state, action) => {
        state.statusLoading = false;
        state.serviceStatusDistribution = action.payload.data;
      })
      .addCase(fetchServiceStatusDistribution.rejected, (state, action) => {
        state.statusLoading = false;
        state.statusError = action.payload;
      });
    
    // Fetch service trends
    builder
      .addCase(fetchServiceTrends.pending, (state) => {
        state.trendsLoading = true;
        state.trendsError = null;
      })
      .addCase(fetchServiceTrends.fulfilled, (state, action) => {
        state.trendsLoading = false;
        state.serviceTrends = action.payload.data;
      })
      .addCase(fetchServiceTrends.rejected, (state, action) => {
        state.trendsLoading = false;
        state.trendsError = action.payload;
      });
  },
});

export const {
  setDateRange,
  clearDateRange,
  setAutoRefresh,
  setRefreshInterval,
  updateLastRefresh,
  updateWidgetLayout,
  toggleWidget,
  reorderWidgets,
  clearOverviewError,
  clearActivitiesError,
  clearSalesError,
  clearFinancialError,
  clearInventoryError,
  clearCustomersError,
  clearPaymentsError,
  clearStatusError,
  clearTrendsError,
  clearAllErrors,
  resetDashboardState,
} = dashboardSlice.actions;

export default dashboardSlice.reducer;

// Selectors
export const selectDashboardOverview = (state) => state.dashboard.overview;
export const selectRecentActivities = (state) => state.dashboard.recentActivities;
export const selectSalesData = (state) => state.dashboard.salesData;
export const selectFinancialData = (state) => state.dashboard.financialData;
export const selectInventoryData = (state) => state.dashboard.inventoryData;
export const selectTopCustomers = (state) => state.dashboard.topCustomers;
export const selectPaymentData = (state) => state.dashboard.paymentData;
export const selectServiceStatusDistribution = (state) => state.dashboard.serviceStatusDistribution;
export const selectServiceTrends = (state) => state.dashboard.serviceTrends;
export const selectDashboardLoading = (state) => ({
  overview: state.dashboard.overviewLoading,
  activities: state.dashboard.activitiesLoading,
  sales: state.dashboard.salesLoading,
  financial: state.dashboard.financialLoading,
  inventory: state.dashboard.inventoryLoading,
  customers: state.dashboard.customersLoading,
  payments: state.dashboard.paymentsLoading,
  status: state.dashboard.statusLoading,
  trends: state.dashboard.trendsLoading,
});
export const selectDashboardErrors = (state) => ({
  overview: state.dashboard.overviewError,
  activities: state.dashboard.activitiesError,
  sales: state.dashboard.salesError,
  financial: state.dashboard.financialError,
  inventory: state.dashboard.inventoryError,
  customers: state.dashboard.customersError,
  payments: state.dashboard.paymentsError,
  status: state.dashboard.statusError,
  trends: state.dashboard.trendsError,
});
export const selectDateRange = (state) => state.dashboard.selectedDateRange;
export const selectAutoRefresh = (state) => state.dashboard.autoRefresh;
export const selectRefreshInterval = (state) => state.dashboard.refreshInterval;
export const selectWidgetLayout = (state) => state.dashboard.widgetLayout;
export const selectLastRefresh = (state) => state.dashboard.lastRefresh;
