<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrackNew - Frontend Integration Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-result {
            transition: all 0.3s ease;
        }
        .success { background-color: #10b981; }
        .error { background-color: #ef4444; }
        .pending { background-color: #f59e0b; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">🚀 TrackNew Integration Test</h1>
                <p class="text-gray-600">Testing Frontend to Backend API Connection</p>
            </div>

            <!-- Test Controls -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="flex justify-center space-x-4">
                    <button id="runTests" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        🧪 Run Integration Tests
                    </button>
                    <button id="clearResults" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        🗑️ Clear Results
                    </button>
                </div>
            </div>

            <!-- Test Results Summary -->
            <div id="summary" class="bg-white rounded-lg shadow-md p-6 mb-6 hidden">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">📊 Test Summary</h2>
                <div class="grid grid-cols-4 gap-4 text-center">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div id="totalTests" class="text-3xl font-bold text-blue-600">0</div>
                        <div class="text-sm text-gray-600">Total Tests</div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div id="passedTests" class="text-3xl font-bold text-green-600">0</div>
                        <div class="text-sm text-gray-600">Passed</div>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <div id="failedTests" class="text-3xl font-bold text-red-600">0</div>
                        <div class="text-sm text-gray-600">Failed</div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div id="successRate" class="text-3xl font-bold text-purple-600">0%</div>
                        <div class="text-sm text-gray-600">Success Rate</div>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">🧪 Test Results</h2>
                <div id="testResults" class="space-y-3">
                    <p class="text-gray-500 text-center py-8">Click "Run Integration Tests" to start testing...</p>
                </div>
            </div>

            <!-- Instructions -->
            <div class="bg-blue-50 rounded-lg p-6 mt-6">
                <h3 class="text-lg font-semibold text-blue-900 mb-3">📋 Instructions</h3>
                <div class="text-blue-800 space-y-2">
                    <p>✅ <strong>Backend Status:</strong> Running on http://localhost:8000</p>
                    <p>🔄 <strong>Test Coverage:</strong> Health checks, Authentication, Dashboard APIs, CRUD operations</p>
                    <p>🎯 <strong>Expected Result:</strong> 100% success rate (15/15 tests passing)</p>
                    <p>🚀 <strong>Next Steps:</strong> If tests pass, the integration is working perfectly!</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        
        const tests = [
            { name: 'Health Check', url: '/health', method: 'GET' },
            { name: 'API Health', url: '/api/health', method: 'GET' },
            { name: 'Dashboard Overview', url: '/api/dashboard/overview', method: 'GET' },
            { name: 'Dashboard Sales', url: '/api/dashboard/sales', method: 'GET' },
            { name: 'Dashboard Financial', url: '/api/dashboard/financial', method: 'GET' },
            { name: 'Dashboard Inventory', url: '/api/dashboard/inventory', method: 'GET' },
            { name: 'Dashboard Top Customers', url: '/api/dashboard/top-customers', method: 'GET' },
            { name: 'Dashboard Activities', url: '/api/dashboard/activities', method: 'GET' },
            { name: 'Customers List', url: '/api/customers', method: 'GET' },
            { name: 'Services List', url: '/api/services', method: 'GET' },
            { name: 'Products List', url: '/api/products', method: 'GET' },
            { name: 'Sales List', url: '/api/sales', method: 'GET' },
            { name: 'Auth Login', url: '/api/auth/login', method: 'POST', data: { email: '<EMAIL>', password: 'admin123' } },
            { name: 'Auth Register', url: '/api/auth/register', method: 'POST', data: { name: 'Test User', email: '<EMAIL>', password: 'password123' } },
            { name: 'Auth Profile', url: '/api/auth/profile', method: 'GET' }
        ];

        async function makeRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                };

                if (data && method === 'POST') {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(url, options);
                const responseData = await response.json();

                return {
                    success: response.ok,
                    status: response.status,
                    data: responseData
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        function createTestElement(test, index) {
            return `
                <div id="test-${index}" class="test-result flex items-center justify-between p-4 rounded-lg border pending">
                    <div class="flex items-center space-x-3">
                        <div class="w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center">
                            <span class="text-white text-xs">⏳</span>
                        </div>
                        <span class="font-medium">${test.name}</span>
                    </div>
                    <div class="text-sm text-gray-600">
                        <span id="status-${index}">Pending...</span>
                    </div>
                </div>
            `;
        }

        function updateTestResult(index, success, status, error = null) {
            const testElement = document.getElementById(`test-${index}`);
            const statusElement = document.getElementById(`status-${index}`);
            
            if (success) {
                testElement.className = 'test-result flex items-center justify-between p-4 rounded-lg border success text-white';
                testElement.querySelector('.w-6').innerHTML = '<span class="text-white text-xs">✅</span>';
                statusElement.textContent = `PASSED (${status})`;
            } else {
                testElement.className = 'test-result flex items-center justify-between p-4 rounded-lg border error text-white';
                testElement.querySelector('.w-6').innerHTML = '<span class="text-white text-xs">❌</span>';
                statusElement.textContent = error ? `ERROR: ${error}` : `FAILED (${status})`;
            }
        }

        function updateSummary(total, passed, failed) {
            document.getElementById('totalTests').textContent = total;
            document.getElementById('passedTests').textContent = passed;
            document.getElementById('failedTests').textContent = failed;
            document.getElementById('successRate').textContent = total > 0 ? Math.round((passed / total) * 100) + '%' : '0%';
            document.getElementById('summary').classList.remove('hidden');
        }

        async function runTests() {
            const runButton = document.getElementById('runTests');
            const testResults = document.getElementById('testResults');
            
            runButton.disabled = true;
            runButton.textContent = '🔄 Running Tests...';
            
            // Clear previous results
            testResults.innerHTML = '';
            
            // Create test elements
            tests.forEach((test, index) => {
                testResults.innerHTML += createTestElement(test, index);
            });

            let passed = 0;
            let failed = 0;

            // Run tests
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                
                try {
                    const result = await makeRequest(
                        `${API_BASE_URL}${test.url}`,
                        test.method,
                        test.data
                    );

                    if (result.success) {
                        updateTestResult(i, true, result.status);
                        passed++;
                    } else {
                        updateTestResult(i, false, result.status, result.error);
                        failed++;
                    }
                } catch (error) {
                    updateTestResult(i, false, null, error.message);
                    failed++;
                }

                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            updateSummary(tests.length, passed, failed);
            
            runButton.disabled = false;
            runButton.textContent = '🧪 Run Integration Tests';
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-gray-500 text-center py-8">Click "Run Integration Tests" to start testing...</p>';
            document.getElementById('summary').classList.add('hidden');
        }

        // Event listeners
        document.getElementById('runTests').addEventListener('click', runTests);
        document.getElementById('clearResults').addEventListener('click', clearResults);
    </script>
</body>
</html>
