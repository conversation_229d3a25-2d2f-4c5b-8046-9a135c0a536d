import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Sidebar state
  sidebarOpen: false,
  sidebarCollapsed: false,
  
  // Theme
  theme: 'light',
  
  // Loading states
  globalLoading: false,
  
  // Modal states
  modals: {},
  
  // Notification preferences
  notifications: {
    desktop: true,
    email: true,
    push: true,
  },
  
  // Layout preferences
  layout: {
    density: 'comfortable', // compact, comfortable, spacious
    tablePageSize: 15,
    cardView: false,
  },
  
  // Filters and search
  filters: {},
  searchQueries: {},
  
  // Recent items
  recentItems: [],
  
  // Breadcrumbs
  breadcrumbs: [],
  
  // Page titles
  pageTitle: 'Dashboard',
  
  // Error states
  errors: {},
  
  // Success messages
  successMessages: [],
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Sidebar actions
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setSidebarOpen: (state, action) => {
      state.sidebarOpen = action.payload;
    },
    toggleSidebarCollapsed: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setSidebarCollapsed: (state, action) => {
      state.sidebarCollapsed = action.payload;
    },
    
    // Theme actions
    setTheme: (state, action) => {
      state.theme = action.payload;
    },
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
    },
    
    // Loading actions
    setGlobalLoading: (state, action) => {
      state.globalLoading = action.payload;
    },
    
    // Modal actions
    openModal: (state, action) => {
      const { modalId, props = {} } = action.payload;
      state.modals[modalId] = { isOpen: true, props };
    },
    closeModal: (state, action) => {
      const modalId = action.payload;
      if (state.modals[modalId]) {
        state.modals[modalId].isOpen = false;
      }
    },
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(modalId => {
        state.modals[modalId].isOpen = false;
      });
    },
    
    // Notification preferences
    setNotificationPreference: (state, action) => {
      const { type, enabled } = action.payload;
      state.notifications[type] = enabled;
    },
    
    // Layout preferences
    setLayoutDensity: (state, action) => {
      state.layout.density = action.payload;
    },
    setTablePageSize: (state, action) => {
      state.layout.tablePageSize = action.payload;
    },
    toggleCardView: (state) => {
      state.layout.cardView = !state.layout.cardView;
    },
    setCardView: (state, action) => {
      state.layout.cardView = action.payload;
    },
    
    // Filter and search actions
    setFilter: (state, action) => {
      const { page, filters } = action.payload;
      state.filters[page] = { ...state.filters[page], ...filters };
    },
    clearFilters: (state, action) => {
      const page = action.payload;
      state.filters[page] = {};
    },
    setSearchQuery: (state, action) => {
      const { page, query } = action.payload;
      state.searchQueries[page] = query;
    },
    clearSearchQuery: (state, action) => {
      const page = action.payload;
      state.searchQueries[page] = '';
    },
    
    // Recent items
    addRecentItem: (state, action) => {
      const item = action.payload;
      const existingIndex = state.recentItems.findIndex(i => i.id === item.id && i.type === item.type);
      
      if (existingIndex !== -1) {
        state.recentItems.splice(existingIndex, 1);
      }
      
      state.recentItems.unshift(item);
      
      // Keep only last 10 items
      if (state.recentItems.length > 10) {
        state.recentItems = state.recentItems.slice(0, 10);
      }
    },
    clearRecentItems: (state) => {
      state.recentItems = [];
    },
    
    // Breadcrumbs
    setBreadcrumbs: (state, action) => {
      state.breadcrumbs = action.payload;
    },
    addBreadcrumb: (state, action) => {
      state.breadcrumbs.push(action.payload);
    },
    clearBreadcrumbs: (state) => {
      state.breadcrumbs = [];
    },
    
    // Page title
    setPageTitle: (state, action) => {
      state.pageTitle = action.payload;
    },
    
    // Error handling
    setError: (state, action) => {
      const { key, error } = action.payload;
      state.errors[key] = error;
    },
    clearError: (state, action) => {
      const key = action.payload;
      delete state.errors[key];
    },
    clearAllErrors: (state) => {
      state.errors = {};
    },
    
    // Success messages
    addSuccessMessage: (state, action) => {
      state.successMessages.push({
        id: Date.now(),
        message: action.payload,
        timestamp: new Date().toISOString(),
      });
    },
    removeSuccessMessage: (state, action) => {
      const id = action.payload;
      state.successMessages = state.successMessages.filter(msg => msg.id !== id);
    },
    clearSuccessMessages: (state) => {
      state.successMessages = [];
    },
    
    // Reset UI state
    resetUIState: (state) => {
      return {
        ...initialState,
        theme: state.theme,
        notifications: state.notifications,
        layout: state.layout,
      };
    },
  },
});

export const {
  toggleSidebar,
  setSidebarOpen,
  toggleSidebarCollapsed,
  setSidebarCollapsed,
  setTheme,
  toggleTheme,
  setGlobalLoading,
  openModal,
  closeModal,
  closeAllModals,
  setNotificationPreference,
  setLayoutDensity,
  setTablePageSize,
  toggleCardView,
  setCardView,
  setFilter,
  clearFilters,
  setSearchQuery,
  clearSearchQuery,
  addRecentItem,
  clearRecentItems,
  setBreadcrumbs,
  addBreadcrumb,
  clearBreadcrumbs,
  setPageTitle,
  setError,
  clearError,
  clearAllErrors,
  addSuccessMessage,
  removeSuccessMessage,
  clearSuccessMessages,
  resetUIState,
} = uiSlice.actions;

export default uiSlice.reducer;

// Selectors
export const selectSidebarOpen = (state) => state.ui.sidebarOpen;
export const selectSidebarCollapsed = (state) => state.ui.sidebarCollapsed;
export const selectTheme = (state) => state.ui.theme;
export const selectGlobalLoading = (state) => state.ui.globalLoading;
export const selectModal = (modalId) => (state) => state.ui.modals[modalId];
export const selectNotifications = (state) => state.ui.notifications;
export const selectLayout = (state) => state.ui.layout;
export const selectFilters = (page) => (state) => state.ui.filters[page] || {};
export const selectSearchQuery = (page) => (state) => state.ui.searchQueries[page] || '';
export const selectRecentItems = (state) => state.ui.recentItems;
export const selectBreadcrumbs = (state) => state.ui.breadcrumbs;
export const selectPageTitle = (state) => state.ui.pageTitle;
export const selectErrors = (state) => state.ui.errors;
export const selectError = (key) => (state) => state.ui.errors[key];
export const selectSuccessMessages = (state) => state.ui.successMessages;
