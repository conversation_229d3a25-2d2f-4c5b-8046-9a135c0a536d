import React from 'react';

function MinimalApp() {
  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: '#333', marginBottom: '20px' }}>
        🎉 Track New Application Test
      </h1>
      
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#28a745', marginBottom: '10px' }}>
          ✅ React Application is Working!
        </h2>
        <p>If you can see this page, React is loading correctly.</p>
      </div>

      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        marginBottom: '20px'
      }}>
        <h3>System Status:</h3>
        <ul>
          <li>✅ React: Working</li>
          <li>✅ JavaScript: Working</li>
          <li>✅ CSS: Working</li>
          <li>✅ Vite Dev Server: Running on port 3001</li>
        </ul>
      </div>

      <div style={{ 
        backgroundColor: '#e3f2fd', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #2196f3'
      }}>
        <h3 style={{ color: '#1976d2' }}>Next Steps:</h3>
        <p>Now we can gradually add back the complex components to identify what's causing the blank page.</p>
        
        <div style={{ marginTop: '15px' }}>
          <button 
            onClick={() => alert('Button click works!')}
            style={{
              backgroundColor: '#2196f3',
              color: 'white',
              padding: '10px 20px',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginRight: '10px'
            }}
          >
            Test Button
          </button>
          
          <button 
            onClick={() => {
              console.log('Console logging works!');
              console.log('Current time:', new Date().toISOString());
            }}
            style={{
              backgroundColor: '#4caf50',
              color: 'white',
              padding: '10px 20px',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Test Console
          </button>
        </div>
      </div>

      <div style={{ 
        marginTop: '20px',
        padding: '15px',
        backgroundColor: '#fff3cd',
        border: '1px solid #ffeaa7',
        borderRadius: '4px'
      }}>
        <strong>Debug Info:</strong>
        <ul style={{ marginTop: '10px' }}>
          <li>Environment: {process.env.NODE_ENV || 'development'}</li>
          <li>React Version: {React.version}</li>
          <li>Current URL: {window.location.href}</li>
          <li>User Agent: {navigator.userAgent.substring(0, 50)}...</li>
        </ul>
      </div>
    </div>
  );
}

export default MinimalApp;
