const express = require('express');
const { body } = require('express-validator');
const paymentController = require('../controllers/paymentController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating payment in
const createPaymentInValidation = [
  body('customer_id')
    .notEmpty()
    .withMessage('Customer ID is required')
    .isInt({ min: 1 })
    .withMessage('Customer ID must be a positive integer'),
  
  body('payment_date')
    .optional()
    .isISO8601()
    .withMessage('Payment date must be a valid date'),
  
  body('payment_method')
    .notEmpty()
    .withMessage('Payment method is required')
    .isIn(['cash', 'bank_transfer', 'cheque', 'credit_card', 'debit_card', 'upi', 'wallet', 'other'])
    .withMessage('Invalid payment method'),
  
  body('amount')
    .notEmpty()
    .withMessage('Amount is required')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be greater than 0'),
  
  body('currency')
    .optional()
    .isLength({ min: 3, max: 3 })
    .withMessage('Currency must be 3 characters'),
  
  body('exchange_rate')
    .optional()
    .isFloat({ min: 0.0001 })
    .withMessage('Exchange rate must be greater than 0'),
  
  body('reference_number')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Reference number must be maximum 100 characters'),
  
  body('bank_name')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Bank name must be maximum 100 characters'),
  
  body('cheque_number')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Cheque number must be maximum 50 characters'),
  
  body('cheque_date')
    .optional()
    .isISO8601()
    .withMessage('Cheque date must be a valid date'),
  
  body('transaction_id')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Transaction ID must be maximum 100 characters'),
  
  body('status')
    .optional()
    .isIn(['pending', 'completed', 'failed', 'cancelled'])
    .withMessage('Invalid status'),
  
  body('payment_type')
    .optional()
    .isIn(['advance', 'invoice_payment', 'refund', 'adjustment'])
    .withMessage('Invalid payment type'),
  
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must be maximum 1000 characters')
];

// Validation rules for creating payment out
const createPaymentOutValidation = [
  body('supplier_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Supplier ID must be a positive integer'),
  
  body('employee_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Employee ID must be a positive integer'),
  
  body('payment_date')
    .optional()
    .isISO8601()
    .withMessage('Payment date must be a valid date'),
  
  body('payment_method')
    .notEmpty()
    .withMessage('Payment method is required')
    .isIn(['cash', 'bank_transfer', 'cheque', 'credit_card', 'debit_card', 'upi', 'wallet', 'other'])
    .withMessage('Invalid payment method'),
  
  body('amount')
    .notEmpty()
    .withMessage('Amount is required')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be greater than 0'),
  
  body('currency')
    .optional()
    .isLength({ min: 3, max: 3 })
    .withMessage('Currency must be 3 characters'),
  
  body('exchange_rate')
    .optional()
    .isFloat({ min: 0.0001 })
    .withMessage('Exchange rate must be greater than 0'),
  
  body('reference_number')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Reference number must be maximum 100 characters'),
  
  body('bank_name')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Bank name must be maximum 100 characters'),
  
  body('cheque_number')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Cheque number must be maximum 50 characters'),
  
  body('cheque_date')
    .optional()
    .isISO8601()
    .withMessage('Cheque date must be a valid date'),
  
  body('transaction_id')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Transaction ID must be maximum 100 characters'),
  
  body('status')
    .optional()
    .isIn(['pending', 'completed', 'failed', 'cancelled'])
    .withMessage('Invalid status'),
  
  body('payment_type')
    .optional()
    .isIn(['purchase_payment', 'expense', 'salary', 'advance', 'refund', 'adjustment'])
    .withMessage('Invalid payment type'),
  
  body('payee_name')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Payee name must be maximum 255 characters'),
  
  body('purpose')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Purpose must be maximum 255 characters'),
  
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must be maximum 1000 characters')
];

// Validation for bulk reconcile
const bulkReconcileValidation = [
  body('payment_ids')
    .isArray({ min: 1 })
    .withMessage('Payment IDs array is required and must not be empty'),
  
  body('payment_ids.*')
    .isInt({ min: 1 })
    .withMessage('Each payment ID must be a positive integer'),
  
  body('type')
    .notEmpty()
    .withMessage('Payment type is required')
    .isIn(['in', 'out'])
    .withMessage('Payment type must be either "in" or "out"')
];

// Routes
router
  .route('/')
  .get(paymentController.getPayments);

router
  .route('/stats')
  .get(paymentController.getPaymentStats);

router
  .route('/cash-flow')
  .get(paymentController.getCashFlowReport);

router
  .route('/bulk-reconcile')
  .post(bulkReconcileValidation, validateRequest, restrictTo('admin', 'sub_admin', 'accountant'), paymentController.bulkReconcilePayments);

router
  .route('/in')
  .post(createPaymentInValidation, validateRequest, restrictTo('admin', 'sub_admin', 'accountant', 'sales'), paymentController.createPaymentIn);

router
  .route('/out')
  .post(createPaymentOutValidation, validateRequest, restrictTo('admin', 'sub_admin', 'accountant'), paymentController.createPaymentOut);

router
  .route('/:type/:id')
  .get(paymentController.getPayment)
  .put(restrictTo('admin', 'sub_admin', 'accountant'), paymentController.updatePayment)
  .delete(restrictTo('admin', 'sub_admin'), paymentController.deletePayment);

router
  .route('/:type/:id/reconcile')
  .put(restrictTo('admin', 'sub_admin', 'accountant'), paymentController.reconcilePayment);

module.exports = router;
