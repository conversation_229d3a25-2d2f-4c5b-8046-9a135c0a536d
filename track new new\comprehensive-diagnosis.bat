@echo off
echo ========================================
echo    COMPREHENSIVE FRONTEND DIAGNOSIS
echo ========================================
echo.

echo [1] Checking if servers are running...
echo.

echo Testing Frontend (port 3001):
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3001' -TimeoutSec 5; Write-Host 'Status:' $response.StatusCode; Write-Host 'Content-Type:' $response.Headers['Content-Type']; Write-Host 'Content Length:' $response.Content.Length; Write-Host 'First 100 chars:' $response.Content.Substring(0, [Math]::Min(100, $response.Content.Length)) } catch { Write-Host 'Frontend Error:' $_.Exception.Message }"

echo.
echo Testing Backend (port 8000):
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/api/health' -TimeoutSec 5; Write-Host 'Status:' $response.StatusCode; Write-Host 'Response:' $response.Content } catch { Write-Host 'Backend Error:' $_.Exception.Message }"

echo.
echo [2] Checking file structure...
echo.

if exist "tracknew-react-frontend\index.html" (
    echo ✅ index.html exists
    echo File size:
    powershell -Command "(Get-Item 'tracknew-react-frontend\index.html').Length"
) else (
    echo ❌ index.html missing
)

if exist "tracknew-react-frontend\src\index.js" (
    echo ✅ src/index.js exists
) else (
    echo ❌ src/index.js missing
)

if exist "tracknew-react-frontend\package.json" (
    echo ✅ package.json exists
) else (
    echo ❌ package.json missing
)

echo.
echo [3] Checking processes...
echo.
netstat -an | findstr ":3001"
netstat -an | findstr ":8000"

echo.
echo [4] Browser instructions...
echo.
echo Please do the following in your browser:
echo 1. Open http://localhost:3001
echo 2. Press F12 to open Developer Tools
echo 3. Go to Console tab
echo 4. Look for any red error messages
echo 5. Go to Network tab and refresh (F5)
echo 6. Check if index.html loads (should show 200 status)
echo.
echo If you see errors, please share them!
echo.

echo [5] Opening browser for testing...
start http://localhost:3001

echo.
echo Diagnosis complete!
pause
