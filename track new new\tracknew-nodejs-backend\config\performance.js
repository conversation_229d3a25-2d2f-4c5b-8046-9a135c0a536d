// Performance optimization configuration
const compression = require('compression');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');

// Compression middleware
const compressionConfig = {
  level: 6,
  threshold: 1024,
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  }
};

// Rate limiting configuration
const rateLimitConfig = {
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000) / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health' || req.path === '/api/health';
  }
};

// Speed limiting configuration (slow down requests)
const speedLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // allow 50 requests per 15 minutes, then...
  delayMs: 500, // begin adding 500ms of delay per request above 50
  maxDelayMs: 20000, // maximum delay of 20 seconds
  skipFailedRequests: false,
  skipSuccessfulRequests: false,
  skip: (req) => {
    // Skip speed limiting for health checks
    return req.path === '/health' || req.path === '/api/health';
  }
};

// Security headers configuration
const helmetConfig = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://api.tracknew.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
};

// Database connection pool configuration
const dbPoolConfig = {
  min: parseInt(process.env.DB_POOL_MIN) || 2,
  max: parseInt(process.env.DB_POOL_MAX) || 20,
  acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 30000,
  idle: parseInt(process.env.DB_POOL_IDLE) || 10000,
  evict: 1000,
  handleDisconnects: true
};

// Redis configuration for caching
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT) || 6379,
  password: process.env.REDIS_PASSWORD,
  db: 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 10000,
  commandTimeout: 5000,
  retryDelayOnClusterDown: 300,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3
};

// Cache configuration
const cacheConfig = {
  defaultTTL: parseInt(process.env.CACHE_TTL) || 3600, // 1 hour
  checkPeriod: 600, // 10 minutes
  useClones: false,
  deleteOnExpire: true,
  enableLegacyCallbacks: false,
  maxKeys: 1000
};

// File upload optimization
const uploadConfig = {
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    files: 5,
    fields: 20,
    fieldNameSize: 100,
    fieldSize: 1024 * 1024 // 1MB
  },
  abortOnLimit: true,
  preservePath: false,
  safeFileNames: true,
  uploadTimeout: 60000 // 1 minute
};

// Logging configuration
const loggingConfig = {
  level: process.env.LOG_LEVEL || 'info',
  format: 'combined',
  dateFormat: 'YYYY-MM-DD HH:mm:ss',
  filename: process.env.LOG_FILE_PATH ? `${process.env.LOG_FILE_PATH}/app.log` : null,
  maxsize: parseInt(process.env.LOG_MAX_SIZE) || 10 * 1024 * 1024, // 10MB
  maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5,
  colorize: process.env.NODE_ENV !== 'production',
  timestamp: true,
  handleExceptions: true,
  humanReadableUnhandledException: true,
  exitOnError: false
};

// Performance monitoring configuration
const monitoringConfig = {
  collectDefaultMetrics: true,
  timeout: 5000,
  gcDurationBuckets: [0.001, 0.01, 0.1, 1, 2, 5],
  eventLoopMonitoringPrecision: 10,
  enableGCMetrics: true,
  enableProcessMetrics: true,
  enableHttpMetrics: true,
  enableDatabaseMetrics: true
};

// Export all configurations
module.exports = {
  compression: compressionConfig,
  rateLimit: rateLimitConfig,
  speedLimit: speedLimitConfig,
  helmet: helmetConfig,
  dbPool: dbPoolConfig,
  redis: redisConfig,
  cache: cacheConfig,
  upload: uploadConfig,
  logging: loggingConfig,
  monitoring: monitoringConfig,
  
  // Middleware setup function
  setupPerformanceMiddleware: (app) => {
    // Security headers
    app.use(helmet(helmetConfig));
    
    // Compression
    app.use(compression(compressionConfig));
    
    // Rate limiting
    app.use(rateLimit(rateLimitConfig));
    
    // Speed limiting
    app.use(slowDown(speedLimitConfig));
    
    // Request timeout
    app.use((req, res, next) => {
      req.setTimeout(30000, () => {
        res.status(408).json({
          error: 'Request timeout',
          message: 'Request took too long to process'
        });
      });
      next();
    });
    
    // Response time header
    app.use((req, res, next) => {
      const start = Date.now();
      res.on('finish', () => {
        const duration = Date.now() - start;
        res.set('X-Response-Time', `${duration}ms`);
      });
      next();
    });
  }
};
