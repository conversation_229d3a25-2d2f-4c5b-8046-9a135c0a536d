const { Invoice, InvoiceItem, Customer, Company, User, Product, Sales, Service, Proforma } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all invoices with filtering and pagination
const getInvoices = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    status,
    payment_status,
    invoice_type,
    customer_id,
    search,
    sort_by = 'created_at',
    sort_order = 'DESC',
    date_from,
    date_to
  } = req.query;

  const offset = (page - 1) * limit;
  const whereClause = { company_id: req.user.company_id };

  // Apply filters
  if (status) {
    whereClause.status = status;
  }

  if (payment_status) {
    whereClause.payment_status = payment_status;
  }

  if (invoice_type) {
    whereClause.invoice_type = invoice_type;
  }

  if (customer_id) {
    whereClause.customer_id = customer_id;
  }

  if (date_from && date_to) {
    whereClause.invoice_date = {
      [Op.between]: [new Date(date_from), new Date(date_to)]
    };
  }

  if (search) {
    whereClause[Op.or] = [
      { invoice_number: { [Op.like]: `%${search}%` } },
      { reference_number: { [Op.like]: `%${search}%` } },
      { po_number: { [Op.like]: `%${search}%` } },
      { notes: { [Op.like]: `%${search}%` } }
    ];
  }

  const { count, rows: invoices } = await Invoice.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'customer_code', 'email', 'mobile_number']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: InvoiceItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit']
          }
        ]
      }
    ],
    order: [[sort_by, sort_order.toUpperCase()]],
    limit: parseInt(limit),
    offset: parseInt(offset)
  });

  res.status(200).json({
    status: 'success',
    data: {
      invoices,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total: count,
        total_pages: Math.ceil(count / limit)
      }
    }
  });
});

// Get invoice by ID
const getInvoice = catchAsync(async (req, res, next) => {
  const invoice = await Invoice.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    },
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: Company,
        as: 'company'
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Sales,
        as: 'sales',
        attributes: ['id', 'sales_number', 'status']
      },
      {
        model: Service,
        as: 'service',
        attributes: ['id', 'service_code', 'status']
      },
      {
        model: Proforma,
        as: 'proforma',
        attributes: ['id', 'proforma_number', 'status']
      },
      {
        model: InvoiceItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit', 'hsn_code']
          }
        ],
        order: [['sort_order', 'ASC']]
      }
    ]
  });

  if (!invoice) {
    return next(new AppError('Invoice not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      invoice
    }
  });
});

// Create new invoice
const createInvoice = catchAsync(async (req, res, next) => {
  const {
    customer_id,
    sales_id,
    service_id,
    proforma_id,
    invoice_date = new Date(),
    due_date,
    invoice_type = 'sales',
    items = [],
    discount_type,
    discount_value = 0,
    shipping_amount = 0,
    adjustment_amount = 0,
    terms_conditions,
    notes,
    internal_notes,
    billing_address,
    shipping_address,
    payment_terms,
    reference_number,
    po_number
  } = req.body;

  // Validate customer exists
  const customer = await Customer.findOne({
    where: { id: customer_id, company_id: req.user.company_id }
  });

  if (!customer) {
    return next(new AppError('Customer not found', 404));
  }

  // Calculate totals
  let subtotal = 0;
  const processedItems = items.map((item, index) => {
    const lineTotal = item.quantity * item.unit_price;
    const itemDiscountAmount = item.discount_type === 'percentage'
      ? (lineTotal * item.discount_value) / 100
      : (item.discount_value || 0);
    const taxableAmount = lineTotal - itemDiscountAmount;

    const cgstAmount = (taxableAmount * (item.cgst_rate || 0)) / 100;
    const sgstAmount = (taxableAmount * (item.sgst_rate || 0)) / 100;
    const igstAmount = (taxableAmount * (item.igst_rate || 0)) / 100;
    const cessAmount = (taxableAmount * (item.cess_rate || 0)) / 100;

    const taxAmount = cgstAmount + sgstAmount + igstAmount + cessAmount;
    const totalAmount = taxableAmount + taxAmount;

    subtotal += totalAmount;

    return {
      ...item,
      discount_amount: itemDiscountAmount,
      taxable_amount: taxableAmount,
      cgst_amount: cgstAmount,
      sgst_amount: sgstAmount,
      igst_amount: igstAmount,
      cess_amount: cessAmount,
      tax_amount: taxAmount,
      total_amount: totalAmount,
      sort_order: index + 1
    };
  });

  // Calculate invoice level discount
  const discountAmount = discount_type === 'percentage'
    ? (subtotal * discount_value) / 100
    : (discount_value || 0);

  const taxAmount = processedItems.reduce((sum, item) => sum + item.tax_amount, 0);
  const totalAmount = subtotal - discountAmount + parseFloat(shipping_amount || 0) + parseFloat(adjustment_amount || 0);
  const balanceAmount = totalAmount; // Initially, balance equals total

  // Create invoice
  const invoice = await Invoice.create({
    company_id: req.user.company_id,
    customer_id,
    sales_id,
    service_id,
    proforma_id,
    invoice_date,
    due_date,
    invoice_type,
    subtotal,
    discount_type,
    discount_value,
    discount_amount,
    tax_amount: taxAmount,
    shipping_amount,
    adjustment_amount,
    total_amount,
    paid_amount: 0,
    balance_amount: balanceAmount,
    terms_conditions,
    notes,
    internal_notes,
    billing_address,
    shipping_address,
    payment_terms,
    reference_number,
    po_number,
    created_by: req.user.id
  });

  // Create invoice items
  if (processedItems.length > 0) {
    const itemsToCreate = processedItems.map(item => ({
      ...item,
      invoice_id: invoice.id
    }));

    await InvoiceItem.bulkCreate(itemsToCreate);
  }

  // Fetch created invoice with associations
  const createdInvoice = await Invoice.findByPk(invoice.id, {
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: InvoiceItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'unit']
          }
        ]
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      invoice: createdInvoice
    }
  });
});

// Update invoice
const updateInvoice = catchAsync(async (req, res, next) => {
  const invoice = await Invoice.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!invoice) {
    return next(new AppError('Invoice not found', 404));
  }

  // Check if invoice can be updated
  if (invoice.status === 'paid') {
    return next(new AppError('Cannot update paid invoice', 400));
  }

  const allowedUpdates = [
    'due_date', 'status', 'payment_status', 'terms_conditions', 'notes',
    'internal_notes', 'billing_address', 'shipping_address', 'payment_terms',
    'reference_number', 'po_number', 'adjustment_amount'
  ];

  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  // Recalculate total if adjustment amount changed
  if (updates.adjustment_amount !== undefined) {
    updates.total_amount = invoice.subtotal - invoice.discount_amount +
                          invoice.shipping_amount + parseFloat(updates.adjustment_amount || 0);
    updates.balance_amount = updates.total_amount - invoice.paid_amount;
  }

  updates.updated_by = req.user.id;

  await invoice.update(updates);

  const updatedInvoice = await Invoice.findByPk(invoice.id, {
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: InvoiceItem,
        as: 'items'
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      invoice: updatedInvoice
    }
  });
});

// Delete invoice
const deleteInvoice = catchAsync(async (req, res, next) => {
  const invoice = await Invoice.findOne({
    where: {
      id: req.params.id,
      company_id: req.user.company_id
    }
  });

  if (!invoice) {
    return next(new AppError('Invoice not found', 404));
  }

  if (invoice.status === 'paid' || invoice.paid_amount > 0) {
    return next(new AppError('Cannot delete invoice with payments', 400));
  }

  await invoice.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Send invoice to customer
const sendInvoice = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { email_addresses, subject, message, send_copy_to_self = false } = req.body;
  const companyId = req.user.company_id;

  const invoice = await Invoice.findOne({
    where: {
      id,
      company_id: companyId
    },
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'email']
      }
    ]
  });

  if (!invoice) {
    return next(new AppError('Invoice not found', 404));
  }

  if (invoice.status === 'cancelled') {
    return next(new AppError('Cannot send cancelled invoice', 400));
  }

  // Update invoice status and sent date
  await invoice.update({
    status: invoice.status === 'draft' ? 'sent' : invoice.status,
    sent_date: new Date(),
    updated_by: req.user.id
  });

  // Here you would integrate with email service
  // For now, we'll just return success

  res.status(200).json({
    status: 'success',
    message: 'Invoice sent successfully',
    data: {
      invoice: {
        id: invoice.id,
        invoice_number: invoice.invoice_number,
        status: invoice.status,
        sent_date: invoice.sent_date
      }
    }
  });
});

// Mark invoice as paid
const markInvoiceAsPaid = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const {
    payment_amount,
    payment_method,
    payment_date = new Date(),
    payment_reference,
    notes
  } = req.body;
  const companyId = req.user.company_id;

  const invoice = await Invoice.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!invoice) {
    return next(new AppError('Invoice not found', 404));
  }

  if (invoice.status === 'cancelled') {
    return next(new AppError('Cannot mark cancelled invoice as paid', 400));
  }

  const newPaidAmount = parseFloat(invoice.paid_amount) + parseFloat(payment_amount);
  const newBalanceAmount = parseFloat(invoice.total_amount) - newPaidAmount;

  let payment_status = 'partially_paid';
  let status = invoice.status;

  if (newBalanceAmount <= 0) {
    payment_status = 'paid';
    status = 'paid';
  } else if (newPaidAmount > parseFloat(invoice.total_amount)) {
    payment_status = 'overpaid';
    status = 'paid';
  }

  await invoice.update({
    paid_amount: newPaidAmount,
    balance_amount: Math.max(0, newBalanceAmount),
    payment_status,
    status,
    last_payment_date: new Date(payment_date),
    updated_by: req.user.id
  });

  res.status(200).json({
    status: 'success',
    message: 'Payment recorded successfully',
    data: {
      invoice: {
        id: invoice.id,
        invoice_number: invoice.invoice_number,
        paid_amount: newPaidAmount,
        balance_amount: Math.max(0, newBalanceAmount),
        payment_status: payment_status,
        status: status
      }
    }
  });
});

// Get invoice statistics
const getInvoiceStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const { start_date, end_date } = req.query;

  // Build date filter
  const dateFilter = {};
  if (start_date || end_date) {
    dateFilter.invoice_date = {};
    if (start_date) {
      dateFilter.invoice_date[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      dateFilter.invoice_date[Op.lte] = new Date(end_date);
    }
  }

  // Total invoices
  const totalInvoices = await Invoice.count({
    where: {
      company_id: companyId,
      ...dateFilter
    }
  });

  // Invoices by status
  const invoicesByStatus = await Invoice.findAll({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      'status',
      [Invoice.sequelize.fn('COUNT', Invoice.sequelize.col('id')), 'count'],
      [Invoice.sequelize.fn('SUM', Invoice.sequelize.col('total_amount')), 'total_amount']
    ],
    group: ['status'],
    raw: true
  });

  // Payment status breakdown
  const paymentStatusBreakdown = await Invoice.findAll({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      'payment_status',
      [Invoice.sequelize.fn('COUNT', Invoice.sequelize.col('id')), 'count'],
      [Invoice.sequelize.fn('SUM', Invoice.sequelize.col('total_amount')), 'total_amount'],
      [Invoice.sequelize.fn('SUM', Invoice.sequelize.col('paid_amount')), 'paid_amount'],
      [Invoice.sequelize.fn('SUM', Invoice.sequelize.col('balance_amount')), 'balance_amount']
    ],
    group: ['payment_status'],
    raw: true
  });

  // Overdue invoices
  const overdueInvoices = await Invoice.count({
    where: {
      company_id: companyId,
      due_date: { [Op.lt]: new Date() },
      payment_status: { [Op.in]: ['unpaid', 'partially_paid'] },
      status: { [Op.ne]: 'cancelled' },
      ...dateFilter
    }
  });

  // Total revenue
  const totalRevenue = await Invoice.findOne({
    where: {
      company_id: companyId,
      status: { [Op.ne]: 'cancelled' },
      ...dateFilter
    },
    attributes: [
      [Invoice.sequelize.fn('SUM', Invoice.sequelize.col('total_amount')), 'total_revenue'],
      [Invoice.sequelize.fn('SUM', Invoice.sequelize.col('paid_amount')), 'total_paid'],
      [Invoice.sequelize.fn('SUM', Invoice.sequelize.col('balance_amount')), 'total_outstanding']
    ],
    raw: true
  });

  // Average invoice value
  const avgInvoiceValue = await Invoice.findOne({
    where: {
      company_id: companyId,
      status: { [Op.ne]: 'cancelled' },
      ...dateFilter
    },
    attributes: [
      [Invoice.sequelize.fn('AVG', Invoice.sequelize.col('total_amount')), 'avg_value']
    ],
    raw: true
  });

  // Top customers by invoice value
  const topCustomers = await Invoice.findAll({
    where: {
      company_id: companyId,
      status: { [Op.ne]: 'cancelled' },
      ...dateFilter
    },
    include: [{
      model: Customer,
      as: 'customer',
      attributes: ['id', 'customer_name']
    }],
    attributes: [
      'customer_id',
      [Invoice.sequelize.fn('COUNT', Invoice.sequelize.col('Invoice.id')), 'invoice_count'],
      [Invoice.sequelize.fn('SUM', Invoice.sequelize.col('total_amount')), 'total_value']
    ],
    group: ['customer_id', 'customer.id'],
    order: [[Invoice.sequelize.fn('SUM', Invoice.sequelize.col('total_amount')), 'DESC']],
    limit: 5,
    raw: false
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_invoices: totalInvoices,
      overdue_invoices: overdueInvoices,
      invoices_by_status: invoicesByStatus,
      payment_status_breakdown: paymentStatusBreakdown,
      total_revenue: parseFloat(totalRevenue?.total_revenue || 0),
      total_paid: parseFloat(totalRevenue?.total_paid || 0),
      total_outstanding: parseFloat(totalRevenue?.total_outstanding || 0),
      avg_invoice_value: parseFloat(avgInvoiceValue?.avg_value || 0),
      top_customers: topCustomers
    }
  });
});

// Duplicate invoice
const duplicateInvoice = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const originalInvoice = await Invoice.findOne({
    where: {
      id,
      company_id: companyId
    },
    include: [
      {
        model: InvoiceItem,
        as: 'items'
      }
    ]
  });

  if (!originalInvoice) {
    return next(new AppError('Invoice not found', 404));
  }

  // Generate new invoice number
  const invoiceNumber = await generateInvoiceNumber(companyId);

  // Create duplicate invoice
  const duplicateData = {
    ...originalInvoice.toJSON(),
    id: undefined,
    invoice_number: invoiceNumber,
    invoice_date: new Date(),
    due_date: null,
    status: 'draft',
    payment_status: 'unpaid',
    paid_amount: 0,
    balance_amount: originalInvoice.total_amount,
    sent_date: null,
    last_payment_date: null,
    created_by: req.user.id,
    updated_by: null,
    created_at: undefined,
    updated_at: undefined,
    deleted_at: undefined
  };

  delete duplicateData.items;

  const newInvoice = await Invoice.create(duplicateData);

  // Duplicate invoice items
  if (originalInvoice.items && originalInvoice.items.length > 0) {
    const itemsToCreate = originalInvoice.items.map(item => ({
      ...item.toJSON(),
      id: undefined,
      invoice_id: newInvoice.id,
      created_at: undefined,
      updated_at: undefined
    }));

    await InvoiceItem.bulkCreate(itemsToCreate);
  }

  // Fetch the created invoice with associations
  const createdInvoice = await Invoice.findByPk(newInvoice.id, {
    include: [
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: InvoiceItem,
        as: 'items'
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    message: 'Invoice duplicated successfully',
    data: {
      invoice: createdInvoice
    }
  });
});

// Convert to recurring invoice
const convertToRecurring = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { recurring_frequency, next_invoice_date } = req.body;
  const companyId = req.user.company_id;

  const invoice = await Invoice.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!invoice) {
    return next(new AppError('Invoice not found', 404));
  }

  await invoice.update({
    is_recurring: true,
    recurring_frequency,
    next_invoice_date: new Date(next_invoice_date),
    updated_by: req.user.id
  });

  res.status(200).json({
    status: 'success',
    message: 'Invoice converted to recurring successfully',
    data: {
      invoice: {
        id: invoice.id,
        invoice_number: invoice.invoice_number,
        is_recurring: true,
        recurring_frequency: invoice.recurring_frequency,
        next_invoice_date: invoice.next_invoice_date
      }
    }
  });
});

// Bulk update invoice status
const bulkUpdateStatus = catchAsync(async (req, res, next) => {
  const { invoice_ids, status } = req.body;
  const companyId = req.user.company_id;

  if (!Array.isArray(invoice_ids) || invoice_ids.length === 0) {
    return next(new AppError('Invoice IDs array is required', 400));
  }

  const [updatedCount] = await Invoice.update(
    {
      status,
      updated_by: req.user.id
    },
    {
      where: {
        id: { [Op.in]: invoice_ids },
        company_id: companyId
      }
    }
  );

  res.status(200).json({
    status: 'success',
    message: `${updatedCount} invoice(s) updated successfully`,
    data: {
      updated_count: updatedCount
    }
  });
});

module.exports = {
  getInvoices,
  getInvoice,
  createInvoice,
  updateInvoice,
  deleteInvoice,
  sendInvoice,
  markInvoiceAsPaid,
  getInvoiceStats,
  duplicateInvoice,
  convertToRecurring,
  bulkUpdateStatus
};
