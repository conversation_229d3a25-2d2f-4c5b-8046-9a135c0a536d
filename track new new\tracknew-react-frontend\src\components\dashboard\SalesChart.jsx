import React from 'react';
import { ChartBarIcon } from '@heroicons/react/24/outline';
import { classNames, formatCurrency } from '../../utils/helpers';
import { LoadingSpinner } from '../ui';

const SalesChart = ({ data, loading = false }) => {
  // Mock chart data for demonstration
  const mockData = [
    { month: 'Jan', sales: 45000, services: 25000 },
    { month: 'Feb', sales: 52000, services: 28000 },
    { month: 'Mar', sales: 48000, services: 32000 },
    { month: 'Apr', sales: 61000, services: 35000 },
    { month: 'May', sales: 55000, services: 30000 },
    { month: 'Jun', sales: 67000, services: 38000 },
  ];

  const chartData = data?.monthly_breakdown || mockData;
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (!chartData || chartData.length === 0) {
    return (
      <div className="text-center py-12">
        <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          No sales data
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Sales data will appear here once you have transactions.
        </p>
      </div>
    );
  }

  // Calculate max value for scaling
  const maxValue = Math.max(
    ...chartData.map(item => Math.max(item.sales || 0, item.services || 0))
  );

  return (
    <div className="space-y-4">
      {/* Chart Legend */}
      <div className="flex items-center justify-center space-x-6 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-500 rounded"></div>
          <span className="text-gray-600 dark:text-gray-400">Sales</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded"></div>
          <span className="text-gray-600 dark:text-gray-400">Services</span>
        </div>
      </div>

      {/* Simple Bar Chart */}
      <div className="space-y-3">
        {chartData.map((item, index) => {
          const salesHeight = maxValue > 0 ? (item.sales / maxValue) * 100 : 0;
          const servicesHeight = maxValue > 0 ? (item.services / maxValue) * 100 : 0;

          return (
            <div key={index} className="flex items-end space-x-2">
              {/* Month Label */}
              <div className="w-8 text-xs text-gray-500 dark:text-gray-400 text-right">
                {item.month}
              </div>

              {/* Bar Container */}
              <div className="flex-1 flex items-end space-x-1 h-16">
                {/* Sales Bar */}
                <div className="flex-1 bg-gray-100 dark:bg-gray-700 rounded-t relative">
                  <div
                    className="bg-blue-500 rounded-t transition-all duration-300 ease-in-out"
                    style={{ height: `${salesHeight}%` }}
                    title={`Sales: ${formatCurrency(item.sales)}`}
                  ></div>
                </div>

                {/* Services Bar */}
                <div className="flex-1 bg-gray-100 dark:bg-gray-700 rounded-t relative">
                  <div
                    className="bg-green-500 rounded-t transition-all duration-300 ease-in-out"
                    style={{ height: `${servicesHeight}%` }}
                    title={`Services: ${formatCurrency(item.services)}`}
                  ></div>
                </div>
              </div>

              {/* Values */}
              <div className="w-20 text-xs text-gray-500 dark:text-gray-400">
                <div>{formatCurrency(item.sales, 'USD', 'en-US').replace('$', '$')}</div>
                <div>{formatCurrency(item.services, 'USD', 'en-US').replace('$', '$')}</div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-600">
        <div className="text-center">
          <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
            {formatCurrency(
              chartData.reduce((sum, item) => sum + (item.sales || 0), 0)
            )}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">Total Sales</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-green-600 dark:text-green-400">
            {formatCurrency(
              chartData.reduce((sum, item) => sum + (item.services || 0), 0)
            )}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">Total Services</div>
        </div>
      </div>
    </div>
  );
};

export default SalesChart;
