import React from 'react';
import { Routes, Route } from 'react-router-dom';

// Simple test component
const TestDashboard = () => {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">
          🎉 Track New Application is Working!
        </h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            ✅ System Status
          </h2>
          <div className="space-y-2">
            <div className="flex items-center">
              <span className="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
              <span>Frontend: React.js application loaded successfully</span>
            </div>
            <div className="flex items-center">
              <span className="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
              <span>Backend: Node.js server running on port 8000</span>
            </div>
            <div className="flex items-center">
              <span className="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
              <span>Routing: React Router working correctly</span>
            </div>
            <div className="flex items-center">
              <span className="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
              <span>Styling: Tailwind CSS loaded and working</span>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">
            🚀 Next Steps
          </h3>
          <p className="text-blue-700">
            The application is now running successfully! You can now proceed to test the full application features.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold text-gray-800">Dashboard</h4>
            <p className="text-gray-600 text-sm">Overview and analytics</p>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold text-gray-800">Customers</h4>
            <p className="text-gray-600 text-sm">Customer management</p>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold text-gray-800">Services</h4>
            <p className="text-gray-600 text-sm">Service tracking</p>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold text-gray-800">Products</h4>
            <p className="text-gray-600 text-sm">Inventory management</p>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold text-gray-800">Sales</h4>
            <p className="text-gray-600 text-sm">Sales processing</p>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold text-gray-800">Reports</h4>
            <p className="text-gray-600 text-sm">Analytics and reports</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const TestLogin = () => {
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-4">Test Login Page</h2>
        <p>This is a test login page to verify routing is working.</p>
      </div>
    </div>
  );
};

function TestApp() {
  return (
    <Routes>
      <Route path="/" element={<TestDashboard />} />
      <Route path="/dashboard" element={<TestDashboard />} />
      <Route path="/login" element={<TestLogin />} />
      <Route path="*" element={<TestDashboard />} />
    </Routes>
  );
}

export default TestApp;
