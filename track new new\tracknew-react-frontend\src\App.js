import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { Toaster } from 'react-hot-toast';
import { HelmetProvider } from 'react-helmet-async';

// Components
import Layout from './components/layout/Layout';
import LoadingSpinner from './components/ui/LoadingSpinner';
import ErrorBoundary from './components/ui/ErrorBoundary';

// Pages
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import Dashboard from './pages/Dashboard';
import Services from './pages/Services';
import Customers from './pages/Customers';
import Products from './pages/Products';
import Sales from './pages/Sales';
import Unauthorized from './pages/Unauthorized';
import IntegrationTest from './pages/IntegrationTest';

// Hooks
import { useAuth } from './hooks/useAuth';

// Styles
import './App.css';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function AppContent() {
  const { isLoading, isAuthenticated, initializeAuth } = useAuth();

  useEffect(() => {
    // Initialize authentication
    initializeAuth();
  }, [initializeAuth]);

  // For development, bypass authentication temporarily
  const isDevelopment = process.env.NODE_ENV === 'development';
  const showLogin = !isDevelopment && !isAuthenticated;

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" text="Loading application..." />
      </div>
    );
  }

  // Show login page if not authenticated (in production)
  if (showLogin) {
    return (
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/unauthorized" element={<Unauthorized />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    );
  }

  // Show main application
  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/login" element={<Login />} />
      <Route path="/register" element={<Register />} />
      <Route path="/unauthorized" element={<Unauthorized />} />

      {/* Main Application Routes */}
      <Route path="/" element={<Layout />}>
        <Route index element={<Navigate to="/dashboard" replace />} />
        <Route path="dashboard" element={<Dashboard />} />

        {/* Services */}
        <Route path="services" element={<Services />} />
        <Route path="services/new" element={<div className="p-8"><h1 className="text-2xl font-bold">Create Service</h1></div>} />
        <Route path="services/:id" element={<div className="p-8"><h1 className="text-2xl font-bold">Service Details</h1></div>} />
        <Route path="services/:id/edit" element={<div className="p-8"><h1 className="text-2xl font-bold">Edit Service</h1></div>} />

        {/* Customers */}
        <Route path="customers" element={<Customers />} />
        <Route path="customers/new" element={<div className="p-8"><h1 className="text-2xl font-bold">Create Customer</h1></div>} />
        <Route path="customers/:id" element={<div className="p-8"><h1 className="text-2xl font-bold">Customer Details</h1></div>} />
        <Route path="customers/:id/edit" element={<div className="p-8"><h1 className="text-2xl font-bold">Edit Customer</h1></div>} />

        {/* Sales */}
        <Route path="sales" element={<Sales />} />
        <Route path="sales/new" element={<div className="p-8"><h1 className="text-2xl font-bold">Create Sale</h1></div>} />
        <Route path="sales/:id" element={<div className="p-8"><h1 className="text-2xl font-bold">Sale Details</h1></div>} />
        <Route path="sales/:id/edit" element={<div className="p-8"><h1 className="text-2xl font-bold">Edit Sale</h1></div>} />

        {/* Products */}
        <Route path="products" element={<Products />} />
        <Route path="products/new" element={<div className="p-8"><h1 className="text-2xl font-bold">Create Product</h1></div>} />
        <Route path="products/:id" element={<div className="p-8"><h1 className="text-2xl font-bold">Product Details</h1></div>} />
        <Route path="products/:id/edit" element={<div className="p-8"><h1 className="text-2xl font-bold">Edit Product</h1></div>} />

        {/* Settings */}
        <Route path="settings" element={<div className="p-8"><h1 className="text-2xl font-bold">Settings</h1></div>} />
        <Route path="profile" element={<div className="p-8"><h1 className="text-2xl font-bold">Profile</h1></div>} />

        {/* Integration Test (Development only) */}
        {isDevelopment && (
          <Route path="integration-test" element={<IntegrationTest />} />
        )}
      </Route>

      {/* 404 Route */}
      <Route path="*" element={<Navigate to="/dashboard" replace />} />
    </Routes>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <HelmetProvider>
        <QueryClientProvider client={queryClient}>
          <AppContent />
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                duration: 3000,
                theme: {
                  primary: 'green',
                  secondary: 'black',
                },
              },
              error: {
                duration: 5000,
                theme: {
                  primary: 'red',
                  secondary: 'black',
                },
              },
            }}
          />
          {process.env.NODE_ENV === 'development' && (
            <ReactQueryDevtools initialIsOpen={false} />
          )}
        </QueryClientProvider>
      </HelmetProvider>
    </ErrorBoundary>
  );
}

export default App;
