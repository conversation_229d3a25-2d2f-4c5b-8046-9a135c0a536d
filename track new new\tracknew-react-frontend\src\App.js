import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { Toaster } from 'react-hot-toast';
import { HelmetProvider } from 'react-helmet-async';

// Redux store
import { store } from './store/store';

// Components
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/auth/ProtectedRoute';
import PublicRoute from './components/auth/PublicRoute';
import LoadingSpinner from './components/ui/LoadingSpinner';
import ErrorBoundary from './components/ui/ErrorBoundary';

// Pages
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import Dashboard from './pages/Dashboard';
import Services from './pages/Services';
import Customers from './pages/Customers';
import Products from './pages/Products';
import Sales from './pages/Sales';
import Unauthorized from './pages/Unauthorized';
import IntegrationTest from './pages/IntegrationTest';

// Hooks
import { useAuth } from './hooks/useAuth';

// Utils
import { initializeFirebase } from './utils/firebase';

// Styles
import './App.css';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function AppContent() {
  const { isLoading, isAuthenticated, initializeAuth } = useAuth();

  useEffect(() => {
    // Initialize authentication
    initializeAuth();

    // Initialize Firebase
    initializeFirebase();
  }, [initializeAuth]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <Router>
      <Routes>
        {/* Public Routes */}
        <Route
          path="/login"
          element={
            <PublicRoute>
              <Login />
            </PublicRoute>
          }
        />
        <Route
          path="/register"
          element={
            <PublicRoute>
              <Register />
            </PublicRoute>
          }
        />

        {/* Unauthorized page */}
        <Route path="/unauthorized" element={<Unauthorized />} />

        {/* Protected Routes */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />

          {/* Services */}
          <Route path="services" element={<Services />} />
          <Route path="services/new" element={<div>Create Service</div>} />
          <Route path="services/:id" element={<div>Service Details</div>} />
          <Route path="services/:id/edit" element={<div>Edit Service</div>} />

          {/* Customers */}
          <Route path="customers" element={<Customers />} />
          <Route path="customers/new" element={<div>Create Customer</div>} />
          <Route path="customers/:id" element={<div>Customer Details</div>} />
          <Route path="customers/:id/edit" element={<div>Edit Customer</div>} />

          {/* Sales */}
          <Route path="sales" element={<Sales />} />
          <Route path="sales/new" element={<div>Create Sale</div>} />
          <Route path="sales/:id" element={<div>Sale Details</div>} />
          <Route path="sales/:id/edit" element={<div>Edit Sale</div>} />
          <Route path="sales/:id/print" element={<div>Print Sale</div>} />

          {/* Products */}
          <Route path="products" element={<Products />} />
          <Route path="products/new" element={<div>Create Product</div>} />
          <Route path="products/:id" element={<div>Product Details</div>} />
          <Route path="products/:id/edit" element={<div>Edit Product</div>} />
          <Route path="products/alerts" element={<div>Stock Alerts</div>} />

          {/* Settings */}
          <Route path="settings" element={<div>User Settings</div>} />

          {/* Profile */}
          <Route path="profile" element={<div>User Profile</div>} />

          {/* Integration Test (Development only) */}
          {process.env.NODE_ENV === 'development' && (
            <Route path="integration-test" element={<IntegrationTest />} />
          )}
        </Route>

        {/* 404 Route */}
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </Router>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <HelmetProvider>
        <Provider store={store}>
          <QueryClientProvider client={queryClient}>
            <AppContent />
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
                success: {
                  duration: 3000,
                  theme: {
                    primary: 'green',
                    secondary: 'black',
                  },
                },
                error: {
                  duration: 5000,
                  theme: {
                    primary: 'red',
                    secondary: 'black',
                  },
                },
              }}
            />
            {process.env.NODE_ENV === 'development' && (
              <ReactQueryDevtools initialIsOpen={false} />
            )}
          </QueryClientProvider>
        </Provider>
      </HelmetProvider>
    </ErrorBoundary>
  );
}

export default App;
