const express = require('express');
const { body } = require('express-validator');

const invoiceController = require('../controllers/invoiceController');
const { protect, restrictTo, restrictToCompany } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Protect all routes
router.use(protect);
router.use(restrictToCompany);

// Validation rules
const createInvoiceValidation = [
  body('customer_id')
    .isInt({ min: 1 })
    .withMessage('Customer ID is required and must be a valid number'),
  body('invoice_date')
    .optional()
    .isISO8601()
    .withMessage('Invoice date must be a valid date'),
  body('due_date')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid date'),
  body('invoice_type')
    .optional()
    .isIn(['sales', 'service', 'proforma', 'credit_note', 'debit_note'])
    .withMessage('Invalid invoice type'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('At least one item is required'),
  body('items.*.item_name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Item name is required and must be less than 255 characters'),
  body('items.*.quantity')
    .isFloat({ min: 0.001 })
    .withMessage('Quantity must be greater than 0'),
  body('items.*.unit_price')
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a positive number'),
  body('items.*.discount_type')
    .optional()
    .isIn(['percentage', 'fixed'])
    .withMessage('Discount type must be percentage or fixed'),
  body('items.*.discount_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount value must be a positive number'),
  body('items.*.cgst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('CGST rate must be between 0 and 100'),
  body('items.*.sgst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('SGST rate must be between 0 and 100'),
  body('items.*.igst_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('IGST rate must be between 0 and 100'),
  body('discount_type')
    .optional()
    .isIn(['percentage', 'fixed'])
    .withMessage('Discount type must be percentage or fixed'),
  body('discount_value')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount value must be a positive number'),
  body('shipping_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Shipping amount must be a positive number'),
  body('adjustment_amount')
    .optional()
    .isFloat()
    .withMessage('Adjustment amount must be a valid number'),
  body('payment_terms')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Payment terms must be less than 100 characters'),
  body('po_number')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('PO number must be less than 100 characters')
];

const updateInvoiceValidation = [
  body('due_date')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid date'),
  body('status')
    .optional()
    .isIn(['draft', 'sent', 'paid', 'partially_paid', 'overdue', 'cancelled'])
    .withMessage('Invalid status value'),
  body('payment_status')
    .optional()
    .isIn(['unpaid', 'partially_paid', 'paid', 'overpaid'])
    .withMessage('Invalid payment status value'),
  body('adjustment_amount')
    .optional()
    .isFloat()
    .withMessage('Adjustment amount must be a valid number'),
  body('payment_terms')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Payment terms must be less than 100 characters'),
  body('po_number')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('PO number must be less than 100 characters'),
  body('terms_conditions')
    .optional()
    .trim()
    .isLength({ max: 5000 })
    .withMessage('Terms and conditions must be less than 5000 characters'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Notes must be less than 2000 characters'),
  body('internal_notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Internal notes must be less than 2000 characters')
];

// Additional validation rules
const sendInvoiceValidation = [
  body('email_addresses')
    .isArray({ min: 1 })
    .withMessage('Email addresses array is required'),
  body('email_addresses.*')
    .isEmail()
    .withMessage('Each email address must be valid'),
  body('subject')
    .notEmpty()
    .withMessage('Subject is required')
    .isLength({ max: 200 })
    .withMessage('Subject must be maximum 200 characters'),
  body('message')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Message must be maximum 2000 characters')
];

const markAsPaidValidation = [
  body('payment_amount')
    .notEmpty()
    .withMessage('Payment amount is required')
    .isFloat({ min: 0.01 })
    .withMessage('Payment amount must be greater than 0'),
  body('payment_method')
    .optional()
    .isIn(['cash', 'cheque', 'bank_transfer', 'credit_card', 'debit_card', 'upi', 'wallet', 'online'])
    .withMessage('Invalid payment method'),
  body('payment_date')
    .optional()
    .isISO8601()
    .withMessage('Payment date must be a valid date')
];

const recurringValidation = [
  body('recurring_frequency')
    .notEmpty()
    .withMessage('Recurring frequency is required')
    .isIn(['monthly', 'quarterly', 'half_yearly', 'yearly'])
    .withMessage('Invalid recurring frequency'),
  body('next_invoice_date')
    .notEmpty()
    .withMessage('Next invoice date is required')
    .isISO8601()
    .withMessage('Next invoice date must be a valid date')
];

const bulkUpdateValidation = [
  body('invoice_ids')
    .isArray({ min: 1 })
    .withMessage('Invoice IDs array is required'),
  body('invoice_ids.*')
    .isInt({ min: 1 })
    .withMessage('Each invoice ID must be a positive integer'),
  body('status')
    .notEmpty()
    .withMessage('Status is required')
    .isIn(['draft', 'sent', 'paid', 'partially_paid', 'overdue', 'cancelled'])
    .withMessage('Invalid status')
];

// Routes
router
  .route('/')
  .get(invoiceController.getInvoices)
  .post(createInvoiceValidation, validateRequest, invoiceController.createInvoice);

router
  .route('/stats')
  .get(invoiceController.getInvoiceStats);

router
  .route('/bulk-update-status')
  .put(bulkUpdateValidation, validateRequest, restrictTo('admin', 'sub_admin'), invoiceController.bulkUpdateStatus);

router
  .route('/:id/send')
  .post(sendInvoiceValidation, validateRequest, invoiceController.sendInvoice);

router
  .route('/:id/mark-paid')
  .put(markAsPaidValidation, validateRequest, restrictTo('admin', 'sub_admin', 'accountant'), invoiceController.markInvoiceAsPaid);

router
  .route('/:id/duplicate')
  .post(restrictTo('admin', 'sub_admin', 'sales'), invoiceController.duplicateInvoice);

router
  .route('/:id/convert-recurring')
  .put(recurringValidation, validateRequest, restrictTo('admin', 'sub_admin'), invoiceController.convertToRecurring);

router
  .route('/:id')
  .get(invoiceController.getInvoice)
  .put(updateInvoiceValidation, validateRequest, invoiceController.updateInvoice)
  .delete(restrictTo('admin', 'sub_admin'), invoiceController.deleteInvoice);

module.exports = router;
