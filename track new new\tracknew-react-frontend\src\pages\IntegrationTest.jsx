import React, { useState } from 'react';
import { 
  PlayIcon, 
  CheckCircleIcon, 
  XCircleIcon, 
  ClockIcon,
  ServerIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CubeIcon
} from '@heroicons/react/24/outline';
import { Button } from '../components/ui';
import { runIntegrationTests } from '../utils/integrationTest';
import { classNames } from '../utils/helpers';

const IntegrationTest = () => {
  const [testResults, setTestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);

  const handleRunTests = async () => {
    setIsRunning(true);
    setTestResults(null);
    
    try {
      const results = await runIntegrationTests();
      setTestResults(results);
    } catch (error) {
      console.error('Test execution failed:', error);
      setTestResults({
        error: error.message,
        summary: { totalTests: 0, passedTests: 0, failedTests: 1, successRate: 0 }
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (success) => {
    if (success) {
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
    }
    return <XCircleIcon className="h-5 w-5 text-red-500" />;
  };

  const getStatusColor = (success) => {
    return success ? 'text-green-600' : 'text-red-600';
  };

  const renderTestSection = (title, icon: Icon, tests, sectionKey) => {
    if (!tests) return null;

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Icon className="h-6 w-6 text-blue-500" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {title}
          </h3>
        </div>

        {sectionKey === 'apiConnection' ? (
          <div className="flex items-center space-x-3">
            {getStatusIcon(tests.success)}
            <span className={classNames('text-sm', getStatusColor(tests.success))}>
              {tests.success ? 'API Connection Successful' : `API Connection Failed: ${tests.error}`}
            </span>
          </div>
        ) : (
          <div className="space-y-3">
            {Object.entries(tests).map(([testName, result]) => (
              <div key={testName} className="flex items-center justify-between">
                <span className="text-sm text-gray-700 dark:text-gray-300 capitalize">
                  {testName.replace(/([A-Z])/g, ' $1').trim()}
                </span>
                <div className="flex items-center space-x-2">
                  {result.tested ? (
                    <>
                      {getStatusIcon(result.success)}
                      <span className={classNames('text-xs', getStatusColor(result.success))}>
                        {result.success ? 'Pass' : 'Fail'}
                      </span>
                    </>
                  ) : (
                    <span className="text-xs text-gray-400">Not tested</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Phase 4: Integration Testing
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Test the integration between React frontend and Node.js backend
        </p>
      </div>

      {/* Test Controls */}
      <div className="flex justify-center">
        <Button
          leftIcon={isRunning ? ClockIcon : PlayIcon}
          onClick={handleRunTests}
          disabled={isRunning}
          loading={isRunning}
          size="lg"
        >
          {isRunning ? 'Running Tests...' : 'Run Integration Tests'}
        </Button>
      </div>

      {/* Test Results */}
      {testResults && (
        <div className="space-y-6">
          {/* Summary */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Test Summary
            </h2>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {testResults.summary.totalTests}
                </div>
                <div className="text-sm text-gray-500">Total Tests</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {testResults.summary.passedTests}
                </div>
                <div className="text-sm text-gray-500">Passed</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {testResults.summary.failedTests}
                </div>
                <div className="text-sm text-gray-500">Failed</div>
              </div>
              
              <div className="text-center">
                <div className={classNames(
                  'text-2xl font-bold',
                  testResults.summary.successRate >= 80 ? 'text-green-600' :
                  testResults.summary.successRate >= 50 ? 'text-yellow-600' : 'text-red-600'
                )}>
                  {testResults.summary.successRate}%
                </div>
                <div className="text-sm text-gray-500">Success Rate</div>
              </div>
            </div>

            {/* Overall Status */}
            <div className="mt-6 text-center">
              {testResults.summary.successRate >= 80 ? (
                <div className="flex items-center justify-center space-x-2 text-green-600">
                  <CheckCircleIcon className="h-6 w-6" />
                  <span className="font-medium">Integration Successful!</span>
                </div>
              ) : testResults.summary.successRate >= 50 ? (
                <div className="flex items-center justify-center space-x-2 text-yellow-600">
                  <ClockIcon className="h-6 w-6" />
                  <span className="font-medium">Partial Integration - Needs Attention</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2 text-red-600">
                  <XCircleIcon className="h-6 w-6" />
                  <span className="font-medium">Integration Failed - Check Backend</span>
                </div>
              )}
            </div>
          </div>

          {/* Detailed Results */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {renderTestSection(
              'API Connection',
              ServerIcon,
              testResults.apiConnection,
              'apiConnection'
            )}

            {renderTestSection(
              'Authentication',
              ShieldCheckIcon,
              testResults.authEndpoints,
              'authEndpoints'
            )}

            {renderTestSection(
              'Dashboard APIs',
              ChartBarIcon,
              testResults.dashboardEndpoints,
              'dashboardEndpoints'
            )}

            {renderTestSection(
              'CRUD Operations',
              CubeIcon,
              testResults.crudEndpoints,
              'crudEndpoints'
            )}
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
            <h3 className="text-lg font-medium text-blue-900 dark:text-blue-200 mb-3">
              Next Steps
            </h3>
            
            {testResults.summary.successRate >= 80 ? (
              <div className="text-blue-800 dark:text-blue-300 space-y-2">
                <p>✅ Integration is working well! You can now:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Test the application manually</li>
                  <li>Run end-to-end tests</li>
                  <li>Deploy to staging environment</li>
                </ul>
              </div>
            ) : testResults.summary.successRate >= 50 ? (
              <div className="text-yellow-800 dark:text-yellow-300 space-y-2">
                <p>⚠️ Some endpoints are failing. Please:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Check backend server is running</li>
                  <li>Verify database connections</li>
                  <li>Check API endpoint implementations</li>
                  <li>Review error logs</li>
                </ul>
              </div>
            ) : (
              <div className="text-red-800 dark:text-red-300 space-y-2">
                <p>❌ Integration is failing. Please:</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Start the backend server (npm run dev)</li>
                  <li>Check database is running and connected</li>
                  <li>Verify API_URL in .env file</li>
                  <li>Check CORS configuration</li>
                  <li>Review backend error logs</li>
                </ul>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Instructions for running backend */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
          Backend Setup Instructions
        </h3>
        
        <div className="text-gray-700 dark:text-gray-300 space-y-2">
          <p>To run the integration tests, make sure the backend is running:</p>
          <div className="bg-gray-100 dark:bg-gray-700 rounded p-3 font-mono text-sm">
            <div>cd "track new new/tracknew-backend"</div>
            <div>npm install</div>
            <div>npm run dev</div>
          </div>
          <p className="text-sm text-gray-500">
            The backend should be running on http://localhost:8000
          </p>
        </div>
      </div>
    </div>
  );
};

export default IntegrationTest;
