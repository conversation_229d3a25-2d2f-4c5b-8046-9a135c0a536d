import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiMethods } from '../../services/api';

// Async thunks
export const fetchServices = createAsyncThunk(
  'service/fetchServices',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/services', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchService = createAsyncThunk(
  'service/fetchService',
  async (id, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get(`/services/${id}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const createService = createAsyncThunk(
  'service/createService',
  async (serviceData, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post('/services', serviceData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateService = createAsyncThunk(
  'service/updateService',
  async ({ id, data }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.put(`/services/${id}`, data);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const deleteService = createAsyncThunk(
  'service/deleteService',
  async (id, { rejectWithValue }) => {
    try {
      await apiMethods.delete(`/services/${id}`);
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchServiceStats = createAsyncThunk(
  'service/fetchServiceStats',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/services/stats', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const assignTechnician = createAsyncThunk(
  'service/assignTechnician',
  async ({ serviceId, technicianId }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post(`/services/${serviceId}/assign`, {
        technician_id: technicianId
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateServiceStatus = createAsyncThunk(
  'service/updateServiceStatus',
  async ({ serviceId, status, notes }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.patch(`/services/${serviceId}/status`, {
        status,
        notes
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  // Data
  services: [],
  currentService: null,
  stats: null,
  
  // Pagination
  pagination: {
    current_page: 1,
    total_pages: 1,
    total_items: 0,
    items_per_page: 15,
    has_next_page: false,
    has_prev_page: false,
  },
  
  // Loading states
  loading: false,
  serviceLoading: false,
  statsLoading: false,
  actionLoading: false,
  
  // Error states
  error: null,
  serviceError: null,
  statsError: null,
  actionError: null,
  
  // UI states
  selectedServices: [],
  filters: {
    status: '',
    priority: '',
    technician_id: '',
    customer_id: '',
    service_type: '',
    date_from: '',
    date_to: '',
  },
  sortBy: 'created_at',
  sortOrder: 'desc',
  searchQuery: '',
};

const serviceSlice = createSlice({
  name: 'service',
  initialState,
  reducers: {
    // Selection actions
    selectService: (state, action) => {
      const serviceId = action.payload;
      if (!state.selectedServices.includes(serviceId)) {
        state.selectedServices.push(serviceId);
      }
    },
    deselectService: (state, action) => {
      const serviceId = action.payload;
      state.selectedServices = state.selectedServices.filter(id => id !== serviceId);
    },
    selectAllServices: (state) => {
      state.selectedServices = state.services.map(service => service.id);
    },
    deselectAllServices: (state) => {
      state.selectedServices = [];
    },
    
    // Filter actions
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    // Sort actions
    setSortBy: (state, action) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action) => {
      state.sortOrder = action.payload;
    },
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    // Search actions
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    clearSearchQuery: (state) => {
      state.searchQuery = '';
    },
    
    // Clear current service
    clearCurrentService: (state) => {
      state.currentService = null;
      state.serviceError = null;
    },
    
    // Clear errors
    clearError: (state) => {
      state.error = null;
    },
    clearServiceError: (state) => {
      state.serviceError = null;
    },
    clearStatsError: (state) => {
      state.statsError = null;
    },
    clearActionError: (state) => {
      state.actionError = null;
    },
    
    // Reset state
    resetServiceState: (state) => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    // Fetch services
    builder
      .addCase(fetchServices.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchServices.fulfilled, (state, action) => {
        state.loading = false;
        state.services = action.payload.data.services;
        state.pagination = action.payload.data.pagination;
      })
      .addCase(fetchServices.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
    
    // Fetch single service
    builder
      .addCase(fetchService.pending, (state) => {
        state.serviceLoading = true;
        state.serviceError = null;
      })
      .addCase(fetchService.fulfilled, (state, action) => {
        state.serviceLoading = false;
        state.currentService = action.payload.data.service;
      })
      .addCase(fetchService.rejected, (state, action) => {
        state.serviceLoading = false;
        state.serviceError = action.payload;
      });
    
    // Create service
    builder
      .addCase(createService.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(createService.fulfilled, (state, action) => {
        state.actionLoading = false;
        state.services.unshift(action.payload.data.service);
      })
      .addCase(createService.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Update service
    builder
      .addCase(updateService.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(updateService.fulfilled, (state, action) => {
        state.actionLoading = false;
        const updatedService = action.payload.data.service;
        const index = state.services.findIndex(service => service.id === updatedService.id);
        if (index !== -1) {
          state.services[index] = updatedService;
        }
        if (state.currentService?.id === updatedService.id) {
          state.currentService = updatedService;
        }
      })
      .addCase(updateService.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Delete service
    builder
      .addCase(deleteService.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(deleteService.fulfilled, (state, action) => {
        state.actionLoading = false;
        const serviceId = action.payload;
        state.services = state.services.filter(service => service.id !== serviceId);
        state.selectedServices = state.selectedServices.filter(id => id !== serviceId);
        if (state.currentService?.id === serviceId) {
          state.currentService = null;
        }
      })
      .addCase(deleteService.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Fetch stats
    builder
      .addCase(fetchServiceStats.pending, (state) => {
        state.statsLoading = true;
        state.statsError = null;
      })
      .addCase(fetchServiceStats.fulfilled, (state, action) => {
        state.statsLoading = false;
        state.stats = action.payload.data;
      })
      .addCase(fetchServiceStats.rejected, (state, action) => {
        state.statsLoading = false;
        state.statsError = action.payload;
      });
    
    // Assign technician
    builder
      .addCase(assignTechnician.fulfilled, (state, action) => {
        const updatedService = action.payload.data.service;
        const index = state.services.findIndex(service => service.id === updatedService.id);
        if (index !== -1) {
          state.services[index] = updatedService;
        }
        if (state.currentService?.id === updatedService.id) {
          state.currentService = updatedService;
        }
      });
    
    // Update service status
    builder
      .addCase(updateServiceStatus.fulfilled, (state, action) => {
        const updatedService = action.payload.data.service;
        const index = state.services.findIndex(service => service.id === updatedService.id);
        if (index !== -1) {
          state.services[index] = updatedService;
        }
        if (state.currentService?.id === updatedService.id) {
          state.currentService = updatedService;
        }
      });
  },
});

export const {
  selectService,
  deselectService,
  selectAllServices,
  deselectAllServices,
  setFilter,
  setFilters,
  clearFilters,
  setSortBy,
  setSortOrder,
  toggleSortOrder,
  setSearchQuery,
  clearSearchQuery,
  clearCurrentService,
  clearError,
  clearServiceError,
  clearStatsError,
  clearActionError,
  resetServiceState,
} = serviceSlice.actions;

export default serviceSlice.reducer;

// Selectors
export const selectServices = (state) => state.service.services;
export const selectCurrentService = (state) => state.service.currentService;
export const selectServiceStats = (state) => state.service.stats;
export const selectServicePagination = (state) => state.service.pagination;
export const selectServiceLoading = (state) => state.service.loading;
export const selectServiceError = (state) => state.service.error;
export const selectSelectedServices = (state) => state.service.selectedServices;
export const selectServiceFilters = (state) => state.service.filters;
export const selectServiceSort = (state) => ({
  sortBy: state.service.sortBy,
  sortOrder: state.service.sortOrder,
});
export const selectServiceSearchQuery = (state) => state.service.searchQuery;
