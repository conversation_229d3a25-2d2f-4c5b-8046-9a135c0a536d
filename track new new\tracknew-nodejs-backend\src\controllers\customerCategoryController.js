const { CustomerCategory, User, Company, Customer } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all customer categories with filtering and pagination
const getCustomerCategories = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    priority_level,
    is_active = true,
    is_default,
    sort_by = 'category_name',
    sort_order = 'ASC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { category_name: { [Op.iLike]: `%${search}%` } },
      { category_code: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add filters
  if (priority_level) {
    whereConditions.priority_level = priority_level;
  }

  if (is_active !== undefined) {
    whereConditions.is_active = is_active === 'true';
  }

  if (is_default !== undefined) {
    whereConditions.is_default = is_default === 'true';
  }

  // Get customer categories with associations
  const { count, rows: customerCategories } = await CustomerCategory.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: User,
        as: 'salesRep',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'serviceRep',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ],
    limit: parseInt(limit),
    offset: offset,
    order: [[sort_by, sort_order.toUpperCase()]],
    distinct: true
  });

  // Calculate pagination
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      customer_categories: customerCategories,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single customer category by ID
const getCustomerCategory = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const customerCategory = await CustomerCategory.findOne({
    where: {
      id,
      company_id: companyId
    },
    include: [
      {
        model: User,
        as: 'salesRep',
        attributes: ['id', 'name', 'email', 'phone']
      },
      {
        model: User,
        as: 'serviceRep',
        attributes: ['id', 'name', 'email', 'phone']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!customerCategory) {
    return next(new AppError('Customer category not found', 404));
  }

  // Get recent customers in this category
  const recentCustomers = await Customer.findAll({
    where: {
      company_id: companyId,
      customer_category_id: id
    },
    attributes: ['id', 'customer_name', 'email', 'mobile_number', 'created_at'],
    order: [['created_at', 'DESC']],
    limit: 10
  });

  res.status(200).json({
    status: 'success',
    data: {
      customer_category: {
        ...customerCategory.toJSON(),
        recent_customers: recentCustomers
      }
    }
  });
});

// Create new customer category
const createCustomerCategory = catchAsync(async (req, res, next) => {
  const {
    category_name,
    category_code,
    description,
    color = '#007bff',
    icon,
    discount_percentage = 0,
    credit_limit = 0,
    payment_terms = 30,
    priority_level = 'normal',
    auto_assign_sales_rep,
    auto_assign_service_rep,
    notification_preferences = { email: true, sms: false, whatsapp: false },
    service_preferences = {},
    billing_preferences = {},
    custom_fields = {},
    tags = [],
    is_active = true,
    is_default = false,
    sort_order = 0
  } = req.body;

  const companyId = req.user.company_id;

  // Check for duplicate name within company
  const existingCategory = await CustomerCategory.findOne({
    where: {
      company_id: companyId,
      category_name: category_name
    }
  });

  if (existingCategory) {
    return next(new AppError('Customer category with this name already exists', 400));
  }

  // Check for duplicate code if provided
  if (category_code) {
    const existingCode = await CustomerCategory.findOne({
      where: {
        company_id: companyId,
        category_code: category_code
      }
    });

    if (existingCode) {
      return next(new AppError('Customer category with this code already exists', 400));
    }
  }

  // Validate sales rep if provided
  if (auto_assign_sales_rep) {
    const salesRep = await User.findOne({
      where: {
        id: auto_assign_sales_rep,
        company_id: companyId,
        is_active: true
      }
    });

    if (!salesRep) {
      return next(new AppError('Sales representative not found or inactive', 400));
    }
  }

  // Validate service rep if provided
  if (auto_assign_service_rep) {
    const serviceRep = await User.findOne({
      where: {
        id: auto_assign_service_rep,
        company_id: companyId,
        is_active: true
      }
    });

    if (!serviceRep) {
      return next(new AppError('Service representative not found or inactive', 400));
    }
  }

  // If setting as default, unset other defaults
  if (is_default) {
    await CustomerCategory.update(
      { is_default: false },
      {
        where: {
          company_id: companyId,
          is_default: true
        }
      }
    );
  }

  const customerCategory = await CustomerCategory.create({
    company_id: companyId,
    category_name,
    category_code,
    description,
    color,
    icon,
    discount_percentage,
    credit_limit,
    payment_terms,
    priority_level,
    auto_assign_sales_rep,
    auto_assign_service_rep,
    notification_preferences,
    service_preferences,
    billing_preferences,
    custom_fields,
    tags,
    is_active,
    is_default,
    sort_order,
    created_by: req.user.id
  });

  // Fetch the created customer category with associations
  const createdCustomerCategory = await CustomerCategory.findByPk(customerCategory.id, {
    include: [
      {
        model: User,
        as: 'salesRep',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'serviceRep',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      customer_category: createdCustomerCategory
    }
  });
});

// Update customer category
const updateCustomerCategory = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const customerCategory = await CustomerCategory.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!customerCategory) {
    return next(new AppError('Customer category not found', 404));
  }

  // Check for duplicate name (excluding current category)
  if (req.body.category_name) {
    const existingCategory = await CustomerCategory.findOne({
      where: {
        company_id: companyId,
        id: { [Op.ne]: id },
        category_name: req.body.category_name
      }
    });

    if (existingCategory) {
      return next(new AppError('Customer category with this name already exists', 400));
    }
  }

  // Check for duplicate code (excluding current category)
  if (req.body.category_code) {
    const existingCode = await CustomerCategory.findOne({
      where: {
        company_id: companyId,
        id: { [Op.ne]: id },
        category_code: req.body.category_code
      }
    });

    if (existingCode) {
      return next(new AppError('Customer category with this code already exists', 400));
    }
  }

  // Validate sales rep if provided
  if (req.body.auto_assign_sales_rep) {
    const salesRep = await User.findOne({
      where: {
        id: req.body.auto_assign_sales_rep,
        company_id: companyId,
        is_active: true
      }
    });

    if (!salesRep) {
      return next(new AppError('Sales representative not found or inactive', 400));
    }
  }

  // Validate service rep if provided
  if (req.body.auto_assign_service_rep) {
    const serviceRep = await User.findOne({
      where: {
        id: req.body.auto_assign_service_rep,
        company_id: companyId,
        is_active: true
      }
    });

    if (!serviceRep) {
      return next(new AppError('Service representative not found or inactive', 400));
    }
  }

  // If setting as default, unset other defaults
  if (req.body.is_default === true) {
    await CustomerCategory.update(
      { is_default: false },
      {
        where: {
          company_id: companyId,
          id: { [Op.ne]: id },
          is_default: true
        }
      }
    );
  }

  // Update customer category
  await customerCategory.update({
    ...req.body,
    updated_by: req.user.id
  });

  // Fetch updated customer category with associations
  const updatedCustomerCategory = await CustomerCategory.findByPk(id, {
    include: [
      {
        model: User,
        as: 'salesRep',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'serviceRep',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      customer_category: updatedCustomerCategory
    }
  });
});

// Delete customer category
const deleteCustomerCategory = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const customerCategory = await CustomerCategory.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!customerCategory) {
    return next(new AppError('Customer category not found', 404));
  }

  // Check if category is default
  if (customerCategory.is_default) {
    return next(new AppError('Cannot delete default customer category', 400));
  }

  // Check if category has customers
  const customerCount = await Customer.count({
    where: {
      customer_category_id: id,
      company_id: companyId
    }
  });

  if (customerCount > 0) {
    return next(new AppError(`Cannot delete category. It has ${customerCount} customer(s). Please reassign them first.`, 400));
  }

  await customerCategory.destroy();

  res.status(200).json({
    status: 'success',
    message: 'Customer category deleted successfully'
  });
});

// Get customer category statistics
const getCustomerCategoryStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  // Total categories count
  const totalCategories = await CustomerCategory.count({
    where: { company_id: companyId }
  });

  // Active categories count
  const activeCategories = await CustomerCategory.count({
    where: {
      company_id: companyId,
      is_active: true
    }
  });

  // Categories by priority level
  const categoriesByPriority = await CustomerCategory.findAll({
    where: { company_id: companyId },
    attributes: [
      'priority_level',
      [CustomerCategory.sequelize.fn('COUNT', CustomerCategory.sequelize.col('id')), 'count']
    ],
    group: ['priority_level'],
    raw: true
  });

  // Categories with customers
  const categoriesWithCustomers = await CustomerCategory.count({
    where: { company_id: companyId },
    include: [{
      model: Customer,
      as: 'customers',
      required: true
    }]
  });

  // Most used categories
  const mostUsedCategories = await CustomerCategory.findAll({
    where: { company_id: companyId },
    attributes: [
      'id',
      'category_name',
      'color',
      'priority_level',
      'customer_count'
    ],
    order: [['customer_count', 'DESC']],
    limit: 5
  });

  // Average discount percentage
  const avgDiscount = await CustomerCategory.findOne({
    where: {
      company_id: companyId,
      discount_percentage: { [Op.ne]: null }
    },
    attributes: [
      [CustomerCategory.sequelize.fn('AVG', CustomerCategory.sequelize.col('discount_percentage')), 'avg_discount']
    ],
    raw: true
  });

  // Average credit limit
  const avgCreditLimit = await CustomerCategory.findOne({
    where: {
      company_id: companyId,
      credit_limit: { [Op.ne]: null }
    },
    attributes: [
      [CustomerCategory.sequelize.fn('AVG', CustomerCategory.sequelize.col('credit_limit')), 'avg_credit_limit']
    ],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_categories: totalCategories,
      active_categories: activeCategories,
      inactive_categories: totalCategories - activeCategories,
      categories_with_customers: categoriesWithCustomers,
      categories_by_priority: categoriesByPriority,
      most_used_categories: mostUsedCategories,
      average_discount: parseFloat(avgDiscount?.avg_discount || 0).toFixed(2),
      average_credit_limit: parseFloat(avgCreditLimit?.avg_credit_limit || 0).toFixed(2)
    }
  });
});

// Get active customer categories (for dropdowns)
const getActiveCustomerCategories = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const categories = await CustomerCategory.findAll({
    where: {
      company_id: companyId,
      is_active: true
    },
    attributes: [
      'id',
      'category_name',
      'category_code',
      'color',
      'icon',
      'discount_percentage',
      'credit_limit',
      'payment_terms',
      'priority_level',
      'is_default'
    ],
    order: [['sort_order', 'ASC'], ['category_name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      categories
    }
  });
});

// Get categories by priority level
const getCategoriesByPriority = catchAsync(async (req, res) => {
  const { priority_level } = req.params;
  const companyId = req.user.company_id;

  const categories = await CustomerCategory.findAll({
    where: {
      company_id: companyId,
      priority_level: priority_level,
      is_active: true
    },
    attributes: ['id', 'category_name', 'description', 'color', 'icon', 'discount_percentage', 'credit_limit'],
    order: [['sort_order', 'ASC'], ['category_name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      categories,
      priority_level
    }
  });
});

// Set default category
const setDefaultCategory = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const customerCategory = await CustomerCategory.findOne({
    where: {
      id,
      company_id: companyId,
      is_active: true
    }
  });

  if (!customerCategory) {
    return next(new AppError('Customer category not found or inactive', 404));
  }

  // Unset all other defaults
  await CustomerCategory.update(
    { is_default: false },
    {
      where: {
        company_id: companyId,
        is_default: true
      }
    }
  );

  // Set this category as default
  await customerCategory.update({
    is_default: true,
    updated_by: req.user.id
  });

  res.status(200).json({
    status: 'success',
    message: 'Default category updated successfully',
    data: {
      category: {
        id: customerCategory.id,
        category_name: customerCategory.category_name,
        is_default: true
      }
    }
  });
});

// Bulk update sort order
const updateSortOrder = catchAsync(async (req, res, next) => {
  const { categories } = req.body;
  const companyId = req.user.company_id;

  if (!Array.isArray(categories) || categories.length === 0) {
    return next(new AppError('Categories array is required', 400));
  }

  // Validate all categories belong to the company
  const categoryIds = categories.map(cat => cat.id);
  const validCategories = await CustomerCategory.findAll({
    where: {
      id: { [Op.in]: categoryIds },
      company_id: companyId
    },
    attributes: ['id']
  });

  if (validCategories.length !== categoryIds.length) {
    return next(new AppError('Some categories not found or do not belong to your company', 400));
  }

  // Update sort orders
  const updatePromises = categories.map(cat =>
    CustomerCategory.update(
      {
        sort_order: cat.sort_order,
        updated_by: req.user.id
      },
      {
        where: {
          id: cat.id,
          company_id: companyId
        }
      }
    )
  );

  await Promise.all(updatePromises);

  res.status(200).json({
    status: 'success',
    message: 'Sort order updated successfully'
  });
});

module.exports = {
  getCustomerCategories,
  getCustomerCategory,
  createCustomerCategory,
  updateCustomerCategory,
  deleteCustomerCategory,
  getCustomerCategoryStats,
  getActiveCustomerCategories,
  getCategoriesByPriority,
  setDefaultCategory,
  updateSortOrder
};
