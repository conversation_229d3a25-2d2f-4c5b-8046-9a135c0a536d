const express = require('express');
const { body } = require('express-validator');
const expenseCategoryController = require('../controllers/expenseCategoryController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating expense category
const createExpenseCategoryValidation = [
  body('name')
    .notEmpty()
    .withMessage('Category name is required')
    .isLength({ min: 2, max: 255 })
    .withMessage('Category name must be between 2 and 255 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('category_code')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category code must be between 1 and 50 characters'),
  
  body('parent_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Parent ID must be a positive integer'),
  
  body('icon')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Icon must be maximum 100 characters'),
  
  body('color_code')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color code must be a valid hex color (e.g., #FF0000)'),
  
  body('expense_type')
    .optional()
    .isIn(['operational', 'capital', 'administrative', 'sales', 'marketing', 'travel', 'entertainment', 'other'])
    .withMessage('Invalid expense type'),
  
  body('is_billable')
    .optional()
    .isBoolean()
    .withMessage('Is billable must be a boolean'),
  
  body('default_markup_percentage')
    .optional()
    .isFloat({ min: 0, max: 1000 })
    .withMessage('Default markup percentage must be between 0 and 1000'),
  
  body('requires_receipt')
    .optional()
    .isBoolean()
    .withMessage('Requires receipt must be a boolean'),
  
  body('requires_approval')
    .optional()
    .isBoolean()
    .withMessage('Requires approval must be a boolean'),
  
  body('approval_threshold')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Approval threshold must be a positive number'),
  
  body('auto_approval_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Auto approval limit must be a positive number'),
  
  body('approval_workflow')
    .optional()
    .isObject()
    .withMessage('Approval workflow must be a valid JSON object'),
  
  body('gl_account_code')
    .optional()
    .isLength({ max: 50 })
    .withMessage('GL account code must be maximum 50 characters'),
  
  body('tax_category')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Tax category must be maximum 100 characters'),
  
  body('is_reimbursable')
    .optional()
    .isBoolean()
    .withMessage('Is reimbursable must be a boolean'),
  
  body('default_reimbursement_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Default reimbursement rate must be between 0 and 100'),
  
  body('mileage_rate')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Mileage rate must be a positive number'),
  
  body('per_diem_rate')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Per diem rate must be a positive number'),
  
  body('daily_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Daily limit must be a positive number'),
  
  body('monthly_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Monthly limit must be a positive number'),
  
  body('yearly_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Yearly limit must be a positive number'),
  
  body('notification_threshold')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Notification threshold must be between 0 and 100'),
  
  body('workflow_template')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Workflow template must be maximum 255 characters'),
  
  body('custom_fields')
    .optional()
    .isObject()
    .withMessage('Custom fields must be a valid JSON object'),
  
  body('validation_rules')
    .optional()
    .isObject()
    .withMessage('Validation rules must be a valid JSON object'),
  
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean'),
  
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must be maximum 1000 characters')
];

// Validation rules for updating expense category
const updateExpenseCategoryValidation = [
  body('name')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Category name must be between 2 and 255 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('category_code')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category code must be between 1 and 50 characters'),
  
  body('parent_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Parent ID must be a positive integer'),
  
  body('color_code')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color code must be a valid hex color (e.g., #FF0000)'),
  
  body('expense_type')
    .optional()
    .isIn(['operational', 'capital', 'administrative', 'sales', 'marketing', 'travel', 'entertainment', 'other'])
    .withMessage('Invalid expense type'),
  
  body('is_billable')
    .optional()
    .isBoolean()
    .withMessage('Is billable must be a boolean'),
  
  body('default_markup_percentage')
    .optional()
    .isFloat({ min: 0, max: 1000 })
    .withMessage('Default markup percentage must be between 0 and 1000'),
  
  body('requires_approval')
    .optional()
    .isBoolean()
    .withMessage('Requires approval must be a boolean'),
  
  body('approval_threshold')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Approval threshold must be a positive number'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean'),
  
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer')
];

// Validation for bulk sort order update
const updateSortOrderValidation = [
  body('categories')
    .isArray({ min: 1 })
    .withMessage('Categories array is required and must not be empty'),
  
  body('categories.*.id')
    .isInt({ min: 1 })
    .withMessage('Each category must have a valid ID'),
  
  body('categories.*.sort_order')
    .isInt({ min: 0 })
    .withMessage('Each category must have a valid sort order')
];

// Routes
router
  .route('/')
  .get(expenseCategoryController.getExpenseCategories)
  .post(createExpenseCategoryValidation, validateRequest, restrictTo('admin', 'sub_admin', 'manager'), expenseCategoryController.createExpenseCategory);

router
  .route('/stats')
  .get(expenseCategoryController.getExpenseCategoryStats);

router
  .route('/tree')
  .get(expenseCategoryController.getExpenseCategoryTree);

router
  .route('/active')
  .get(expenseCategoryController.getActiveExpenseCategories);

router
  .route('/sort-order')
  .put(updateSortOrderValidation, validateRequest, restrictTo('admin', 'sub_admin', 'manager'), expenseCategoryController.updateSortOrder);

router
  .route('/by-type/:expense_type')
  .get(expenseCategoryController.getCategoriesByType);

router
  .route('/:id')
  .get(expenseCategoryController.getExpenseCategory)
  .put(updateExpenseCategoryValidation, validateRequest, restrictTo('admin', 'sub_admin', 'manager'), expenseCategoryController.updateExpenseCategory)
  .delete(restrictTo('admin', 'sub_admin'), expenseCategoryController.deleteExpenseCategory);

module.exports = router;
