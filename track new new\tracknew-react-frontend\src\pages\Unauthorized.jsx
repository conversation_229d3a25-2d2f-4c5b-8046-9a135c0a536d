import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ExclamationTriangleIcon, ArrowLeftIcon, HomeIcon } from '@heroicons/react/24/outline';
import { Button } from '../components/ui';

const Unauthorized = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
          <ExclamationTriangleIcon className="h-8 w-8 text-red-600 dark:text-red-400" />
        </div>
        
        <div className="mt-6 text-center">
          <h1 className="text-3xl font-extrabold text-gray-900 dark:text-white">
            Access Denied
          </h1>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            You don't have permission to access this page.
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              What can you do?
            </h2>
            
            <div className="space-y-4 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <p className="text-left">
                  Contact your administrator to request access to this feature
                </p>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <p className="text-left">
                  Check if you're logged in with the correct account
                </p>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <p className="text-left">
                  Return to the dashboard and try a different section
                </p>
              </div>
            </div>
          </div>

          <div className="mt-8 space-y-3">
            <Button
              fullWidth
              leftIcon={ArrowLeftIcon}
              onClick={() => navigate(-1)}
              variant="outline"
            >
              Go Back
            </Button>
            
            <Button
              fullWidth
              leftIcon={HomeIcon}
              onClick={() => navigate('/dashboard')}
            >
              Go to Dashboard
            </Button>
          </div>
        </div>
      </div>

      <div className="mt-8 text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          Need help? Contact support at{' '}
          <a 
            href="mailto:<EMAIL>" 
            className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
          >
            <EMAIL>
          </a>
        </p>
      </div>
    </div>
  );
};

export default Unauthorized;
