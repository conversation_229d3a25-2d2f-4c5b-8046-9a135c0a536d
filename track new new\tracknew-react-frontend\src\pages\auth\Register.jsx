import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { 
  UserPlusIcon, 
  UserIcon, 
  EnvelopeIcon, 
  LockClosedIcon,
  BuildingOfficeIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';

// Redux
import {
  register,
  clearError,
  selectAuthLoading,
  selectAuthError,
  selectUser
} from '../../store/slices/authSlice';

// Components
import { Button } from '../../components/ui';
import { Input, Select, Checkbox } from '../../components/forms';

const Register = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  // Local state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    company_name: '',
    phone: '',
    role: 'admin',
    terms_accepted: false
  });
  
  // Selectors
  const loading = useSelector(selectAuthLoading);
  const error = useSelector(selectAuthError);
  const user = useSelector(selectUser);

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      navigate('/dashboard', { replace: true });
    }
  }, [user, navigate]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate passwords match
    if (formData.password !== formData.password_confirmation) {
      return;
    }
    
    const result = await dispatch(register({
      name: formData.name,
      email: formData.email,
      password: formData.password,
      password_confirmation: formData.password_confirmation,
      company_name: formData.company_name,
      phone: formData.phone,
      role: formData.role
    }));
    
    if (register.fulfilled.match(result)) {
      navigate('/dashboard', { replace: true });
    }
  };

  const isFormValid = 
    formData.name &&
    formData.email &&
    formData.password &&
    formData.password_confirmation &&
    formData.company_name &&
    formData.terms_accepted &&
    formData.password === formData.password_confirmation;

  const passwordsMatch = 
    !formData.password_confirmation || 
    formData.password === formData.password_confirmation;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
            <UserPlusIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
            Create your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
            Or{' '}
            <Link
              to="/login"
              className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
            >
              sign in to your existing account
            </Link>
          </p>
        </div>

        {/* Registration Form */}
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Name */}
            <Input
              label="Full Name"
              name="name"
              type="text"
              autoComplete="name"
              required
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter your full name"
              leftIcon={UserIcon}
              error={error?.field === 'name' ? error.message : ''}
            />

            {/* Email */}
            <Input
              label="Email address"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter your email"
              leftIcon={EnvelopeIcon}
              error={error?.field === 'email' ? error.message : ''}
            />

            {/* Company Name */}
            <Input
              label="Company Name"
              name="company_name"
              type="text"
              autoComplete="organization"
              required
              value={formData.company_name}
              onChange={handleInputChange}
              placeholder="Enter your company name"
              leftIcon={BuildingOfficeIcon}
              error={error?.field === 'company_name' ? error.message : ''}
            />

            {/* Phone */}
            <Input
              label="Phone Number"
              name="phone"
              type="tel"
              autoComplete="tel"
              value={formData.phone}
              onChange={handleInputChange}
              placeholder="Enter your phone number"
              leftIcon={PhoneIcon}
              error={error?.field === 'phone' ? error.message : ''}
            />

            {/* Password */}
            <Input
              label="Password"
              name="password"
              type="password"
              autoComplete="new-password"
              required
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Create a password"
              leftIcon={LockClosedIcon}
              showPasswordToggle={true}
              error={error?.field === 'password' ? error.message : ''}
              helperText="Password must be at least 8 characters long"
            />

            {/* Confirm Password */}
            <Input
              label="Confirm Password"
              name="password_confirmation"
              type="password"
              autoComplete="new-password"
              required
              value={formData.password_confirmation}
              onChange={handleInputChange}
              placeholder="Confirm your password"
              leftIcon={LockClosedIcon}
              showPasswordToggle={true}
              error={!passwordsMatch ? 'Passwords do not match' : ''}
            />

            {/* Role */}
            <Select
              label="Role"
              name="role"
              value={formData.role}
              onChange={(value) => setFormData(prev => ({ ...prev, role: value }))}
              options={[
                { value: 'admin', label: 'Administrator' },
                { value: 'manager', label: 'Manager' },
                { value: 'employee', label: 'Employee' }
              ]}
            />
          </div>

          {/* Terms and Conditions */}
          <div className="space-y-4">
            <Checkbox
              name="terms_accepted"
              checked={formData.terms_accepted}
              onChange={(checked) => setFormData(prev => ({ ...prev, terms_accepted: checked }))}
              label={
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  I agree to the{' '}
                  <Link to="/terms" className="text-blue-600 hover:text-blue-500 dark:text-blue-400">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link to="/privacy" className="text-blue-600 hover:text-blue-500 dark:text-blue-400">
                    Privacy Policy
                  </Link>
                </span>
              }
              required
            />
          </div>

          {/* Error Message */}
          {error && !error.field && (
            <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
              <div className="text-sm text-red-700 dark:text-red-400">
                {error.message || 'An error occurred during registration'}
              </div>
            </div>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            fullWidth
            loading={loading}
            disabled={!isFormValid}
            size="lg"
            color="green"
          >
            Create Account
          </Button>
        </form>

        {/* Footer */}
        <div className="text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Already have an account?{' '}
            <Link
              to="/login"
              className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Sign in here
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Register;
