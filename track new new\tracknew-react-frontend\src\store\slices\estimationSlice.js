import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiMethods } from '../../services/api';

// Async thunks
export const fetchEstimations = createAsyncThunk(
  'estimation/fetchEstimations',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/estimations', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchEstimation = createAsyncThunk(
  'estimation/fetchEstimation',
  async (id, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get(`/estimations/${id}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const createEstimation = createAsyncThunk(
  'estimation/createEstimation',
  async (estimationData, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post('/estimations', estimationData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateEstimation = createAsyncThunk(
  'estimation/updateEstimation',
  async ({ id, data }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.put(`/estimations/${id}`, data);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const deleteEstimation = createAsyncThunk(
  'estimation/deleteEstimation',
  async (id, { rejectWithValue }) => {
    try {
      await apiMethods.delete(`/estimations/${id}`);
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const convertToSale = createAsyncThunk(
  'estimation/convertToSale',
  async (estimationId, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post(`/estimations/${estimationId}/convert-to-sale`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const sendEstimation = createAsyncThunk(
  'estimation/sendEstimation',
  async ({ estimationId, sendData }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post(`/estimations/${estimationId}/send`, sendData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchEstimationStats = createAsyncThunk(
  'estimation/fetchEstimationStats',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/estimations/stats', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  // Data
  estimations: [],
  currentEstimation: null,
  stats: null,
  
  // Pagination
  pagination: {
    current_page: 1,
    total_pages: 1,
    total_items: 0,
    items_per_page: 15,
    has_next_page: false,
    has_prev_page: false,
  },
  
  // Loading states
  loading: false,
  estimationLoading: false,
  statsLoading: false,
  actionLoading: false,
  
  // Error states
  error: null,
  estimationError: null,
  statsError: null,
  actionError: null,
  
  // UI states
  selectedEstimations: [],
  filters: {
    status: '',
    customer_id: '',
    date_from: '',
    date_to: '',
    amount_from: '',
    amount_to: '',
    valid_until_from: '',
    valid_until_to: '',
  },
  sortBy: 'created_at',
  sortOrder: 'desc',
  searchQuery: '',
  
  // Estimation builder
  builder: {
    customer: null,
    items: [],
    discount: 0,
    tax_rate: 0,
    notes: '',
    terms_conditions: '',
    valid_until: null,
  },
};

const estimationSlice = createSlice({
  name: 'estimation',
  initialState,
  reducers: {
    // Selection actions
    selectEstimation: (state, action) => {
      const estimationId = action.payload;
      if (!state.selectedEstimations.includes(estimationId)) {
        state.selectedEstimations.push(estimationId);
      }
    },
    deselectEstimation: (state, action) => {
      const estimationId = action.payload;
      state.selectedEstimations = state.selectedEstimations.filter(id => id !== estimationId);
    },
    selectAllEstimations: (state) => {
      state.selectedEstimations = state.estimations.map(estimation => estimation.id);
    },
    deselectAllEstimations: (state) => {
      state.selectedEstimations = [];
    },
    
    // Filter actions
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    // Sort actions
    setSortBy: (state, action) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action) => {
      state.sortOrder = action.payload;
    },
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    // Search actions
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    clearSearchQuery: (state) => {
      state.searchQuery = '';
    },
    
    // Builder actions
    setBuilderCustomer: (state, action) => {
      state.builder.customer = action.payload;
    },
    addBuilderItem: (state, action) => {
      const item = action.payload;
      const existingItem = state.builder.items.find(i => i.product_id === item.product_id);
      
      if (existingItem) {
        existingItem.quantity += item.quantity || 1;
        existingItem.total_amount = existingItem.quantity * existingItem.unit_price;
      } else {
        state.builder.items.push({
          ...item,
          quantity: item.quantity || 1,
          total_amount: (item.quantity || 1) * item.unit_price,
        });
      }
    },
    removeBuilderItem: (state, action) => {
      const productId = action.payload;
      state.builder.items = state.builder.items.filter(item => item.product_id !== productId);
    },
    updateBuilderItem: (state, action) => {
      const { productId, updates } = action.payload;
      const item = state.builder.items.find(i => i.product_id === productId);
      if (item) {
        Object.assign(item, updates);
        item.total_amount = item.quantity * item.unit_price;
      }
    },
    setBuilderDiscount: (state, action) => {
      state.builder.discount = action.payload;
    },
    setBuilderTaxRate: (state, action) => {
      state.builder.tax_rate = action.payload;
    },
    setBuilderNotes: (state, action) => {
      state.builder.notes = action.payload;
    },
    setBuilderTermsConditions: (state, action) => {
      state.builder.terms_conditions = action.payload;
    },
    setBuilderValidUntil: (state, action) => {
      state.builder.valid_until = action.payload;
    },
    clearBuilder: (state) => {
      state.builder = initialState.builder;
    },
    
    // Clear current estimation
    clearCurrentEstimation: (state) => {
      state.currentEstimation = null;
      state.estimationError = null;
    },
    
    // Clear errors
    clearError: (state) => {
      state.error = null;
    },
    clearEstimationError: (state) => {
      state.estimationError = null;
    },
    clearStatsError: (state) => {
      state.statsError = null;
    },
    clearActionError: (state) => {
      state.actionError = null;
    },
    
    // Reset state
    resetEstimationState: (state) => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    // Fetch estimations
    builder
      .addCase(fetchEstimations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchEstimations.fulfilled, (state, action) => {
        state.loading = false;
        state.estimations = action.payload.data.estimations;
        state.pagination = action.payload.data.pagination;
      })
      .addCase(fetchEstimations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
    
    // Fetch single estimation
    builder
      .addCase(fetchEstimation.pending, (state) => {
        state.estimationLoading = true;
        state.estimationError = null;
      })
      .addCase(fetchEstimation.fulfilled, (state, action) => {
        state.estimationLoading = false;
        state.currentEstimation = action.payload.data.estimation;
      })
      .addCase(fetchEstimation.rejected, (state, action) => {
        state.estimationLoading = false;
        state.estimationError = action.payload;
      });
    
    // Create estimation
    builder
      .addCase(createEstimation.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(createEstimation.fulfilled, (state, action) => {
        state.actionLoading = false;
        state.estimations.unshift(action.payload.data.estimation);
        // Clear builder after successful creation
        state.builder = initialState.builder;
      })
      .addCase(createEstimation.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Update estimation
    builder
      .addCase(updateEstimation.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(updateEstimation.fulfilled, (state, action) => {
        state.actionLoading = false;
        const updatedEstimation = action.payload.data.estimation;
        const index = state.estimations.findIndex(estimation => estimation.id === updatedEstimation.id);
        if (index !== -1) {
          state.estimations[index] = updatedEstimation;
        }
        if (state.currentEstimation?.id === updatedEstimation.id) {
          state.currentEstimation = updatedEstimation;
        }
      })
      .addCase(updateEstimation.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Delete estimation
    builder
      .addCase(deleteEstimation.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(deleteEstimation.fulfilled, (state, action) => {
        state.actionLoading = false;
        const estimationId = action.payload;
        state.estimations = state.estimations.filter(estimation => estimation.id !== estimationId);
        state.selectedEstimations = state.selectedEstimations.filter(id => id !== estimationId);
        if (state.currentEstimation?.id === estimationId) {
          state.currentEstimation = null;
        }
      })
      .addCase(deleteEstimation.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Convert to sale
    builder
      .addCase(convertToSale.fulfilled, (state, action) => {
        const updatedEstimation = action.payload.data.estimation;
        const index = state.estimations.findIndex(estimation => estimation.id === updatedEstimation.id);
        if (index !== -1) {
          state.estimations[index] = updatedEstimation;
        }
        if (state.currentEstimation?.id === updatedEstimation.id) {
          state.currentEstimation = updatedEstimation;
        }
      });
    
    // Send estimation
    builder
      .addCase(sendEstimation.fulfilled, (state, action) => {
        const updatedEstimation = action.payload.data.estimation;
        const index = state.estimations.findIndex(estimation => estimation.id === updatedEstimation.id);
        if (index !== -1) {
          state.estimations[index] = updatedEstimation;
        }
        if (state.currentEstimation?.id === updatedEstimation.id) {
          state.currentEstimation = updatedEstimation;
        }
      });
    
    // Fetch stats
    builder
      .addCase(fetchEstimationStats.pending, (state) => {
        state.statsLoading = true;
        state.statsError = null;
      })
      .addCase(fetchEstimationStats.fulfilled, (state, action) => {
        state.statsLoading = false;
        state.stats = action.payload.data;
      })
      .addCase(fetchEstimationStats.rejected, (state, action) => {
        state.statsLoading = false;
        state.statsError = action.payload;
      });
  },
});

export const {
  selectEstimation,
  deselectEstimation,
  selectAllEstimations,
  deselectAllEstimations,
  setFilter,
  setFilters,
  clearFilters,
  setSortBy,
  setSortOrder,
  toggleSortOrder,
  setSearchQuery,
  clearSearchQuery,
  setBuilderCustomer,
  addBuilderItem,
  removeBuilderItem,
  updateBuilderItem,
  setBuilderDiscount,
  setBuilderTaxRate,
  setBuilderNotes,
  setBuilderTermsConditions,
  setBuilderValidUntil,
  clearBuilder,
  clearCurrentEstimation,
  clearError,
  clearEstimationError,
  clearStatsError,
  clearActionError,
  resetEstimationState,
} = estimationSlice.actions;

export default estimationSlice.reducer;

// Selectors
export const selectEstimations = (state) => state.estimation.estimations;
export const selectCurrentEstimation = (state) => state.estimation.currentEstimation;
export const selectEstimationStats = (state) => state.estimation.stats;
export const selectEstimationPagination = (state) => state.estimation.pagination;
export const selectEstimationLoading = (state) => state.estimation.loading;
export const selectEstimationError = (state) => state.estimation.error;
export const selectSelectedEstimations = (state) => state.estimation.selectedEstimations;
export const selectEstimationFilters = (state) => state.estimation.filters;
export const selectEstimationSort = (state) => ({
  sortBy: state.estimation.sortBy,
  sortOrder: state.estimation.sortOrder,
});
export const selectEstimationSearchQuery = (state) => state.estimation.searchQuery;
export const selectEstimationBuilder = (state) => state.estimation.builder;
export const selectBuilderTotal = (state) => {
  const { items, discount, tax_rate } = state.estimation.builder;
  const subtotal = items.reduce((sum, item) => sum + item.total_amount, 0);
  const discountAmount = (subtotal * discount) / 100;
  const taxableAmount = subtotal - discountAmount;
  const taxAmount = (taxableAmount * tax_rate) / 100;
  return {
    subtotal,
    discount: discountAmount,
    tax: taxAmount,
    total: taxableAmount + taxAmount,
  };
};
