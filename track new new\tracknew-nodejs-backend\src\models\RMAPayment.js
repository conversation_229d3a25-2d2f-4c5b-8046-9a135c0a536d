const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const RMAPayment = sequelize.define('RMAPayment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  rma_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'rmas',
      key: 'id'
    }
  },
  payment_type: {
    type: DataTypes.ENUM('refund', 'charge', 'adjustment', 'credit', 'debit'),
    allowNull: false
  },
  payment_reason: {
    type: DataTypes.ENUM('repair_cost', 'replacement_cost', 'shipping_cost', 'restocking_fee', 'refund_amount', 'credit_adjustment', 'other'),
    allowNull: false
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'INR'
  },
  payment_method: {
    type: DataTypes.ENUM('cash', 'cheque', 'bank_transfer', 'credit_card', 'debit_card', 'upi', 'wallet', 'online', 'credit_note', 'adjustment'),
    defaultValue: 'cash'
  },
  payment_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  reference_number: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Transaction reference number'
  },
  bank_name: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  account_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  cheque_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  cheque_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  transaction_id: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  gateway_response: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Payment gateway response data'
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'),
    defaultValue: 'pending'
  },
  approval_status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected'),
    defaultValue: 'pending'
  },
  approved_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  approved_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  rejection_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  internal_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  customer_notified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  notification_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  receipt_generated: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  receipt_number: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of attachment file paths'
  },
  tax_amount: {
    type: DataTypes.DECIMAL(8, 2),
    defaultValue: 0.00
  },
  tax_rate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00
  },
  net_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Amount after tax adjustments'
  },
  exchange_rate: {
    type: DataTypes.DECIMAL(10, 4),
    defaultValue: 1.0000
  },
  base_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Amount in base currency'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'rma_payments',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['rma_id']
    },
    {
      fields: ['payment_type']
    },
    {
      fields: ['payment_reason']
    },
    {
      fields: ['payment_method']
    },
    {
      fields: ['payment_date']
    },
    {
      fields: ['status']
    },
    {
      fields: ['approval_status']
    },
    {
      fields: ['approved_by']
    },
    {
      fields: ['reference_number']
    },
    {
      fields: ['transaction_id']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = RMAPayment;
