const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const RolePermission = sequelize.define('RolePermission', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  role_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'roles',
      key: 'id'
    }
  },
  permission_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'permissions',
      key: 'id'
    }
  },
  granted_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who granted this permission'
  },
  granted_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  valid_from: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Permission validity start date'
  },
  valid_until: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Permission validity end date'
  },
  conditions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of conditions for this permission grant'
  },
  restrictions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of additional restrictions'
  },
  approval_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether approval is required to use this permission'
  },
  approval_status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected'),
    allowNull: true
  },
  approved_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  approved_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'role_permissions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['role_id']
    },
    {
      fields: ['permission_id']
    },
    {
      fields: ['role_id', 'permission_id'],
      unique: true
    },
    {
      fields: ['granted_by']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['approval_status']
    },
    {
      fields: ['granted_at']
    }
  ]
});

module.exports = RolePermission;
