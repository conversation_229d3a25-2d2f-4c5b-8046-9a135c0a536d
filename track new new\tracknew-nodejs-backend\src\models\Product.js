const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Product = sequelize.define('Product', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  short_description: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  product_code: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  sku: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Stock Keeping Unit'
  },
  barcode: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  qr_code: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  brand_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'brands',
      key: 'id'
    }
  },
  category_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  unit_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'units',
      key: 'id'
    }
  },
  product_type: {
    type: DataTypes.ENUM('product', 'service', 'digital', 'subscription'),
    defaultValue: 'product'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'discontinued', 'out_of_stock'),
    defaultValue: 'active'
  },
  cost_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  selling_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  mrp: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Maximum Retail Price'
  },
  wholesale_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  minimum_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Minimum selling price allowed'
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'INR'
  },
  tax_rate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    comment: 'Default tax rate percentage'
  },
  tax_inclusive: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether price includes tax'
  },
  hsn_code: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: 'HSN/SAC code for tax purposes'
  },
  track_inventory: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  current_stock: {
    type: DataTypes.DECIMAL(10, 3),
    defaultValue: 0.000
  },
  minimum_stock: {
    type: DataTypes.DECIMAL(10, 3),
    defaultValue: 0.000,
    comment: 'Minimum stock level for alerts'
  },
  maximum_stock: {
    type: DataTypes.DECIMAL(10, 3),
    allowNull: true,
    comment: 'Maximum stock level'
  },
  reorder_level: {
    type: DataTypes.DECIMAL(10, 3),
    allowNull: true,
    comment: 'Stock level at which to reorder'
  },
  reorder_quantity: {
    type: DataTypes.DECIMAL(10, 3),
    allowNull: true,
    comment: 'Quantity to order when restocking'
  },
  lead_time_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Lead time for procurement in days'
  },
  weight: {
    type: DataTypes.DECIMAL(8, 3),
    allowNull: true
  },
  weight_unit: {
    type: DataTypes.ENUM('kg', 'g', 'lb', 'oz'),
    defaultValue: 'kg'
  },
  dimensions_length: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true
  },
  dimensions_width: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true
  },
  dimensions_height: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true
  },
  dimension_unit: {
    type: DataTypes.ENUM('cm', 'm', 'in', 'ft'),
    defaultValue: 'cm'
  },
  color: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  size: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  model: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  variant: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  material: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  warranty_period: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Warranty period in months'
  },
  warranty_terms: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  return_policy: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  care_instructions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  features: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of product features'
  },
  specifications: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of technical specifications'
  },
  tags: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Comma-separated tags for search'
  },
  meta_title: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'SEO meta title'
  },
  meta_description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'SEO meta description'
  },
  meta_keywords: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'SEO meta keywords'
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of image file paths'
  },
  primary_image: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Primary product image path'
  },
  documents: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of document file paths'
  },
  videos: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of video file paths or URLs'
  },
  is_featured: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_digital: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_downloadable: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  download_limit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum download attempts for digital products'
  },
  download_expiry_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Download link expiry in days'
  },
  is_subscription: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  subscription_period: {
    type: DataTypes.ENUM('daily', 'weekly', 'monthly', 'quarterly', 'yearly'),
    allowNull: true
  },
  subscription_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  trial_period_days: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  view_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of times product was viewed'
  },
  purchase_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of times product was purchased'
  },
  rating_average: {
    type: DataTypes.DECIMAL(3, 2),
    defaultValue: 0.00,
    comment: 'Average customer rating'
  },
  rating_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of ratings received'
  },
  custom_fields: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object for custom field values'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'products',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['brand_id']
    },
    {
      fields: ['category_id']
    },
    {
      fields: ['unit_id']
    },
    {
      fields: ['product_code']
    },
    {
      fields: ['sku']
    },
    {
      fields: ['barcode']
    },
    {
      fields: ['status']
    },
    {
      fields: ['product_type']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_featured']
    },
    {
      fields: ['track_inventory']
    },
    {
      fields: ['current_stock']
    },
    {
      fields: ['company_id', 'product_code'],
      unique: true
    },
    {
      fields: ['company_id', 'sku'],
      unique: true
    }
  ]
});

module.exports = Product;
