import React from 'react';
import { Link } from 'react-router-dom';
import {
  UserPlusIcon,
  WrenchScrewdriverIcon,
  ShoppingCartIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { classNames, formatRelativeTime, formatCurrency } from '../../utils/helpers';
import { LoadingSpinner } from '../ui';

const RecentActivities = ({ activities = [], loading = false }) => {
  const getActivityIcon = (type) => {
    const iconClasses = "h-5 w-5";
    
    switch (type) {
      case 'customer_created':
        return <UserPlusIcon className={classNames(iconClasses, "text-blue-500")} />;
      case 'service_created':
      case 'service_updated':
        return <WrenchScrewdriverIcon className={classNames(iconClasses, "text-purple-500")} />;
      case 'sale_created':
        return <ShoppingCartIcon className={classNames(iconClasses, "text-green-500")} />;
      case 'payment_received':
        return <CurrencyDollarIcon className={classNames(iconClasses, "text-green-500")} />;
      case 'invoice_generated':
        return <DocumentTextIcon className={classNames(iconClasses, "text-blue-500")} />;
      case 'service_completed':
        return <CheckCircleIcon className={classNames(iconClasses, "text-green-500")} />;
      case 'service_overdue':
        return <ExclamationTriangleIcon className={classNames(iconClasses, "text-red-500")} />;
      default:
        return <ClockIcon className={classNames(iconClasses, "text-gray-500")} />;
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'customer_created':
      case 'invoice_generated':
        return 'bg-blue-100 dark:bg-blue-900/20';
      case 'service_created':
      case 'service_updated':
        return 'bg-purple-100 dark:bg-purple-900/20';
      case 'sale_created':
      case 'payment_received':
      case 'service_completed':
        return 'bg-green-100 dark:bg-green-900/20';
      case 'service_overdue':
        return 'bg-red-100 dark:bg-red-900/20';
      default:
        return 'bg-gray-100 dark:bg-gray-700';
    }
  };

  const formatActivityMessage = (activity) => {
    const { type, data } = activity;
    
    switch (type) {
      case 'customer_created':
        return `New customer "${data.customer_name}" was added`;
      case 'service_created':
        return `New service request created for ${data.customer_name}`;
      case 'service_updated':
        return `Service #${data.service_id} status updated to ${data.status}`;
      case 'service_completed':
        return `Service #${data.service_id} completed for ${data.customer_name}`;
      case 'service_overdue':
        return `Service #${data.service_id} is overdue`;
      case 'sale_created':
        return `New sale of ${formatCurrency(data.amount)} created`;
      case 'payment_received':
        return `Payment of ${formatCurrency(data.amount)} received from ${data.customer_name}`;
      case 'invoice_generated':
        return `Invoice #${data.invoice_number} generated for ${data.customer_name}`;
      default:
        return activity.message || 'Activity occurred';
    }
  };

  const getActivityLink = (activity) => {
    const { type, data } = activity;
    
    switch (type) {
      case 'customer_created':
        return `/customers/${data.customer_id}`;
      case 'service_created':
      case 'service_updated':
      case 'service_completed':
      case 'service_overdue':
        return `/services/${data.service_id}`;
      case 'sale_created':
        return `/sales/${data.sale_id}`;
      case 'invoice_generated':
        return `/invoices/${data.invoice_id}`;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <LoadingSpinner />
      </div>
    );
  }

  if (!activities || activities.length === 0) {
    return (
      <div className="text-center py-8">
        <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          No recent activities
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Activities will appear here as they happen.
        </p>
      </div>
    );
  }

  return (
    <div className="flow-root">
      <ul className="-mb-8">
        {activities.map((activity, activityIdx) => {
          const link = getActivityLink(activity);
          const ActivityWrapper = link ? Link : 'div';
          const wrapperProps = link ? { to: link } : {};

          return (
            <li key={activity.id || activityIdx}>
              <div className="relative pb-8">
                {activityIdx !== activities.length - 1 ? (
                  <span
                    className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-600"
                    aria-hidden="true"
                  />
                ) : null}
                
                <div className="relative flex space-x-3">
                  {/* Activity Icon */}
                  <div className={classNames(
                    'relative px-1',
                    'flex h-8 w-8 items-center justify-center rounded-full ring-8 ring-white dark:ring-gray-800',
                    getActivityColor(activity.type)
                  )}>
                    {getActivityIcon(activity.type)}
                  </div>
                  
                  {/* Activity Content */}
                  <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                    <div className="min-w-0 flex-1">
                      <ActivityWrapper
                        {...wrapperProps}
                        className={classNames(
                          'text-sm text-gray-900 dark:text-white',
                          link && 'hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer'
                        )}
                      >
                        {formatActivityMessage(activity)}
                      </ActivityWrapper>
                      
                      {/* Additional Info */}
                      {activity.data?.description && (
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                          {activity.data.description}
                        </p>
                      )}
                    </div>
                    
                    {/* Timestamp */}
                    <div className="whitespace-nowrap text-right text-xs text-gray-500 dark:text-gray-400">
                      <time dateTime={activity.created_at}>
                        {formatRelativeTime(activity.created_at)}
                      </time>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          );
        })}
      </ul>
      
      {/* View All Link */}
      {activities.length > 0 && (
        <div className="mt-6 text-center">
          <Link
            to="/activities"
            className="text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
          >
            View all activities →
          </Link>
        </div>
      )}
    </div>
  );
};

export default RecentActivities;
