const { Employee, User, Company } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all employees with filtering and pagination
const getEmployees = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    department,
    designation,
    employment_type,
    employment_status,
    reporting_manager_id,
    is_active = true,
    can_login,
    access_level,
    sort_by = 'full_name',
    sort_order = 'ASC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { full_name: { [Op.iLike]: `%${search}%` } },
      { first_name: { [Op.iLike]: `%${search}%` } },
      { last_name: { [Op.iLike]: `%${search}%` } },
      { employee_id: { [Op.iLike]: `%${search}%` } },
      { email: { [Op.iLike]: `%${search}%` } },
      { phone: { [Op.iLike]: `%${search}%` } },
      { mobile: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add filters
  if (department) {
    whereConditions.department = { [Op.iLike]: `%${department}%` };
  }

  if (designation) {
    whereConditions.designation = { [Op.iLike]: `%${designation}%` };
  }

  if (employment_type) {
    whereConditions.employment_type = employment_type;
  }

  if (employment_status) {
    whereConditions.employment_status = employment_status;
  }

  if (reporting_manager_id) {
    whereConditions.reporting_manager_id = reporting_manager_id;
  }

  if (is_active !== undefined) {
    whereConditions.is_active = is_active === 'true';
  }

  if (can_login !== undefined) {
    whereConditions.can_login = can_login === 'true';
  }

  if (access_level) {
    whereConditions.access_level = access_level;
  }

  // Get employees with associations
  const { count, rows: employees } = await Employee.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'is_active', 'last_login']
      },
      {
        model: Employee,
        as: 'reportingManager',
        attributes: ['id', 'full_name', 'employee_id', 'designation']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ],
    limit: parseInt(limit),
    offset: offset,
    order: [[sort_by, sort_order.toUpperCase()]],
    distinct: true
  });

  // Calculate pagination
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      employees,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single employee by ID
const getEmployee = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const employee = await Employee.findOne({
    where: {
      id,
      company_id: companyId
    },
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'is_active', 'last_login', 'created_at']
      },
      {
        model: Employee,
        as: 'reportingManager',
        attributes: ['id', 'full_name', 'employee_id', 'designation', 'email', 'phone']
      },
      {
        model: Employee,
        as: 'subordinates',
        attributes: ['id', 'full_name', 'employee_id', 'designation', 'employment_status']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!employee) {
    return next(new AppError('Employee not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      employee
    }
  });
});

// Create new employee
const createEmployee = catchAsync(async (req, res, next) => {
  const {
    employee_id,
    first_name,
    last_name,
    email,
    phone,
    mobile,
    date_of_birth,
    gender,
    marital_status,
    address,
    city,
    state,
    postal_code,
    country,
    department,
    designation,
    job_title,
    employment_type = 'full_time',
    employment_status = 'active',
    hire_date,
    probation_end_date,
    confirmation_date,
    reporting_manager_id,
    salary,
    salary_currency = 'INR',
    salary_frequency = 'monthly',
    bank_name,
    bank_account_number,
    bank_ifsc_code,
    pan_number,
    aadhar_number,
    passport_number,
    driving_license,
    skills = [],
    certifications = [],
    education = [],
    experience = [],
    languages = [],
    work_location,
    work_shift = 'day',
    work_hours_per_week = 40,
    overtime_eligible = true,
    remote_work_allowed = false,
    travel_required = false,
    leave_balance = {},
    benefits = [],
    access_level = 'basic',
    can_login = false,
    login_allowed_from,
    login_allowed_until,
    notes,
    internal_notes,
    tags,
    custom_fields = {},
    is_active = true
  } = req.body;

  const companyId = req.user.company_id;

  // Generate full name
  const full_name = `${first_name} ${last_name}`.trim();

  // Check for duplicate employee ID within company
  const existingEmployee = await Employee.findOne({
    where: {
      company_id: companyId,
      employee_id: employee_id
    }
  });

  if (existingEmployee) {
    return next(new AppError('Employee with this ID already exists', 400));
  }

  // Check for duplicate email if provided
  if (email) {
    const existingEmail = await Employee.findOne({
      where: {
        company_id: companyId,
        email: email
      }
    });

    if (existingEmail) {
      return next(new AppError('Employee with this email already exists', 400));
    }
  }

  // Validate reporting manager if provided
  if (reporting_manager_id) {
    const manager = await Employee.findOne({
      where: {
        id: reporting_manager_id,
        company_id: companyId,
        is_active: true
      }
    });

    if (!manager) {
      return next(new AppError('Reporting manager not found or inactive', 400));
    }
  }

  const employee = await Employee.create({
    company_id: companyId,
    employee_id,
    first_name,
    last_name,
    full_name,
    email,
    phone,
    mobile,
    date_of_birth: date_of_birth ? new Date(date_of_birth) : null,
    gender,
    marital_status,
    address,
    city,
    state,
    postal_code,
    country,
    department,
    designation,
    job_title,
    employment_type,
    employment_status,
    hire_date: hire_date ? new Date(hire_date) : null,
    probation_end_date: probation_end_date ? new Date(probation_end_date) : null,
    confirmation_date: confirmation_date ? new Date(confirmation_date) : null,
    reporting_manager_id,
    salary,
    salary_currency,
    salary_frequency,
    bank_name,
    bank_account_number,
    bank_ifsc_code,
    pan_number,
    aadhar_number,
    passport_number,
    driving_license,
    skills,
    certifications,
    education,
    experience,
    languages,
    work_location,
    work_shift,
    work_hours_per_week,
    overtime_eligible,
    remote_work_allowed,
    travel_required,
    leave_balance,
    benefits,
    access_level,
    can_login,
    login_allowed_from: login_allowed_from ? new Date(login_allowed_from) : null,
    login_allowed_until: login_allowed_until ? new Date(login_allowed_until) : null,
    notes,
    internal_notes,
    tags,
    custom_fields,
    is_active,
    created_by: req.user.id
  });

  // Fetch the created employee with associations
  const createdEmployee = await Employee.findByPk(employee.id, {
    include: [
      {
        model: Employee,
        as: 'reportingManager',
        attributes: ['id', 'full_name', 'employee_id', 'designation']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      employee: createdEmployee
    }
  });
});

// Update employee
const updateEmployee = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const employee = await Employee.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!employee) {
    return next(new AppError('Employee not found', 404));
  }

  // Check for duplicate employee ID (excluding current employee)
  if (req.body.employee_id) {
    const existingEmployee = await Employee.findOne({
      where: {
        company_id: companyId,
        id: { [Op.ne]: id },
        employee_id: req.body.employee_id
      }
    });

    if (existingEmployee) {
      return next(new AppError('Employee with this ID already exists', 400));
    }
  }

  // Check for duplicate email (excluding current employee)
  if (req.body.email) {
    const existingEmail = await Employee.findOne({
      where: {
        company_id: companyId,
        id: { [Op.ne]: id },
        email: req.body.email
      }
    });

    if (existingEmail) {
      return next(new AppError('Employee with this email already exists', 400));
    }
  }

  // Validate reporting manager if provided
  if (req.body.reporting_manager_id) {
    // Cannot report to self
    if (req.body.reporting_manager_id === id) {
      return next(new AppError('Employee cannot report to themselves', 400));
    }

    const manager = await Employee.findOne({
      where: {
        id: req.body.reporting_manager_id,
        company_id: companyId,
        is_active: true
      }
    });

    if (!manager) {
      return next(new AppError('Reporting manager not found or inactive', 400));
    }
  }

  // Update full name if first_name or last_name changed
  if (req.body.first_name || req.body.last_name) {
    const firstName = req.body.first_name || employee.first_name;
    const lastName = req.body.last_name || employee.last_name;
    req.body.full_name = `${firstName} ${lastName}`.trim();
  }

  // Handle date fields
  const dateFields = ['date_of_birth', 'hire_date', 'probation_end_date', 'confirmation_date', 'termination_date', 'login_allowed_from', 'login_allowed_until'];
  dateFields.forEach(field => {
    if (req.body[field]) {
      req.body[field] = new Date(req.body[field]);
    }
  });

  // Update employee
  await employee.update({
    ...req.body,
    updated_by: req.user.id
  });

  // Fetch updated employee with associations
  const updatedEmployee = await Employee.findByPk(id, {
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'is_active']
      },
      {
        model: Employee,
        as: 'reportingManager',
        attributes: ['id', 'full_name', 'employee_id', 'designation']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      employee: updatedEmployee
    }
  });
});

// Delete employee
const deleteEmployee = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const employee = await Employee.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!employee) {
    return next(new AppError('Employee not found', 404));
  }

  // Check if employee has subordinates
  const subordinateCount = await Employee.count({
    where: {
      reporting_manager_id: id,
      company_id: companyId,
      is_active: true
    }
  });

  if (subordinateCount > 0) {
    return next(new AppError(`Cannot delete employee. They have ${subordinateCount} subordinate(s). Please reassign them first.`, 400));
  }

  await employee.destroy();

  res.status(200).json({
    status: 'success',
    message: 'Employee deleted successfully'
  });
});

// Get employee statistics
const getEmployeeStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  // Total employees count
  const totalEmployees = await Employee.count({
    where: { company_id: companyId }
  });

  // Active employees count
  const activeEmployees = await Employee.count({
    where: {
      company_id: companyId,
      is_active: true
    }
  });

  // Employees by employment status
  const employeesByStatus = await Employee.findAll({
    where: { company_id: companyId },
    attributes: [
      'employment_status',
      [Employee.sequelize.fn('COUNT', Employee.sequelize.col('id')), 'count']
    ],
    group: ['employment_status'],
    raw: true
  });

  // Employees by employment type
  const employeesByType = await Employee.findAll({
    where: { company_id: companyId },
    attributes: [
      'employment_type',
      [Employee.sequelize.fn('COUNT', Employee.sequelize.col('id')), 'count']
    ],
    group: ['employment_type'],
    raw: true
  });

  // Employees by department
  const employeesByDepartment = await Employee.findAll({
    where: {
      company_id: companyId,
      department: { [Op.ne]: null }
    },
    attributes: [
      'department',
      [Employee.sequelize.fn('COUNT', Employee.sequelize.col('id')), 'count']
    ],
    group: ['department'],
    order: [[Employee.sequelize.fn('COUNT', Employee.sequelize.col('id')), 'DESC']],
    limit: 10,
    raw: true
  });

  // Employees with system access
  const employeesWithAccess = await Employee.count({
    where: {
      company_id: companyId,
      can_login: true,
      is_active: true
    }
  });

  // Recent hires (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const recentHires = await Employee.count({
    where: {
      company_id: companyId,
      hire_date: { [Op.gte]: thirtyDaysAgo }
    }
  });

  // Employees on probation
  const employeesOnProbation = await Employee.count({
    where: {
      company_id: companyId,
      probation_end_date: { [Op.gte]: new Date() },
      employment_status: 'active'
    }
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_employees: totalEmployees,
      active_employees: activeEmployees,
      inactive_employees: totalEmployees - activeEmployees,
      employees_with_access: employeesWithAccess,
      recent_hires: recentHires,
      employees_on_probation: employeesOnProbation,
      employees_by_status: employeesByStatus,
      employees_by_type: employeesByType,
      employees_by_department: employeesByDepartment
    }
  });
});

// Get employees by department
const getEmployeesByDepartment = catchAsync(async (req, res) => {
  const { department } = req.params;
  const companyId = req.user.company_id;

  const employees = await Employee.findAll({
    where: {
      company_id: companyId,
      department: { [Op.iLike]: `%${department}%` },
      is_active: true
    },
    attributes: ['id', 'full_name', 'employee_id', 'designation', 'email', 'phone', 'employment_status'],
    order: [['full_name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      employees,
      department
    }
  });
});

// Get employees by manager
const getEmployeesByManager = catchAsync(async (req, res) => {
  const { manager_id } = req.params;
  const companyId = req.user.company_id;

  const employees = await Employee.findAll({
    where: {
      company_id: companyId,
      reporting_manager_id: manager_id,
      is_active: true
    },
    attributes: ['id', 'full_name', 'employee_id', 'designation', 'email', 'phone', 'employment_status', 'hire_date'],
    order: [['full_name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      employees,
      manager_id
    }
  });
});

// Get active employees (for dropdowns)
const getActiveEmployees = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const employees = await Employee.findAll({
    where: {
      company_id: companyId,
      is_active: true,
      employment_status: 'active'
    },
    attributes: ['id', 'full_name', 'employee_id', 'designation', 'department'],
    order: [['full_name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      employees
    }
  });
});

// Update employee status
const updateEmployeeStatus = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { employment_status, termination_date, termination_reason } = req.body;
  const companyId = req.user.company_id;

  const employee = await Employee.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!employee) {
    return next(new AppError('Employee not found', 404));
  }

  const updateData = {
    employment_status,
    updated_by: req.user.id
  };

  // Handle termination
  if (employment_status === 'terminated' || employment_status === 'resigned') {
    updateData.termination_date = termination_date ? new Date(termination_date) : new Date();
    updateData.termination_reason = termination_reason;
    updateData.is_active = false;
    updateData.can_login = false;
  }

  await employee.update(updateData);

  res.status(200).json({
    status: 'success',
    message: 'Employee status updated successfully',
    data: {
      employee: {
        id: employee.id,
        full_name: employee.full_name,
        employment_status: employee.employment_status,
        is_active: employee.is_active
      }
    }
  });
});

module.exports = {
  getEmployees,
  getEmployee,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  getEmployeeStats,
  getEmployeesByDepartment,
  getEmployeesByManager,
  getActiveEmployees,
  updateEmployeeStatus
};
