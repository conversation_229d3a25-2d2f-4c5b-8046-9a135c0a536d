const express = require('express');
const { body } = require('express-validator');
const unitController = require('../controllers/unitController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating unit
const createUnitValidation = [
  body('name')
    .notEmpty()
    .withMessage('Unit name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Unit name must be between 2 and 100 characters'),
  
  body('short_name')
    .notEmpty()
    .withMessage('Short name is required')
    .isLength({ min: 1, max: 20 })
    .withMessage('Short name must be between 1 and 20 characters'),
  
  body('unit_type')
    .optional()
    .isIn(['weight', 'length', 'area', 'volume', 'quantity', 'time', 'temperature', 'other'])
    .withMessage('Invalid unit type'),
  
  body('base_unit_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Base unit ID must be a positive integer'),
  
  body('conversion_factor')
    .optional()
    .isFloat({ min: 0.000001 })
    .withMessage('Conversion factor must be a positive number'),
  
  body('is_base_unit')
    .optional()
    .isBoolean()
    .withMessage('is_base_unit must be a boolean'),
  
  body('decimal_places')
    .optional()
    .isInt({ min: 0, max: 10 })
    .withMessage('Decimal places must be between 0 and 10'),
  
  body('symbol')
    .optional()
    .isLength({ max: 10 })
    .withMessage('Symbol must be maximum 10 characters'),
  
  body('prefix')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Prefix must be maximum 20 characters'),
  
  body('suffix')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Suffix must be maximum 20 characters'),
  
  body('format_pattern')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Format pattern must be maximum 50 characters'),
  
  body('is_fractional')
    .optional()
    .isBoolean()
    .withMessage('is_fractional must be a boolean'),
  
  body('minimum_value')
    .optional()
    .isFloat()
    .withMessage('Minimum value must be a number'),
  
  body('maximum_value')
    .optional()
    .isFloat()
    .withMessage('Maximum value must be a number'),
  
  body('step_value')
    .optional()
    .isFloat({ min: 0.000001 })
    .withMessage('Step value must be a positive number'),
  
  body('rounding_method')
    .optional()
    .isIn(['round', 'floor', 'ceil'])
    .withMessage('Invalid rounding method'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must be maximum 1000 characters')
];

// Validation rules for updating unit
const updateUnitValidation = [
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Unit name must be between 2 and 100 characters'),
  
  body('short_name')
    .optional()
    .isLength({ min: 1, max: 20 })
    .withMessage('Short name must be between 1 and 20 characters'),
  
  body('unit_type')
    .optional()
    .isIn(['weight', 'length', 'area', 'volume', 'quantity', 'time', 'temperature', 'other'])
    .withMessage('Invalid unit type'),
  
  body('base_unit_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Base unit ID must be a positive integer'),
  
  body('conversion_factor')
    .optional()
    .isFloat({ min: 0.000001 })
    .withMessage('Conversion factor must be a positive number'),
  
  body('is_base_unit')
    .optional()
    .isBoolean()
    .withMessage('is_base_unit must be a boolean'),
  
  body('decimal_places')
    .optional()
    .isInt({ min: 0, max: 10 })
    .withMessage('Decimal places must be between 0 and 10'),
  
  body('symbol')
    .optional()
    .isLength({ max: 10 })
    .withMessage('Symbol must be maximum 10 characters'),
  
  body('prefix')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Prefix must be maximum 20 characters'),
  
  body('suffix')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Suffix must be maximum 20 characters'),
  
  body('format_pattern')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Format pattern must be maximum 50 characters'),
  
  body('is_fractional')
    .optional()
    .isBoolean()
    .withMessage('is_fractional must be a boolean'),
  
  body('minimum_value')
    .optional()
    .isFloat()
    .withMessage('Minimum value must be a number'),
  
  body('maximum_value')
    .optional()
    .isFloat()
    .withMessage('Maximum value must be a number'),
  
  body('step_value')
    .optional()
    .isFloat({ min: 0.000001 })
    .withMessage('Step value must be a positive number'),
  
  body('rounding_method')
    .optional()
    .isIn(['round', 'floor', 'ceil'])
    .withMessage('Invalid rounding method'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must be maximum 1000 characters')
];

// Routes
router
  .route('/')
  .get(unitController.getUnits)
  .post(createUnitValidation, validateRequest, unitController.createUnit);

router
  .route('/stats')
  .get(unitController.getUnitStats);

router
  .route('/base-units')
  .get(unitController.getBaseUnits);

router
  .route('/by-type/:unit_type')
  .get(unitController.getUnitsByType);

router
  .route('/:id')
  .get(unitController.getUnit)
  .put(updateUnitValidation, validateRequest, unitController.updateUnit)
  .delete(restrictTo('admin', 'sub_admin'), unitController.deleteUnit);

module.exports = router;
