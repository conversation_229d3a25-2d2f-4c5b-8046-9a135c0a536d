import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useLocation } from 'react-router-dom';
import { 
  HomeIcon,
  UsersIcon,
  CogIcon,
  ShoppingCartIcon,
  DocumentTextIcon,
  ChartBarIcon,
  BellIcon,
  UserGroupIcon,
  CubeIcon,
  ClipboardDocumentListIcon,
  BanknotesIcon,
  TruckIcon,
  WrenchScrewdriverIcon,
  BuildingOfficeIcon,
  DocumentDuplicateIcon,
  CalendarDaysIcon,
  ExclamationTriangleIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { Disclosure } from '@headlessui/react';

// Redux imports
import { 
  selectSidebarOpen, 
  selectSidebarCollapsed,
  setSidebarOpen 
} from '../../store/slices/uiSlice';

const Sidebar = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  
  // Selectors
  const sidebarOpen = useSelector(selectSidebarOpen);
  const sidebarCollapsed = useSelector(selectSidebarCollapsed);

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: HomeIcon, current: false },
    {
      name: 'Customers',
      icon: UsersIcon,
      current: false,
      children: [
        { name: 'All Customers', href: '/customers' },
        { name: 'Add Customer', href: '/customers/new' },
        { name: 'Customer Categories', href: '/customer-categories' },
      ],
    },
    {
      name: 'Leads',
      icon: UserGroupIcon,
      current: false,
      children: [
        { name: 'All Leads', href: '/leads' },
        { name: 'Add Lead', href: '/leads/new' },
        { name: 'Lead Pipeline', href: '/leads/pipeline' },
      ],
    },
    {
      name: 'Services',
      icon: WrenchScrewdriverIcon,
      current: false,
      children: [
        { name: 'All Services', href: '/services' },
        { name: 'New Service', href: '/services/new' },
        { name: 'Service Categories', href: '/service-categories' },
      ],
    },
    {
      name: 'Products',
      icon: CubeIcon,
      current: false,
      children: [
        { name: 'All Products', href: '/products' },
        { name: 'Add Product', href: '/products/new' },
        { name: 'Categories', href: '/categories' },
        { name: 'Brands', href: '/brands' },
        { name: 'Units', href: '/units' },
        { name: 'Low Stock', href: '/products/low-stock' },
      ],
    },
    {
      name: 'Sales',
      icon: ShoppingCartIcon,
      current: false,
      children: [
        { name: 'All Sales', href: '/sales' },
        { name: 'New Sale', href: '/sales/new' },
        { name: 'Estimations', href: '/estimations' },
        { name: 'Invoices', href: '/invoices' },
      ],
    },
    {
      name: 'Inventory',
      icon: TruckIcon,
      current: false,
      children: [
        { name: 'Stock Movement', href: '/stock-movement' },
        { name: 'Warehouses', href: '/warehouses' },
        { name: 'Purchase Orders', href: '/purchase-orders' },
        { name: 'Suppliers', href: '/suppliers' },
      ],
    },
    {
      name: 'AMC',
      icon: CalendarDaysIcon,
      current: false,
      children: [
        { name: 'All AMCs', href: '/amcs' },
        { name: 'New AMC', href: '/amcs/new' },
        { name: 'Expiring Soon', href: '/amcs/expiring' },
      ],
    },
    {
      name: 'Payments',
      icon: BanknotesIcon,
      current: false,
      children: [
        { name: 'Payment In', href: '/payments/in' },
        { name: 'Payment Out', href: '/payments/out' },
        { name: 'Expenses', href: '/expenses' },
      ],
    },
    {
      name: 'Reports',
      icon: ChartBarIcon,
      current: false,
      children: [
        { name: 'Sales Reports', href: '/reports/sales' },
        { name: 'Service Reports', href: '/reports/services' },
        { name: 'Financial Reports', href: '/reports/financial' },
        { name: 'Inventory Reports', href: '/reports/inventory' },
      ],
    },
    {
      name: 'Management',
      icon: BuildingOfficeIcon,
      current: false,
      children: [
        { name: 'Employees', href: '/employees' },
        { name: 'Roles', href: '/roles' },
        { name: 'Taxes', href: '/taxes' },
        { name: 'RMA', href: '/rma' },
      ],
    },
    { name: 'Notifications', href: '/notifications', icon: BellIcon, current: false },
    { name: 'Documents', href: '/documents', icon: DocumentDuplicateIcon, current: false },
    { name: 'Settings', href: '/settings', icon: CogIcon, current: false },
  ];

  const isCurrentPath = (href) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  const hasCurrentChild = (children) => {
    return children?.some(child => isCurrentPath(child.href));
  };

  const closeSidebar = () => {
    if (window.innerWidth < 1024) {
      dispatch(setSidebarOpen(false));
    }
  };

  return (
    <>
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => dispatch(setSidebarOpen(false))}
        >
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" />
        </div>
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        ${sidebarCollapsed ? 'lg:w-16' : 'lg:w-64'}
      `}>
        <div className="flex flex-col h-full">
          {/* Sidebar content */}
          <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
            <nav className="mt-5 flex-1 px-2 space-y-1">
              {navigation.map((item) => (
                <div key={item.name}>
                  {!item.children ? (
                    <Link
                      to={item.href}
                      onClick={closeSidebar}
                      className={`
                        group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150
                        ${isCurrentPath(item.href)
                          ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white'
                        }
                      `}
                    >
                      <item.icon
                        className={`
                          mr-3 flex-shrink-0 h-6 w-6
                          ${isCurrentPath(item.href)
                            ? 'text-blue-500 dark:text-blue-400'
                            : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
                          }
                        `}
                      />
                      {!sidebarCollapsed && item.name}
                    </Link>
                  ) : (
                    <Disclosure as="div" defaultOpen={hasCurrentChild(item.children)}>
                      {({ open }) => (
                        <>
                          <Disclosure.Button
                            className={`
                              group w-full flex items-center px-2 py-2 text-left text-sm font-medium rounded-md transition-colors duration-150
                              ${hasCurrentChild(item.children)
                                ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white'
                              }
                            `}
                          >
                            <item.icon
                              className={`
                                mr-3 flex-shrink-0 h-6 w-6
                                ${hasCurrentChild(item.children)
                                  ? 'text-blue-500 dark:text-blue-400'
                                  : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
                                }
                              `}
                            />
                            {!sidebarCollapsed && (
                              <>
                                <span className="flex-1">{item.name}</span>
                                {open ? (
                                  <ChevronDownIcon className="ml-3 h-5 w-5 flex-shrink-0" />
                                ) : (
                                  <ChevronRightIcon className="ml-3 h-5 w-5 flex-shrink-0" />
                                )}
                              </>
                            )}
                          </Disclosure.Button>
                          {!sidebarCollapsed && (
                            <Disclosure.Panel className="space-y-1">
                              {item.children.map((subItem) => (
                                <Link
                                  key={subItem.name}
                                  to={subItem.href}
                                  onClick={closeSidebar}
                                  className={`
                                    group flex items-center pl-11 pr-2 py-2 text-sm font-medium rounded-md transition-colors duration-150
                                    ${isCurrentPath(subItem.href)
                                      ? 'bg-blue-50 text-blue-700 dark:bg-blue-800 dark:text-blue-200'
                                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white'
                                    }
                                  `}
                                >
                                  {subItem.name}
                                </Link>
                              ))}
                            </Disclosure.Panel>
                          )}
                        </>
                      )}
                    </Disclosure>
                  )}
                </div>
              ))}
            </nav>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
