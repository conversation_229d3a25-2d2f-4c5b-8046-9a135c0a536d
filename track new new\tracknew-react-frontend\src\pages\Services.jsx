import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  PlusIcon,
  WrenchScrewdriverIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  UserIcon,
  CalendarDaysIcon
} from '@heroicons/react/24/outline';

// Redux
import {
  fetchServices,
  deleteService,
  updateServiceStatus,
  selectServices,
  selectServicePagination,
  selectServiceLoading,
  selectServiceError,
  selectSelectedServices,
  selectServiceFilters,
  selectServiceSearchQuery,
  setSearchQuery,
  setFilter,
  clearFilters,
  selectService,
  deselectService,
  selectAllServices,
  deselectAllServices
} from '../store/slices/serviceSlice';

// Components
import { Button, DataTable, Pagination, ConfirmModal } from '../components/ui';
import { Input, Select } from '../components/forms';
import { formatDate, formatCurrency, getStatusColor, classNames } from '../utils/helpers';

const Services = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  // Local state
  const [showFilters, setShowFilters] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState(null);
  
  // Selectors
  const services = useSelector(selectServices);
  const pagination = useSelector(selectServicePagination);
  const loading = useSelector(selectServiceLoading);
  const error = useSelector(selectServiceError);
  const selectedServices = useSelector(selectSelectedServices);
  const filters = useSelector(selectServiceFilters);
  const searchQuery = useSelector(selectServiceSearchQuery);

  // Fetch services on mount and when filters change
  useEffect(() => {
    dispatch(fetchServices({
      page: pagination.current_page,
      limit: pagination.items_per_page,
      search: searchQuery,
      ...filters
    }));
  }, [dispatch, pagination.current_page, pagination.items_per_page, searchQuery, filters]);

  // Service status options
  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'pending', label: 'Pending' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'on_hold', label: 'On Hold' }
  ];

  const priorityOptions = [
    { value: '', label: 'All Priorities' },
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'urgent', label: 'Urgent' }
  ];

  // Get status icon
  const getStatusIcon = (status) => {
    const iconClasses = "h-4 w-4";
    switch (status) {
      case 'pending':
        return <ClockIcon className={classNames(iconClasses, "text-yellow-500")} />;
      case 'in_progress':
        return <WrenchScrewdriverIcon className={classNames(iconClasses, "text-blue-500")} />;
      case 'completed':
        return <CheckCircleIcon className={classNames(iconClasses, "text-green-500")} />;
      case 'cancelled':
        return <XCircleIcon className={classNames(iconClasses, "text-red-500")} />;
      case 'on_hold':
        return <ExclamationTriangleIcon className={classNames(iconClasses, "text-orange-500")} />;
      default:
        return <ClockIcon className={classNames(iconClasses, "text-gray-500")} />;
    }
  };

  // Table columns
  const columns = [
    {
      key: 'service_number',
      title: 'Service #',
      sortable: true,
      render: (value, service) => (
        <div>
          <div className="text-sm font-medium text-gray-900 dark:text-white">
            #{service.service_number || service.id}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {service.service_type || 'General Service'}
          </div>
        </div>
      )
    },
    {
      key: 'customer',
      title: 'Customer',
      render: (value, service) => (
        <div className="flex items-center space-x-2">
          <UserIcon className="h-4 w-4 text-gray-400" />
          <div>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {service.customer?.name || 'Unknown Customer'}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {service.customer?.phone || '-'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'title',
      title: 'Service Details',
      render: (value, service) => (
        <div>
          <div className="text-sm font-medium text-gray-900 dark:text-white">
            {service.title || service.description?.substring(0, 50) + '...' || 'Service Request'}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {service.product_name && `Product: ${service.product_name}`}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (value, service) => (
        <div className="flex items-center space-x-2">
          {getStatusIcon(value)}
          <span className={classNames(
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            getStatusColor(value)
          )}>
            {value?.replace('_', ' ') || 'pending'}
          </span>
        </div>
      )
    },
    {
      key: 'priority',
      title: 'Priority',
      sortable: true,
      render: (value) => (
        <span className={classNames(
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          value === 'urgent' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
          value === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
          value === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
          'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
        )}>
          {value || 'medium'}
        </span>
      )
    },
    {
      key: 'scheduled_date',
      title: 'Scheduled',
      sortable: true,
      render: (value, service) => (
        <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
          <CalendarDaysIcon className="h-4 w-4" />
          <span>{value ? formatDate(value) : 'Not scheduled'}</span>
        </div>
      )
    },
    {
      key: 'estimated_cost',
      title: 'Cost',
      sortable: true,
      render: (value) => (
        <span className="text-sm font-medium text-gray-900 dark:text-white">
          {value ? formatCurrency(value) : '-'}
        </span>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: 'View',
      icon: EyeIcon,
      onClick: (service) => navigate(`/services/${service.id}`)
    },
    {
      label: 'Edit',
      icon: PencilIcon,
      onClick: (service) => navigate(`/services/${service.id}/edit`)
    },
    {
      label: 'Complete',
      icon: CheckCircleIcon,
      onClick: (service) => {
        dispatch(updateServiceStatus({ id: service.id, status: 'completed' }));
      },
      disabled: (service) => service.status === 'completed' || service.status === 'cancelled'
    },
    {
      label: 'Delete',
      icon: TrashIcon,
      onClick: (service) => {
        setServiceToDelete(service);
        setDeleteModalOpen(true);
      },
      disabled: (service) => service.status === 'in_progress'
    }
  ];

  // Handlers
  const handleSearch = (query) => {
    dispatch(setSearchQuery(query));
  };

  const handleFilterChange = (key, value) => {
    dispatch(setFilter({ key, value }));
  };

  const handleClearFilters = () => {
    dispatch(clearFilters());
    dispatch(setSearchQuery(''));
  };

  const handlePageChange = (page) => {
    dispatch(fetchServices({
      page,
      limit: pagination.items_per_page,
      search: searchQuery,
      ...filters
    }));
  };

  const handleRowSelect = (serviceId, checked) => {
    if (checked) {
      dispatch(selectService(serviceId));
    } else {
      dispatch(deselectService(serviceId));
    }
  };

  const handleSelectAll = (serviceIds) => {
    if (serviceIds.length > 0) {
      dispatch(selectAllServices());
    } else {
      dispatch(deselectAllServices());
    }
  };

  const handleDeleteService = async () => {
    if (serviceToDelete) {
      await dispatch(deleteService(serviceToDelete.id));
      setDeleteModalOpen(false);
      setServiceToDelete(null);
      // Refresh the list
      dispatch(fetchServices({
        page: pagination.current_page,
        limit: pagination.items_per_page,
        search: searchQuery,
        ...filters
      }));
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Services
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Manage service requests and track their progress
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            leftIcon={WrenchScrewdriverIcon}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </Button>
          
          <Button
            leftIcon={PlusIcon}
            onClick={() => navigate('/services/new')}
          >
            New Service
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              label="Status"
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
              options={statusOptions}
            />
            
            <Select
              label="Priority"
              value={filters.priority}
              onChange={(value) => handleFilterChange('priority', value)}
              options={priorityOptions}
            />
            
            <Input
              label="Customer"
              value={filters.customer}
              onChange={(e) => handleFilterChange('customer', e.target.value)}
              placeholder="Filter by customer"
            />
            
            <div className="flex items-end space-x-2">
              <Button
                variant="outline"
                onClick={handleClearFilters}
                className="flex-1"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <DataTable
          data={services}
          columns={columns}
          loading={loading}
          error={error}
          searchQuery={searchQuery}
          onSearch={handleSearch}
          selectedRows={selectedServices}
          onSelectRow={handleRowSelect}
          onSelectAll={handleSelectAll}
          selectable={true}
          actions={actions}
          onRowClick={(service) => navigate(`/services/${service.id}`)}
          emptyMessage="No services found"
        />
        
        {/* Pagination */}
        <Pagination
          currentPage={pagination.current_page}
          totalPages={pagination.total_pages}
          totalItems={pagination.total_items}
          itemsPerPage={pagination.items_per_page}
          onPageChange={handlePageChange}
        />
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteService}
        title="Delete Service"
        message={`Are you sure you want to delete service #${serviceToDelete?.service_number || serviceToDelete?.id}? This action cannot be undone.`}
        confirmText="Delete"
        confirmColor="red"
      />
    </div>
  );
};

export default Services;
