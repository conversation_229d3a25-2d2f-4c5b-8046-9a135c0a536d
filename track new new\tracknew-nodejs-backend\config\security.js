// Security configuration for production
const crypto = require('crypto');

// Security headers configuration
const securityHeaders = {
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdn.tailwindcss.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.tailwindcss.com"],
      connectSrc: ["'self'", "https://api.tracknew.com", "wss:", "ws:"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      manifestSrc: ["'self'"],
      workerSrc: ["'self'", "blob:"],
      upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null,
    },
    reportOnly: false
  },
  
  // HTTP Strict Transport Security
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  
  // X-Frame-Options
  frameguard: {
    action: 'deny'
  },
  
  // X-Content-Type-Options
  noSniff: true,
  
  // X-XSS-Protection
  xssFilter: true,
  
  // Referrer Policy
  referrerPolicy: {
    policy: 'strict-origin-when-cross-origin'
  },
  
  // Permissions Policy
  permissionsPolicy: {
    features: {
      camera: ['none'],
      microphone: ['none'],
      geolocation: ['self'],
      notifications: ['self'],
      payment: ['none'],
      usb: ['none']
    }
  }
};

// CORS configuration
const corsConfig = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'https://tracknew.com',
      'https://www.tracknew.com',
      'https://app.tracknew.com',
      ...(process.env.NODE_ENV === 'development' ? [
        'http://localhost:3000',
        'http://localhost:3001',
        'http://127.0.0.1:3000'
      ] : [])
    ];

    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS policy'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type', 
    'Authorization', 
    'X-Requested-With',
    'X-API-Key',
    'X-Client-Version',
    'Accept',
    'Origin'
  ],
  exposedHeaders: [
    'X-Total-Count',
    'X-Page-Count',
    'X-Current-Page',
    'X-Response-Time'
  ],
  maxAge: 86400 // 24 hours
};

// JWT configuration
const jwtConfig = {
  secret: process.env.JWT_SECRET || crypto.randomBytes(64).toString('hex'),
  refreshSecret: process.env.JWT_REFRESH_SECRET || crypto.randomBytes(64).toString('hex'),
  expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  issuer: 'tracknew-api',
  audience: 'tracknew-app',
  algorithm: 'HS256',
  clockTolerance: 30, // 30 seconds
  ignoreExpiration: false,
  ignoreNotBefore: false
};

// Password policy
const passwordPolicy = {
  minLength: 8,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  preventCommonPasswords: true,
  preventUserInfo: true,
  maxAttempts: 5,
  lockoutDuration: 15 * 60 * 1000, // 15 minutes
  passwordHistory: 5 // Remember last 5 passwords
};

// Session configuration
const sessionConfig = {
  secret: process.env.SESSION_SECRET || crypto.randomBytes(64).toString('hex'),
  name: 'tracknew.sid',
  resave: false,
  saveUninitialized: false,
  rolling: true,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax'
  },
  store: null // Will be set to Redis store in production
};

// API key configuration
const apiKeyConfig = {
  headerName: 'X-API-Key',
  length: 32,
  prefix: 'tn_',
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000 // requests per window
  }
};

// File upload security
const uploadSecurity = {
  allowedMimeTypes: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv'
  ],
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
  scanForViruses: process.env.NODE_ENV === 'production',
  quarantinePath: '/var/quarantine',
  allowedExtensions: [
    '.jpg', '.jpeg', '.png', '.gif', '.webp',
    '.pdf', '.doc', '.docx', '.xls', '.xlsx',
    '.txt', '.csv'
  ]
};

// Input validation rules
const validationRules = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s\-\(\)]+$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  noSpecialChars: /^[a-zA-Z0-9\s\-_\.]+$/,
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  sanitizeHtml: true,
  maxStringLength: 1000,
  maxNumberValue: Number.MAX_SAFE_INTEGER
};

// Audit logging configuration
const auditConfig = {
  enabled: true,
  logLevel: 'info',
  includeRequestBody: false, // Don't log sensitive data
  includeResponseBody: false,
  sensitiveFields: [
    'password',
    'token',
    'secret',
    'key',
    'authorization',
    'cookie',
    'session'
  ],
  retentionDays: 90,
  alertOnSuspiciousActivity: true
};

// Security monitoring
const monitoringConfig = {
  enableBruteForceDetection: true,
  enableSQLInjectionDetection: true,
  enableXSSDetection: true,
  enableCSRFProtection: true,
  enableDDoSProtection: true,
  alertThresholds: {
    failedLogins: 10,
    suspiciousRequests: 50,
    errorRate: 0.1 // 10%
  },
  notificationChannels: [
    'email',
    'slack',
    'webhook'
  ]
};

// Encryption configuration
const encryptionConfig = {
  algorithm: 'aes-256-gcm',
  keyLength: 32,
  ivLength: 16,
  tagLength: 16,
  saltLength: 32,
  iterations: 100000,
  hashAlgorithm: 'sha256'
};

module.exports = {
  securityHeaders,
  corsConfig,
  jwtConfig,
  passwordPolicy,
  sessionConfig,
  apiKeyConfig,
  uploadSecurity,
  validationRules,
  auditConfig,
  monitoringConfig,
  encryptionConfig,
  
  // Security middleware setup
  setupSecurityMiddleware: (app) => {
    // Trust proxy (for load balancers)
    if (process.env.TRUST_PROXY === 'true') {
      app.set('trust proxy', 1);
    }
    
    // Disable X-Powered-By header
    app.disable('x-powered-by');
    
    // Security headers middleware will be applied in performance.js
    
    // Request ID for tracking
    app.use((req, res, next) => {
      req.id = crypto.randomUUID();
      res.set('X-Request-ID', req.id);
      next();
    });
    
    // Security logging
    app.use((req, res, next) => {
      if (auditConfig.enabled) {
        console.log(`[SECURITY] ${new Date().toISOString()} - ${req.method} ${req.path} - IP: ${req.ip} - ID: ${req.id}`);
      }
      next();
    });
  },
  
  // Utility functions
  generateSecureToken: (length = 32) => {
    return crypto.randomBytes(length).toString('hex');
  },
  
  hashPassword: async (password) => {
    const bcrypt = require('bcrypt');
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    return await bcrypt.hash(password, saltRounds);
  },
  
  verifyPassword: async (password, hash) => {
    const bcrypt = require('bcrypt');
    return await bcrypt.compare(password, hash);
  },
  
  encryptData: (data, key) => {
    const cipher = crypto.createCipher(encryptionConfig.algorithm, key);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  },
  
  decryptData: (encryptedData, key) => {
    const decipher = crypto.createDecipher(encryptionConfig.algorithm, key);
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
};
