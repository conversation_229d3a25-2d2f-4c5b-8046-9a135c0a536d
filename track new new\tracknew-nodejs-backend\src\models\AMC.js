const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const AMC = sequelize.define('AMC', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  amc_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  contract_type: {
    type: DataTypes.ENUM('annual', 'quarterly', 'monthly', 'custom'),
    defaultValue: 'annual'
  },
  start_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  end_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  contract_value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  payment_terms: {
    type: DataTypes.ENUM('advance', 'monthly', 'quarterly', 'on_completion'),
    defaultValue: 'advance'
  },
  payment_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Amount per payment cycle'
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'INR'
  },
  status: {
    type: DataTypes.ENUM('draft', 'active', 'expired', 'cancelled', 'renewed'),
    defaultValue: 'draft'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
    defaultValue: 'medium'
  },
  service_frequency: {
    type: DataTypes.ENUM('weekly', 'monthly', 'quarterly', 'half_yearly', 'yearly', 'custom'),
    defaultValue: 'monthly'
  },
  service_frequency_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Custom frequency in days'
  },
  next_service_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  last_service_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  total_services_planned: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  total_services_completed: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  auto_renewal: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  renewal_notice_days: {
    type: DataTypes.INTEGER,
    defaultValue: 30,
    comment: 'Days before expiry to send renewal notice'
  },
  terms_and_conditions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  special_instructions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  contact_person: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  contact_email: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  site_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  billing_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  warranty_included: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  parts_included: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  labor_included: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  emergency_support: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  response_time_hours: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Guaranteed response time in hours'
  },
  resolution_time_hours: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Guaranteed resolution time in hours'
  },
  penalty_clause: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  escalation_matrix: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object containing escalation contacts'
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of attachment file paths'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  reminder_enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  notification_enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  approved_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  approved_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'amcs',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['amc_number'],
      unique: true
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['start_date']
    },
    {
      fields: ['end_date']
    },
    {
      fields: ['next_service_date']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['auto_renewal']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = AMC;
