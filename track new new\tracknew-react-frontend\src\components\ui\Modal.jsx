import React, { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { classNames } from '../../utils/helpers';
import Button from './Button';

const Modal = ({
  isOpen = false,
  onClose,
  title,
  description,
  children,
  footer,
  size = 'md',
  closeOnOverlayClick = true,
  showCloseButton = true,
  preventClose = false,
  className = '',
  overlayClassName = '',
  contentClassName = '',
  headerClassName = '',
  bodyClassName = '',
  footerClassName = '',
  ...props
}) => {
  const sizeClasses = {
    xs: 'max-w-xs',
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
    full: 'max-w-full',
  };

  const handleClose = () => {
    if (!preventClose && onClose) {
      onClose();
    }
  };

  const handleOverlayClick = () => {
    if (closeOnOverlayClick) {
      handleClose();
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog 
        as="div" 
        className="relative z-50" 
        onClose={handleClose}
        {...props}
      >
        {/* Overlay */}
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div 
            className={classNames(
              'fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm',
              overlayClassName
            )}
            onClick={handleOverlayClick}
          />
        </Transition.Child>

        {/* Modal Container */}
        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel 
                className={classNames(
                  'w-full transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left align-middle shadow-xl transition-all',
                  sizeClasses[size],
                  contentClassName,
                  className
                )}
              >
                {/* Header */}
                {(title || description || showCloseButton) && (
                  <div className={classNames(
                    'flex items-start justify-between p-6 border-b border-gray-200 dark:border-gray-700',
                    headerClassName
                  )}>
                    <div className="flex-1">
                      {title && (
                        <Dialog.Title
                          as="h3"
                          className="text-lg font-medium leading-6 text-gray-900 dark:text-white"
                        >
                          {title}
                        </Dialog.Title>
                      )}
                      {description && (
                        <div className="mt-2">
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {description}
                          </p>
                        </div>
                      )}
                    </div>
                    
                    {showCloseButton && (
                      <button
                        type="button"
                        className="ml-4 rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        onClick={handleClose}
                        disabled={preventClose}
                      >
                        <span className="sr-only">Close</span>
                        <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                      </button>
                    )}
                  </div>
                )}

                {/* Body */}
                <div className={classNames(
                  'p-6',
                  bodyClassName
                )}>
                  {children}
                </div>

                {/* Footer */}
                {footer && (
                  <div className={classNames(
                    'flex items-center justify-end space-x-3 px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600',
                    footerClassName
                  )}>
                    {footer}
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

// Confirmation Modal Component
export const ConfirmModal = ({
  isOpen = false,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmColor = 'red',
  loading = false,
  ...props
}) => {
  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
  };

  const footer = (
    <>
      <Button
        variant="outline"
        color="gray"
        onClick={onClose}
        disabled={loading}
      >
        {cancelText}
      </Button>
      <Button
        variant="primary"
        color={confirmColor}
        onClick={handleConfirm}
        loading={loading}
      >
        {confirmText}
      </Button>
    </>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      footer={footer}
      size="sm"
      preventClose={loading}
      {...props}
    >
      <p className="text-sm text-gray-500 dark:text-gray-400">
        {message}
      </p>
    </Modal>
  );
};

// Alert Modal Component
export const AlertModal = ({
  isOpen = false,
  onClose,
  title = 'Alert',
  message,
  type = 'info',
  buttonText = 'OK',
  ...props
}) => {
  const typeConfig = {
    info: {
      color: 'blue',
      icon: '💡',
    },
    success: {
      color: 'green',
      icon: '✅',
    },
    warning: {
      color: 'yellow',
      icon: '⚠️',
    },
    error: {
      color: 'red',
      icon: '❌',
    },
  };

  const config = typeConfig[type] || typeConfig.info;

  const footer = (
    <Button
      variant="primary"
      color={config.color}
      onClick={onClose}
    >
      {buttonText}
    </Button>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center space-x-2">
          <span>{config.icon}</span>
          <span>{title}</span>
        </div>
      }
      footer={footer}
      size="sm"
      {...props}
    >
      <p className="text-sm text-gray-500 dark:text-gray-400">
        {message}
      </p>
    </Modal>
  );
};

export default Modal;
