const { Warehouse, User, Company, StockMovement, Product, PurchaseOrder, InvoiceItem } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all warehouses with filtering and pagination
const getWarehouses = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    warehouse_type,
    is_active = true,
    is_default,
    manager_id,
    city,
    state,
    sort_by = 'warehouse_name',
    sort_order = 'ASC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { warehouse_name: { [Op.iLike]: `%${search}%` } },
      { warehouse_code: { [Op.iLike]: `%${search}%` } },
      { address: { [Op.iLike]: `%${search}%` } },
      { contact_person: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add filters
  if (warehouse_type) {
    whereConditions.warehouse_type = warehouse_type;
  }

  if (is_active !== undefined) {
    whereConditions.is_active = is_active === 'true';
  }

  if (is_default !== undefined) {
    whereConditions.is_default = is_default === 'true';
  }

  if (manager_id) {
    whereConditions.manager_id = manager_id;
  }

  if (city) {
    whereConditions.city = { [Op.iLike]: `%${city}%` };
  }

  if (state) {
    whereConditions.state = { [Op.iLike]: `%${state}%` };
  }

  // Get warehouses with associations
  const { count, rows: warehouses } = await Warehouse.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: User,
        as: 'manager',
        attributes: ['id', 'name', 'email', 'phone']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updater',
        attributes: ['id', 'name', 'email']
      }
    ],
    limit: parseInt(limit),
    offset: offset,
    order: [[sort_by, sort_order.toUpperCase()]],
    distinct: true
  });

  // Calculate pagination
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      warehouses,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single warehouse by ID
const getWarehouse = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const warehouse = await Warehouse.findOne({
    where: {
      id,
      company_id: companyId
    },
    include: [
      {
        model: User,
        as: 'manager',
        attributes: ['id', 'name', 'email', 'phone', 'designation']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updater',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!warehouse) {
    return next(new AppError('Warehouse not found', 404));
  }

  // Get additional warehouse statistics
  const stockMovementCount = await StockMovement.count({
    where: { warehouse_id: id }
  });

  const purchaseOrderCount = await PurchaseOrder.count({
    where: { warehouse_id: id }
  });

  const invoiceItemCount = await InvoiceItem.count({
    where: { warehouse_id: id }
  });

  res.status(200).json({
    status: 'success',
    data: {
      warehouse,
      statistics: {
        stock_movements: stockMovementCount,
        purchase_orders: purchaseOrderCount,
        invoice_items: invoiceItemCount
      }
    }
  });
});

// Create new warehouse
const createWarehouse = catchAsync(async (req, res, next) => {
  const {
    warehouse_name,
    warehouse_code,
    warehouse_type = 'main',
    address,
    city,
    state,
    country = 'India',
    pincode,
    contact_person,
    phone_number,
    email,
    manager_id,
    capacity,
    current_utilization = 0,
    is_active = true,
    is_default = false,
    allow_negative_stock = false,
    auto_reorder_enabled = false,
    operating_hours = {},
    facilities = [],
    notes
  } = req.body;

  const companyId = req.user.company_id;

  // Check for duplicate warehouse code within company
  if (warehouse_code) {
    const existingWarehouse = await Warehouse.findOne({
      where: {
        company_id: companyId,
        warehouse_code: warehouse_code
      }
    });

    if (existingWarehouse) {
      return next(new AppError('Warehouse with this code already exists', 400));
    }
  }

  // Check for duplicate warehouse name within company
  const existingName = await Warehouse.findOne({
    where: {
      company_id: companyId,
      warehouse_name: warehouse_name
    }
  });

  if (existingName) {
    return next(new AppError('Warehouse with this name already exists', 400));
  }

  // Validate manager if provided
  if (manager_id) {
    const manager = await User.findOne({
      where: {
        id: manager_id,
        company_id: companyId,
        is_active: true
      }
    });

    if (!manager) {
      return next(new AppError('Manager not found or inactive', 400));
    }
  }

  // If setting as default, unset other default warehouses
  if (is_default) {
    await Warehouse.update(
      { is_default: false },
      {
        where: {
          company_id: companyId,
          is_default: true
        }
      }
    );
  }

  const warehouse = await Warehouse.create({
    company_id: companyId,
    warehouse_name,
    warehouse_code,
    warehouse_type,
    address,
    city,
    state,
    country,
    pincode,
    contact_person,
    phone_number,
    email,
    manager_id,
    capacity,
    current_utilization,
    is_active,
    is_default,
    allow_negative_stock,
    auto_reorder_enabled,
    operating_hours,
    facilities,
    notes,
    created_by: req.user.id
  });

  // Fetch the created warehouse with associations
  const createdWarehouse = await Warehouse.findByPk(warehouse.id, {
    include: [
      {
        model: User,
        as: 'manager',
        attributes: ['id', 'name', 'email', 'phone']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      warehouse: createdWarehouse
    }
  });
});

// Update warehouse
const updateWarehouse = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const warehouse = await Warehouse.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!warehouse) {
    return next(new AppError('Warehouse not found', 404));
  }

  // Check if warehouse is being used in transactions before deactivating
  if (req.body.is_active === false) {
    const stockMovementCount = await StockMovement.count({
      where: { warehouse_id: id }
    });

    const purchaseOrderCount = await PurchaseOrder.count({
      where: { warehouse_id: id }
    });

    if (stockMovementCount > 0 || purchaseOrderCount > 0) {
      return next(new AppError(`Cannot deactivate warehouse. It has ${stockMovementCount + purchaseOrderCount} associated transaction(s)`, 400));
    }
  }

  // Check for duplicate warehouse code (excluding current warehouse)
  if (req.body.warehouse_code) {
    const existingWarehouse = await Warehouse.findOne({
      where: {
        company_id: companyId,
        id: { [Op.ne]: id },
        warehouse_code: req.body.warehouse_code
      }
    });

    if (existingWarehouse) {
      return next(new AppError('Warehouse with this code already exists', 400));
    }
  }

  // Check for duplicate warehouse name (excluding current warehouse)
  if (req.body.warehouse_name) {
    const existingName = await Warehouse.findOne({
      where: {
        company_id: companyId,
        id: { [Op.ne]: id },
        warehouse_name: req.body.warehouse_name
      }
    });

    if (existingName) {
      return next(new AppError('Warehouse with this name already exists', 400));
    }
  }

  // Validate manager if provided
  if (req.body.manager_id) {
    const manager = await User.findOne({
      where: {
        id: req.body.manager_id,
        company_id: companyId,
        is_active: true
      }
    });

    if (!manager) {
      return next(new AppError('Manager not found or inactive', 400));
    }
  }

  // If setting as default, unset other default warehouses
  if (req.body.is_default === true) {
    await Warehouse.update(
      { is_default: false },
      {
        where: {
          company_id: companyId,
          id: { [Op.ne]: id },
          is_default: true
        }
      }
    );
  }

  // Update warehouse
  await warehouse.update({
    ...req.body,
    updated_by: req.user.id
  });

  // Fetch updated warehouse with associations
  const updatedWarehouse = await Warehouse.findByPk(id, {
    include: [
      {
        model: User,
        as: 'manager',
        attributes: ['id', 'name', 'email', 'phone']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updater',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      warehouse: updatedWarehouse
    }
  });
});

// Delete warehouse
const deleteWarehouse = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const warehouse = await Warehouse.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!warehouse) {
    return next(new AppError('Warehouse not found', 404));
  }

  // Check if warehouse is being used in transactions
  const stockMovementCount = await StockMovement.count({
    where: { warehouse_id: id }
  });

  const purchaseOrderCount = await PurchaseOrder.count({
    where: { warehouse_id: id }
  });

  const invoiceItemCount = await InvoiceItem.count({
    where: { warehouse_id: id }
  });

  if (stockMovementCount > 0 || purchaseOrderCount > 0 || invoiceItemCount > 0) {
    return next(new AppError(`Cannot delete warehouse. It has ${stockMovementCount + purchaseOrderCount + invoiceItemCount} associated transaction(s)`, 400));
  }

  // Cannot delete default warehouse
  if (warehouse.is_default) {
    return next(new AppError('Cannot delete default warehouse. Please set another warehouse as default first', 400));
  }

  await warehouse.destroy();

  res.status(200).json({
    status: 'success',
    message: 'Warehouse deleted successfully'
  });
});

// Get warehouse statistics
const getWarehouseStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  // Total warehouses count
  const totalWarehouses = await Warehouse.count({
    where: { company_id: companyId }
  });

  // Active warehouses count
  const activeWarehouses = await Warehouse.count({
    where: {
      company_id: companyId,
      is_active: true
    }
  });

  // Warehouses by type
  const warehousesByType = await Warehouse.findAll({
    where: { company_id: companyId },
    attributes: [
      'warehouse_type',
      [Warehouse.sequelize.fn('COUNT', Warehouse.sequelize.col('id')), 'count']
    ],
    group: ['warehouse_type'],
    raw: true
  });

  // Warehouses by location (state)
  const warehousesByState = await Warehouse.findAll({
    where: {
      company_id: companyId,
      state: { [Op.ne]: null }
    },
    attributes: [
      'state',
      [Warehouse.sequelize.fn('COUNT', Warehouse.sequelize.col('id')), 'count']
    ],
    group: ['state'],
    order: [[Warehouse.sequelize.fn('COUNT', Warehouse.sequelize.col('id')), 'DESC']],
    limit: 10,
    raw: true
  });

  // Capacity utilization
  const capacityStats = await Warehouse.findAll({
    where: {
      company_id: companyId,
      capacity: { [Op.ne]: null }
    },
    attributes: [
      [Warehouse.sequelize.fn('SUM', Warehouse.sequelize.col('capacity')), 'total_capacity'],
      [Warehouse.sequelize.fn('AVG', Warehouse.sequelize.col('current_utilization')), 'avg_utilization'],
      [Warehouse.sequelize.fn('MAX', Warehouse.sequelize.col('current_utilization')), 'max_utilization'],
      [Warehouse.sequelize.fn('MIN', Warehouse.sequelize.col('current_utilization')), 'min_utilization']
    ],
    raw: true
  });

  // Most active warehouses (by stock movements)
  const mostActiveWarehouses = await Warehouse.findAll({
    where: { company_id: companyId },
    include: [{
      model: StockMovement,
      as: 'stockMovements',
      attributes: []
    }],
    attributes: [
      'id',
      'warehouse_name',
      'warehouse_code',
      'warehouse_type',
      [Warehouse.sequelize.fn('COUNT', Warehouse.sequelize.col('stockMovements.id')), 'movement_count']
    ],
    group: ['Warehouse.id'],
    order: [[Warehouse.sequelize.fn('COUNT', Warehouse.sequelize.col('stockMovements.id')), 'DESC']],
    limit: 5,
    raw: false
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_warehouses: totalWarehouses,
      active_warehouses: activeWarehouses,
      inactive_warehouses: totalWarehouses - activeWarehouses,
      warehouses_by_type: warehousesByType,
      warehouses_by_state: warehousesByState,
      capacity_stats: capacityStats[0] || {},
      most_active_warehouses: mostActiveWarehouses
    }
  });
});

// Get warehouses by type
const getWarehousesByType = catchAsync(async (req, res) => {
  const { warehouse_type } = req.params;
  const companyId = req.user.company_id;

  const warehouses = await Warehouse.findAll({
    where: {
      company_id: companyId,
      warehouse_type: warehouse_type,
      is_active: true
    },
    attributes: ['id', 'warehouse_name', 'warehouse_code', 'address', 'city', 'state'],
    include: [
      {
        model: User,
        as: 'manager',
        attributes: ['id', 'name', 'email']
      }
    ],
    order: [['warehouse_name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      warehouses
    }
  });
});

// Get active warehouses
const getActiveWarehouses = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const warehouses = await Warehouse.findAll({
    where: {
      company_id: companyId,
      is_active: true
    },
    attributes: ['id', 'warehouse_name', 'warehouse_code', 'warehouse_type', 'is_default', 'allow_negative_stock'],
    order: [['is_default', 'DESC'], ['warehouse_name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      warehouses
    }
  });
});

// Get default warehouse
const getDefaultWarehouse = catchAsync(async (req, res, next) => {
  const companyId = req.user.company_id;

  const warehouse = await Warehouse.findOne({
    where: {
      company_id: companyId,
      is_default: true,
      is_active: true
    },
    include: [
      {
        model: User,
        as: 'manager',
        attributes: ['id', 'name', 'email', 'phone']
      }
    ]
  });

  if (!warehouse) {
    return next(new AppError('No default warehouse found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      warehouse
    }
  });
});

// Set default warehouse
const setDefaultWarehouse = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const warehouse = await Warehouse.findOne({
    where: {
      id,
      company_id: companyId,
      is_active: true
    }
  });

  if (!warehouse) {
    return next(new AppError('Warehouse not found or inactive', 404));
  }

  // Unset current default warehouse
  await Warehouse.update(
    { is_default: false },
    {
      where: {
        company_id: companyId,
        is_default: true
      }
    }
  );

  // Set new default warehouse
  await warehouse.update({
    is_default: true,
    updated_by: req.user.id
  });

  res.status(200).json({
    status: 'success',
    message: 'Default warehouse updated successfully',
    data: {
      warehouse
    }
  });
});

// Update warehouse utilization
const updateUtilization = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { current_utilization } = req.body;
  const companyId = req.user.company_id;

  if (current_utilization === undefined || current_utilization < 0 || current_utilization > 100) {
    return next(new AppError('Current utilization must be between 0 and 100', 400));
  }

  const warehouse = await Warehouse.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!warehouse) {
    return next(new AppError('Warehouse not found', 404));
  }

  await warehouse.update({
    current_utilization,
    updated_by: req.user.id
  });

  res.status(200).json({
    status: 'success',
    message: 'Warehouse utilization updated successfully',
    data: {
      warehouse: {
        id: warehouse.id,
        warehouse_name: warehouse.warehouse_name,
        current_utilization: warehouse.current_utilization,
        capacity: warehouse.capacity
      }
    }
  });
});

module.exports = {
  getWarehouses,
  getWarehouse,
  createWarehouse,
  updateWarehouse,
  deleteWarehouse,
  getWarehouseStats,
  getWarehousesByType,
  getActiveWarehouses,
  getDefaultWarehouse,
  setDefaultWarehouse,
  updateUtilization
};
