var e,t=Object.defineProperty,r=(e,r,a)=>((e,r,a)=>r in e?t(e,r,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[r]=a)(e,"symbol"!=typeof r?r+"":r,a);import{r as a,a as s,g as n}from"./vendor-BRaCMJ4j.js";import{r as i,b as l,R as o,u as d,L as c,c as u,O as m,N as h,B as g,d as p,f}from"./router-CH1RGDGB.js";import{c as x,a as y,b as v,d as b,e as w,u as j,f as k,P as C}from"./redux-CparIzla.js";import{a as N}from"./utils-rSMZb3Ae.js";import{q as E,a as S,A as L,_,I as O}from"./ui-BWpWsGca.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var P,A,M={exports:{}},I={};var R,F=(A||(A=1,M.exports=function(){if(P)return I;P=1;var e=a(),t=Symbol.for("react.element"),r=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,n=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function l(e,r,a){var l,o={},d=null,c=null;for(l in void 0!==a&&(d=""+a),void 0!==r.key&&(d=""+r.key),void 0!==r.ref&&(c=r.ref),r)s.call(r,l)&&!i.hasOwnProperty(l)&&(o[l]=r[l]);if(e&&e.defaultProps)for(l in r=e.defaultProps)void 0===o[l]&&(o[l]=r[l]);return{$$typeof:t,type:e,key:d,ref:c,props:o,_owner:n.current}}return I.Fragment=r,I.jsx=l,I.jsxs=l,I}()),M.exports),T={};const D=n(function(){if(R)return T;R=1;var e=s();return T.createRoot=e.createRoot,T.hydrateRoot=e.hydrateRoot,T}());var W="persist:",B="persist/FLUSH",$="persist/REHYDRATE",q="persist/PAUSE",V="persist/PERSIST",Q="persist/PURGE",H="persist/REGISTER";function U(e){return(U="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function z(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function K(e,t,r,a){a.debug;var s=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Z(r,!0).forEach((function(t){z(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Z(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},r);return e&&"object"===U(e)&&Object.keys(e).forEach((function(a){"_persist"!==a&&t[a]===r[a]&&(s[a]=e[a])})),s}function G(e){var t,r=e.blacklist||null,a=e.whitelist||null,s=e.transforms||[],n=e.throttle||0,i="".concat(void 0!==e.keyPrefix?e.keyPrefix:W).concat(e.key),l=e.storage;t=!1===e.serialize?function(e){return e}:"function"==typeof e.serialize?e.serialize:Y;var o=e.writeFailHandler||null,d={},c={},u=[],m=null,h=null;function g(){if(0===u.length)return m&&clearInterval(m),void(m=null);var e=u.shift(),r=s.reduce((function(t,r){return r.in(t,e,d)}),d[e]);if(void 0!==r)try{c[e]=t(r)}catch(a){}else delete c[e];0===u.length&&(Object.keys(c).forEach((function(e){void 0===d[e]&&delete c[e]})),h=l.setItem(i,t(c)).catch(f))}function p(e){return(!a||-1!==a.indexOf(e)||"_persist"===e)&&(!r||-1===r.indexOf(e))}function f(e){o&&o(e)}return{update:function(e){Object.keys(e).forEach((function(t){p(t)&&d[t]!==e[t]&&-1===u.indexOf(t)&&u.push(t)})),Object.keys(d).forEach((function(t){void 0===e[t]&&p(t)&&-1===u.indexOf(t)&&void 0!==d[t]&&u.push(t)})),null===m&&(m=setInterval(g,n)),d=e},flush:function(){for(;0!==u.length;)g();return h||Promise.resolve()}}}function Y(e){return JSON.stringify(e)}function J(e){var t,r=e.transforms||[],a="".concat(void 0!==e.keyPrefix?e.keyPrefix:W).concat(e.key),s=e.storage;return e.debug,t=!1===e.deserialize?function(e){return e}:"function"==typeof e.deserialize?e.deserialize:X,s.getItem(a).then((function(e){if(e)try{var a={},s=t(e);return Object.keys(s).forEach((function(e){a[e]=r.reduceRight((function(t,r){return r.out(t,e,s)}),t(s[e]))})),a}catch(n){throw n}}))}function X(e){return JSON.parse(e)}function ee(e){}function te(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function re(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?te(r,!0).forEach((function(t){ae(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):te(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ae(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function se(e,t){if(null==e)return{};var r,a,s=function(e,t){if(null==e)return{};var r,a,s={},n=Object.keys(e);for(a=0;a<n.length;a++)r=n[a],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)r=n[a],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}function ne(e,t){var r=void 0!==e.version?e.version:-1;e.debug;var a=void 0===e.stateReconciler?K:e.stateReconciler,s=e.getStoredState||J,n=void 0!==e.timeout?e.timeout:5e3,i=null,l=!1,o=!0,d=function(e){return e._persist.rehydrated&&i&&!o&&i.update(e),e};return function(c,u){var m=c||{},h=m._persist,g=se(m,["_persist"]);if(u.type===V){var p=!1,f=function(t,r){p||(u.rehydrate(e.key,t,r),p=!0)};if(n&&setTimeout((function(){!p&&f(void 0,new Error('redux-persist: persist timed out for persist key "'.concat(e.key,'"')))}),n),o=!1,i||(i=G(e)),h)return re({},t(g,u),{_persist:h});if("function"!=typeof u.rehydrate||"function"!=typeof u.register)throw new Error("redux-persist: either rehydrate or register is not a function on the PERSIST action. This can happen if the action is being replayed. This is an unexplored use case, please open an issue and we will figure out a resolution.");return u.register(e.key),s(e).then((function(t){(e.migrate||function(e,t){return Promise.resolve(e)})(t,r).then((function(e){f(e)}),(function(e){f(void 0,e)}))}),(function(e){f(void 0,e)})),re({},t(g,u),{_persist:{version:r,rehydrated:!1}})}if(u.type===Q)return l=!0,u.result(function(e){var t=e.storage,r="".concat(void 0!==e.keyPrefix?e.keyPrefix:W).concat(e.key);return t.removeItem(r,ee)}(e)),re({},t(g,u),{_persist:h});if(u.type===B)return u.result(i&&i.flush()),re({},t(g,u),{_persist:h});if(u.type===q)o=!0;else if(u.type===$){if(l)return re({},g,{_persist:re({},h,{rehydrated:!0})});if(u.key===e.key){var x=t(g,u),y=u.payload,v=re({},!1!==a&&void 0!==y?a(y,c,x,e):x,{_persist:re({},h,{rehydrated:!0})});return d(v)}}if(!h)return t(c,u);var b=t(g,u);return b===g?c:d(re({},b,{_persist:h}))}}function ie(e){return function(e){if(Array.isArray(e)){for(var t=0,r=new Array(e.length);t<e.length;t++)r[t]=e[t];return r}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function le(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function oe(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?le(r,!0).forEach((function(t){de(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):le(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function de(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ce={registry:[],bootstrapped:!1},ue=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ce,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case H:return oe({},e,{registry:[].concat(ie(e.registry),[t.key])});case $:var r=e.registry.indexOf(t.key),a=ie(e.registry);return a.splice(r,1),oe({},e,{registry:a,bootstrapped:0===a.length});default:return e}};var me,he,ge,pe={},fe={},xe={};function ye(){if(me)return xe;function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(){}me=1,xe.__esModule=!0,xe.default=function(t){var a="".concat(t,"Storage");return function(t){if("object"!==("undefined"==typeof self?"undefined":e(self))||!(t in self))return!1;try{var r=self[t],a="redux-persist ".concat(t," test");r.setItem(a,"test"),r.getItem(a),r.removeItem(a)}catch(Ve){return!1}return!0}(a)?self[a]:r};var r={getItem:t,setItem:t,removeItem:t};return xe}const ve=n(function(){if(ge)return pe;var e;ge=1,pe.__esModule=!0,pe.default=void 0;var t=(0,((e=function(){if(he)return fe;he=1,fe.__esModule=!0,fe.default=function(e){var r=(0,t.default)(e);return{getItem:function(e){return new Promise((function(t,a){t(r.getItem(e))}))},setItem:function(e,t){return new Promise((function(a,s){a(r.setItem(e,t))}))},removeItem:function(e){return new Promise((function(t,a){t(r.removeItem(e))}))}}};var e,t=(e=ye())&&e.__esModule?e:{default:e};return fe}())&&e.__esModule?e:{default:e}).default)("local");return pe.default=t,pe}());let be,we,je,ke={data:""},Ce=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Ne=/\/\*[^]*?\*\/|  +/g,Ee=/\n+/g,Se=(e,t)=>{let r="",a="",s="";for(let n in e){let i=e[n];"@"==n[0]?"i"==n[1]?r=n+" "+i+";":a+="f"==n[1]?Se(i,n):n+"{"+Se(i,"k"==n[1]?"":t)+"}":"object"==typeof i?a+=Se(i,t?t.replace(/([^,])+/g,(e=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)))):n):null!=i&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=Se.p?Se.p(n,i):n+":"+i+";")}return r+(t&&s?t+"{"+s+"}":s)+a},Le={},_e=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+_e(e[r]);return t}return e};function Oe(e){let t=this||{},r=e.call?e(t.p):e;return((e,t,r,a,s)=>{let n=_e(e),i=Le[n]||(Le[n]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(n));if(!Le[i]){let t=n!==e?e:(e=>{let t,r,a=[{}];for(;t=Ce.exec(e.replace(Ne,""));)t[4]?a.shift():t[3]?(r=t[3].replace(Ee," ").trim(),a.unshift(a[0][r]=a[0][r]||{})):a[0][t[1]]=t[2].replace(Ee," ").trim();return a[0]})(e);Le[i]=Se(s?{["@keyframes "+i]:t}:t,r?"":"."+i)}let l=r&&Le.g?Le.g:null;return r&&(Le.g=Le[i]),o=Le[i],d=t,c=a,(u=l)?d.data=d.data.replace(u,o):-1===d.data.indexOf(o)&&(d.data=c?o+d.data:d.data+o),i;var o,d,c,u})(r.unshift?r.raw?((e,t,r)=>e.reduce(((e,a,s)=>{let n=t[s];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?"."+t:e&&"object"==typeof e?e.props?"":Se(e,""):!1===e?"":e}return e+a+(null==n?"":n)}),""))(r,[].slice.call(arguments,1),t.p):r.reduce(((e,r)=>Object.assign(e,r&&r.call?r(t.p):r)),{}):r,(a=t.target,"object"==typeof window?((a?a.querySelector("#_goober"):window._goober)||Object.assign((a||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:a||ke),t.g,t.o,t.k);var a}Oe.bind({g:1});let Pe=Oe.bind({k:1});function Ae(e,t){let r=this||{};return function(){let t=arguments;return function a(s,n){let i=Object.assign({},s),l=i.className||a.className;r.p=Object.assign({theme:we&&we()},i),r.o=/ *go\d+/.test(l),i.className=Oe.apply(r,t)+(l?" "+l:"");let o=e;return e[0]&&(o=i.as||e,delete i.as),je&&o[0]&&je(i),be(o,i)}}}var Me=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,Ie=(()=>{let e=0;return()=>(++e).toString()})(),Re=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),Fe=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map((e=>e.id===t.toast.id?{...e,...t.toast}:e))};case 2:let{toast:r}=t;return Fe(e,{type:e.toasts.find((e=>e.id===r.id))?1:0,toast:r});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map((e=>e.id===a||void 0===a?{...e,dismissed:!0,visible:!1}:e))};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==t.toastId))};case 5:return{...e,pausedAt:t.time};case 6:let s=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map((e=>({...e,pauseDuration:e.pauseDuration+s})))}}},Te=[],De={toasts:[],pausedAt:void 0},We=e=>{De=Fe(De,e),Te.forEach((e=>{e(De)}))},Be={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},$e=e=>(t,r)=>{let a=((e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||Ie()}))(t,e,r);return We({type:2,toast:a}),a.id},qe=(e,t)=>$e("blank")(e,t);qe.error=$e("error"),qe.success=$e("success"),qe.loading=$e("loading"),qe.custom=$e("custom"),qe.dismiss=e=>{We({type:3,toastId:e})},qe.remove=e=>We({type:4,toastId:e}),qe.promise=(e,t,r)=>{let a=qe.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then((e=>{let s=t.success?Me(t.success,e):void 0;return s?qe.success(s,{id:a,...r,...null==r?void 0:r.success}):qe.dismiss(a),e})).catch((e=>{let s=t.error?Me(t.error,e):void 0;s?qe.error(s,{id:a,...r,...null==r?void 0:r.error}):qe.dismiss(a)})),e};var Ve,Qe,He,Ue,Ze=(e,t)=>{We({type:1,toast:{id:e,height:t}})},ze=()=>{We({type:5,time:Date.now()})},Ke=new Map,Ge=e=>{let{toasts:t,pausedAt:r}=((e={})=>{let[t,r]=i.useState(De),a=i.useRef(De);i.useEffect((()=>(a.current!==De&&r(De),Te.push(r),()=>{let e=Te.indexOf(r);e>-1&&Te.splice(e,1)})),[]);let s=t.toasts.map((t=>{var r,a,s;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(a=e[t.type])?void 0:a.duration)||(null==e?void 0:e.duration)||Be[t.type],style:{...e.style,...null==(s=e[t.type])?void 0:s.style,...t.style}}}));return{...t,toasts:s}})(e);i.useEffect((()=>{if(r)return;let e=Date.now(),a=t.map((t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(r<0))return setTimeout((()=>qe.dismiss(t.id)),r);t.visible&&qe.dismiss(t.id)}));return()=>{a.forEach((e=>e&&clearTimeout(e)))}}),[t,r]);let a=i.useCallback((()=>{r&&We({type:6,time:Date.now()})}),[r]),s=i.useCallback(((e,r)=>{let{reverseOrder:a=!1,gutter:s=8,defaultPosition:n}=r||{},i=t.filter((t=>(t.position||n)===(e.position||n)&&t.height)),l=i.findIndex((t=>t.id===e.id)),o=i.filter(((e,t)=>t<l&&e.visible)).length;return i.filter((e=>e.visible)).slice(...a?[o+1]:[0,o]).reduce(((e,t)=>e+(t.height||0)+s),0)}),[t]);return i.useEffect((()=>{t.forEach((e=>{if(e.dismissed)((e,t=1e3)=>{if(Ke.has(e))return;let r=setTimeout((()=>{Ke.delete(e),We({type:4,toastId:e})}),t);Ke.set(e,r)})(e.id,e.removeDelay);else{let t=Ke.get(e.id);t&&(clearTimeout(t),Ke.delete(e.id))}}))}),[t]),{toasts:t,handlers:{updateHeight:Ze,startPause:ze,endPause:a,calculateOffset:s}}},Ye=Pe`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Je=Pe`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Xe=Pe`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,et=Ae("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Ye} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Je} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Xe} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,tt=Pe`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,rt=Ae("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${tt} 1s linear infinite;
`,at=Pe`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,st=Pe`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,nt=Ae("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${at} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${st} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,it=Ae("div")`
  position: absolute;
`,lt=Ae("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,ot=Pe`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,dt=Ae("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${ot} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,ct=({toast:e})=>{let{icon:t,type:r,iconTheme:a}=e;return void 0!==t?"string"==typeof t?i.createElement(dt,null,t):t:"blank"===r?null:i.createElement(lt,null,i.createElement(rt,{...a}),"loading"!==r&&i.createElement(it,null,"error"===r?i.createElement(et,{...a}):i.createElement(nt,{...a})))},ut=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,mt=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,ht=Ae("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,gt=Ae("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,pt=i.memo((({toast:e,position:t,style:r,children:a})=>{let s=e.height?((e,t)=>{let r=e.includes("top")?1:-1,[a,s]=Re()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[ut(r),mt(r)];return{animation:t?`${Pe(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Pe(s)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(e.position||t||"top-center",e.visible):{opacity:0},n=i.createElement(ct,{toast:e}),l=i.createElement(gt,{...e.ariaProps},Me(e.message,e));return i.createElement(ht,{className:e.className,style:{...s,...r,...e.style}},"function"==typeof a?a({icon:n,message:l}):i.createElement(i.Fragment,null,n,l))}));Ve=i.createElement,Se.p=Qe,be=Ve,we=He,je=Ue;var ft=({id:e,className:t,style:r,onHeightUpdate:a,children:s})=>{let n=i.useCallback((t=>{if(t){let r=()=>{let r=t.getBoundingClientRect().height;a(e,r)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}}),[e,a]);return i.createElement("div",{ref:n,className:t,style:r},s)},xt=Oe`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,yt=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:a,children:s,containerStyle:n,containerClassName:l})=>{let{toasts:o,handlers:d}=Ge(r);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...n},className:l,onMouseEnter:d.startPause,onMouseLeave:d.endPause},o.map((r=>{let n=r.position||t,l=((e,t)=>{let r=e.includes("top"),a=r?{top:0}:{bottom:0},s=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:Re()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...a,...s}})(n,d.calculateOffset(r,{reverseOrder:e,gutter:a,defaultPosition:t}));return i.createElement(ft,{id:r.id,key:r.id,onHeightUpdate:d.updateHeight,className:r.visible?xt:"",style:l},"custom"===r.type?Me(r.message,r):s?s(r):i.createElement(pt,{toast:r,position:n}))})))};const vt={development:{API_BASE_URL:"http://localhost:8000/api",APP_URL:"http://localhost:3000"},production:{API_BASE_URL:"/api",APP_URL:window.location.origin},test:{API_BASE_URL:"http://localhost:8000/api",APP_URL:"http://localhost:3000"}}.production;const bt=N.create({baseURL:{}.REACT_APP_API_URL||vt.API_BASE_URL,timeout:3e4,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0});bt.interceptors.request.use((e=>{const t=$o.getState().auth.token;return t&&(e.headers.Authorization=`Bearer ${t}`),e}),(e=>Promise.reject(e))),bt.interceptors.response.use((e=>e),(async e=>{var t,r,a,s,n,i;const l=e.config;if(401===(null==(t=e.response)?void 0:t.status)&&!l._retry){l._retry=!0;try{const e=$o.getState();if(e.auth.refreshToken){await $o.dispatch(Ot()).unwrap();const e=$o.getState().auth.token;if(e)return l.headers.Authorization=`Bearer ${e}`,bt(l)}}catch(o){return $o.dispatch(Tt()),window.location.href="/login",Promise.reject(o)}}if(403===(null==(r=e.response)?void 0:r.status))qe.error("You do not have permission to perform this action");else if(404===(null==(a=e.response)?void 0:a.status))qe.error("Resource not found");else if(422===(null==(s=e.response)?void 0:s.status)){const t=e.response.data.errors;t&&"object"==typeof t?Object.values(t).forEach((e=>{Array.isArray(e)&&e.forEach((e=>qe.error(e)))})):qe.error(e.response.data.message||"Validation error")}else 429===(null==(n=e.response)?void 0:n.status)?qe.error("Too many requests. Please try again later."):(null==(i=e.response)?void 0:i.status)>=500?qe.error("Server error. Please try again later."):"ECONNABORTED"===e.code?qe.error("Request timeout. Please check your connection."):e.response||qe.error("Network error. Please check your connection.");return Promise.reject(e)}));const wt=(e,t={})=>bt.get(e,t),jt=(e,t={},r={})=>bt.post(e,t,r),kt=(e,t={},r={})=>bt.put(e,t,r),Ct=(e,t={},r={})=>bt.patch(e,t,r),Nt=(e,t={})=>bt.delete(e,t),Et={login:e=>bt.post("/auth/login",e),register:e=>bt.post("/auth/register",e),logout:()=>bt.post("/auth/logout"),refreshToken:()=>bt.post("/auth/refresh"),forgotPassword:e=>bt.post("/auth/forgot-password",{email:e}),resetPassword:e=>bt.post("/auth/reset-password",e),verifyEmail:e=>bt.post("/auth/verify-email",{token:e}),getProfile:()=>bt.get("/auth/profile"),updateProfile:e=>bt.put("/auth/profile",e)},St=y("auth/login",(async({login:e,password:t},{rejectWithValue:r})=>{var a,s;try{const r=await Et.login({login:e,password:t});return qe.success("Login successful!"),r.data}catch(n){const e=(null==(s=null==(a=n.response)?void 0:a.data)?void 0:s.message)||"Login failed";return qe.error(e),r(e)}})),Lt=y("auth/register",(async(e,{rejectWithValue:t})=>{var r,a;try{const t=await Et.register(e);return qe.success("Registration successful!"),t.data}catch(s){const e=(null==(a=null==(r=s.response)?void 0:r.data)?void 0:a.message)||"Registration failed";return qe.error(e),t(e)}})),_t=y("auth/logout",(async(e,{rejectWithValue:t})=>{try{return await Et.logout(),qe.success("Logged out successfully"),{}}catch(r){return{}}})),Ot=y("auth/refreshToken",(async(e,{getState:t,rejectWithValue:r})=>{var a,s;try{const{auth:e}=t();return(await Et.refreshToken(e.refreshToken)).data}catch(n){return r((null==(s=null==(a=n.response)?void 0:a.data)?void 0:s.message)||"Token refresh failed")}})),Pt=y("auth/getCurrentUser",(async(e,{rejectWithValue:t})=>{var r,a;try{return(await Et.getCurrentUser()).data}catch(s){return t((null==(a=null==(r=s.response)?void 0:r.data)?void 0:a.message)||"Failed to get user data")}})),At=y("auth/forgotPassword",(async({email:e},{rejectWithValue:t})=>{var r,a;try{const t=await Et.forgotPassword({email:e});return qe.success("Password reset email sent!"),t.data}catch(s){const e=(null==(a=null==(r=s.response)?void 0:r.data)?void 0:a.message)||"Failed to send reset email";return qe.error(e),t(e)}})),Mt=y("auth/resetPassword",(async({token:e,password:t},{rejectWithValue:r})=>{var a,s;try{const r=await Et.resetPassword({token:e,password:t});return qe.success("Password reset successful!"),r.data}catch(n){const e=(null==(s=null==(a=n.response)?void 0:a.data)?void 0:s.message)||"Password reset failed";return qe.error(e),r(e)}})),It=y("auth/updatePassword",(async({currentPassword:e,newPassword:t},{rejectWithValue:r})=>{var a,s;try{const r=await Et.updatePassword({passwordCurrent:e,password:t});return qe.success("Password updated successfully!"),r.data}catch(n){const e=(null==(s=null==(a=n.response)?void 0:a.data)?void 0:s.message)||"Password update failed";return qe.error(e),r(e)}})),Rt=v({name:"auth",initialState:{user:null,token:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null,loginAttempts:0,isAccountLocked:!1},reducers:{clearError:e=>{e.error=null},clearAuth:e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.error=null,e.loginAttempts=0,e.isAccountLocked=!1},setCredentials:(e,t)=>{const{user:r,token:a,refreshToken:s}=t.payload;e.user=r,e.token=a,e.refreshToken=s,e.isAuthenticated=!0,e.error=null,e.loginAttempts=0,e.isAccountLocked=!1},updateUser:(e,t)=>{e.user={...e.user,...t.payload}},incrementLoginAttempts:e=>{e.loginAttempts+=1,e.loginAttempts>=5&&(e.isAccountLocked=!0)},resetLoginAttempts:e=>{e.loginAttempts=0,e.isAccountLocked=!1}},extraReducers:e=>{e.addCase(St.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(St.fulfilled,((e,t)=>{e.isLoading=!1,e.user=t.payload.data.user,e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null,e.loginAttempts=0,e.isAccountLocked=!1})).addCase(St.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload,e.loginAttempts+=1,e.loginAttempts>=5&&(e.isAccountLocked=!0)})).addCase(Lt.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(Lt.fulfilled,((e,t)=>{e.isLoading=!1,e.user=t.payload.data.user,e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null})).addCase(Lt.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload})).addCase(_t.fulfilled,(e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.error=null,e.loginAttempts=0,e.isAccountLocked=!1})).addCase(Ot.fulfilled,((e,t)=>{e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.user=t.payload.data.user})).addCase(Ot.rejected,(e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1})).addCase(Pt.pending,(e=>{e.isLoading=!0})).addCase(Pt.fulfilled,((e,t)=>{e.isLoading=!1,e.user=t.payload.data.user,e.isAuthenticated=!0})).addCase(Pt.rejected,(e=>{e.isLoading=!1,e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1})).addCase(At.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(At.fulfilled,(e=>{e.isLoading=!1,e.error=null})).addCase(At.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload})).addCase(Mt.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(Mt.fulfilled,((e,t)=>{e.isLoading=!1,e.user=t.payload.data.user,e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null})).addCase(Mt.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload})).addCase(It.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(It.fulfilled,((e,t)=>{e.isLoading=!1,e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.error=null})).addCase(It.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload}))}}),{clearError:Ft,clearAuth:Tt,setCredentials:Dt,updateUser:Wt,incrementLoginAttempts:Bt,resetLoginAttempts:$t}=Rt.actions,qt=St,Vt=Lt,Qt=_t,Ht=Ft,Ut=e=>e.auth.user,Zt=e=>e.auth.isLoading,zt=e=>e.auth.error,Kt=Rt.reducer,Gt={sidebarOpen:!1,sidebarCollapsed:!1,theme:"light",globalLoading:!1,modals:{},notifications:{desktop:!0,email:!0,push:!0},layout:{density:"comfortable",tablePageSize:15,cardView:!1},filters:{},searchQueries:{},recentItems:[],breadcrumbs:[],pageTitle:"Dashboard",errors:{},successMessages:[]},Yt=v({name:"ui",initialState:Gt,reducers:{toggleSidebar:e=>{e.sidebarOpen=!e.sidebarOpen},setSidebarOpen:(e,t)=>{e.sidebarOpen=t.payload},toggleSidebarCollapsed:e=>{e.sidebarCollapsed=!e.sidebarCollapsed},setSidebarCollapsed:(e,t)=>{e.sidebarCollapsed=t.payload},setTheme:(e,t)=>{e.theme=t.payload},toggleTheme:e=>{e.theme="light"===e.theme?"dark":"light"},setGlobalLoading:(e,t)=>{e.globalLoading=t.payload},openModal:(e,t)=>{const{modalId:r,props:a={}}=t.payload;e.modals[r]={isOpen:!0,props:a}},closeModal:(e,t)=>{const r=t.payload;e.modals[r]&&(e.modals[r].isOpen=!1)},closeAllModals:e=>{Object.keys(e.modals).forEach((t=>{e.modals[t].isOpen=!1}))},setNotificationPreference:(e,t)=>{const{type:r,enabled:a}=t.payload;e.notifications[r]=a},setLayoutDensity:(e,t)=>{e.layout.density=t.payload},setTablePageSize:(e,t)=>{e.layout.tablePageSize=t.payload},toggleCardView:e=>{e.layout.cardView=!e.layout.cardView},setCardView:(e,t)=>{e.layout.cardView=t.payload},setFilter:(e,t)=>{const{page:r,filters:a}=t.payload;e.filters[r]={...e.filters[r],...a}},clearFilters:(e,t)=>{const r=t.payload;e.filters[r]={}},setSearchQuery:(e,t)=>{const{page:r,query:a}=t.payload;e.searchQueries[r]=a},clearSearchQuery:(e,t)=>{const r=t.payload;e.searchQueries[r]=""},addRecentItem:(e,t)=>{const r=t.payload,a=e.recentItems.findIndex((e=>e.id===r.id&&e.type===r.type));-1!==a&&e.recentItems.splice(a,1),e.recentItems.unshift(r),e.recentItems.length>10&&(e.recentItems=e.recentItems.slice(0,10))},clearRecentItems:e=>{e.recentItems=[]},setBreadcrumbs:(e,t)=>{e.breadcrumbs=t.payload},addBreadcrumb:(e,t)=>{e.breadcrumbs.push(t.payload)},clearBreadcrumbs:e=>{e.breadcrumbs=[]},setPageTitle:(e,t)=>{e.pageTitle=t.payload},setError:(e,t)=>{const{key:r,error:a}=t.payload;e.errors[r]=a},clearError:(e,t)=>{const r=t.payload;delete e.errors[r]},clearAllErrors:e=>{e.errors={}},addSuccessMessage:(e,t)=>{e.successMessages.push({id:Date.now(),message:t.payload,timestamp:(new Date).toISOString()})},removeSuccessMessage:(e,t)=>{const r=t.payload;e.successMessages=e.successMessages.filter((e=>e.id!==r))},clearSuccessMessages:e=>{e.successMessages=[]},resetUIState:e=>({...Gt,theme:e.theme,notifications:e.notifications,layout:e.layout})}}),{toggleSidebar:Jt,setSidebarOpen:Xt,toggleSidebarCollapsed:er,setSidebarCollapsed:tr,setTheme:rr,toggleTheme:ar,setGlobalLoading:sr,openModal:nr,closeModal:ir,closeAllModals:lr,setNotificationPreference:or,setLayoutDensity:dr,setTablePageSize:cr,toggleCardView:ur,setCardView:mr,setFilter:hr,clearFilters:gr,setSearchQuery:pr,clearSearchQuery:fr,addRecentItem:xr,clearRecentItems:yr,setBreadcrumbs:vr,addBreadcrumb:br,clearBreadcrumbs:wr,setPageTitle:jr,setError:kr,clearError:Cr,clearAllErrors:Nr,addSuccessMessage:Er,removeSuccessMessage:Sr,clearSuccessMessages:Lr,resetUIState:_r}=Yt.actions,Or=Yt.reducer,Pr=e=>e.ui.sidebarOpen,Ar=e=>e.ui.sidebarCollapsed,Mr=e=>e.ui.theme,Ir=e=>e.ui.globalLoading,Rr=y("service/fetchServices",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/services",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Fr=y("service/fetchService",(async(e,{rejectWithValue:t})=>{var r;try{return(await wt(`/services/${e}`)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Tr=y("service/createService",(async(e,{rejectWithValue:t})=>{var r;try{return(await jt("/services",e)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Dr=y("service/updateService",(async({id:e,data:t},{rejectWithValue:r})=>{var a;try{return(await kt(`/services/${e}`,t)).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),Wr=y("service/deleteService",(async(e,{rejectWithValue:t})=>{var r;try{return await Nt(`/services/${e}`),e}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Br=y("service/fetchServiceStats",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/services/stats",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),$r=y("service/assignTechnician",(async({serviceId:e,technicianId:t},{rejectWithValue:r})=>{var a;try{return(await jt(`/services/${e}/assign`,{technician_id:t})).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),qr=y("service/updateServiceStatus",(async({serviceId:e,status:t,notes:r},{rejectWithValue:a})=>{var s;try{return(await Ct(`/services/${e}/status`,{status:t,notes:r})).data}catch(n){return a((null==(s=n.response)?void 0:s.data)||n.message)}})),Vr={services:[],currentService:null,stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,serviceLoading:!1,statsLoading:!1,actionLoading:!1,error:null,serviceError:null,statsError:null,actionError:null,selectedServices:[],filters:{status:"",priority:"",technician_id:"",customer_id:"",service_type:"",date_from:"",date_to:""},sortBy:"created_at",sortOrder:"desc",searchQuery:""},Qr=v({name:"service",initialState:Vr,reducers:{selectService:(e,t)=>{const r=t.payload;e.selectedServices.includes(r)||e.selectedServices.push(r)},deselectService:(e,t)=>{const r=t.payload;e.selectedServices=e.selectedServices.filter((e=>e!==r))},selectAllServices:e=>{e.selectedServices=e.services.map((e=>e.id))},deselectAllServices:e=>{e.selectedServices=[]},setFilter:(e,t)=>{const{key:r,value:a}=t.payload;e.filters[r]=a},setFilters:(e,t)=>{e.filters={...e.filters,...t.payload}},clearFilters:e=>{e.filters=Vr.filters},setSortBy:(e,t)=>{e.sortBy=t.payload},setSortOrder:(e,t)=>{e.sortOrder=t.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,t)=>{e.searchQuery=t.payload},clearSearchQuery:e=>{e.searchQuery=""},clearCurrentService:e=>{e.currentService=null,e.serviceError=null},clearError:e=>{e.error=null},clearServiceError:e=>{e.serviceError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetServiceState:e=>Vr},extraReducers:e=>{e.addCase(Rr.pending,(e=>{e.loading=!0,e.error=null})).addCase(Rr.fulfilled,((e,t)=>{e.loading=!1,e.services=t.payload.data.services,e.pagination=t.payload.data.pagination})).addCase(Rr.rejected,((e,t)=>{e.loading=!1,e.error=t.payload})),e.addCase(Fr.pending,(e=>{e.serviceLoading=!0,e.serviceError=null})).addCase(Fr.fulfilled,((e,t)=>{e.serviceLoading=!1,e.currentService=t.payload.data.service})).addCase(Fr.rejected,((e,t)=>{e.serviceLoading=!1,e.serviceError=t.payload})),e.addCase(Tr.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Tr.fulfilled,((e,t)=>{e.actionLoading=!1,e.services.unshift(t.payload.data.service)})).addCase(Tr.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(Dr.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Dr.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload.data.service,s=e.services.findIndex((e=>e.id===a.id));-1!==s&&(e.services[s]=a),(null==(r=e.currentService)?void 0:r.id)===a.id&&(e.currentService=a)})).addCase(Dr.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(Wr.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Wr.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload;e.services=e.services.filter((e=>e.id!==a)),e.selectedServices=e.selectedServices.filter((e=>e!==a)),(null==(r=e.currentService)?void 0:r.id)===a&&(e.currentService=null)})).addCase(Wr.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(Br.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(Br.fulfilled,((e,t)=>{e.statsLoading=!1,e.stats=t.payload.data})).addCase(Br.rejected,((e,t)=>{e.statsLoading=!1,e.statsError=t.payload})),e.addCase($r.fulfilled,((e,t)=>{var r;const a=t.payload.data.service,s=e.services.findIndex((e=>e.id===a.id));-1!==s&&(e.services[s]=a),(null==(r=e.currentService)?void 0:r.id)===a.id&&(e.currentService=a)})),e.addCase(qr.fulfilled,((e,t)=>{var r;const a=t.payload.data.service,s=e.services.findIndex((e=>e.id===a.id));-1!==s&&(e.services[s]=a),(null==(r=e.currentService)?void 0:r.id)===a.id&&(e.currentService=a)}))}}),{selectService:Hr,deselectService:Ur,selectAllServices:Zr,deselectAllServices:zr,setFilter:Kr,setFilters:Gr,clearFilters:Yr,setSortBy:Jr,setSortOrder:Xr,toggleSortOrder:ea,setSearchQuery:ta,clearSearchQuery:ra,clearCurrentService:aa,clearError:sa,clearServiceError:na,clearStatsError:ia,clearActionError:la,resetServiceState:oa}=Qr.actions,da=Qr.reducer,ca=e=>e.service.services,ua=e=>e.service.pagination,ma=e=>e.service.loading,ha=e=>e.service.error,ga=e=>e.service.selectedServices,pa=e=>e.service.filters,fa=e=>e.service.searchQuery,xa=y("customer/fetchCustomers",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/customers",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ya=y("customer/fetchCustomer",(async(e,{rejectWithValue:t})=>{var r;try{return(await wt(`/customers/${e}`)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),va=y("customer/createCustomer",(async(e,{rejectWithValue:t})=>{var r;try{return(await jt("/customers",e)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ba=y("customer/updateCustomer",(async({id:e,data:t},{rejectWithValue:r})=>{var a;try{return(await kt(`/customers/${e}`,t)).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),wa=y("customer/deleteCustomer",(async(e,{rejectWithValue:t})=>{var r;try{return await Nt(`/customers/${e}`),e}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ja=y("customer/fetchCustomerStats",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/customers/stats",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ka=y("customer/fetchCustomerHistory",(async(e,{rejectWithValue:t})=>{var r;try{return(await wt(`/customers/${e}/history`)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Ca=y("customer/bulkDeleteCustomers",(async(e,{rejectWithValue:t})=>{var r;try{return await Nt("/customers/bulk",{data:{customer_ids:e}}),e}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Na={customers:[],currentCustomer:null,customerHistory:null,stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,customerLoading:!1,historyLoading:!1,statsLoading:!1,actionLoading:!1,error:null,customerError:null,historyError:null,statsError:null,actionError:null,selectedCustomers:[],filters:{status:"",customer_type:"",category_id:"",city:"",state:"",date_from:"",date_to:""},sortBy:"created_at",sortOrder:"desc",searchQuery:""},Ea=v({name:"customer",initialState:Na,reducers:{selectCustomer:(e,t)=>{const r=t.payload;e.selectedCustomers.includes(r)||e.selectedCustomers.push(r)},deselectCustomer:(e,t)=>{const r=t.payload;e.selectedCustomers=e.selectedCustomers.filter((e=>e!==r))},selectAllCustomers:e=>{e.selectedCustomers=e.customers.map((e=>e.id))},deselectAllCustomers:e=>{e.selectedCustomers=[]},setFilter:(e,t)=>{const{key:r,value:a}=t.payload;e.filters[r]=a},setFilters:(e,t)=>{e.filters={...e.filters,...t.payload}},clearFilters:e=>{e.filters=Na.filters},setSortBy:(e,t)=>{e.sortBy=t.payload},setSortOrder:(e,t)=>{e.sortOrder=t.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,t)=>{e.searchQuery=t.payload},clearSearchQuery:e=>{e.searchQuery=""},clearCurrentCustomer:e=>{e.currentCustomer=null,e.customerError=null},clearCustomerHistory:e=>{e.customerHistory=null,e.historyError=null},clearError:e=>{e.error=null},clearCustomerError:e=>{e.customerError=null},clearHistoryError:e=>{e.historyError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetCustomerState:e=>Na},extraReducers:e=>{e.addCase(xa.pending,(e=>{e.loading=!0,e.error=null})).addCase(xa.fulfilled,((e,t)=>{e.loading=!1,e.customers=t.payload.data.customers,e.pagination=t.payload.data.pagination})).addCase(xa.rejected,((e,t)=>{e.loading=!1,e.error=t.payload})),e.addCase(ya.pending,(e=>{e.customerLoading=!0,e.customerError=null})).addCase(ya.fulfilled,((e,t)=>{e.customerLoading=!1,e.currentCustomer=t.payload.data.customer})).addCase(ya.rejected,((e,t)=>{e.customerLoading=!1,e.customerError=t.payload})),e.addCase(va.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(va.fulfilled,((e,t)=>{e.actionLoading=!1,e.customers.unshift(t.payload.data.customer)})).addCase(va.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(ba.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(ba.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload.data.customer,s=e.customers.findIndex((e=>e.id===a.id));-1!==s&&(e.customers[s]=a),(null==(r=e.currentCustomer)?void 0:r.id)===a.id&&(e.currentCustomer=a)})).addCase(ba.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(wa.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(wa.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload;e.customers=e.customers.filter((e=>e.id!==a)),e.selectedCustomers=e.selectedCustomers.filter((e=>e!==a)),(null==(r=e.currentCustomer)?void 0:r.id)===a&&(e.currentCustomer=null)})).addCase(wa.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(ka.pending,(e=>{e.historyLoading=!0,e.historyError=null})).addCase(ka.fulfilled,((e,t)=>{e.historyLoading=!1,e.customerHistory=t.payload.data})).addCase(ka.rejected,((e,t)=>{e.historyLoading=!1,e.historyError=t.payload})),e.addCase(ja.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(ja.fulfilled,((e,t)=>{e.statsLoading=!1,e.stats=t.payload.data})).addCase(ja.rejected,((e,t)=>{e.statsLoading=!1,e.statsError=t.payload})),e.addCase(Ca.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Ca.fulfilled,((e,t)=>{e.actionLoading=!1;const r=t.payload;e.customers=e.customers.filter((e=>!r.includes(e.id))),e.selectedCustomers=e.selectedCustomers.filter((e=>!r.includes(e)))})).addCase(Ca.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload}))}}),{selectCustomer:Sa,deselectCustomer:La,selectAllCustomers:_a,deselectAllCustomers:Oa,setFilter:Pa,setFilters:Aa,clearFilters:Ma,setSortBy:Ia,setSortOrder:Ra,toggleSortOrder:Fa,setSearchQuery:Ta,clearSearchQuery:Da,clearCurrentCustomer:Wa,clearCustomerHistory:Ba,clearError:$a,clearCustomerError:qa,clearHistoryError:Va,clearStatsError:Qa,clearActionError:Ha,resetCustomerState:Ua}=Ea.actions,Za=Ea.reducer,za=e=>e.customer.customers,Ka=e=>e.customer.pagination,Ga=e=>e.customer.loading,Ya=e=>e.customer.error,Ja=e=>e.customer.selectedCustomers,Xa=e=>e.customer.filters,es=e=>e.customer.searchQuery,ts=y("lead/fetchLeads",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/leads",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),rs=y("lead/fetchLead",(async(e,{rejectWithValue:t})=>{var r;try{return(await wt(`/leads/${e}`)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),as=y("lead/createLead",(async(e,{rejectWithValue:t})=>{var r;try{return(await jt("/leads",e)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ss=y("lead/updateLead",(async({id:e,data:t},{rejectWithValue:r})=>{var a;try{return(await kt(`/leads/${e}`,t)).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),ns=y("lead/deleteLead",(async(e,{rejectWithValue:t})=>{var r;try{return await Nt(`/leads/${e}`),e}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),is=y("lead/convertLeadToCustomer",(async(e,{rejectWithValue:t})=>{var r;try{return(await jt(`/leads/${e}/convert`)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ls=y("lead/updateLeadStatus",(async({leadId:e,status:t,notes:r},{rejectWithValue:a})=>{var s;try{return(await Ct(`/leads/${e}/status`,{status:t,notes:r})).data}catch(n){return a((null==(s=n.response)?void 0:s.data)||n.message)}})),os=y("lead/fetchLeadStats",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/leads/stats",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ds={leads:[],currentLead:null,stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,leadLoading:!1,statsLoading:!1,actionLoading:!1,error:null,leadError:null,statsError:null,actionError:null,selectedLeads:[],filters:{status:"",priority:"",source:"",assigned_to:"",date_from:"",date_to:""},sortBy:"created_at",sortOrder:"desc",searchQuery:"",pipeline:{new:0,contacted:0,qualified:0,proposal:0,negotiation:0,won:0,lost:0}},cs=v({name:"lead",initialState:ds,reducers:{selectLead:(e,t)=>{const r=t.payload;e.selectedLeads.includes(r)||e.selectedLeads.push(r)},deselectLead:(e,t)=>{const r=t.payload;e.selectedLeads=e.selectedLeads.filter((e=>e!==r))},selectAllLeads:e=>{e.selectedLeads=e.leads.map((e=>e.id))},deselectAllLeads:e=>{e.selectedLeads=[]},setFilter:(e,t)=>{const{key:r,value:a}=t.payload;e.filters[r]=a},setFilters:(e,t)=>{e.filters={...e.filters,...t.payload}},clearFilters:e=>{e.filters=ds.filters},setSortBy:(e,t)=>{e.sortBy=t.payload},setSortOrder:(e,t)=>{e.sortOrder=t.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,t)=>{e.searchQuery=t.payload},clearSearchQuery:e=>{e.searchQuery=""},clearCurrentLead:e=>{e.currentLead=null,e.leadError=null},updatePipeline:(e,t)=>{e.pipeline={...e.pipeline,...t.payload}},clearError:e=>{e.error=null},clearLeadError:e=>{e.leadError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetLeadState:e=>ds},extraReducers:e=>{e.addCase(ts.pending,(e=>{e.loading=!0,e.error=null})).addCase(ts.fulfilled,((e,t)=>{e.loading=!1,e.leads=t.payload.data.leads,e.pagination=t.payload.data.pagination,t.payload.data.pipeline&&(e.pipeline=t.payload.data.pipeline)})).addCase(ts.rejected,((e,t)=>{e.loading=!1,e.error=t.payload})),e.addCase(rs.pending,(e=>{e.leadLoading=!0,e.leadError=null})).addCase(rs.fulfilled,((e,t)=>{e.leadLoading=!1,e.currentLead=t.payload.data.lead})).addCase(rs.rejected,((e,t)=>{e.leadLoading=!1,e.leadError=t.payload})),e.addCase(as.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(as.fulfilled,((e,t)=>{e.actionLoading=!1,e.leads.unshift(t.payload.data.lead)})).addCase(as.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(ss.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(ss.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload.data.lead,s=e.leads.findIndex((e=>e.id===a.id));-1!==s&&(e.leads[s]=a),(null==(r=e.currentLead)?void 0:r.id)===a.id&&(e.currentLead=a)})).addCase(ss.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(ns.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(ns.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload;e.leads=e.leads.filter((e=>e.id!==a)),e.selectedLeads=e.selectedLeads.filter((e=>e!==a)),(null==(r=e.currentLead)?void 0:r.id)===a&&(e.currentLead=null)})).addCase(ns.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(is.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(is.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload.data.lead,s=e.leads.findIndex((e=>e.id===a.id));-1!==s&&(e.leads[s]=a),(null==(r=e.currentLead)?void 0:r.id)===a.id&&(e.currentLead=a)})).addCase(is.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(ls.fulfilled,((e,t)=>{var r;const a=t.payload.data.lead,s=e.leads.findIndex((e=>e.id===a.id));-1!==s&&(e.leads[s]=a),(null==(r=e.currentLead)?void 0:r.id)===a.id&&(e.currentLead=a)})),e.addCase(os.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(os.fulfilled,((e,t)=>{e.statsLoading=!1,e.stats=t.payload.data,t.payload.data.pipeline&&(e.pipeline=t.payload.data.pipeline)})).addCase(os.rejected,((e,t)=>{e.statsLoading=!1,e.statsError=t.payload}))}}),{selectLead:us,deselectLead:ms,selectAllLeads:hs,deselectAllLeads:gs,setFilter:ps,setFilters:fs,clearFilters:xs,setSortBy:ys,setSortOrder:vs,toggleSortOrder:bs,setSearchQuery:ws,clearSearchQuery:js,clearCurrentLead:ks,updatePipeline:Cs,clearError:Ns,clearLeadError:Es,clearStatsError:Ss,clearActionError:Ls,resetLeadState:_s}=cs.actions,Os=cs.reducer,Ps=y("amc/fetchAMCs",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/amcs",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),As=y("amc/fetchAMC",(async(e,{rejectWithValue:t})=>{var r;try{return(await wt(`/amcs/${e}`)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Ms=y("amc/createAMC",(async(e,{rejectWithValue:t})=>{var r;try{return(await jt("/amcs",e)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Is=y("amc/updateAMC",(async({id:e,data:t},{rejectWithValue:r})=>{var a;try{return(await kt(`/amcs/${e}`,t)).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),Rs=y("amc/deleteAMC",(async(e,{rejectWithValue:t})=>{var r;try{return await Nt(`/amcs/${e}`),e}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Fs=y("amc/renewAMC",(async({amcId:e,renewalData:t},{rejectWithValue:r})=>{var a;try{return(await jt(`/amcs/${e}/renew`,t)).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),Ts=y("amc/fetchAMCStats",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/amcs/stats",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Ds=y("amc/fetchExpiringAMCs",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/amcs/expiring",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Ws={amcs:[],currentAMC:null,expiringAMCs:[],stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,amcLoading:!1,expiringLoading:!1,statsLoading:!1,actionLoading:!1,error:null,amcError:null,expiringError:null,statsError:null,actionError:null,selectedAMCs:[],filters:{status:"",customer_id:"",start_date:"",end_date:"",expiry_from:"",expiry_to:""},sortBy:"created_at",sortOrder:"desc",searchQuery:""},Bs=v({name:"amc",initialState:Ws,reducers:{selectAMC:(e,t)=>{const r=t.payload;e.selectedAMCs.includes(r)||e.selectedAMCs.push(r)},deselectAMC:(e,t)=>{const r=t.payload;e.selectedAMCs=e.selectedAMCs.filter((e=>e!==r))},selectAllAMCs:e=>{e.selectedAMCs=e.amcs.map((e=>e.id))},deselectAllAMCs:e=>{e.selectedAMCs=[]},setFilter:(e,t)=>{const{key:r,value:a}=t.payload;e.filters[r]=a},setFilters:(e,t)=>{e.filters={...e.filters,...t.payload}},clearFilters:e=>{e.filters=Ws.filters},setSortBy:(e,t)=>{e.sortBy=t.payload},setSortOrder:(e,t)=>{e.sortOrder=t.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,t)=>{e.searchQuery=t.payload},clearSearchQuery:e=>{e.searchQuery=""},clearCurrentAMC:e=>{e.currentAMC=null,e.amcError=null},clearError:e=>{e.error=null},clearAMCError:e=>{e.amcError=null},clearExpiringError:e=>{e.expiringError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetAMCState:e=>Ws},extraReducers:e=>{e.addCase(Ps.pending,(e=>{e.loading=!0,e.error=null})).addCase(Ps.fulfilled,((e,t)=>{e.loading=!1,e.amcs=t.payload.data.amcs,e.pagination=t.payload.data.pagination})).addCase(Ps.rejected,((e,t)=>{e.loading=!1,e.error=t.payload})),e.addCase(As.pending,(e=>{e.amcLoading=!0,e.amcError=null})).addCase(As.fulfilled,((e,t)=>{e.amcLoading=!1,e.currentAMC=t.payload.data.amc})).addCase(As.rejected,((e,t)=>{e.amcLoading=!1,e.amcError=t.payload})),e.addCase(Ms.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Ms.fulfilled,((e,t)=>{e.actionLoading=!1,e.amcs.unshift(t.payload.data.amc)})).addCase(Ms.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(Is.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Is.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload.data.amc,s=e.amcs.findIndex((e=>e.id===a.id));-1!==s&&(e.amcs[s]=a),(null==(r=e.currentAMC)?void 0:r.id)===a.id&&(e.currentAMC=a)})).addCase(Is.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(Rs.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Rs.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload;e.amcs=e.amcs.filter((e=>e.id!==a)),e.selectedAMCs=e.selectedAMCs.filter((e=>e!==a)),(null==(r=e.currentAMC)?void 0:r.id)===a&&(e.currentAMC=null)})).addCase(Rs.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(Fs.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Fs.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload.data.amc,s=e.amcs.findIndex((e=>e.id===a.id));-1!==s&&(e.amcs[s]=a),(null==(r=e.currentAMC)?void 0:r.id)===a.id&&(e.currentAMC=a)})).addCase(Fs.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(Ds.pending,(e=>{e.expiringLoading=!0,e.expiringError=null})).addCase(Ds.fulfilled,((e,t)=>{e.expiringLoading=!1,e.expiringAMCs=t.payload.data.amcs||t.payload.data.expiring_amcs})).addCase(Ds.rejected,((e,t)=>{e.expiringLoading=!1,e.expiringError=t.payload})),e.addCase(Ts.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(Ts.fulfilled,((e,t)=>{e.statsLoading=!1,e.stats=t.payload.data})).addCase(Ts.rejected,((e,t)=>{e.statsLoading=!1,e.statsError=t.payload}))}}),{selectAMC:$s,deselectAMC:qs,selectAllAMCs:Vs,deselectAllAMCs:Qs,setFilter:Hs,setFilters:Us,clearFilters:Zs,setSortBy:zs,setSortOrder:Ks,toggleSortOrder:Gs,setSearchQuery:Ys,clearSearchQuery:Js,clearCurrentAMC:Xs,clearError:en,clearAMCError:tn,clearExpiringError:rn,clearStatsError:an,clearActionError:sn,resetAMCState:nn}=Bs.actions,ln=Bs.reducer,on=y("sales/fetchSales",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/sales",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),dn=y("sales/fetchSale",(async(e,{rejectWithValue:t})=>{var r;try{return(await wt(`/sales/${e}`)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),cn=y("sales/createSale",(async(e,{rejectWithValue:t})=>{var r;try{return(await jt("/sales",e)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),un=y("sales/updateSale",(async({id:e,data:t},{rejectWithValue:r})=>{var a;try{return(await kt(`/sales/${e}`,t)).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),mn=y("sales/deleteSale",(async(e,{rejectWithValue:t})=>{var r;try{return await Nt(`/sales/${e}`),e}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),hn=y("sales/fetchSalesStats",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/sales/stats",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),gn=y("sales/generateInvoice",(async(e,{rejectWithValue:t})=>{var r;try{return(await jt(`/sales/${e}/generate-invoice`)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),pn=y("sales/addPayment",(async({saleId:e,paymentData:t},{rejectWithValue:r})=>{var a;try{return(await jt(`/sales/${e}/payments`,t)).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),fn={sales:[],currentSale:null,stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,saleLoading:!1,statsLoading:!1,actionLoading:!1,error:null,saleError:null,statsError:null,actionError:null,selectedSales:[],filters:{status:"",payment_status:"",customer_id:"",sales_person:"",date_from:"",date_to:"",amount_from:"",amount_to:""},sortBy:"sales_date",sortOrder:"desc",searchQuery:"",cart:{items:[],customer:null,discount:0,tax_rate:0,notes:"",payment_terms:"immediate"}},xn=v({name:"sales",initialState:fn,reducers:{selectSale:(e,t)=>{const r=t.payload;e.selectedSales.includes(r)||e.selectedSales.push(r)},deselectSale:(e,t)=>{const r=t.payload;e.selectedSales=e.selectedSales.filter((e=>e!==r))},selectAllSales:e=>{e.selectedSales=e.sales.map((e=>e.id))},deselectAllSales:e=>{e.selectedSales=[]},setFilter:(e,t)=>{const{key:r,value:a}=t.payload;e.filters[r]=a},setFilters:(e,t)=>{e.filters={...e.filters,...t.payload}},clearFilters:e=>{e.filters=fn.filters},setSortBy:(e,t)=>{e.sortBy=t.payload},setSortOrder:(e,t)=>{e.sortOrder=t.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,t)=>{e.searchQuery=t.payload},clearSearchQuery:e=>{e.searchQuery=""},addToCart:(e,t)=>{const r=t.payload,a=e.cart.items.find((e=>e.product_id===r.product_id));a?(a.quantity+=r.quantity||1,a.total_amount=a.quantity*a.unit_price):e.cart.items.push({...r,quantity:r.quantity||1,total_amount:(r.quantity||1)*r.unit_price})},removeFromCart:(e,t)=>{const r=t.payload;e.cart.items=e.cart.items.filter((e=>e.product_id!==r))},updateCartItem:(e,t)=>{const{productId:r,updates:a}=t.payload,s=e.cart.items.find((e=>e.product_id===r));s&&(Object.assign(s,a),s.total_amount=s.quantity*s.unit_price)},setCartCustomer:(e,t)=>{e.cart.customer=t.payload},setCartDiscount:(e,t)=>{e.cart.discount=t.payload},setCartTaxRate:(e,t)=>{e.cart.tax_rate=t.payload},setCartNotes:(e,t)=>{e.cart.notes=t.payload},setCartPaymentTerms:(e,t)=>{e.cart.payment_terms=t.payload},clearCart:e=>{e.cart=fn.cart},clearCurrentSale:e=>{e.currentSale=null,e.saleError=null},clearError:e=>{e.error=null},clearSaleError:e=>{e.saleError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetSalesState:e=>fn},extraReducers:e=>{e.addCase(on.pending,(e=>{e.loading=!0,e.error=null})).addCase(on.fulfilled,((e,t)=>{e.loading=!1,e.sales=t.payload.data.sales,e.pagination=t.payload.data.pagination})).addCase(on.rejected,((e,t)=>{e.loading=!1,e.error=t.payload})),e.addCase(dn.pending,(e=>{e.saleLoading=!0,e.saleError=null})).addCase(dn.fulfilled,((e,t)=>{e.saleLoading=!1,e.currentSale=t.payload.data.sale})).addCase(dn.rejected,((e,t)=>{e.saleLoading=!1,e.saleError=t.payload})),e.addCase(cn.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(cn.fulfilled,((e,t)=>{e.actionLoading=!1,e.sales.unshift(t.payload.data.sale),e.cart=fn.cart})).addCase(cn.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(un.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(un.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload.data.sale,s=e.sales.findIndex((e=>e.id===a.id));-1!==s&&(e.sales[s]=a),(null==(r=e.currentSale)?void 0:r.id)===a.id&&(e.currentSale=a)})).addCase(un.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(mn.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(mn.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload;e.sales=e.sales.filter((e=>e.id!==a)),e.selectedSales=e.selectedSales.filter((e=>e!==a)),(null==(r=e.currentSale)?void 0:r.id)===a&&(e.currentSale=null)})).addCase(mn.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(hn.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(hn.fulfilled,((e,t)=>{e.statsLoading=!1,e.stats=t.payload.data})).addCase(hn.rejected,((e,t)=>{e.statsLoading=!1,e.statsError=t.payload})),e.addCase(gn.fulfilled,((e,t)=>{var r;const a=t.payload.data.sale,s=e.sales.findIndex((e=>e.id===a.id));-1!==s&&(e.sales[s]=a),(null==(r=e.currentSale)?void 0:r.id)===a.id&&(e.currentSale=a)})),e.addCase(pn.fulfilled,((e,t)=>{var r;const a=t.payload.data.sale,s=e.sales.findIndex((e=>e.id===a.id));-1!==s&&(e.sales[s]=a),(null==(r=e.currentSale)?void 0:r.id)===a.id&&(e.currentSale=a)}))}}),{selectSale:yn,deselectSale:vn,selectAllSales:bn,deselectAllSales:wn,setFilter:jn,setFilters:kn,clearFilters:Cn,setSortBy:Nn,setSortOrder:En,toggleSortOrder:Sn,setSearchQuery:Ln,clearSearchQuery:_n,addToCart:On,removeFromCart:Pn,updateCartItem:An,setCartCustomer:Mn,setCartDiscount:In,setCartTaxRate:Rn,setCartNotes:Fn,setCartPaymentTerms:Tn,clearCart:Dn,clearCurrentSale:Wn,clearError:Bn,clearSaleError:$n,clearStatsError:qn,clearActionError:Vn,resetSalesState:Qn}=xn.actions,Hn=xn.reducer,Un=e=>e.sales.sales,Zn=e=>e.sales.pagination,zn=e=>e.sales.loading,Kn=e=>e.sales.error,Gn=e=>e.sales.selectedSales,Yn=e=>e.sales.filters,Jn=e=>e.sales.searchQuery,Xn=y("product/fetchProducts",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/products",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ei=y("product/fetchProduct",(async(e,{rejectWithValue:t})=>{var r;try{return(await wt(`/products/${e}`)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ti=y("product/createProduct",(async(e,{rejectWithValue:t})=>{var r;try{return(await jt("/products",e)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ri=y("product/updateProduct",(async({id:e,data:t},{rejectWithValue:r})=>{var a;try{return(await kt(`/products/${e}`,t)).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),ai=y("product/deleteProduct",(async(e,{rejectWithValue:t})=>{var r;try{return await Nt(`/products/${e}`),e}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),si=y("product/fetchProductStats",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/products/stats",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ni=y("product/fetchLowStockProducts",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/products/low-stock",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ii=y("product/updateStock",(async({productId:e,stockData:t},{rejectWithValue:r})=>{var a;try{return(await jt(`/products/${e}/stock`,t)).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),li=y("product/bulkUpdatePrices",(async({productIds:e,priceData:t},{rejectWithValue:r})=>{var a;try{return(await jt("/products/bulk-update-prices",{product_ids:e,...t})).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),oi={products:[],currentProduct:null,lowStockProducts:[],stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,productLoading:!1,lowStockLoading:!1,statsLoading:!1,actionLoading:!1,error:null,productError:null,lowStockError:null,statsError:null,actionError:null,selectedProducts:[],filters:{category_id:"",brand_id:"",status:"",stock_status:"",price_from:"",price_to:"",warehouse_id:""},sortBy:"product_name",sortOrder:"asc",searchQuery:"",viewMode:"list"},di=v({name:"product",initialState:oi,reducers:{selectProduct:(e,t)=>{const r=t.payload;e.selectedProducts.includes(r)||e.selectedProducts.push(r)},deselectProduct:(e,t)=>{const r=t.payload;e.selectedProducts=e.selectedProducts.filter((e=>e!==r))},selectAllProducts:e=>{e.selectedProducts=e.products.map((e=>e.id))},deselectAllProducts:e=>{e.selectedProducts=[]},setFilter:(e,t)=>{const{key:r,value:a}=t.payload;e.filters[r]=a},setFilters:(e,t)=>{e.filters={...e.filters,...t.payload}},clearFilters:e=>{e.filters=oi.filters},setSortBy:(e,t)=>{e.sortBy=t.payload},setSortOrder:(e,t)=>{e.sortOrder=t.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,t)=>{e.searchQuery=t.payload},clearSearchQuery:e=>{e.searchQuery=""},setViewMode:(e,t)=>{e.viewMode=t.payload},toggleViewMode:e=>{e.viewMode="list"===e.viewMode?"grid":"list"},clearCurrentProduct:e=>{e.currentProduct=null,e.productError=null},clearError:e=>{e.error=null},clearProductError:e=>{e.productError=null},clearLowStockError:e=>{e.lowStockError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetProductState:e=>oi},extraReducers:e=>{e.addCase(Xn.pending,(e=>{e.loading=!0,e.error=null})).addCase(Xn.fulfilled,((e,t)=>{e.loading=!1,e.products=t.payload.data.products,e.pagination=t.payload.data.pagination})).addCase(Xn.rejected,((e,t)=>{e.loading=!1,e.error=t.payload})),e.addCase(ei.pending,(e=>{e.productLoading=!0,e.productError=null})).addCase(ei.fulfilled,((e,t)=>{e.productLoading=!1,e.currentProduct=t.payload.data.product})).addCase(ei.rejected,((e,t)=>{e.productLoading=!1,e.productError=t.payload})),e.addCase(ti.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(ti.fulfilled,((e,t)=>{e.actionLoading=!1,e.products.unshift(t.payload.data.product)})).addCase(ti.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(ri.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(ri.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload.data.product,s=e.products.findIndex((e=>e.id===a.id));-1!==s&&(e.products[s]=a),(null==(r=e.currentProduct)?void 0:r.id)===a.id&&(e.currentProduct=a)})).addCase(ri.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(ai.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(ai.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload;e.products=e.products.filter((e=>e.id!==a)),e.selectedProducts=e.selectedProducts.filter((e=>e!==a)),(null==(r=e.currentProduct)?void 0:r.id)===a&&(e.currentProduct=null)})).addCase(ai.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(ni.pending,(e=>{e.lowStockLoading=!0,e.lowStockError=null})).addCase(ni.fulfilled,((e,t)=>{e.lowStockLoading=!1,e.lowStockProducts=t.payload.data.products||t.payload.data.low_stock_products})).addCase(ni.rejected,((e,t)=>{e.lowStockLoading=!1,e.lowStockError=t.payload})),e.addCase(si.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(si.fulfilled,((e,t)=>{e.statsLoading=!1,e.stats=t.payload.data})).addCase(si.rejected,((e,t)=>{e.statsLoading=!1,e.statsError=t.payload})),e.addCase(ii.fulfilled,((e,t)=>{var r;const a=t.payload.data.product,s=e.products.findIndex((e=>e.id===a.id));-1!==s&&(e.products[s]=a),(null==(r=e.currentProduct)?void 0:r.id)===a.id&&(e.currentProduct=a)})),e.addCase(li.fulfilled,((e,t)=>{t.payload.data.products.forEach((t=>{const r=e.products.findIndex((e=>e.id===t.id));-1!==r&&(e.products[r]=t)}))}))}}),{selectProduct:ci,deselectProduct:ui,selectAllProducts:mi,deselectAllProducts:hi,setFilter:gi,setFilters:pi,clearFilters:fi,setSortBy:xi,setSortOrder:yi,toggleSortOrder:vi,setSearchQuery:bi,clearSearchQuery:wi,setViewMode:ji,toggleViewMode:ki,clearCurrentProduct:Ci,clearError:Ni,clearProductError:Ei,clearLowStockError:Si,clearStatsError:Li,clearActionError:_i,resetProductState:Oi}=di.actions,Pi=ii,Ai=di.reducer,Mi=e=>e.product.products,Ii=e=>e.product.pagination,Ri=e=>e.product.loading,Fi=e=>e.product.error,Ti=e=>e.product.selectedProducts,Di=e=>e.product.filters,Wi=e=>e.product.searchQuery,Bi=y("estimation/fetchEstimations",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/estimations",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),$i=y("estimation/fetchEstimation",(async(e,{rejectWithValue:t})=>{var r;try{return(await wt(`/estimations/${e}`)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),qi=y("estimation/createEstimation",(async(e,{rejectWithValue:t})=>{var r;try{return(await jt("/estimations",e)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Vi=y("estimation/updateEstimation",(async({id:e,data:t},{rejectWithValue:r})=>{var a;try{return(await kt(`/estimations/${e}`,t)).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),Qi=y("estimation/deleteEstimation",(async(e,{rejectWithValue:t})=>{var r;try{return await Nt(`/estimations/${e}`),e}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Hi=y("estimation/convertToSale",(async(e,{rejectWithValue:t})=>{var r;try{return(await jt(`/estimations/${e}/convert-to-sale`)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Ui=y("estimation/sendEstimation",(async({estimationId:e,sendData:t},{rejectWithValue:r})=>{var a;try{return(await jt(`/estimations/${e}/send`,t)).data}catch(s){return r((null==(a=s.response)?void 0:a.data)||s.message)}})),Zi=y("estimation/fetchEstimationStats",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/estimations/stats",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),zi={estimations:[],currentEstimation:null,stats:null,pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},loading:!1,estimationLoading:!1,statsLoading:!1,actionLoading:!1,error:null,estimationError:null,statsError:null,actionError:null,selectedEstimations:[],filters:{status:"",customer_id:"",date_from:"",date_to:"",amount_from:"",amount_to:"",valid_until_from:"",valid_until_to:""},sortBy:"created_at",sortOrder:"desc",searchQuery:"",builder:{customer:null,items:[],discount:0,tax_rate:0,notes:"",terms_conditions:"",valid_until:null}},Ki=v({name:"estimation",initialState:zi,reducers:{selectEstimation:(e,t)=>{const r=t.payload;e.selectedEstimations.includes(r)||e.selectedEstimations.push(r)},deselectEstimation:(e,t)=>{const r=t.payload;e.selectedEstimations=e.selectedEstimations.filter((e=>e!==r))},selectAllEstimations:e=>{e.selectedEstimations=e.estimations.map((e=>e.id))},deselectAllEstimations:e=>{e.selectedEstimations=[]},setFilter:(e,t)=>{const{key:r,value:a}=t.payload;e.filters[r]=a},setFilters:(e,t)=>{e.filters={...e.filters,...t.payload}},clearFilters:e=>{e.filters=zi.filters},setSortBy:(e,t)=>{e.sortBy=t.payload},setSortOrder:(e,t)=>{e.sortOrder=t.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},setSearchQuery:(e,t)=>{e.searchQuery=t.payload},clearSearchQuery:e=>{e.searchQuery=""},setBuilderCustomer:(e,t)=>{e.builder.customer=t.payload},addBuilderItem:(e,t)=>{const r=t.payload,a=e.builder.items.find((e=>e.product_id===r.product_id));a?(a.quantity+=r.quantity||1,a.total_amount=a.quantity*a.unit_price):e.builder.items.push({...r,quantity:r.quantity||1,total_amount:(r.quantity||1)*r.unit_price})},removeBuilderItem:(e,t)=>{const r=t.payload;e.builder.items=e.builder.items.filter((e=>e.product_id!==r))},updateBuilderItem:(e,t)=>{const{productId:r,updates:a}=t.payload,s=e.builder.items.find((e=>e.product_id===r));s&&(Object.assign(s,a),s.total_amount=s.quantity*s.unit_price)},setBuilderDiscount:(e,t)=>{e.builder.discount=t.payload},setBuilderTaxRate:(e,t)=>{e.builder.tax_rate=t.payload},setBuilderNotes:(e,t)=>{e.builder.notes=t.payload},setBuilderTermsConditions:(e,t)=>{e.builder.terms_conditions=t.payload},setBuilderValidUntil:(e,t)=>{e.builder.valid_until=t.payload},clearBuilder:e=>{e.builder=zi.builder},clearCurrentEstimation:e=>{e.currentEstimation=null,e.estimationError=null},clearError:e=>{e.error=null},clearEstimationError:e=>{e.estimationError=null},clearStatsError:e=>{e.statsError=null},clearActionError:e=>{e.actionError=null},resetEstimationState:e=>zi},extraReducers:e=>{e.addCase(Bi.pending,(e=>{e.loading=!0,e.error=null})).addCase(Bi.fulfilled,((e,t)=>{e.loading=!1,e.estimations=t.payload.data.estimations,e.pagination=t.payload.data.pagination})).addCase(Bi.rejected,((e,t)=>{e.loading=!1,e.error=t.payload})),e.addCase($i.pending,(e=>{e.estimationLoading=!0,e.estimationError=null})).addCase($i.fulfilled,((e,t)=>{e.estimationLoading=!1,e.currentEstimation=t.payload.data.estimation})).addCase($i.rejected,((e,t)=>{e.estimationLoading=!1,e.estimationError=t.payload})),e.addCase(qi.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(qi.fulfilled,((e,t)=>{e.actionLoading=!1,e.estimations.unshift(t.payload.data.estimation),e.builder=zi.builder})).addCase(qi.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(Vi.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Vi.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload.data.estimation,s=e.estimations.findIndex((e=>e.id===a.id));-1!==s&&(e.estimations[s]=a),(null==(r=e.currentEstimation)?void 0:r.id)===a.id&&(e.currentEstimation=a)})).addCase(Vi.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(Qi.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(Qi.fulfilled,((e,t)=>{var r;e.actionLoading=!1;const a=t.payload;e.estimations=e.estimations.filter((e=>e.id!==a)),e.selectedEstimations=e.selectedEstimations.filter((e=>e!==a)),(null==(r=e.currentEstimation)?void 0:r.id)===a&&(e.currentEstimation=null)})).addCase(Qi.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(Hi.fulfilled,((e,t)=>{var r;const a=t.payload.data.estimation,s=e.estimations.findIndex((e=>e.id===a.id));-1!==s&&(e.estimations[s]=a),(null==(r=e.currentEstimation)?void 0:r.id)===a.id&&(e.currentEstimation=a)})),e.addCase(Ui.fulfilled,((e,t)=>{var r;const a=t.payload.data.estimation,s=e.estimations.findIndex((e=>e.id===a.id));-1!==s&&(e.estimations[s]=a),(null==(r=e.currentEstimation)?void 0:r.id)===a.id&&(e.currentEstimation=a)})),e.addCase(Zi.pending,(e=>{e.statsLoading=!0,e.statsError=null})).addCase(Zi.fulfilled,((e,t)=>{e.statsLoading=!1,e.stats=t.payload.data})).addCase(Zi.rejected,((e,t)=>{e.statsLoading=!1,e.statsError=t.payload}))}}),{selectEstimation:Gi,deselectEstimation:Yi,selectAllEstimations:Ji,deselectAllEstimations:Xi,setFilter:el,setFilters:tl,clearFilters:rl,setSortBy:al,setSortOrder:sl,toggleSortOrder:nl,setSearchQuery:il,clearSearchQuery:ll,setBuilderCustomer:ol,addBuilderItem:dl,removeBuilderItem:cl,updateBuilderItem:ul,setBuilderDiscount:ml,setBuilderTaxRate:hl,setBuilderNotes:gl,setBuilderTermsConditions:pl,setBuilderValidUntil:fl,clearBuilder:xl,clearCurrentEstimation:yl,clearError:vl,clearEstimationError:bl,clearStatsError:wl,clearActionError:jl,resetEstimationState:kl}=Ki.actions,Cl=Ki.reducer,Nl=y("dashboard/fetchDashboardOverview",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/dashboard/overview",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),El=y("dashboard/fetchRecentActivities",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/dashboard/activities",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Sl=y("dashboard/fetchSalesDashboard",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/dashboard/sales",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Ll=y("dashboard/fetchFinancialDashboard",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/dashboard/financial",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),_l=y("dashboard/fetchInventoryDashboard",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/dashboard/inventory",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Ol=y("dashboard/fetchTopCustomers",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/dashboard/top-customers",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Pl=y("dashboard/fetchPaymentDashboard",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/dashboard/payments",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Al=y("dashboard/fetchServiceStatusDistribution",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/dashboard/status-distribution",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Ml=y("dashboard/fetchServiceTrends",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/dashboard/trends",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),Il={overview:null,recentActivities:[],salesData:null,financialData:null,inventoryData:null,topCustomers:[],paymentData:null,serviceStatusDistribution:null,serviceTrends:null,overviewLoading:!1,activitiesLoading:!1,salesLoading:!1,financialLoading:!1,inventoryLoading:!1,customersLoading:!1,paymentsLoading:!1,statusLoading:!1,trendsLoading:!1,overviewError:null,activitiesError:null,salesError:null,financialError:null,inventoryError:null,customersError:null,paymentsError:null,statusError:null,trendsError:null,selectedDateRange:{start:null,end:null},refreshInterval:3e5,autoRefresh:!0,widgetLayout:[{id:"overview",enabled:!0,order:1},{id:"sales",enabled:!0,order:2},{id:"financial",enabled:!0,order:3},{id:"inventory",enabled:!0,order:4},{id:"customers",enabled:!0,order:5},{id:"payments",enabled:!0,order:6},{id:"activities",enabled:!0,order:7},{id:"status",enabled:!0,order:8},{id:"trends",enabled:!0,order:9}],lastRefresh:null},Rl=v({name:"dashboard",initialState:Il,reducers:{setDateRange:(e,t)=>{e.selectedDateRange=t.payload},clearDateRange:e=>{e.selectedDateRange={start:null,end:null}},setAutoRefresh:(e,t)=>{e.autoRefresh=t.payload},setRefreshInterval:(e,t)=>{e.refreshInterval=t.payload},updateLastRefresh:e=>{e.lastRefresh=(new Date).toISOString()},updateWidgetLayout:(e,t)=>{e.widgetLayout=t.payload},toggleWidget:(e,t)=>{const r=t.payload,a=e.widgetLayout.find((e=>e.id===r));a&&(a.enabled=!a.enabled)},reorderWidgets:(e,t)=>{const{sourceIndex:r,destinationIndex:a}=t.payload,[s]=e.widgetLayout.splice(r,1);e.widgetLayout.splice(a,0,s),e.widgetLayout.forEach(((e,t)=>{e.order=t+1}))},clearOverviewError:e=>{e.overviewError=null},clearActivitiesError:e=>{e.activitiesError=null},clearSalesError:e=>{e.salesError=null},clearFinancialError:e=>{e.financialError=null},clearInventoryError:e=>{e.inventoryError=null},clearCustomersError:e=>{e.customersError=null},clearPaymentsError:e=>{e.paymentsError=null},clearStatusError:e=>{e.statusError=null},clearTrendsError:e=>{e.trendsError=null},clearAllErrors:e=>{e.overviewError=null,e.activitiesError=null,e.salesError=null,e.financialError=null,e.inventoryError=null,e.customersError=null,e.paymentsError=null,e.statusError=null,e.trendsError=null},resetDashboardState:e=>({...Il,widgetLayout:e.widgetLayout,autoRefresh:e.autoRefresh,refreshInterval:e.refreshInterval})},extraReducers:e=>{e.addCase(Nl.pending,(e=>{e.overviewLoading=!0,e.overviewError=null})).addCase(Nl.fulfilled,((e,t)=>{e.overviewLoading=!1,e.overview=t.payload.data,e.lastRefresh=(new Date).toISOString()})).addCase(Nl.rejected,((e,t)=>{e.overviewLoading=!1,e.overviewError=t.payload})),e.addCase(El.pending,(e=>{e.activitiesLoading=!0,e.activitiesError=null})).addCase(El.fulfilled,((e,t)=>{e.activitiesLoading=!1,e.recentActivities=t.payload.data.activities||t.payload.data.recent_services||[]})).addCase(El.rejected,((e,t)=>{e.activitiesLoading=!1,e.activitiesError=t.payload})),e.addCase(Sl.pending,(e=>{e.salesLoading=!0,e.salesError=null})).addCase(Sl.fulfilled,((e,t)=>{e.salesLoading=!1,e.salesData=t.payload.data})).addCase(Sl.rejected,((e,t)=>{e.salesLoading=!1,e.salesError=t.payload})),e.addCase(Ll.pending,(e=>{e.financialLoading=!0,e.financialError=null})).addCase(Ll.fulfilled,((e,t)=>{e.financialLoading=!1,e.financialData=t.payload.data})).addCase(Ll.rejected,((e,t)=>{e.financialLoading=!1,e.financialError=t.payload})),e.addCase(_l.pending,(e=>{e.inventoryLoading=!0,e.inventoryError=null})).addCase(_l.fulfilled,((e,t)=>{e.inventoryLoading=!1,e.inventoryData=t.payload.data})).addCase(_l.rejected,((e,t)=>{e.inventoryLoading=!1,e.inventoryError=t.payload})),e.addCase(Ol.pending,(e=>{e.customersLoading=!0,e.customersError=null})).addCase(Ol.fulfilled,((e,t)=>{e.customersLoading=!1,e.topCustomers=t.payload.data.top_customers||[]})).addCase(Ol.rejected,((e,t)=>{e.customersLoading=!1,e.customersError=t.payload})),e.addCase(Pl.pending,(e=>{e.paymentsLoading=!0,e.paymentsError=null})).addCase(Pl.fulfilled,((e,t)=>{e.paymentsLoading=!1,e.paymentData=t.payload.data})).addCase(Pl.rejected,((e,t)=>{e.paymentsLoading=!1,e.paymentsError=t.payload})),e.addCase(Al.pending,(e=>{e.statusLoading=!0,e.statusError=null})).addCase(Al.fulfilled,((e,t)=>{e.statusLoading=!1,e.serviceStatusDistribution=t.payload.data})).addCase(Al.rejected,((e,t)=>{e.statusLoading=!1,e.statusError=t.payload})),e.addCase(Ml.pending,(e=>{e.trendsLoading=!0,e.trendsError=null})).addCase(Ml.fulfilled,((e,t)=>{e.trendsLoading=!1,e.serviceTrends=t.payload.data})).addCase(Ml.rejected,((e,t)=>{e.trendsLoading=!1,e.trendsError=t.payload}))}}),{setDateRange:Fl,clearDateRange:Tl,setAutoRefresh:Dl,setRefreshInterval:Wl,updateLastRefresh:Bl,updateWidgetLayout:$l,toggleWidget:ql,reorderWidgets:Vl,clearOverviewError:Ql,clearActivitiesError:Hl,clearSalesError:Ul,clearFinancialError:Zl,clearInventoryError:zl,clearCustomersError:Kl,clearPaymentsError:Gl,clearStatusError:Yl,clearTrendsError:Jl,clearAllErrors:Xl,resetDashboardState:eo}=Rl.actions,to=Rl.reducer,ro=e=>e.dashboard.overview,ao=e=>e.dashboard.recentActivities,so=e=>e.dashboard.salesData,no=e=>e.dashboard.financialData,io=e=>e.dashboard.inventoryData,lo=e=>e.dashboard.topCustomers,oo=e=>({overview:e.dashboard.overviewLoading,activities:e.dashboard.activitiesLoading,sales:e.dashboard.salesLoading,financial:e.dashboard.financialLoading,inventory:e.dashboard.inventoryLoading,customers:e.dashboard.customersLoading,payments:e.dashboard.paymentsLoading,status:e.dashboard.statusLoading,trends:e.dashboard.trendsLoading}),co=y("notification/fetchNotifications",(async(e={},{rejectWithValue:t})=>{var r;try{return(await wt("/notifications",{params:e})).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),uo=y("notification/markAsRead",(async(e,{rejectWithValue:t})=>{var r;try{return(await Ct(`/notifications/${e}/read`)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),mo=y("notification/markAllAsRead",(async(e,{rejectWithValue:t})=>{var r;try{return(await Ct("/notifications/mark-all-read")).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),ho=y("notification/deleteNotification",(async(e,{rejectWithValue:t})=>{var r;try{return await Nt(`/notifications/${e}`),e}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),go=y("notification/fetchNotificationSettings",(async(e,{rejectWithValue:t})=>{var r;try{return(await wt("/notifications/settings")).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),po=y("notification/updateNotificationSettings",(async(e,{rejectWithValue:t})=>{var r;try{return(await kt("/notifications/settings",e)).data}catch(a){return t((null==(r=a.response)?void 0:r.data)||a.message)}})),fo={notifications:[],settings:{email_notifications:!0,push_notifications:!0,sms_notifications:!1,desktop_notifications:!0,notification_types:{service_updates:!0,payment_reminders:!0,system_alerts:!0,marketing:!1}},pagination:{current_page:1,total_pages:1,total_items:0,items_per_page:15,has_next_page:!1,has_prev_page:!1},unreadCount:0,totalCount:0,loading:!1,settingsLoading:!1,actionLoading:!1,error:null,settingsError:null,actionError:null,showNotificationPanel:!1,filters:{type:"",read_status:"",date_from:"",date_to:""},sortBy:"created_at",sortOrder:"desc"},xo=v({name:"notification",initialState:fo,reducers:{toggleNotificationPanel:e=>{e.showNotificationPanel=!e.showNotificationPanel},setNotificationPanel:(e,t)=>{e.showNotificationPanel=t.payload},setFilter:(e,t)=>{const{key:r,value:a}=t.payload;e.filters[r]=a},setFilters:(e,t)=>{e.filters={...e.filters,...t.payload}},clearFilters:e=>{e.filters=fo.filters},setSortBy:(e,t)=>{e.sortBy=t.payload},setSortOrder:(e,t)=>{e.sortOrder=t.payload},toggleSortOrder:e=>{e.sortOrder="asc"===e.sortOrder?"desc":"asc"},addNotification:(e,t)=>{const r={id:Date.now(),...t.payload,created_at:(new Date).toISOString(),is_read:!1};e.notifications.unshift(r),e.unreadCount+=1,e.totalCount+=1},removeNotification:(e,t)=>{const r=t.payload,a=e.notifications.find((e=>e.id===r));a&&!a.is_read&&(e.unreadCount-=1),e.notifications=e.notifications.filter((e=>e.id!==r)),e.totalCount-=1},markNotificationAsRead:(e,t)=>{const r=t.payload,a=e.notifications.find((e=>e.id===r));a&&!a.is_read&&(a.is_read=!0,e.unreadCount-=1)},updateLocalSettings:(e,t)=>{e.settings={...e.settings,...t.payload}},clearError:e=>{e.error=null},clearSettingsError:e=>{e.settingsError=null},clearActionError:e=>{e.actionError=null},resetNotificationState:e=>fo},extraReducers:e=>{e.addCase(co.pending,(e=>{e.loading=!0,e.error=null})).addCase(co.fulfilled,((e,t)=>{e.loading=!1,e.notifications=t.payload.data.notifications,e.pagination=t.payload.data.pagination,e.unreadCount=t.payload.data.unread_count||0,e.totalCount=t.payload.data.total_count||0})).addCase(co.rejected,((e,t)=>{e.loading=!1,e.error=t.payload})),e.addCase(uo.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(uo.fulfilled,((e,t)=>{e.actionLoading=!1;const r=t.meta.arg,a=e.notifications.find((e=>e.id===r));a&&!a.is_read&&(a.is_read=!0,e.unreadCount-=1)})).addCase(uo.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(mo.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(mo.fulfilled,(e=>{e.actionLoading=!1,e.notifications.forEach((e=>{e.is_read=!0})),e.unreadCount=0})).addCase(mo.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(ho.pending,(e=>{e.actionLoading=!0,e.actionError=null})).addCase(ho.fulfilled,((e,t)=>{e.actionLoading=!1;const r=t.payload,a=e.notifications.find((e=>e.id===r));a&&!a.is_read&&(e.unreadCount-=1),e.notifications=e.notifications.filter((e=>e.id!==r)),e.totalCount-=1})).addCase(ho.rejected,((e,t)=>{e.actionLoading=!1,e.actionError=t.payload})),e.addCase(go.pending,(e=>{e.settingsLoading=!0,e.settingsError=null})).addCase(go.fulfilled,((e,t)=>{e.settingsLoading=!1,e.settings=t.payload.data.settings})).addCase(go.rejected,((e,t)=>{e.settingsLoading=!1,e.settingsError=t.payload})),e.addCase(po.pending,(e=>{e.settingsLoading=!0,e.settingsError=null})).addCase(po.fulfilled,((e,t)=>{e.settingsLoading=!1,e.settings=t.payload.data.settings})).addCase(po.rejected,((e,t)=>{e.settingsLoading=!1,e.settingsError=t.payload}))}}),{toggleNotificationPanel:yo,setNotificationPanel:vo,setFilter:bo,setFilters:wo,clearFilters:jo,setSortBy:ko,setSortOrder:Co,toggleSortOrder:No,addNotification:Eo,removeNotification:So,markNotificationAsRead:Lo,updateLocalSettings:_o,clearError:Oo,clearSettingsError:Po,clearActionError:Ao,resetNotificationState:Mo}=xo.actions,Io=xo.reducer,Ro=e=>e.notification.notifications,Fo=e=>e.notification.unreadCount,To=e=>e.notification.loading,Do=e=>e.notification.showNotificationPanel,Wo={key:"root",storage:ve,whitelist:["auth","ui"],blacklist:["service","customer","lead","amc","sales","product","estimation","dashboard","notification"]},Bo=w({auth:ne({key:"auth",storage:ve,whitelist:["user","token","isAuthenticated"]},Kt),ui:Or,service:da,customer:Za,lead:Os,amc:ln,sales:Hn,product:Ai,estimation:Cl,dashboard:to,notification:Io}),$o=b({reducer:ne(Wo,Bo),middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE"],ignoredPaths:["register"]}}),devTools:!1});var qo,Vo,Qo,Ho;function Uo(e,t){return(Uo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Zo(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Uo(e,t)}qo=$o,Vo=x(ue,ce,void 0),Qo=function(e){Vo.dispatch({type:H,key:e})},Ho=function(e,t,r){var a={type:$,payload:t,err:r,key:e};qo.dispatch(a),Vo.dispatch(a)},oe({},Vo,{purge:function(){var e=[];return qo.dispatch({type:Q,result:function(t){e.push(t)}}),Promise.all(e)},flush:function(){var e=[];return qo.dispatch({type:B,result:function(t){e.push(t)}}),Promise.all(e)},pause:function(){qo.dispatch({type:q})},persist:function(){qo.dispatch({type:V,register:Qo,rehydrate:Ho})}}).persist();var zo=function(){function e(){this.listeners=[]}var t=e.prototype;return t.subscribe=function(e){var t=this,r=e||function(){};return this.listeners.push(r),this.onSubscribe(),function(){t.listeners=t.listeners.filter((function(e){return e!==r})),t.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},e}();function Ko(){return Ko=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},Ko.apply(null,arguments)}var Go="undefined"==typeof window;function Yo(){}function Jo(e){return Array.isArray(e)?e:[e]}function Xo(e,t,r){return cd(e)?"function"==typeof t?Ko({},r,{queryKey:e,queryFn:t}):Ko({},t,{queryKey:e}):e}function ed(e,t,r){return cd(e)?[Ko({},t,{queryKey:e}),r]:[e||{},t]}function td(e,t){var r=e.active,a=e.exact,s=e.fetching,n=e.inactive,i=e.predicate,l=e.queryKey,o=e.stale;if(cd(l))if(a){if(t.queryHash!==ad(l,t.options))return!1}else if(!nd(t.queryKey,l))return!1;var d=function(e,t){return!0===e&&!0===t||null==e&&null==t?"all":!1===e&&!1===t?"none":(null!=e?e:!t)?"active":"inactive"}(r,n);if("none"===d)return!1;if("all"!==d){var c=t.isActive();if("active"===d&&!c)return!1;if("inactive"===d&&c)return!1}return("boolean"!=typeof o||t.isStale()===o)&&(("boolean"!=typeof s||t.isFetching()===s)&&!(i&&!i(t)))}function rd(e,t){var r=e.exact,a=e.fetching,s=e.predicate,n=e.mutationKey;if(cd(n)){if(!t.options.mutationKey)return!1;if(r){if(sd(t.options.mutationKey)!==sd(n))return!1}else if(!nd(t.options.mutationKey,n))return!1}return("boolean"!=typeof a||"loading"===t.state.status===a)&&!(s&&!s(t))}function ad(e,t){return((null==t?void 0:t.queryKeyHashFn)||sd)(e)}function sd(e){var t,r=Jo(e);return t=r,JSON.stringify(t,(function(e,t){return od(t)?Object.keys(t).sort().reduce((function(e,r){return e[r]=t[r],e}),{}):t}))}function nd(e,t){return id(Jo(e),Jo(t))}function id(e,t){return e===t||typeof e==typeof t&&(!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((function(r){return!id(e[r],t[r])})))}function ld(e,t){if(e===t)return e;var r=Array.isArray(e)&&Array.isArray(t);if(r||od(e)&&od(t)){for(var a=r?e.length:Object.keys(e).length,s=r?t:Object.keys(t),n=s.length,i=r?[]:{},l=0,o=0;o<n;o++){var d=r?o:s[o];i[d]=ld(e[d],t[d]),i[d]===e[d]&&l++}return a===n&&l===a?e:i}return t}function od(e){if(!dd(e))return!1;var t=e.constructor;if(void 0===t)return!0;var r=t.prototype;return!!dd(r)&&!!r.hasOwnProperty("isPrototypeOf")}function dd(e){return"[object Object]"===Object.prototype.toString.call(e)}function cd(e){return"string"==typeof e||Array.isArray(e)}function ud(e){Promise.resolve().then(e).catch((function(e){return setTimeout((function(){throw e}))}))}function md(){if("function"==typeof AbortController)return new AbortController}var hd=new(function(e){function t(){var t;return(t=e.call(this)||this).setup=function(e){var t;if(!Go&&(null==(t=window)?void 0:t.addEventListener)){var r=function(){return e()};return window.addEventListener("visibilitychange",r,!1),window.addEventListener("focus",r,!1),function(){window.removeEventListener("visibilitychange",r),window.removeEventListener("focus",r)}}},t}Zo(t,e);var r=t.prototype;return r.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},r.onUnsubscribe=function(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)},r.setEventListener=function(e){var t,r=this;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e((function(e){"boolean"==typeof e?r.setFocused(e):r.onFocus()}))},r.setFocused=function(e){this.focused=e,e&&this.onFocus()},r.onFocus=function(){this.listeners.forEach((function(e){e()}))},r.isFocused=function(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)},t}(zo)),gd=new(function(e){function t(){var t;return(t=e.call(this)||this).setup=function(e){var t;if(!Go&&(null==(t=window)?void 0:t.addEventListener)){var r=function(){return e()};return window.addEventListener("online",r,!1),window.addEventListener("offline",r,!1),function(){window.removeEventListener("online",r),window.removeEventListener("offline",r)}}},t}Zo(t,e);var r=t.prototype;return r.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},r.onUnsubscribe=function(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)},r.setEventListener=function(e){var t,r=this;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e((function(e){"boolean"==typeof e?r.setOnline(e):r.onOnline()}))},r.setOnline=function(e){this.online=e,e&&this.onOnline()},r.onOnline=function(){this.listeners.forEach((function(e){e()}))},r.isOnline=function(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine},t}(zo));function pd(e){return Math.min(1e3*Math.pow(2,e),3e4)}function fd(e){return"function"==typeof(null==e?void 0:e.cancel)}var xd=function(e){this.revert=null==e?void 0:e.revert,this.silent=null==e?void 0:e.silent};function yd(e){return e instanceof xd}var vd=function(e){var t,r,a,s,n=this,i=!1;this.abort=e.abort,this.cancel=function(e){return null==t?void 0:t(e)},this.cancelRetry=function(){i=!0},this.continueRetry=function(){i=!1},this.continue=function(){return null==r?void 0:r()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise((function(e,t){a=e,s=t}));var l=function(t){n.isResolved||(n.isResolved=!0,null==e.onSuccess||e.onSuccess(t),null==r||r(),a(t))},o=function(t){n.isResolved||(n.isResolved=!0,null==e.onError||e.onError(t),null==r||r(),s(t))};!function a(){if(!n.isResolved){var s;try{s=e.fn()}catch(d){s=Promise.reject(d)}t=function(e){if(!n.isResolved&&(o(new xd(e)),null==n.abort||n.abort(),fd(s)))try{s.cancel()}catch(t){}},n.isTransportCancelable=fd(s),Promise.resolve(s).then(l).catch((function(t){var s,l;if(!n.isResolved){var d,c=null!=(s=e.retry)?s:3,u=null!=(l=e.retryDelay)?l:pd,m="function"==typeof u?u(n.failureCount,t):u,h=!0===c||"number"==typeof c&&n.failureCount<c||"function"==typeof c&&c(n.failureCount,t);if(!i&&h)n.failureCount++,null==e.onFail||e.onFail(n.failureCount,t),(d=m,new Promise((function(e){setTimeout(e,d)}))).then((function(){if(!hd.isFocused()||!gd.isOnline())return new Promise((function(t){r=t,n.isPaused=!0,null==e.onPause||e.onPause()})).then((function(){r=void 0,n.isPaused=!1,null==e.onContinue||e.onContinue()}))})).then((function(){i?o(t):a()}));else o(t)}}))}}()},bd=new(function(){function e(){this.queue=[],this.transactions=0,this.notifyFn=function(e){e()},this.batchNotifyFn=function(e){e()}}var t=e.prototype;return t.batch=function(e){var t;this.transactions++;try{t=e()}finally{this.transactions--,this.transactions||this.flush()}return t},t.schedule=function(e){var t=this;this.transactions?this.queue.push(e):ud((function(){t.notifyFn(e)}))},t.batchCalls=function(e){var t=this;return function(){for(var r=arguments.length,a=new Array(r),s=0;s<r;s++)a[s]=arguments[s];t.schedule((function(){e.apply(void 0,a)}))}},t.flush=function(){var e=this,t=this.queue;this.queue=[],t.length&&ud((function(){e.batchNotifyFn((function(){t.forEach((function(t){e.notifyFn(t)}))}))}))},t.setNotifyFunction=function(e){this.notifyFn=e},t.setBatchNotifyFunction=function(e){this.batchNotifyFn=e},e}()),wd=console;function jd(){return wd}var kd=function(){function e(e){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.cache=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.initialState=e.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=e.meta,this.scheduleGc()}var t=e.prototype;return t.setOptions=function(e){var t;this.options=Ko({},this.defaultOptions,e),this.meta=null==e?void 0:e.meta,this.cacheTime=Math.max(this.cacheTime||0,null!=(t=this.options.cacheTime)?t:3e5)},t.setDefaultOptions=function(e){this.defaultOptions=e},t.scheduleGc=function(){var e,t=this;this.clearGcTimeout(),"number"==typeof(e=this.cacheTime)&&e>=0&&e!==1/0&&(this.gcTimeout=setTimeout((function(){t.optionalRemove()}),this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(e,t){var r,a,s=this.state.data,n=function(e,t){return"function"==typeof e?e(t):e}(e,s);return(null==(r=(a=this.options).isDataEqual)?void 0:r.call(a,s,n))?n=s:!1!==this.options.structuralSharing&&(n=ld(s,n)),this.dispatch({data:n,type:"success",dataUpdatedAt:null==t?void 0:t.updatedAt}),n},t.setState=function(e,t){this.dispatch({type:"setState",state:e,setStateOptions:t})},t.cancel=function(e){var t,r=this.promise;return null==(t=this.retryer)||t.cancel(e),r?r.then(Yo).catch(Yo):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some((function(e){return!1!==e.options.enabled}))},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some((function(e){return e.getCurrentResult().isStale}))},t.isStaleByTime=function(e){return void 0===e&&(e=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!function(e,t){return Math.max(e+(t||0)-Date.now(),0)}(this.state.dataUpdatedAt,e)},t.onFocus=function(){var e,t=this.observers.find((function(e){return e.shouldFetchOnWindowFocus()}));t&&t.refetch(),null==(e=this.retryer)||e.continue()},t.onOnline=function(){var e,t=this.observers.find((function(e){return e.shouldFetchOnReconnect()}));t&&t.refetch(),null==(e=this.retryer)||e.continue()},t.addObserver=function(e){-1===this.observers.indexOf(e)&&(this.observers.push(e),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:e}))},t.removeObserver=function(e){-1!==this.observers.indexOf(e)&&(this.observers=this.observers.filter((function(t){return t!==e})),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:e}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(e,t){var r,a,s,n=this;if(this.state.isFetching)if(this.state.dataUpdatedAt&&(null==t?void 0:t.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var i;return null==(i=this.retryer)||i.continueRetry(),this.promise}if(e&&this.setOptions(e),!this.options.queryFn){var l=this.observers.find((function(e){return e.options.queryFn}));l&&this.setOptions(l.options)}var o=Jo(this.queryKey),d=md(),c={queryKey:o,pageParam:void 0,meta:this.meta};Object.defineProperty(c,"signal",{enumerable:!0,get:function(){if(d)return n.abortSignalConsumed=!0,d.signal}});var u,m,h={fetchOptions:t,options:this.options,queryKey:o,state:this.state,fetchFn:function(){return n.options.queryFn?(n.abortSignalConsumed=!1,n.options.queryFn(c)):Promise.reject("Missing queryFn")},meta:this.meta};(null==(r=this.options.behavior)?void 0:r.onFetch)&&(null==(u=this.options.behavior)||u.onFetch(h));(this.revertState=this.state,this.state.isFetching&&this.state.fetchMeta===(null==(a=h.fetchOptions)?void 0:a.meta))||this.dispatch({type:"fetch",meta:null==(m=h.fetchOptions)?void 0:m.meta});return this.retryer=new vd({fn:h.fetchFn,abort:null==d||null==(s=d.abort)?void 0:s.bind(d),onSuccess:function(e){n.setData(e),null==n.cache.config.onSuccess||n.cache.config.onSuccess(e,n),0===n.cacheTime&&n.optionalRemove()},onError:function(e){yd(e)&&e.silent||n.dispatch({type:"error",error:e}),yd(e)||(null==n.cache.config.onError||n.cache.config.onError(e,n),jd().error(e)),0===n.cacheTime&&n.optionalRemove()},onFail:function(){n.dispatch({type:"failed"})},onPause:function(){n.dispatch({type:"pause"})},onContinue:function(){n.dispatch({type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(e){var t=this;this.state=this.reducer(this.state,e),bd.batch((function(){t.observers.forEach((function(t){t.onQueryUpdate(e)})),t.cache.notify({query:t,type:"queryUpdated",action:e})}))},t.getDefaultState=function(e){var t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==e.initialData?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0,a=void 0!==t;return{data:t,dataUpdateCount:0,dataUpdatedAt:a?null!=r?r:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:a?"success":"idle"}},t.reducer=function(e,t){var r,a;switch(t.type){case"failed":return Ko({},e,{fetchFailureCount:e.fetchFailureCount+1});case"pause":return Ko({},e,{isPaused:!0});case"continue":return Ko({},e,{isPaused:!1});case"fetch":return Ko({},e,{fetchFailureCount:0,fetchMeta:null!=(r=t.meta)?r:null,isFetching:!0,isPaused:!1},!e.dataUpdatedAt&&{error:null,status:"loading"});case"success":return Ko({},e,{data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:null!=(a=t.dataUpdatedAt)?a:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var s=t.error;return yd(s)&&s.revert&&this.revertState?Ko({},this.revertState):Ko({},e,{error:s,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return Ko({},e,{isInvalidated:!0});case"setState":return Ko({},e,t.state);default:return e}},e}(),Cd=function(e){function t(t){var r;return(r=e.call(this)||this).config=t||{},r.queries=[],r.queriesMap={},r}Zo(t,e);var r=t.prototype;return r.build=function(e,t,r){var a,s=t.queryKey,n=null!=(a=t.queryHash)?a:ad(s,t),i=this.get(n);return i||(i=new kd({cache:this,queryKey:s,queryHash:n,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(s),meta:t.meta}),this.add(i)),i},r.add=function(e){this.queriesMap[e.queryHash]||(this.queriesMap[e.queryHash]=e,this.queries.push(e),this.notify({type:"queryAdded",query:e}))},r.remove=function(e){var t=this.queriesMap[e.queryHash];t&&(e.destroy(),this.queries=this.queries.filter((function(t){return t!==e})),t===e&&delete this.queriesMap[e.queryHash],this.notify({type:"queryRemoved",query:e}))},r.clear=function(){var e=this;bd.batch((function(){e.queries.forEach((function(t){e.remove(t)}))}))},r.get=function(e){return this.queriesMap[e]},r.getAll=function(){return this.queries},r.find=function(e,t){var r=ed(e,t)[0];return void 0===r.exact&&(r.exact=!0),this.queries.find((function(e){return td(r,e)}))},r.findAll=function(e,t){var r=ed(e,t)[0];return Object.keys(r).length>0?this.queries.filter((function(e){return td(r,e)})):this.queries},r.notify=function(e){var t=this;bd.batch((function(){t.listeners.forEach((function(t){t(e)}))}))},r.onFocus=function(){var e=this;bd.batch((function(){e.queries.forEach((function(e){e.onFocus()}))}))},r.onOnline=function(){var e=this;bd.batch((function(){e.queries.forEach((function(e){e.onOnline()}))}))},t}(zo),Nd=function(){function e(e){this.options=Ko({},e.defaultOptions,e.options),this.mutationId=e.mutationId,this.mutationCache=e.mutationCache,this.observers=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0},this.meta=e.meta}var t=e.prototype;return t.setState=function(e){this.dispatch({type:"setState",state:e})},t.addObserver=function(e){-1===this.observers.indexOf(e)&&this.observers.push(e)},t.removeObserver=function(e){this.observers=this.observers.filter((function(t){return t!==e}))},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(Yo).catch(Yo)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var e,t=this,r="loading"===this.state.status,a=Promise.resolve();return r||(this.dispatch({type:"loading",variables:this.options.variables}),a=a.then((function(){null==t.mutationCache.config.onMutate||t.mutationCache.config.onMutate(t.state.variables,t)})).then((function(){return null==t.options.onMutate?void 0:t.options.onMutate(t.state.variables)})).then((function(e){e!==t.state.context&&t.dispatch({type:"loading",context:e,variables:t.state.variables})}))),a.then((function(){return t.executeMutation()})).then((function(r){e=r,null==t.mutationCache.config.onSuccess||t.mutationCache.config.onSuccess(e,t.state.variables,t.state.context,t)})).then((function(){return null==t.options.onSuccess?void 0:t.options.onSuccess(e,t.state.variables,t.state.context)})).then((function(){return null==t.options.onSettled?void 0:t.options.onSettled(e,null,t.state.variables,t.state.context)})).then((function(){return t.dispatch({type:"success",data:e}),e})).catch((function(e){return null==t.mutationCache.config.onError||t.mutationCache.config.onError(e,t.state.variables,t.state.context,t),jd().error(e),Promise.resolve().then((function(){return null==t.options.onError?void 0:t.options.onError(e,t.state.variables,t.state.context)})).then((function(){return null==t.options.onSettled?void 0:t.options.onSettled(void 0,e,t.state.variables,t.state.context)})).then((function(){throw t.dispatch({type:"error",error:e}),e}))}))},t.executeMutation=function(){var e,t=this;return this.retryer=new vd({fn:function(){return t.options.mutationFn?t.options.mutationFn(t.state.variables):Promise.reject("No mutationFn found")},onFail:function(){t.dispatch({type:"failed"})},onPause:function(){t.dispatch({type:"pause"})},onContinue:function(){t.dispatch({type:"continue"})},retry:null!=(e=this.options.retry)?e:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(e){var t=this;this.state=function(e,t){switch(t.type){case"failed":return Ko({},e,{failureCount:e.failureCount+1});case"pause":return Ko({},e,{isPaused:!0});case"continue":return Ko({},e,{isPaused:!1});case"loading":return Ko({},e,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return Ko({},e,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return Ko({},e,{data:void 0,error:t.error,failureCount:e.failureCount+1,isPaused:!1,status:"error"});case"setState":return Ko({},e,t.state);default:return e}}(this.state,e),bd.batch((function(){t.observers.forEach((function(t){t.onMutationUpdate(e)})),t.mutationCache.notify(t)}))},e}();var Ed=function(e){function t(t){var r;return(r=e.call(this)||this).config=t||{},r.mutations=[],r.mutationId=0,r}Zo(t,e);var r=t.prototype;return r.build=function(e,t,r){var a=new Nd({mutationCache:this,mutationId:++this.mutationId,options:e.defaultMutationOptions(t),state:r,defaultOptions:t.mutationKey?e.getMutationDefaults(t.mutationKey):void 0,meta:t.meta});return this.add(a),a},r.add=function(e){this.mutations.push(e),this.notify(e)},r.remove=function(e){this.mutations=this.mutations.filter((function(t){return t!==e})),e.cancel(),this.notify(e)},r.clear=function(){var e=this;bd.batch((function(){e.mutations.forEach((function(t){e.remove(t)}))}))},r.getAll=function(){return this.mutations},r.find=function(e){return void 0===e.exact&&(e.exact=!0),this.mutations.find((function(t){return rd(e,t)}))},r.findAll=function(e){return this.mutations.filter((function(t){return rd(e,t)}))},r.notify=function(e){var t=this;bd.batch((function(){t.listeners.forEach((function(t){t(e)}))}))},r.onFocus=function(){this.resumePausedMutations()},r.onOnline=function(){this.resumePausedMutations()},r.resumePausedMutations=function(){var e=this.mutations.filter((function(e){return e.state.isPaused}));return bd.batch((function(){return e.reduce((function(e,t){return e.then((function(){return t.continue().catch(Yo)}))}),Promise.resolve())}))},t}(zo);function Sd(e,t){return null==e.getNextPageParam?void 0:e.getNextPageParam(t[t.length-1],t)}var Ld=function(){function e(e){void 0===e&&(e={}),this.queryCache=e.queryCache||new Cd,this.mutationCache=e.mutationCache||new Ed,this.defaultOptions=e.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=e.prototype;return t.mount=function(){var e=this;this.unsubscribeFocus=hd.subscribe((function(){hd.isFocused()&&gd.isOnline()&&(e.mutationCache.onFocus(),e.queryCache.onFocus())})),this.unsubscribeOnline=gd.subscribe((function(){hd.isFocused()&&gd.isOnline()&&(e.mutationCache.onOnline(),e.queryCache.onOnline())}))},t.unmount=function(){var e,t;null==(e=this.unsubscribeFocus)||e.call(this),null==(t=this.unsubscribeOnline)||t.call(this)},t.isFetching=function(e,t){var r=ed(e,t)[0];return r.fetching=!0,this.queryCache.findAll(r).length},t.isMutating=function(e){return this.mutationCache.findAll(Ko({},e,{fetching:!0})).length},t.getQueryData=function(e,t){var r;return null==(r=this.queryCache.find(e,t))?void 0:r.state.data},t.getQueriesData=function(e){return this.getQueryCache().findAll(e).map((function(e){return[e.queryKey,e.state.data]}))},t.setQueryData=function(e,t,r){var a=Xo(e),s=this.defaultQueryOptions(a);return this.queryCache.build(this,s).setData(t,r)},t.setQueriesData=function(e,t,r){var a=this;return bd.batch((function(){return a.getQueryCache().findAll(e).map((function(e){var s=e.queryKey;return[s,a.setQueryData(s,t,r)]}))}))},t.getQueryState=function(e,t){var r;return null==(r=this.queryCache.find(e,t))?void 0:r.state},t.removeQueries=function(e,t){var r=ed(e,t)[0],a=this.queryCache;bd.batch((function(){a.findAll(r).forEach((function(e){a.remove(e)}))}))},t.resetQueries=function(e,t,r){var a=this,s=ed(e,t,r),n=s[0],i=s[1],l=this.queryCache,o=Ko({},n,{active:!0});return bd.batch((function(){return l.findAll(n).forEach((function(e){e.reset()})),a.refetchQueries(o,i)}))},t.cancelQueries=function(e,t,r){var a=this,s=ed(e,t,r),n=s[0],i=s[1],l=void 0===i?{}:i;void 0===l.revert&&(l.revert=!0);var o=bd.batch((function(){return a.queryCache.findAll(n).map((function(e){return e.cancel(l)}))}));return Promise.all(o).then(Yo).catch(Yo)},t.invalidateQueries=function(e,t,r){var a,s,n,i=this,l=ed(e,t,r),o=l[0],d=l[1],c=Ko({},o,{active:null==(a=null!=(s=o.refetchActive)?s:o.active)||a,inactive:null!=(n=o.refetchInactive)&&n});return bd.batch((function(){return i.queryCache.findAll(o).forEach((function(e){e.invalidate()})),i.refetchQueries(c,d)}))},t.refetchQueries=function(e,t,r){var a=this,s=ed(e,t,r),n=s[0],i=s[1],l=bd.batch((function(){return a.queryCache.findAll(n).map((function(e){return e.fetch(void 0,Ko({},i,{meta:{refetchPage:null==n?void 0:n.refetchPage}}))}))})),o=Promise.all(l).then(Yo);return(null==i?void 0:i.throwOnError)||(o=o.catch(Yo)),o},t.fetchQuery=function(e,t,r){var a=Xo(e,t,r),s=this.defaultQueryOptions(a);void 0===s.retry&&(s.retry=!1);var n=this.queryCache.build(this,s);return n.isStaleByTime(s.staleTime)?n.fetch(s):Promise.resolve(n.state.data)},t.prefetchQuery=function(e,t,r){return this.fetchQuery(e,t,r).then(Yo).catch(Yo)},t.fetchInfiniteQuery=function(e,t,r){var a=Xo(e,t,r);return a.behavior={onFetch:function(e){e.fetchFn=function(){var t,r,a,s,n,i,l,o,d,c=null==(t=e.fetchOptions)||null==(r=t.meta)?void 0:r.refetchPage,u=null==(a=e.fetchOptions)||null==(s=a.meta)?void 0:s.fetchMore,m=null==u?void 0:u.pageParam,h="forward"===(null==u?void 0:u.direction),g="backward"===(null==u?void 0:u.direction),p=(null==(n=e.state.data)?void 0:n.pages)||[],f=(null==(i=e.state.data)?void 0:i.pageParams)||[],x=md(),y=null==x?void 0:x.signal,v=f,b=!1,w=e.options.queryFn||function(){return Promise.reject("Missing queryFn")},j=function(e,t,r,a){return v=a?[t].concat(v):[].concat(v,[t]),a?[r].concat(e):[].concat(e,[r])},k=function(t,r,a,s){if(b)return Promise.reject("Cancelled");if(void 0===a&&!r&&t.length)return Promise.resolve(t);var n={queryKey:e.queryKey,signal:y,pageParam:a,meta:e.meta},i=w(n),l=Promise.resolve(i).then((function(e){return j(t,a,e,s)}));return fd(i)&&(l.cancel=i.cancel),l};if(p.length)if(h){var C=void 0!==m,N=C?m:Sd(e.options,p);l=k(p,C,N)}else if(g){var E=void 0!==m,S=E?m:(o=e.options,d=p,null==o.getPreviousPageParam?void 0:o.getPreviousPageParam(d[0],d));l=k(p,E,S,!0)}else!function(){v=[];var t=void 0===e.options.getNextPageParam,r=!c||!p[0]||c(p[0],0,p);l=r?k([],t,f[0]):Promise.resolve(j([],f[0],p[0]));for(var a=function(r){l=l.then((function(a){if(!c||!p[r]||c(p[r],r,p)){var s=t?f[r]:Sd(e.options,a);return k(a,t,s)}return Promise.resolve(j(a,f[r],p[r]))}))},s=1;s<p.length;s++)a(s)}();else l=k([]);var L=l.then((function(e){return{pages:e,pageParams:v}}));return L.cancel=function(){b=!0,null==x||x.abort(),fd(l)&&l.cancel()},L}}},this.fetchQuery(a)},t.prefetchInfiniteQuery=function(e,t,r){return this.fetchInfiniteQuery(e,t,r).then(Yo).catch(Yo)},t.cancelMutations=function(){var e=this,t=bd.batch((function(){return e.mutationCache.getAll().map((function(e){return e.cancel()}))}));return Promise.all(t).then(Yo).catch(Yo)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(e){return this.mutationCache.build(this,e).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(e){this.defaultOptions=e},t.setQueryDefaults=function(e,t){var r=this.queryDefaults.find((function(t){return sd(e)===sd(t.queryKey)}));r?r.defaultOptions=t:this.queryDefaults.push({queryKey:e,defaultOptions:t})},t.getQueryDefaults=function(e){var t;return e?null==(t=this.queryDefaults.find((function(t){return nd(e,t.queryKey)})))?void 0:t.defaultOptions:void 0},t.setMutationDefaults=function(e,t){var r=this.mutationDefaults.find((function(t){return sd(e)===sd(t.mutationKey)}));r?r.defaultOptions=t:this.mutationDefaults.push({mutationKey:e,defaultOptions:t})},t.getMutationDefaults=function(e){var t;return e?null==(t=this.mutationDefaults.find((function(t){return nd(e,t.mutationKey)})))?void 0:t.defaultOptions:void 0},t.defaultQueryOptions=function(e){if(null==e?void 0:e._defaulted)return e;var t=Ko({},this.defaultOptions.queries,this.getQueryDefaults(null==e?void 0:e.queryKey),e,{_defaulted:!0});return!t.queryHash&&t.queryKey&&(t.queryHash=ad(t.queryKey,t)),t},t.defaultQueryObserverOptions=function(e){return this.defaultQueryOptions(e)},t.defaultMutationOptions=function(e){return(null==e?void 0:e._defaulted)?e:Ko({},this.defaultOptions.mutations,this.getMutationDefaults(null==e?void 0:e.mutationKey),e,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},e}(),_d=l.unstable_batchedUpdates;bd.setBatchNotifyFunction(_d);var Od=console;wd=Od;var Pd=o.createContext(void 0),Ad=o.createContext(!1);var Md=function(e){var t=e.client,r=e.contextSharing,a=void 0!==r&&r,s=e.children;o.useEffect((function(){return t.mount(),function(){t.unmount()}}),[t]);var n=function(e){return e&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=Pd),window.ReactQueryClientContext):Pd}(a);return o.createElement(Ad.Provider,{value:a},o.createElement(n.Provider,{value:t},s))},Id=(e=>(e.BASE="base",e.BODY="body",e.HEAD="head",e.HTML="html",e.LINK="link",e.META="meta",e.NOSCRIPT="noscript",e.SCRIPT="script",e.STYLE="style",e.TITLE="title",e.FRAGMENT="Symbol(react.fragment)",e))(Id||{}),Rd={rel:["amphtml","canonical","alternate"]},Fd={type:["application/ld+json"]},Td={charset:"",name:["generator","robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]};Object.values(Id);var Dd={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"};Object.entries(Dd).reduce(((e,[t,r])=>(e[r]=t,e)),{});var Wd="data-rh",Bd=(e,t)=>Array.isArray(e)?e.reduce(((e,r)=>(((e,t)=>{const r=Object.keys(e);for(let a=0;a<r.length;a+=1)if(t[r[a]]&&t[r[a]].includes(e[r[a]]))return!0;return!1})(r,t)?e.priority.push(r):e.default.push(r),e)),{priority:[],default:[]}):{default:e,priority:[]},$d=["noscript","script","style"],qd=(e,t=!0)=>!1===t?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),Vd=e=>Object.keys(e).reduce(((t,r)=>{const a=void 0!==e[r]?`${r}="${e[r]}"`:`${r}`;return t?`${t} ${a}`:a}),""),Qd=(e,t,r,a)=>{const s=Vd(r),n=(i=t,Array.isArray(i)?i.join(""):i);var i;return s?`<${e} ${Wd}="true" ${s}>${qd(n,a)}</${e}>`:`<${e} ${Wd}="true">${qd(n,a)}</${e}>`},Hd=(e,t={})=>Object.keys(e).reduce(((t,r)=>(t[Dd[r]||r]=e[r],t)),t),Ud=(e,t)=>t.map(((t,r)=>{const a={key:r,[Wd]:!0};return Object.keys(t).forEach((e=>{const r=Dd[e]||e;if("innerHTML"===r||"cssText"===r){const e=t.innerHTML||t.cssText;a.dangerouslySetInnerHTML={__html:e}}else a[r]=t[e]})),o.createElement(e,a)})),Zd=(e,t,r=!0)=>{switch(e){case"title":return{toComponent:()=>((e,t,r)=>{const a=Hd(r,{key:t,[Wd]:!0});return[o.createElement("title",a,t)]})(0,t.title,t.titleAttributes),toString:()=>Qd(e,t.title,t.titleAttributes,r)};case"bodyAttributes":case"htmlAttributes":return{toComponent:()=>Hd(t),toString:()=>Vd(t)};default:return{toComponent:()=>Ud(e,t),toString:()=>((e,t,r=!0)=>t.reduce(((t,a)=>{const s=a,n=Object.keys(s).filter((e=>!("innerHTML"===e||"cssText"===e))).reduce(((e,t)=>{const a=void 0===s[t]?t:`${t}="${qd(s[t],r)}"`;return e?`${e} ${a}`:a}),""),i=s.innerHTML||s.cssText||"",l=-1===$d.indexOf(e);return`${t}<${e} ${Wd}="true" ${n}${l?"/>":`>${i}</${e}>`}`}),""))(e,t,r)}}},zd=e=>{const{baseTag:t,bodyAttributes:r,encode:a=!0,htmlAttributes:s,noscriptTags:n,styleTags:i,title:l="",titleAttributes:o,prioritizeSeoTags:d}=e;let{linkTags:c,metaTags:u,scriptTags:m}=e,h={toComponent:()=>{},toString:()=>""};return d&&({priorityMethods:h,linkTags:c,metaTags:u,scriptTags:m}=(({metaTags:e,linkTags:t,scriptTags:r,encode:a})=>{const s=Bd(e,Td),n=Bd(t,Rd),i=Bd(r,Fd);return{priorityMethods:{toComponent:()=>[...Ud("meta",s.priority),...Ud("link",n.priority),...Ud("script",i.priority)],toString:()=>`${Zd("meta",s.priority,a)} ${Zd("link",n.priority,a)} ${Zd("script",i.priority,a)}`},metaTags:s.default,linkTags:n.default,scriptTags:i.default}})(e)),{priority:h,base:Zd("base",t,a),bodyAttributes:Zd("bodyAttributes",r,a),htmlAttributes:Zd("htmlAttributes",s,a),link:Zd("link",c,a),meta:Zd("meta",u,a),noscript:Zd("noscript",n,a),script:Zd("script",m,a),style:Zd("style",i,a),title:Zd("title",{title:l,titleAttributes:o},a)}},Kd=[],Gd=!("undefined"==typeof window||!window.document||!window.document.createElement),Yd=class{constructor(e,t){r(this,"instances",[]),r(this,"canUseDOM",Gd),r(this,"context"),r(this,"value",{setHelmet:e=>{this.context.helmet=e},helmetInstances:{get:()=>this.canUseDOM?Kd:this.instances,add:e=>{(this.canUseDOM?Kd:this.instances).push(e)},remove:e=>{const t=(this.canUseDOM?Kd:this.instances).indexOf(e);(this.canUseDOM?Kd:this.instances).splice(t,1)}}}),this.context=e,this.canUseDOM=t||!1,t||(e.helmet=zd({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))}},Jd=o.createContext({}),Xd=(e=class extends i.Component{constructor(t){super(t),r(this,"helmetData"),this.helmetData=new Yd(this.props.context||{},e.canUseDOM)}render(){return o.createElement(Jd.Provider,{value:this.helmetData.value},this.props.children)}},r(e,"canUseDOM",Gd),e);function ec({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))}const tc=i.forwardRef(ec);function rc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))}const ac=i.forwardRef(rc);function sc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181"}))}const nc=i.forwardRef(sc);function ic({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))}const lc=i.forwardRef(ic);function oc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z"}))}const dc=i.forwardRef(oc);function cc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const uc=i.forwardRef(cc);function mc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}const hc=i.forwardRef(mc);function gc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"}))}const pc=i.forwardRef(gc);function fc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))}const xc=i.forwardRef(fc);function yc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}const vc=i.forwardRef(yc);function bc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const wc=i.forwardRef(bc);function jc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}const kc=i.forwardRef(jc);function Cc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m18.75 4.5-7.5 7.5 7.5 7.5m-6-15L5.25 12l7.5 7.5"}))}const Nc=i.forwardRef(Cc);function Ec({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m5.25 4.5 7.5 7.5-7.5 7.5m6-15 7.5 7.5-7.5 7.5"}))}const Sc=i.forwardRef(Ec);function Lc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))}const _c=i.forwardRef(Lc);function Oc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 19.5 8.25 12l7.5-7.5"}))}const Pc=i.forwardRef(Oc);function Ac({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const Mc=i.forwardRef(Ac);function Ic({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 15 12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9"}))}const Rc=i.forwardRef(Ic);function Fc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))}const Tc=i.forwardRef(Fc);function Dc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const Wc=i.forwardRef(Dc);function Bc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const $c=i.forwardRef(Bc);function qc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}const Vc=i.forwardRef(qc);function Qc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"}))}const Hc=i.forwardRef(Qc);function Uc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const Zc=i.forwardRef(Uc);function zc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"}))}const Kc=i.forwardRef(zc);function Gc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const Yc=i.forwardRef(Gc);function Jc({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"}))}const Xc=i.forwardRef(Jc);function eu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}const tu=i.forwardRef(eu);function ru({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z"}))}const au=i.forwardRef(ru);function su({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const nu=i.forwardRef(su);function iu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))}const lu=i.forwardRef(iu);function ou({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const du=i.forwardRef(ou);function cu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))}const uu=i.forwardRef(cu);function mu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))}const hu=i.forwardRef(mu);function gu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))}const pu=i.forwardRef(gu);function fu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))}const xu=i.forwardRef(fu);function yu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const vu=i.forwardRef(yu);function bu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))}const wu=i.forwardRef(bu);function ju({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 12h14"}))}const ku=i.forwardRef(ju);function Cu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))}const Nu=i.forwardRef(Cu);function Eu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const Su=i.forwardRef(Eu);function Lu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}const _u=i.forwardRef(Lu);function Ou({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))}const Pu=i.forwardRef(Ou);function Au({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const Mu=i.forwardRef(Au);function Iu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z"}))}const Ru=i.forwardRef(Iu);function Fu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0 1 3.75 9.375v-4.5ZM3.75 14.625c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5a1.125 1.125 0 0 1-1.125-1.125v-4.5ZM13.5 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0 1 13.5 9.375v-4.5Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 6.75h.75v.75h-.75v-.75ZM6.75 16.5h.75v.75h-.75v-.75ZM16.5 6.75h.75v.75h-.75v-.75ZM13.5 13.5h.75v.75h-.75v-.75ZM13.5 19.5h.75v.75h-.75v-.75ZM19.5 13.5h.75v.75h-.75v-.75ZM19.5 19.5h.75v.75h-.75v-.75ZM16.5 16.5h.75v.75h-.75v-.75Z"}))}const Tu=i.forwardRef(Fu);function Du({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"}))}const Wu=i.forwardRef(Du);function Bu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))}const $u=i.forwardRef(Bu);function qu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 6h.008v.008H6V6Z"}))}const Vu=i.forwardRef(qu);function Qu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const Hu=i.forwardRef(Qu);function Uu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))}const Zu=i.forwardRef(Uu);function zu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const Ku=i.forwardRef(zu);function Gu({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))}const Yu=i.forwardRef(Gu);function Ju({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.766Z"}))}const Xu=i.forwardRef(Ju);function em({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}const tm=i.forwardRef(em);function rm({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}const am=i.forwardRef(rm);function sm({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z"}))}const nm=i.forwardRef(sm);function im({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const lm=i.forwardRef(im);function om({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const dm=i.forwardRef(om),cm=()=>{const e=j(),t=d(),r=k(Ut),a=k(Mr);k(Pr);const s=k(Fo),n=()=>{e(Qt()),t("/login")};return F.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:F.jsxs("div",{className:"flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8",children:[F.jsxs("div",{className:"flex items-center space-x-4",children:[F.jsx("button",{onClick:()=>{e(Jt())},className:"p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",children:F.jsx(uc,{className:"h-6 w-6"})}),F.jsxs(c,{to:"/dashboard",className:"flex items-center space-x-2",children:[F.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:F.jsx("span",{className:"text-white font-bold text-sm",children:"TN"})}),F.jsx("span",{className:"text-xl font-semibold text-gray-900 dark:text-white hidden sm:block",children:"TrackNew"})]})]}),F.jsx("div",{className:"flex-1 max-w-lg mx-4",children:F.jsxs("div",{className:"relative",children:[F.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:F.jsx(vu,{className:"h-5 w-5 text-gray-400"})}),F.jsx("input",{type:"text",placeholder:"Search customers, services, products...",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]})}),F.jsxs("div",{className:"flex items-center space-x-4",children:[F.jsx("button",{onClick:()=>{e(ar())},className:"p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",children:"light"===a?F.jsx(Nu,{className:"h-5 w-5"}):F.jsx($u,{className:"h-5 w-5"})}),F.jsxs("button",{onClick:()=>{e(yo())},className:"relative p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",children:[F.jsx(hc,{className:"h-6 w-6"}),s>0&&F.jsx("span",{className:"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full",children:s>99?"99+":s})]}),F.jsxs(E,{as:"div",className:"relative",children:[F.jsx(E.Button,{className:"flex items-center space-x-3 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:F.jsxs("div",{className:"flex items-center space-x-2",children:[F.jsx(Ku,{className:"h-8 w-8 text-gray-400"}),F.jsxs("div",{className:"hidden md:block text-left",children:[F.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:(null==r?void 0:r.name)||"User"}),F.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:(null==r?void 0:r.role)||"User"})]})]})}),F.jsx(S,{as:i.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:F.jsxs(E.Items,{className:"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white dark:bg-gray-800 py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none",children:[F.jsx(E.Item,{children:({active:e})=>F.jsxs(c,{to:"/profile",className:(e?"bg-gray-100 dark:bg-gray-700":"")+" flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300",children:[F.jsx(Ku,{className:"mr-3 h-5 w-5"}),"Your Profile"]})}),F.jsx(E.Item,{children:({active:e})=>F.jsxs(c,{to:"/settings",className:(e?"bg-gray-100 dark:bg-gray-700":"")+" flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300",children:[F.jsx($c,{className:"mr-3 h-5 w-5"}),"Settings"]})}),F.jsx("div",{className:"border-t border-gray-100 dark:border-gray-700"}),F.jsx(E.Item,{children:({active:e})=>F.jsx("button",{onClick:n,className:(e?"bg-gray-100 dark:bg-gray-700":"")+" flex w-full items-center px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300",children:"Sign out"})})]})})]})]})]})})},um=()=>{const e=j(),t=u(),r=k(Pr),a=k(Ar),s=[{name:"Dashboard",href:"/dashboard",icon:hu,current:!1},{name:"Customers",icon:am,current:!1,children:[{name:"All Customers",href:"/customers"},{name:"Add Customer",href:"/customers/new"},{name:"Customer Categories",href:"/customer-categories"}]},{name:"Leads",icon:Yu,current:!1,children:[{name:"All Leads",href:"/leads"},{name:"Add Lead",href:"/leads/new"},{name:"Lead Pipeline",href:"/leads/pipeline"}]},{name:"Services",icon:nm,current:!1,children:[{name:"All Services",href:"/services"},{name:"New Service",href:"/services/new"},{name:"Service Categories",href:"/service-categories"}]},{name:"Products",icon:Hc,current:!1,children:[{name:"All Products",href:"/products"},{name:"Add Product",href:"/products/new"},{name:"Categories",href:"/categories"},{name:"Brands",href:"/brands"},{name:"Units",href:"/units"},{name:"Low Stock",href:"/products/low-stock"}]},{name:"Sales",icon:Wu,current:!1,children:[{name:"All Sales",href:"/sales"},{name:"New Sale",href:"/sales/new"},{name:"Estimations",href:"/estimations"},{name:"Invoices",href:"/invoices"}]},{name:"Inventory",icon:Zu,current:!1,children:[{name:"Stock Movement",href:"/stock-movement"},{name:"Warehouses",href:"/warehouses"},{name:"Purchase Orders",href:"/purchase-orders"},{name:"Suppliers",href:"/suppliers"}]},{name:"AMC",icon:xc,current:!1,children:[{name:"All AMCs",href:"/amcs"},{name:"New AMC",href:"/amcs/new"},{name:"Expiring Soon",href:"/amcs/expiring"}]},{name:"Payments",icon:dc,current:!1,children:[{name:"Payment In",href:"/payments/in"},{name:"Payment Out",href:"/payments/out"},{name:"Expenses",href:"/expenses"}]},{name:"Reports",icon:vc,current:!1,children:[{name:"Sales Reports",href:"/reports/sales"},{name:"Service Reports",href:"/reports/services"},{name:"Financial Reports",href:"/reports/financial"},{name:"Inventory Reports",href:"/reports/inventory"}]},{name:"Management",icon:pc,current:!1,children:[{name:"Employees",href:"/employees"},{name:"Roles",href:"/roles"},{name:"Taxes",href:"/taxes"},{name:"RMA",href:"/rma"}]},{name:"Notifications",href:"/notifications",icon:hc,current:!1},{name:"Documents",href:"/documents",icon:Kc,current:!1},{name:"Settings",href:"/settings",icon:Vc,current:!1}],n=e=>t.pathname===e||t.pathname.startsWith(e+"/"),i=e=>null==e?void 0:e.some((e=>n(e.href))),l=()=>{window.innerWidth<1024&&e(Xt(!1))};return F.jsxs(F.Fragment,{children:[r&&F.jsx("div",{className:"fixed inset-0 z-40 lg:hidden",onClick:()=>e(Xt(!1)),children:F.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75"})}),F.jsx("div",{className:`\n        fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n        ${r?"translate-x-0":"-translate-x-full"}\n        ${a?"lg:w-16":"lg:w-64"}\n      `,children:F.jsx("div",{className:"flex flex-col h-full",children:F.jsx("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:F.jsx("nav",{className:"mt-5 flex-1 px-2 space-y-1",children:s.map((e=>F.jsx("div",{children:e.children?F.jsx(L,{as:"div",defaultOpen:i(e.children),children:({open:t})=>F.jsxs(F.Fragment,{children:[F.jsxs(L.Button,{className:`\n                              group w-full flex items-center px-2 py-2 text-left text-sm font-medium rounded-md transition-colors duration-150\n                              ${i(e.children)?"bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"}\n                            `,children:[F.jsx(e.icon,{className:`\n                                mr-3 flex-shrink-0 h-6 w-6\n                                ${i(e.children)?"text-blue-500 dark:text-blue-400":"text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300"}\n                              `}),!a&&F.jsxs(F.Fragment,{children:[F.jsx("span",{className:"flex-1",children:e.name}),t?F.jsx(_c,{className:"ml-3 h-5 w-5 flex-shrink-0"}):F.jsx(Mc,{className:"ml-3 h-5 w-5 flex-shrink-0"})]})]}),!a&&F.jsx(L.Panel,{className:"space-y-1",children:e.children.map((e=>F.jsx(c,{to:e.href,onClick:l,className:`\n                                    group flex items-center pl-11 pr-2 py-2 text-sm font-medium rounded-md transition-colors duration-150\n                                    ${n(e.href)?"bg-blue-50 text-blue-700 dark:bg-blue-800 dark:text-blue-200":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"}\n                                  `,children:e.name},e.name)))})]})}):F.jsxs(c,{to:e.href,onClick:l,className:`\n                        group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150\n                        ${n(e.href)?"bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"}\n                      `,children:[F.jsx(e.icon,{className:`\n                          mr-3 flex-shrink-0 h-6 w-6\n                          ${n(e.href)?"text-blue-500 dark:text-blue-400":"text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300"}\n                        `}),!a&&e.name]})},e.name)))})})})})]})};function mm(...e){return e.filter(Boolean).join(" ")}function hm(e,t="USD",r="en-US"){return null==e||isNaN(e)?"-":new Intl.NumberFormat(r,{style:"currency",currency:t}).format(e)}function gm(e,t="medium",r="en-US"){if(!e)return"-";const a="string"==typeof e?new Date(e):e;if(isNaN(a.getTime()))return"-";return new Intl.DateTimeFormat(r,{short:{year:"numeric",month:"short",day:"numeric"},medium:{year:"numeric",month:"short",day:"numeric"},long:{year:"numeric",month:"long",day:"numeric"},full:{weekday:"long",year:"numeric",month:"long",day:"numeric"}}[t]).format(a)}function pm(e,t="en-US"){if(!e)return"-";const r="string"==typeof e?new Date(e):e;if(isNaN(r.getTime()))return"-";const a=new Date,s=Math.floor((a-r)/1e3),n={year:31536e3,month:2592e3,week:604800,day:86400,hour:3600,minute:60};for(const[i,l]of Object.entries(n)){const e=Math.floor(s/l);if(e>=1)return new Intl.RelativeTimeFormat(t,{numeric:"auto"}).format(-e,i)}return"just now"}function fm(e,t=2){return e?e.split(" ").slice(0,t).map((e=>e.charAt(0).toUpperCase())).join(""):""}function xm(e){const t=["bg-red-500","bg-yellow-500","bg-green-500","bg-blue-500","bg-indigo-500","bg-purple-500","bg-pink-500","bg-gray-500"];if(!e)return t[0];let r=0;for(let a=0;a<e.length;a++)r=e.charCodeAt(a)+((r<<5)-r);return t[Math.abs(r)%t.length]}function ym(e){return{active:"text-green-800 bg-green-100",inactive:"text-gray-800 bg-gray-100",pending:"text-yellow-800 bg-yellow-100",completed:"text-green-800 bg-green-100",cancelled:"text-red-800 bg-red-100",open:"text-blue-800 bg-blue-100","in-progress":"text-yellow-800 bg-yellow-100",resolved:"text-green-800 bg-green-100",closed:"text-gray-800 bg-gray-100",paid:"text-green-800 bg-green-100",unpaid:"text-red-800 bg-red-100","partially-paid":"text-yellow-800 bg-yellow-100",overdue:"text-red-800 bg-red-100",new:"text-blue-800 bg-blue-100",contacted:"text-yellow-800 bg-yellow-100",qualified:"text-purple-800 bg-purple-100",proposal:"text-indigo-800 bg-indigo-100",negotiation:"text-orange-800 bg-orange-100",won:"text-green-800 bg-green-100",lost:"text-red-800 bg-red-100"}[null==e?void 0:e.toLowerCase()]||"text-gray-800 bg-gray-100"}const vm=()=>{const e=j(),t=k(Ro),r=k(Do),a=k(To),s=k(Fo);i.useEffect((()=>{r&&e(co({limit:50}))}),[r,e]);const n=()=>{e(vo(!1))},l=e=>{const t="h-6 w-6";switch(e){case"success":return F.jsx(wc,{className:mm(t,"text-green-500")});case"warning":return F.jsx(nu,{className:mm(t,"text-yellow-500")});case"error":return F.jsx(nu,{className:mm(t,"text-red-500")});case"info":return F.jsx(pu,{className:mm(t,"text-blue-500")});default:return F.jsx(hc,{className:mm(t,"text-gray-500")})}},o=e=>{switch(e){case"success":return"border-l-green-500";case"warning":return"border-l-yellow-500";case"error":return"border-l-red-500";case"info":return"border-l-blue-500";default:return"border-l-gray-500"}};return F.jsx(S.Root,{show:r,as:i.Fragment,children:F.jsxs(_,{as:"div",className:"relative z-50",onClose:n,children:[F.jsx(S.Child,{as:i.Fragment,enter:"ease-in-out duration-500",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in-out duration-500",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:F.jsx("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"})}),F.jsx("div",{className:"fixed inset-0 overflow-hidden",children:F.jsx("div",{className:"absolute inset-0 overflow-hidden",children:F.jsx("div",{className:"pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10",children:F.jsx(S.Child,{as:i.Fragment,enter:"transform transition ease-in-out duration-500 sm:duration-700",enterFrom:"translate-x-full",enterTo:"translate-x-0",leave:"transform transition ease-in-out duration-500 sm:duration-700",leaveFrom:"translate-x-0",leaveTo:"translate-x-full",children:F.jsxs(_.Panel,{className:"pointer-events-auto relative w-screen max-w-md",children:[F.jsx(S.Child,{as:i.Fragment,enter:"ease-in-out duration-500",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in-out duration-500",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:F.jsx("div",{className:"absolute left-0 top-0 -ml-8 flex pr-2 pt-4 sm:-ml-10 sm:pr-4",children:F.jsxs("button",{type:"button",className:"relative rounded-md text-gray-300 hover:text-white focus:outline-none focus:ring-2 focus:ring-white",onClick:n,children:[F.jsx("span",{className:"absolute -inset-2.5"}),F.jsx("span",{className:"sr-only",children:"Close panel"}),F.jsx(dm,{className:"h-6 w-6","aria-hidden":"true"})]})})}),F.jsxs("div",{className:"flex h-full flex-col overflow-y-scroll bg-white dark:bg-gray-800 py-6 shadow-xl",children:[F.jsx("div",{className:"px-4 sm:px-6",children:F.jsx(_.Title,{className:"text-base font-semibold leading-6 text-gray-900 dark:text-white",children:F.jsxs("div",{className:"flex items-center justify-between",children:[F.jsxs("div",{className:"flex items-center space-x-2",children:[F.jsx(hc,{className:"h-6 w-6"}),F.jsx("span",{children:"Notifications"}),s>0&&F.jsx("span",{className:"inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800",children:s})]}),s>0&&F.jsx("button",{onClick:()=>{e(mo())},className:"text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300",children:"Mark all read"})]})})}),F.jsx("div",{className:"relative mt-6 flex-1 px-4 sm:px-6",children:a?F.jsx("div",{className:"flex items-center justify-center h-32",children:F.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):0===t.length?F.jsxs("div",{className:"text-center py-12",children:[F.jsx(hc,{className:"mx-auto h-12 w-12 text-gray-400"}),F.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No notifications"}),F.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"You're all caught up!"})]}):F.jsx("div",{className:"space-y-4",children:t.map((t=>F.jsx("div",{className:mm("relative rounded-lg border-l-4 bg-white dark:bg-gray-700 p-4 shadow-sm",o(t.type),t.is_read?"":"bg-blue-50 dark:bg-blue-900/20"),children:F.jsxs("div",{className:"flex",children:[F.jsx("div",{className:"flex-shrink-0",children:l(t.type)}),F.jsx("div",{className:"ml-3 flex-1",children:F.jsxs("div",{className:"flex items-start justify-between",children:[F.jsxs("div",{className:"flex-1",children:[F.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.title}),F.jsx("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-300",children:t.message}),F.jsx("p",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:pm(t.created_at)})]}),F.jsxs("div",{className:"ml-4 flex space-x-2",children:[!t.is_read&&F.jsx("button",{onClick:()=>{return r=t.id,void e(uo(r));var r},className:"text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300",title:"Mark as read",children:F.jsx(kc,{className:"h-4 w-4"})}),F.jsx("button",{onClick:()=>{return r=t.id,void e(ho(r));var r},className:"text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",title:"Delete",children:F.jsx(dm,{className:"h-4 w-4"})})]})]})})]})},t.id)))})})]})]})})})})})]})})},bm=({size:e="md",color:t="blue",className:r="",text:a=null})=>F.jsxs("div",{className:mm("flex flex-col items-center justify-center",r),children:[F.jsxs("svg",{className:mm("animate-spin",{xs:"h-3 w-3",sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12","2xl":"h-16 w-16"}[e],{blue:"text-blue-600",gray:"text-gray-600",green:"text-green-600",red:"text-red-600",yellow:"text-yellow-600",purple:"text-purple-600",pink:"text-pink-600",indigo:"text-indigo-600",white:"text-white"}[t]),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[F.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),F.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a&&F.jsx("span",{className:mm("mt-2 text-gray-600 dark:text-gray-400",{xs:"text-xs",sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl","2xl":"text-2xl"}[e]),children:a})]}),wm=()=>{const e=j(),t=k(Pr),r=k(Mr),a=k(Ir),s=k(Do);return i.useEffect((()=>{"dark"===r?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}),[r]),i.useEffect((()=>{const e=()=>{};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[t,e]),F.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[a&&F.jsx("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center",children:F.jsx(bm,{size:"lg"})}),F.jsx(um,{}),F.jsxs("div",{className:"\n        flex flex-col min-h-screen transition-all duration-300 ease-in-out\n        lg:ml-64\n      ",children:[F.jsx(cm,{}),F.jsx("main",{className:"flex-1 relative overflow-hidden",children:F.jsx("div",{className:"h-full",children:F.jsx(m,{})})})]}),s&&F.jsx(vm,{}),F.jsx(yt,{position:"top-right",toastOptions:{duration:4e3,style:{background:"dark"===r?"#374151":"#ffffff",color:"dark"===r?"#f9fafb":"#111827",border:"1px solid "+("dark"===r?"#4b5563":"#e5e7eb")},success:{iconTheme:{primary:"#10b981",secondary:"#ffffff"}},error:{iconTheme:{primary:"#ef4444",secondary:"#ffffff"}}}})]})},jm=i.forwardRef((({children:e,type:t="button",variant:r="primary",size:a="md",color:s="blue",loading:n=!1,disabled:i=!1,fullWidth:l=!1,leftIcon:o,rightIcon:d,loadingText:c,onClick:u,className:m="",...h},g)=>{const p={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-4 w-4",lg:"h-5 w-5",xl:"h-5 w-5"},f=i||n,x=mm("inline-flex items-center justify-center font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{xs:"px-2.5 py-1.5 text-xs",sm:"px-3 py-2 text-sm",md:"px-4 py-2 text-sm",lg:"px-4 py-2 text-base",xl:"px-6 py-3 text-base"}[a],{primary:{blue:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 dark:bg-blue-600 dark:hover:bg-blue-700",green:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 dark:bg-green-600 dark:hover:bg-green-700",red:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 dark:bg-red-600 dark:hover:bg-red-700",yellow:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 dark:bg-yellow-600 dark:hover:bg-yellow-700",purple:"bg-purple-600 text-white hover:bg-purple-700 focus:ring-purple-500 dark:bg-purple-600 dark:hover:bg-purple-700",pink:"bg-pink-600 text-white hover:bg-pink-700 focus:ring-pink-500 dark:bg-pink-600 dark:hover:bg-pink-700",indigo:"bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500 dark:bg-indigo-600 dark:hover:bg-indigo-700",gray:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 dark:bg-gray-600 dark:hover:bg-gray-700"},secondary:{blue:"bg-blue-100 text-blue-700 hover:bg-blue-200 focus:ring-blue-500 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800",green:"bg-green-100 text-green-700 hover:bg-green-200 focus:ring-green-500 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800",red:"bg-red-100 text-red-700 hover:bg-red-200 focus:ring-red-500 dark:bg-red-900 dark:text-red-300 dark:hover:bg-red-800",yellow:"bg-yellow-100 text-yellow-700 hover:bg-yellow-200 focus:ring-yellow-500 dark:bg-yellow-900 dark:text-yellow-300 dark:hover:bg-yellow-800",purple:"bg-purple-100 text-purple-700 hover:bg-purple-200 focus:ring-purple-500 dark:bg-purple-900 dark:text-purple-300 dark:hover:bg-purple-800",pink:"bg-pink-100 text-pink-700 hover:bg-pink-200 focus:ring-pink-500 dark:bg-pink-900 dark:text-pink-300 dark:hover:bg-pink-800",indigo:"bg-indigo-100 text-indigo-700 hover:bg-indigo-200 focus:ring-indigo-500 dark:bg-indigo-900 dark:text-indigo-300 dark:hover:bg-indigo-800",gray:"bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"},outline:{blue:"border border-blue-300 text-blue-700 hover:bg-blue-50 focus:ring-blue-500 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900",green:"border border-green-300 text-green-700 hover:bg-green-50 focus:ring-green-500 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900",red:"border border-red-300 text-red-700 hover:bg-red-50 focus:ring-red-500 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900",yellow:"border border-yellow-300 text-yellow-700 hover:bg-yellow-50 focus:ring-yellow-500 dark:border-yellow-600 dark:text-yellow-400 dark:hover:bg-yellow-900",purple:"border border-purple-300 text-purple-700 hover:bg-purple-50 focus:ring-purple-500 dark:border-purple-600 dark:text-purple-400 dark:hover:bg-purple-900",pink:"border border-pink-300 text-pink-700 hover:bg-pink-50 focus:ring-pink-500 dark:border-pink-600 dark:text-pink-400 dark:hover:bg-pink-900",indigo:"border border-indigo-300 text-indigo-700 hover:bg-indigo-50 focus:ring-indigo-500 dark:border-indigo-600 dark:text-indigo-400 dark:hover:bg-indigo-900",gray:"border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700"},ghost:{blue:"text-blue-700 hover:bg-blue-100 focus:ring-blue-500 dark:text-blue-400 dark:hover:bg-blue-900",green:"text-green-700 hover:bg-green-100 focus:ring-green-500 dark:text-green-400 dark:hover:bg-green-900",red:"text-red-700 hover:bg-red-100 focus:ring-red-500 dark:text-red-400 dark:hover:bg-red-900",yellow:"text-yellow-700 hover:bg-yellow-100 focus:ring-yellow-500 dark:text-yellow-400 dark:hover:bg-yellow-900",purple:"text-purple-700 hover:bg-purple-100 focus:ring-purple-500 dark:text-purple-400 dark:hover:bg-purple-900",pink:"text-pink-700 hover:bg-pink-100 focus:ring-pink-500 dark:text-pink-400 dark:hover:bg-pink-900",indigo:"text-indigo-700 hover:bg-indigo-100 focus:ring-indigo-500 dark:text-indigo-400 dark:hover:bg-indigo-900",gray:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-700"},link:{blue:"text-blue-600 hover:text-blue-500 underline-offset-4 hover:underline focus:ring-blue-500 dark:text-blue-400 dark:hover:text-blue-300",green:"text-green-600 hover:text-green-500 underline-offset-4 hover:underline focus:ring-green-500 dark:text-green-400 dark:hover:text-green-300",red:"text-red-600 hover:text-red-500 underline-offset-4 hover:underline focus:ring-red-500 dark:text-red-400 dark:hover:text-red-300",yellow:"text-yellow-600 hover:text-yellow-500 underline-offset-4 hover:underline focus:ring-yellow-500 dark:text-yellow-400 dark:hover:text-yellow-300",purple:"text-purple-600 hover:text-purple-500 underline-offset-4 hover:underline focus:ring-purple-500 dark:text-purple-400 dark:hover:text-purple-300",pink:"text-pink-600 hover:text-pink-500 underline-offset-4 hover:underline focus:ring-pink-500 dark:text-pink-400 dark:hover:text-pink-300",indigo:"text-indigo-600 hover:text-indigo-500 underline-offset-4 hover:underline focus:ring-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300",gray:"text-gray-600 hover:text-gray-500 underline-offset-4 hover:underline focus:ring-gray-500 dark:text-gray-400 dark:hover:text-gray-300"}}[r][s],l&&"w-full",m);return F.jsx("button",{ref:g,type:t,className:x,disabled:f,onClick:e=>{!f&&u&&u(e)},...h,children:n?F.jsxs(F.Fragment,{children:[F.jsx(bm,{size:"xs"===a?"xs":"sm",color:"white",className:"mr-2"}),c||"Loading..."]}):F.jsxs(F.Fragment,{children:[o&&F.jsx(o,{className:mm(p[a],e&&"mr-2")}),e,d&&F.jsx(d,{className:mm(p[a],e&&"ml-2")})]})})}));jm.displayName="Button";const km=({isOpen:e=!1,onClose:t,title:r,description:a,children:s,footer:n,size:l="md",closeOnOverlayClick:o=!0,showCloseButton:d=!0,preventClose:c=!1,className:u="",overlayClassName:m="",contentClassName:h="",headerClassName:g="",bodyClassName:p="",footerClassName:f="",...x})=>{const y=()=>{!c&&t&&t()};return F.jsx(S,{appear:!0,show:e,as:i.Fragment,children:F.jsxs(_,{as:"div",className:"relative z-50",onClose:y,...x,children:[F.jsx(S.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:F.jsx("div",{className:mm("fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm",m),onClick:()=>{o&&y()}})}),F.jsx("div",{className:"fixed inset-0 overflow-y-auto",children:F.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:F.jsx(S.Child,{as:i.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:F.jsxs(_.Panel,{className:mm("w-full transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left align-middle shadow-xl transition-all",{xs:"max-w-xs",sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl","3xl":"max-w-3xl","4xl":"max-w-4xl","5xl":"max-w-5xl","6xl":"max-w-6xl","7xl":"max-w-7xl",full:"max-w-full"}[l],h,u),children:[(r||a||d)&&F.jsxs("div",{className:mm("flex items-start justify-between p-6 border-b border-gray-200 dark:border-gray-700",g),children:[F.jsxs("div",{className:"flex-1",children:[r&&F.jsx(_.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900 dark:text-white",children:r}),a&&F.jsx("div",{className:"mt-2",children:F.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:a})})]}),d&&F.jsxs("button",{type:"button",className:"ml-4 rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",onClick:y,disabled:c,children:[F.jsx("span",{className:"sr-only",children:"Close"}),F.jsx(dm,{className:"h-6 w-6","aria-hidden":"true"})]})]}),F.jsx("div",{className:mm("p-6",p),children:s}),n&&F.jsx("div",{className:mm("flex items-center justify-end space-x-3 px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600",f),children:n})]})})})})]})})},Cm=({isOpen:e=!1,onClose:t,onConfirm:r,title:a="Confirm Action",message:s="Are you sure you want to proceed?",confirmText:n="Confirm",cancelText:i="Cancel",confirmColor:l="red",loading:o=!1,...d})=>{const c=F.jsxs(F.Fragment,{children:[F.jsx(jm,{variant:"outline",color:"gray",onClick:t,disabled:o,children:i}),F.jsx(jm,{variant:"primary",color:l,onClick:()=>{r&&r()},loading:o,children:n})]});return F.jsx(km,{isOpen:e,onClose:t,title:a,footer:c,size:"sm",preventClose:o,...d,children:F.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:s})})},Nm=({data:e=[],columns:t=[],loading:r=!1,error:a=null,sortBy:s=null,sortOrder:n="asc",onSort:i,searchQuery:l="",onSearch:o,selectedRows:d=[],onSelectRow:c,onSelectAll:u,selectable:m=!1,actions:h=[],onRowClick:g,emptyMessage:p="No data available",className:f="",tableClassName:x="",headerClassName:y="",bodyClassName:v="",rowClassName:b="",cellClassName:w="",...j})=>{const k=e=>d.includes(e),C=e.length>0&&d.length===e.length,N=d.length>0&&d.length<e.length,S=(e,t)=>{if(t.render)return t.render(e[t.key],e);const r=e[t.key];return null==r?F.jsx("span",{className:"text-gray-400",children:"-"}):r},L=e=>h&&0!==h.length?F.jsxs(E,{as:"div",className:"relative",children:[F.jsx(E.Button,{className:"p-1 rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500",children:F.jsx(Xc,{className:"h-5 w-5"})}),F.jsx(E.Items,{className:"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-700",children:F.jsx("div",{className:"py-1",children:h.map(((t,r)=>F.jsx(E.Item,{children:({active:r})=>{var a,s;return F.jsxs("button",{onClick:()=>t.onClick(e),disabled:null==(a=t.disabled)?void 0:a.call(t,e),className:mm("flex w-full items-center px-4 py-2 text-sm",r?"bg-gray-100 text-gray-900 dark:bg-gray-600 dark:text-white":"text-gray-700 dark:text-gray-300",(null==(s=t.disabled)?void 0:s.call(t,e))&&"opacity-50 cursor-not-allowed"),children:[t.icon&&F.jsx(t.icon,{className:"mr-3 h-4 w-4"}),t.label]})}},r)))})})]}):null;return a?F.jsxs("div",{className:"text-center py-12",children:[F.jsx("div",{className:"text-red-500 mb-2",children:"Error loading data"}),F.jsx("div",{className:"text-sm text-gray-500",children:a})]}):F.jsxs("div",{className:mm("w-full",f),...j,children:[o&&F.jsx("div",{className:"mb-4",children:F.jsxs("div",{className:"relative",children:[F.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:F.jsx(vu,{className:"h-5 w-5 text-gray-400"})}),F.jsx("input",{type:"text",placeholder:"Search...",value:l,onChange:e=>o(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]})}),F.jsx("div",{className:"overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg",children:F.jsx("div",{className:"overflow-x-auto",children:F.jsxs("table",{className:mm("min-w-full divide-y divide-gray-300 dark:divide-gray-600",x),children:[F.jsx("thead",{className:mm("bg-gray-50 dark:bg-gray-700",y),children:F.jsxs("tr",{children:[m&&F.jsx("th",{className:"relative w-12 px-6 sm:w-16 sm:px-8",children:F.jsx("input",{type:"checkbox",checked:C,ref:e=>{e&&(e.indeterminate=N)},onChange:t=>{return r=t.target.checked,void(u&&u(r?e.map((e=>e.id)):[]));var r},className:"absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),t.map((e=>{return F.jsx("th",{className:mm("px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300",e.sortable&&"cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600",e.className),onClick:()=>{return e.sortable&&(t=e.key,void(i&&i(t,s===t&&"asc"===n?"desc":"asc")));var t},children:F.jsxs("div",{className:"flex items-center space-x-1",children:[F.jsx("span",{children:e.title}),e.sortable&&(t=e.key,s!==t?F.jsx(Rc,{className:"h-4 w-4 text-gray-400"}):"asc"===n?F.jsx(Tc,{className:"h-4 w-4 text-gray-600"}):F.jsx(_c,{className:"h-4 w-4 text-gray-600"}))]})},e.key);var t})),h&&h.length>0&&F.jsx("th",{className:"relative px-6 py-3",children:F.jsx("span",{className:"sr-only",children:"Actions"})})]})}),F.jsx("tbody",{className:mm("bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700",v),children:r?F.jsx("tr",{children:F.jsx("td",{colSpan:t.length+(m?1:0)+((null==h?void 0:h.length)>0?1:0),className:"px-6 py-12 text-center",children:F.jsx(bm,{size:"lg"})})}):0===e.length?F.jsx("tr",{children:F.jsx("td",{colSpan:t.length+(m?1:0)+((null==h?void 0:h.length)>0?1:0),className:"px-6 py-12 text-center text-gray-500 dark:text-gray-400",children:p})}):e.map(((e,r)=>F.jsxs("tr",{className:mm("hover:bg-gray-50 dark:hover:bg-gray-700",g&&"cursor-pointer",k(e.id)&&"bg-blue-50 dark:bg-blue-900/20",b),onClick:()=>null==g?void 0:g(e),children:[m&&F.jsx("td",{className:"relative w-12 px-6 sm:w-16 sm:px-8",children:F.jsx("input",{type:"checkbox",checked:k(e.id),onChange:t=>{var r,a;t.stopPropagation(),r=e.id,a=t.target.checked,c&&c(r,a)},className:"absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),t.map((t=>F.jsx("td",{className:mm("px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",w,t.cellClassName),children:S(e,t)},t.key))),h&&h.length>0&&F.jsx("td",{className:"relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6",children:F.jsx("div",{onClick:e=>e.stopPropagation(),children:L(e)})})]},e.id||r)))})]})})})]})},Em=i.forwardRef((({label:e,value:t,onChange:r,options:a=[],placeholder:s="Select an option...",error:n,helperText:l,required:d=!1,disabled:c=!1,multiple:u=!1,searchable:m=!1,clearable:h=!1,size:g="md",className:p="",labelClassName:f="",errorClassName:x="",helperClassName:y="",optionKey:v="value",optionLabel:b="label",optionDisabled:w="disabled",renderOption:j,renderValue:k,...C},N)=>{const[E,L]=o.useState(""),_=o.useId(),P=o.useId(),A=o.useId(),M=o.useMemo((()=>m&&E?a.filter((e=>("string"==typeof e?e:e[b]).toLowerCase().includes(E.toLowerCase()))):a),[a,E,m,b]),I=e=>"string"==typeof e?e:e[v],R=e=>"string"==typeof e?e:e[b],T=e=>"string"!=typeof e&&e[w],D=e=>{null==r||r(e)},W=h&&(u&&Array.isArray(t)&&t.length>0||!u&&t);return F.jsxs("div",{className:mm("w-full",p),children:[e&&F.jsx("label",{htmlFor:_,className:mm("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",d&&"after:content-['*'] after:ml-0.5 after:text-red-500",f),children:e}),F.jsx(O,{value:t,onChange:D,multiple:u,disabled:c,...C,children:F.jsxs("div",{className:"relative",children:[F.jsxs(O.Button,{ref:N,className:mm("relative w-full cursor-default rounded-md border bg-white text-left shadow-sm transition-colors duration-200","focus:outline-none focus:ring-1 focus:border-blue-500 focus:ring-blue-500","dark:bg-gray-700 dark:border-gray-600 dark:text-white",{sm:"px-3 py-2 text-sm",md:"px-3 py-2 text-sm",lg:"px-4 py-3 text-base"}[g],n?"border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500 dark:border-red-500":"border-gray-300",c&&"bg-gray-50 text-gray-500 cursor-not-allowed dark:bg-gray-800"),children:[F.jsx("span",{className:mm("block truncate",(!t||u&&(!Array.isArray(t)||0===t.length))&&"text-gray-400"),children:k?k(t,a):(()=>{if(u){if(!Array.isArray(t)||0===t.length)return s;if(1===t.length){const e=a.find((e=>I(e)===t[0]));return e?R(e):t[0]}return`${t.length} selected`}if(!t)return s;const e=a.find((e=>I(e)===t));return e?R(e):t})()}),F.jsxs("span",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2",children:[W&&F.jsx("button",{type:"button",onClick:e=>{e.stopPropagation(),D(u?[]:"")},className:"pointer-events-auto mr-1 text-gray-400 hover:text-gray-600 focus:outline-none",children:F.jsx(dm,{className:"h-4 w-4"})}),F.jsx(Rc,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})]})]}),F.jsx(S,{as:i.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:F.jsxs(O.Options,{className:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-700 dark:ring-gray-600 sm:text-sm",children:[m&&F.jsx("div",{className:"sticky top-0 bg-white dark:bg-gray-700 px-3 py-2 border-b border-gray-200 dark:border-gray-600",children:F.jsx("input",{type:"text",placeholder:"Search...",value:E,onChange:e=>L(e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"})}),0===M.length?F.jsx("div",{className:"relative cursor-default select-none py-2 px-4 text-gray-700 dark:text-gray-300",children:E?"No results found":"No options available"}):M.map(((e,t)=>F.jsx(O.Option,{className:({active:t})=>mm("relative cursor-default select-none py-2 pl-10 pr-4",t?"bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100":"text-gray-900 dark:text-gray-100",T(e)&&"opacity-50 cursor-not-allowed"),value:I(e),disabled:T(e),children:({selected:t})=>F.jsxs(F.Fragment,{children:[F.jsx("span",{className:mm("block truncate",t?"font-medium":"font-normal"),children:j?j(e,t):R(e)}),t&&F.jsx("span",{className:"absolute inset-y-0 left-0 flex items-center pl-3 text-blue-600 dark:text-blue-400",children:F.jsx(kc,{className:"h-5 w-5","aria-hidden":"true"})})]})},`${I(e)}-${t}`)))]})})]})}),n&&F.jsx("p",{id:P,className:mm("mt-1 text-sm text-red-600 dark:text-red-400",x),children:n}),l&&!n&&F.jsx("p",{id:A,className:mm("mt-1 text-sm text-gray-500 dark:text-gray-400",y),children:l})]})}));Em.displayName="Select";const Sm=({currentPage:e=1,totalPages:t=1,totalItems:r=0,itemsPerPage:a=15,onPageChange:s,onItemsPerPageChange:n,showItemsPerPage:i=!0,showPageInfo:l=!0,showFirstLast:o=!0,maxVisiblePages:d=5,itemsPerPageOptions:c=[10,15,25,50,100],size:u="md",className:m="",...h})=>{const g=0===r?0:(e-1)*a+1,p=Math.min(e*a,r),f=r=>{r>=1&&r<=t&&r!==e&&(null==s||s(r))},x=(()=>{const r=[],a=Math.floor(d/2);let s=Math.max(1,e-a),n=Math.min(t,s+d-1);n-s+1<d&&(s=Math.max(1,n-d+1));for(let e=s;e<=n;e++)r.push(e);return r})(),y={sm:{button:"px-2 py-1 text-xs",select:"text-xs",text:"text-xs"},md:{button:"px-3 py-2 text-sm",select:"text-sm",text:"text-sm"},lg:{button:"px-4 py-2 text-base",select:"text-base",text:"text-base"}},v=mm("inline-flex items-center justify-center border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600",y[u].button),b=t=>mm(v,t===e?"bg-blue-50 border-blue-500 text-blue-600 dark:bg-blue-900 dark:border-blue-500 dark:text-blue-300":"hover:text-gray-700 dark:hover:text-gray-200");return t<=1?null:F.jsxs("div",{className:mm("flex items-center justify-between bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700",m),...h,children:[F.jsxs("div",{className:"flex items-center space-x-4",children:[i&&n&&F.jsxs("div",{className:"flex items-center space-x-2",children:[F.jsx("span",{className:mm("text-gray-700 dark:text-gray-300",y[u].text),children:"Show"}),F.jsx(Em,{value:a,onChange:e=>{null==n||n(e)},options:c.map((e=>({value:e,label:e.toString()}))),size:u,className:"w-20"}),F.jsx("span",{className:mm("text-gray-700 dark:text-gray-300",y[u].text),children:"per page"})]}),l&&F.jsxs("div",{className:mm("text-gray-700 dark:text-gray-300",y[u].text),children:["Showing ",g," to ",p," of ",r," results"]})]}),F.jsxs("div",{className:"flex items-center space-x-1",children:[o&&e>1&&F.jsx("button",{onClick:()=>f(1),className:mm(v,"rounded-l-md"),title:"First page",children:F.jsx(Nc,{className:"h-4 w-4"})}),F.jsx("button",{onClick:()=>f(e-1),disabled:e<=1,className:mm(v,!o&&"rounded-l-md"),title:"Previous page",children:F.jsx(Pc,{className:"h-4 w-4"})}),x.map((e=>F.jsx("button",{onClick:()=>f(e),className:b(e),children:e},e))),F.jsx("button",{onClick:()=>f(e+1),disabled:e>=t,className:mm(v,!o&&"rounded-r-md"),title:"Next page",children:F.jsx(Mc,{className:"h-4 w-4"})}),o&&e<t&&F.jsx("button",{onClick:()=>f(t),className:mm(v,"rounded-r-md"),title:"Last page",children:F.jsx(Sc,{className:"h-4 w-4"})})]})]})};class Lm extends o.Component{constructor(e){super(e),r(this,"handleReload",(()=>{window.location.reload()})),r(this,"handleReset",(()=>{this.setState({hasError:!1,error:null,errorInfo:null})})),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?F.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[F.jsxs("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[F.jsx("div",{className:"mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20",children:F.jsx(nu,{className:"h-8 w-8 text-red-600 dark:text-red-400"})}),F.jsxs("div",{className:"mt-6 text-center",children:[F.jsx("h1",{className:"text-3xl font-extrabold text-gray-900 dark:text-white",children:"Something went wrong"}),F.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"We're sorry, but something unexpected happened."})]})]}),F.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:F.jsxs("div",{className:"bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[!1,F.jsxs("div",{className:"space-y-4",children:[F.jsx(jm,{fullWidth:!0,leftIcon:ac,onClick:this.handleReload,children:"Reload Page"}),F.jsx(jm,{fullWidth:!0,variant:"outline",onClick:this.handleReset,children:"Try Again"})]}),F.jsx("div",{className:"mt-6 text-center",children:F.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["If this problem persists, please contact support at"," ",F.jsx("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:text-blue-500 dark:text-blue-400",children:"<EMAIL>"})]})})]})})]}):this.props.children}}const _m=({children:e,requiredRole:t=null,requiredPermissions:r=[],fallbackPath:a="/login"})=>{const s=k(Ut),n=k(Zt),i=u();if(n)return F.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:F.jsx(bm,{size:"lg",text:"Checking authentication..."})});if(!s)return F.jsx(h,{to:a,state:{from:i},replace:!0});if(t&&s.role!==t){const e={employee:1,manager:2,admin:3,super_admin:4};if((e[s.role]||0)<(e[t]||0))return F.jsx(h,{to:"/unauthorized",replace:!0})}if(r.length>0){const e=s.permissions||[];if(!r.every((t=>e.includes(t))))return F.jsx(h,{to:"/unauthorized",replace:!0})}return e},Om=({children:e,redirectPath:t="/dashboard",restricted:r=!0})=>{const a=k(Ut);return k(Zt)?F.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:F.jsx(bm,{size:"lg",text:"Loading..."})}):r&&a?F.jsx(h,{to:t,replace:!0}):e},Pm=i.forwardRef((({label:e,type:t="text",placeholder:r,value:a,onChange:s,onBlur:n,onFocus:i,error:l,helperText:d,required:c=!1,disabled:u=!1,readOnly:m=!1,autoComplete:h,autoFocus:g=!1,maxLength:p,minLength:f,pattern:x,size:y="md",variant:v="default",leftIcon:b,rightIcon:w,leftElement:j,rightElement:k,className:C="",inputClassName:N="",labelClassName:E="",errorClassName:S="",helperClassName:L="",showPasswordToggle:_=!1,...O},P)=>{const[A,M]=o.useState(!1),[I,R]=o.useState(!1),T=o.useId(),D=o.useId(),W=o.useId(),B="password"===t&&A?"text":t,$=mm("block w-full rounded-md shadow-sm transition-colors duration-200","placeholder-gray-400 text-gray-900 dark:text-white","dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400","focus:outline-none focus:ring-1",{sm:"px-3 py-2 text-sm",md:"px-3 py-2 text-sm",lg:"px-4 py-3 text-base"}[y],l?"border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-500":{default:"border-gray-300 focus:border-blue-500 focus:ring-blue-500",filled:"border-transparent bg-gray-100 focus:bg-white focus:border-blue-500 focus:ring-blue-500",outlined:"border-2 border-gray-300 focus:border-blue-500 focus:ring-0"}[v],u&&"bg-gray-50 text-gray-500 cursor-not-allowed dark:bg-gray-800",m&&"bg-gray-50 cursor-default dark:bg-gray-800",(b||j)&&"pl-10",(w||k||"password"===t&&_||l)&&"pr-10",N);return F.jsxs("div",{className:mm("w-full",C),children:[e&&F.jsx("label",{htmlFor:T,className:mm("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",c&&"after:content-['*'] after:ml-0.5 after:text-red-500",E),children:e}),F.jsxs("div",{className:"relative",children:[b&&F.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:F.jsx(b,{className:"h-5 w-5 text-gray-400"})}),j&&F.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center",children:j}),F.jsx("input",{ref:P,id:T,type:B,value:a,onChange:s,onFocus:e=>{R(!0),null==i||i(e)},onBlur:e=>{R(!1),null==n||n(e)},placeholder:r,required:c,disabled:u,readOnly:m,autoComplete:h,autoFocus:g,maxLength:p,minLength:f,pattern:x,className:$,"aria-invalid":l?"true":"false","aria-describedby":mm(l&&D,d&&W),...O}),F.jsxs("div",{className:"absolute inset-y-0 right-0 flex items-center",children:[l&&F.jsx(au,{className:"h-5 w-5 text-red-500 mr-3"}),"password"===t&&_&&F.jsx("button",{type:"button",onClick:()=>{M(!A)},className:"mr-3 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600",tabIndex:-1,children:A?F.jsx(lu,{className:"h-5 w-5"}):F.jsx(du,{className:"h-5 w-5"})}),w&&!l&&F.jsx("div",{className:"mr-3",children:F.jsx(w,{className:"h-5 w-5 text-gray-400"})}),k&&!l&&F.jsx("div",{className:"mr-3",children:k})]})]}),l&&F.jsx("p",{id:D,className:mm("mt-1 text-sm text-red-600 dark:text-red-400",S),children:l}),d&&!l&&F.jsx("p",{id:W,className:mm("mt-1 text-sm text-gray-500 dark:text-gray-400",L),children:d})]})}));Pm.displayName="Input";const Am=i.forwardRef((({label:e,placeholder:t,value:r,onChange:a,onBlur:s,onFocus:n,error:i,helperText:l,required:d=!1,disabled:c=!1,readOnly:u=!1,autoFocus:m=!1,maxLength:h,minLength:g,rows:p=4,cols:f,resize:x="vertical",autoResize:y=!1,showCharCount:v=!1,size:b="md",variant:w="default",className:j="",textareaClassName:k="",labelClassName:C="",errorClassName:N="",helperClassName:E="",...S},L)=>{const[_,O]=o.useState(!1),P=o.useRef(null),A=o.useId(),M=o.useId(),I=o.useId();o.useEffect((()=>{if(y&&P.current){const e=P.current;e.style.height="auto",e.style.height=`${e.scrollHeight}px`}}),[r,y]);const R=mm("block w-full rounded-md shadow-sm transition-colors duration-200","placeholder-gray-400 text-gray-900 dark:text-white","dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400","focus:outline-none focus:ring-1",{sm:"px-3 py-2 text-sm",md:"px-3 py-2 text-sm",lg:"px-4 py-3 text-base"}[b],{none:"resize-none",vertical:"resize-y",horizontal:"resize-x",both:"resize"}[x],i?"border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-500":{default:"border-gray-300 focus:border-blue-500 focus:ring-blue-500",filled:"border-transparent bg-gray-100 focus:bg-white focus:border-blue-500 focus:ring-blue-500",outlined:"border-2 border-gray-300 focus:border-blue-500 focus:ring-0"}[w],c&&"bg-gray-50 text-gray-500 cursor-not-allowed dark:bg-gray-800",u&&"bg-gray-50 cursor-default dark:bg-gray-800",k),T=r?r.length:0,D=h&&T>h;return F.jsxs("div",{className:mm("w-full",j),children:[e&&F.jsx("label",{htmlFor:A,className:mm("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",d&&"after:content-['*'] after:ml-0.5 after:text-red-500",C),children:e}),F.jsxs("div",{className:"relative",children:[F.jsx("textarea",{ref:e=>{P.current=e,"function"==typeof L?L(e):L&&(L.current=e)},id:A,value:r,onChange:e=>{if(null==a||a(e),y&&P.current){const e=P.current;e.style.height="auto",e.style.height=`${e.scrollHeight}px`}},onFocus:e=>{O(!0),null==n||n(e)},onBlur:e=>{O(!1),null==s||s(e)},placeholder:t,required:d,disabled:c,readOnly:u,autoFocus:m,maxLength:h,minLength:g,rows:y?1:p,cols:f,className:R,"aria-invalid":i?"true":"false","aria-describedby":mm(i&&M,l&&I),...S}),i&&F.jsx("div",{className:"absolute top-2 right-2",children:F.jsx(au,{className:"h-5 w-5 text-red-500"})})]}),F.jsxs("div",{className:"flex justify-between items-start mt-1",children:[F.jsxs("div",{className:"flex-1",children:[i&&F.jsx("p",{id:M,className:mm("text-sm text-red-600 dark:text-red-400",N),children:i}),l&&!i&&F.jsx("p",{id:I,className:mm("text-sm text-gray-500 dark:text-gray-400",E),children:l})]}),v&&(h||T>0)&&F.jsx("div",{className:"flex-shrink-0 ml-2",children:F.jsxs("span",{className:mm("text-xs",D?"text-red-600 dark:text-red-400":"text-gray-500 dark:text-gray-400"),children:[T,h&&`/${h}`]})})]})]})}));Am.displayName="Textarea";const Mm=i.forwardRef((({label:e,description:t,checked:r=!1,indeterminate:a=!1,onChange:s,onBlur:n,onFocus:i,error:l,required:d=!1,disabled:c=!1,size:u="md",color:m="blue",labelPosition:h="right",className:g="",checkboxClassName:p="",labelClassName:f="",descriptionClassName:x="",errorClassName:y="",...v},b)=>{const[w,j]=o.useState(!1),k=o.useRef(null),C=o.useId(),N=o.useId();o.useEffect((()=>{k.current&&(k.current.indeterminate=a)}),[a]);const E={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"},S={sm:"text-sm",md:"text-sm",lg:"text-base"},L={sm:"text-xs",md:"text-sm",lg:"text-sm"},_=mm("rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700","focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800","transition-colors duration-200",E[u],{blue:"text-blue-600 focus:ring-blue-500",green:"text-green-600 focus:ring-green-500",red:"text-red-600 focus:ring-red-500",yellow:"text-yellow-600 focus:ring-yellow-500",purple:"text-purple-600 focus:ring-purple-500",pink:"text-pink-600 focus:ring-pink-500",indigo:"text-indigo-600 focus:ring-indigo-500",gray:"text-gray-600 focus:ring-gray-500"}[m],l&&"border-red-300 text-red-600 focus:ring-red-500 dark:border-red-500",c&&"opacity-50 cursor-not-allowed",p),O=e=>{j(!0),null==i||i(e)},P=e=>{j(!1),null==n||n(e)},A=e=>{null==s||s(e.target.checked,e)};return F.jsxs("div",{className:mm("w-full",g),children:[F.jsxs("div",{className:mm("flex items-start","left"===h?"flex-row-reverse":"flex-row","left"===h?"justify-end":"justify-start"),children:[F.jsxs("div",{className:"relative flex items-center",children:[F.jsx("input",{ref:e=>{k.current=e,"function"==typeof b?b(e):b&&(b.current=e)},id:C,type:"checkbox",checked:r,onChange:A,onFocus:O,onBlur:P,required:d,disabled:c,className:_,"aria-invalid":l?"true":"false","aria-describedby":l?N:void 0,...v}),F.jsx("div",{className:mm("absolute inset-0 flex items-center justify-center pointer-events-none",E[u]),children:a?F.jsx(ku,{className:mm("text-white","sm"===u?"h-3 w-3":"md"===u?"h-4 w-4":"h-5 w-5")}):r?F.jsx(kc,{className:mm("text-white","sm"===u?"h-3 w-3":"md"===u?"h-4 w-4":"h-5 w-5")}):null})]}),(e||t)&&F.jsx("div",{className:mm("flex-1","left"===h?"mr-3":"ml-3"),children:F.jsxs("div",{className:"flex-1",children:[e&&F.jsx("label",{htmlFor:C,className:mm("font-medium text-gray-900 dark:text-white cursor-pointer",S[u],d&&"after:content-['*'] after:ml-0.5 after:text-red-500",c&&"opacity-50 cursor-not-allowed",f),children:e}),t&&F.jsx("p",{className:mm("text-gray-500 dark:text-gray-400",L[u],c&&"opacity-50",x),children:t})]})})]}),l&&F.jsx("p",{id:N,className:mm("mt-1 text-sm text-red-600 dark:text-red-400",y),children:l})]})}));Mm.displayName="Checkbox";const Im=i.forwardRef((({label:e,value:t,onChange:r,onBlur:a,onFocus:s,error:n,helperText:i,required:l=!1,disabled:d=!1,readOnly:c=!1,autoFocus:u=!1,min:m,max:h,placeholder:g="Select date...",showTime:p=!1,clearable:f=!1,size:x="md",format:y="medium",className:v="",inputClassName:b="",labelClassName:w="",errorClassName:j="",helperClassName:k="",...C},N)=>{const[E,S]=o.useState(!1),L=o.useId(),_=o.useId(),O=o.useId(),P=mm("block w-full rounded-md border shadow-sm transition-colors duration-200","placeholder-gray-400 text-gray-900 dark:text-white","dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400","focus:outline-none focus:ring-1 focus:border-blue-500 focus:ring-blue-500","pr-10",{sm:"px-3 py-2 text-sm",md:"px-3 py-2 text-sm",lg:"px-4 py-3 text-base"}[x],n?"border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-500":"border-gray-300",d&&"bg-gray-50 text-gray-500 cursor-not-allowed dark:bg-gray-800",c&&"bg-gray-50 cursor-default dark:bg-gray-800",f&&t&&"pr-16",b);return F.jsxs("div",{className:mm("w-full",v),children:[e&&F.jsx("label",{htmlFor:L,className:mm("block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",l&&"after:content-['*'] after:ml-0.5 after:text-red-500",w),children:e}),F.jsxs("div",{className:"relative",children:[F.jsx("input",{ref:N,id:L,type:p?"datetime-local":"date",value:(()=>{if(!t)return"";if("string"==typeof t)return t;if(t instanceof Date){if(p){return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}T${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`}return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`}return t})(),onChange:e=>{const t=e.target.value;null==r||r(t)},onFocus:e=>{S(!0),null==s||s(e)},onBlur:e=>{S(!1),null==a||a(e)},placeholder:g,required:l,disabled:d,readOnly:c,autoFocus:u,min:m,max:h,className:P,"aria-invalid":n?"true":"false","aria-describedby":mm(n&&_,i&&O),...C}),F.jsxs("div",{className:"absolute inset-y-0 right-0 flex items-center",children:[f&&t&&!d&&!c&&F.jsx("button",{type:"button",onClick:e=>{e.stopPropagation(),null==r||r("")},className:"mr-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600",tabIndex:-1,children:F.jsx(dm,{className:"h-4 w-4"})}),F.jsx("div",{className:"mr-3 pointer-events-none",children:F.jsx(xc,{className:"h-5 w-5 text-gray-400"})})]})]}),n&&F.jsx("p",{id:_,className:mm("mt-1 text-sm text-red-600 dark:text-red-400",j),children:n}),i&&!n&&F.jsx("p",{id:O,className:mm("mt-1 text-sm text-gray-500 dark:text-gray-400",k),children:i})]})}));Im.displayName="DatePicker";const Rm=()=>{const e=j(),t=d(),r=u(),[a,s]=i.useState({email:"",password:"",remember_me:!1}),[n,l]=i.useState(!1),o=k(Zt),m=k(zt),h=k(Ut);i.useEffect((()=>{var e,a;if(h){const s=(null==(a=null==(e=r.state)?void 0:e.from)?void 0:a.pathname)||"/dashboard";t(s,{replace:!0})}}),[h,t,r]),i.useEffect((()=>()=>{e(Ft())}),[e]);const g=e=>{const{name:t,value:r,type:a,checked:n}=e.target;s((e=>({...e,[t]:"checkbox"===a?n:r})))},p=a.email&&a.password;return F.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:F.jsxs("div",{className:"max-w-md w-full space-y-8",children:[F.jsxs("div",{children:[F.jsx("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900",children:F.jsx(xu,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),F.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white",children:"Sign in to your account"}),F.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:["Or"," ",F.jsx(c,{to:"/register",className:"font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300",children:"create a new account"})]})]}),F.jsxs("form",{className:"mt-8 space-y-6",onSubmit:async s=>{var n,i;s.preventDefault();const l=await e(qt({email:a.email,password:a.password,remember_me:a.remember_me}));if(qt.fulfilled.match(l)){const e=(null==(i=null==(n=r.state)?void 0:n.from)?void 0:i.pathname)||"/dashboard";t(e,{replace:!0})}},children:[F.jsxs("div",{className:"space-y-4",children:[F.jsx(Pm,{label:"Email address",name:"email",type:"email",autoComplete:"email",required:!0,value:a.email,onChange:g,placeholder:"Enter your email",leftIcon:tm,error:"email"===(null==m?void 0:m.field)?m.message:""}),F.jsx(Pm,{label:"Password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:a.password,onChange:g,placeholder:"Enter your password",leftIcon:xu,showPasswordToggle:!0,error:"password"===(null==m?void 0:m.field)?m.message:""})]}),F.jsxs("div",{className:"flex items-center justify-between",children:[F.jsx(Mm,{name:"remember_me",checked:a.remember_me,onChange:e=>s((t=>({...t,remember_me:e}))),label:"Remember me"}),F.jsx("div",{className:"text-sm",children:F.jsx(c,{to:"/forgot-password",className:"font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300",children:"Forgot your password?"})})]}),m&&!m.field&&F.jsx("div",{className:"rounded-md bg-red-50 dark:bg-red-900/20 p-4",children:F.jsx("div",{className:"text-sm text-red-700 dark:text-red-400",children:m.message||"An error occurred during login"})}),F.jsx(jm,{type:"submit",fullWidth:!0,loading:o,disabled:!p,size:"lg",children:"Sign in"})]}),F.jsxs("div",{className:"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[F.jsx("h3",{className:"text-sm font-medium text-blue-800 dark:text-blue-200 mb-2",children:"Demo Credentials"}),F.jsxs("div",{className:"text-xs text-blue-700 dark:text-blue-300 space-y-1",children:[F.jsx("div",{children:"Email: <EMAIL>"}),F.jsx("div",{children:"Password: admin123"})]}),F.jsx(jm,{variant:"outline",size:"sm",color:"blue",className:"mt-2",onClick:()=>{s({email:"<EMAIL>",password:"admin123",remember_me:!1})},children:"Use Demo Credentials"})]}),F.jsx("div",{className:"text-center",children:F.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["By signing in, you agree to our"," ",F.jsx(c,{to:"/terms",className:"text-blue-600 hover:text-blue-500 dark:text-blue-400",children:"Terms of Service"})," ","and"," ",F.jsx(c,{to:"/privacy",className:"text-blue-600 hover:text-blue-500 dark:text-blue-400",children:"Privacy Policy"})]})})]})})},Fm=()=>{const e=j(),t=d(),[r,a]=i.useState({name:"",email:"",password:"",password_confirmation:"",company_name:"",phone:"",role:"admin",terms_accepted:!1}),s=k(Zt),n=k(zt),l=k(Ut);i.useEffect((()=>{l&&t("/dashboard",{replace:!0})}),[l,t]),i.useEffect((()=>()=>{e(Ft())}),[e]);const o=e=>{const{name:t,value:r,type:s,checked:n}=e.target;a((e=>({...e,[t]:"checkbox"===s?n:r})))},u=r.name&&r.email&&r.password&&r.password_confirmation&&r.company_name&&r.terms_accepted&&r.password===r.password_confirmation,m=!r.password_confirmation||r.password===r.password_confirmation;return F.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:F.jsxs("div",{className:"max-w-md w-full space-y-8",children:[F.jsxs("div",{children:[F.jsx("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900",children:F.jsx(Xu,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),F.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white",children:"Create your account"}),F.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:["Or"," ",F.jsx(c,{to:"/login",className:"font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300",children:"sign in to your existing account"})]})]}),F.jsxs("form",{className:"mt-8 space-y-6",onSubmit:async a=>{if(a.preventDefault(),r.password!==r.password_confirmation)return;const s=await e(Vt({name:r.name,email:r.email,password:r.password,password_confirmation:r.password_confirmation,company_name:r.company_name,phone:r.phone,role:r.role}));Vt.fulfilled.match(s)&&t("/dashboard",{replace:!0})},children:[F.jsxs("div",{className:"space-y-4",children:[F.jsx(Pm,{label:"Full Name",name:"name",type:"text",autoComplete:"name",required:!0,value:r.name,onChange:o,placeholder:"Enter your full name",leftIcon:tm,error:"name"===(null==n?void 0:n.field)?n.message:""}),F.jsx(Pm,{label:"Email address",name:"email",type:"email",autoComplete:"email",required:!0,value:r.email,onChange:o,placeholder:"Enter your email",leftIcon:tu,error:"email"===(null==n?void 0:n.field)?n.message:""}),F.jsx(Pm,{label:"Company Name",name:"company_name",type:"text",autoComplete:"organization",required:!0,value:r.company_name,onChange:o,placeholder:"Enter your company name",leftIcon:pc,error:"company_name"===(null==n?void 0:n.field)?n.message:""}),F.jsx(Pm,{label:"Phone Number",name:"phone",type:"tel",autoComplete:"tel",value:r.phone,onChange:o,placeholder:"Enter your phone number",leftIcon:_u,error:"phone"===(null==n?void 0:n.field)?n.message:""}),F.jsx(Pm,{label:"Password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:r.password,onChange:o,placeholder:"Create a password",leftIcon:xu,showPasswordToggle:!0,error:"password"===(null==n?void 0:n.field)?n.message:"",helperText:"Password must be at least 8 characters long"}),F.jsx(Pm,{label:"Confirm Password",name:"password_confirmation",type:"password",autoComplete:"new-password",required:!0,value:r.password_confirmation,onChange:o,placeholder:"Confirm your password",leftIcon:xu,showPasswordToggle:!0,error:m?"":"Passwords do not match"}),F.jsx(Em,{label:"Role",name:"role",value:r.role,onChange:e=>a((t=>({...t,role:e}))),options:[{value:"admin",label:"Administrator"},{value:"manager",label:"Manager"},{value:"employee",label:"Employee"}]})]}),F.jsx("div",{className:"space-y-4",children:F.jsx(Mm,{name:"terms_accepted",checked:r.terms_accepted,onChange:e=>a((t=>({...t,terms_accepted:e}))),label:F.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:["I agree to the"," ",F.jsx(c,{to:"/terms",className:"text-blue-600 hover:text-blue-500 dark:text-blue-400",children:"Terms of Service"})," ","and"," ",F.jsx(c,{to:"/privacy",className:"text-blue-600 hover:text-blue-500 dark:text-blue-400",children:"Privacy Policy"})]}),required:!0})}),n&&!n.field&&F.jsx("div",{className:"rounded-md bg-red-50 dark:bg-red-900/20 p-4",children:F.jsx("div",{className:"text-sm text-red-700 dark:text-red-400",children:n.message||"An error occurred during registration"})}),F.jsx(jm,{type:"submit",fullWidth:!0,loading:s,disabled:!u,size:"lg",color:"green",children:"Create Account"})]}),F.jsx("div",{className:"text-center",children:F.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Already have an account?"," ",F.jsx(c,{to:"/login",className:"font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300",children:"Sign in here"})]})})]})})},Tm=({title:e,value:t,change:r,changeType:a="increase",icon:s,color:n="blue",loading:i=!1,className:l="",...o})=>{const d={blue:{bg:"bg-blue-50 dark:bg-blue-900/20",icon:"text-blue-600 dark:text-blue-400",border:"border-blue-200 dark:border-blue-800"},green:{bg:"bg-green-50 dark:bg-green-900/20",icon:"text-green-600 dark:text-green-400",border:"border-green-200 dark:border-green-800"},purple:{bg:"bg-purple-50 dark:bg-purple-900/20",icon:"text-purple-600 dark:text-purple-400",border:"border-purple-200 dark:border-purple-800"},orange:{bg:"bg-orange-50 dark:bg-orange-900/20",icon:"text-orange-600 dark:text-orange-400",border:"border-orange-200 dark:border-orange-800"},red:{bg:"bg-red-50 dark:bg-red-900/20",icon:"text-red-600 dark:text-red-400",border:"border-red-200 dark:border-red-800"},yellow:{bg:"bg-yellow-50 dark:bg-yellow-900/20",icon:"text-yellow-600 dark:text-yellow-400",border:"border-yellow-200 dark:border-yellow-800"}},c=d[n]||d.blue;return F.jsx("div",{className:mm("bg-white dark:bg-gray-800 rounded-lg shadow border",c.border,l),...o,children:F.jsx("div",{className:"p-6",children:i?F.jsx("div",{className:"flex items-center justify-center h-20",children:F.jsx(bm,{size:"md"})}):F.jsxs(F.Fragment,{children:[F.jsxs("div",{className:"flex items-center justify-between",children:[F.jsx("div",{className:"flex items-center",children:F.jsx("div",{className:mm("flex items-center justify-center w-12 h-12 rounded-lg",c.bg),children:s&&F.jsx(s,{className:mm("w-6 h-6",c.icon)})})}),null!=r&&F.jsxs("div",{className:mm("flex items-center space-x-1 text-sm font-medium",{increase:"text-green-600 dark:text-green-400",decrease:"text-red-600 dark:text-red-400",neutral:"text-gray-600 dark:text-gray-400"}[a]),children:["increase"===a?F.jsx(lc,{className:"w-4 h-4"}):"decrease"===a?F.jsx(nc,{className:"w-4 h-4"}):null,F.jsxs("span",{children:[Math.abs(r),"%"]})]})]}),F.jsxs("div",{className:"mt-4",children:[F.jsx("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:e}),F.jsx("p",{className:"mt-2 text-3xl font-bold text-gray-900 dark:text-white",children:t}),null!=r&&F.jsxs("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:["increase"===a?"+":"decrease"===a?"-":"",Math.abs(r),"% from last month"]})]})]})})})},Dm=({activities:e=[],loading:t=!1})=>{const r=e=>{const t="h-5 w-5";switch(e){case"customer_created":return F.jsx(Xu,{className:mm(t,"text-blue-500")});case"service_created":case"service_updated":return F.jsx(nm,{className:mm(t,"text-purple-500")});case"sale_created":return F.jsx(Wu,{className:mm(t,"text-green-500")});case"payment_received":return F.jsx(Zc,{className:mm(t,"text-green-500")});case"invoice_generated":return F.jsx(Yc,{className:mm(t,"text-blue-500")});case"service_completed":return F.jsx(wc,{className:mm(t,"text-green-500")});case"service_overdue":return F.jsx(nu,{className:mm(t,"text-red-500")});default:return F.jsx(Wc,{className:mm(t,"text-gray-500")})}},a=e=>{switch(e){case"customer_created":case"invoice_generated":return"bg-blue-100 dark:bg-blue-900/20";case"service_created":case"service_updated":return"bg-purple-100 dark:bg-purple-900/20";case"sale_created":case"payment_received":case"service_completed":return"bg-green-100 dark:bg-green-900/20";case"service_overdue":return"bg-red-100 dark:bg-red-900/20";default:return"bg-gray-100 dark:bg-gray-700"}},s=e=>{const{type:t,data:r}=e;switch(t){case"customer_created":return`New customer "${r.customer_name}" was added`;case"service_created":return`New service request created for ${r.customer_name}`;case"service_updated":return`Service #${r.service_id} status updated to ${r.status}`;case"service_completed":return`Service #${r.service_id} completed for ${r.customer_name}`;case"service_overdue":return`Service #${r.service_id} is overdue`;case"sale_created":return`New sale of ${hm(r.amount)} created`;case"payment_received":return`Payment of ${hm(r.amount)} received from ${r.customer_name}`;case"invoice_generated":return`Invoice #${r.invoice_number} generated for ${r.customer_name}`;default:return e.message||"Activity occurred"}};return t?F.jsx("div",{className:"flex items-center justify-center h-32",children:F.jsx(bm,{})}):e&&0!==e.length?F.jsxs("div",{className:"flow-root",children:[F.jsx("ul",{className:"-mb-8",children:e.map(((t,n)=>{var i;const l=(e=>{const{type:t,data:r}=e;switch(t){case"customer_created":return`/customers/${r.customer_id}`;case"service_created":case"service_updated":case"service_completed":case"service_overdue":return`/services/${r.service_id}`;case"sale_created":return`/sales/${r.sale_id}`;case"invoice_generated":return`/invoices/${r.invoice_id}`;default:return null}})(t),o=l?c:"div",d=l?{to:l}:{};return F.jsx("li",{children:F.jsxs("div",{className:"relative pb-8",children:[n!==e.length-1?F.jsx("span",{className:"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-600","aria-hidden":"true"}):null,F.jsxs("div",{className:"relative flex space-x-3",children:[F.jsx("div",{className:mm("relative px-1","flex h-8 w-8 items-center justify-center rounded-full ring-8 ring-white dark:ring-gray-800",a(t.type)),children:r(t.type)}),F.jsxs("div",{className:"flex min-w-0 flex-1 justify-between space-x-4 pt-1.5",children:[F.jsxs("div",{className:"min-w-0 flex-1",children:[F.jsx(o,{...d,className:mm("text-sm text-gray-900 dark:text-white",l&&"hover:text-blue-600 dark:hover:text-blue-400 cursor-pointer"),children:s(t)}),(null==(i=t.data)?void 0:i.description)&&F.jsx("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:t.data.description})]}),F.jsx("div",{className:"whitespace-nowrap text-right text-xs text-gray-500 dark:text-gray-400",children:F.jsx("time",{dateTime:t.created_at,children:pm(t.created_at)})})]})]})]})},t.id||n)}))}),e.length>0&&F.jsx("div",{className:"mt-6 text-center",children:F.jsx(c,{to:"/activities",className:"text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300",children:"View all activities →"})})]}):F.jsxs("div",{className:"text-center py-8",children:[F.jsx(Wc,{className:"mx-auto h-12 w-12 text-gray-400"}),F.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No recent activities"}),F.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Activities will appear here as they happen."})]})},Wm=({customers:e=[],loading:t=!1})=>t?F.jsx("div",{className:"flex items-center justify-center h-32",children:F.jsx(bm,{})}):e&&0!==e.length?F.jsxs("div",{className:"space-y-4",children:[e.map(((e,t)=>F.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200",children:[F.jsxs("div",{className:"flex items-center space-x-4",children:[F.jsx("div",{className:mm("flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold",0===t?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":1===t?"bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200":2===t?"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"),children:t+1}),F.jsx("div",{className:"flex-shrink-0",children:e.avatar?F.jsx("img",{className:"h-10 w-10 rounded-full",src:e.avatar,alt:e.name}):F.jsx("div",{className:mm("h-10 w-10 rounded-full flex items-center justify-center text-white font-medium text-sm",xm(e.name)),children:fm(e.name)})}),F.jsxs("div",{className:"flex-1 min-w-0",children:[F.jsxs("div",{className:"flex items-center space-x-2",children:[F.jsx(c,{to:`/customers/${e.id}`,className:"text-sm font-medium text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 truncate",children:e.name}),e.is_vip&&F.jsx("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",children:"VIP"})]}),F.jsxs("div",{className:"flex items-center space-x-4 mt-1",children:[e.email&&F.jsxs("div",{className:"flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400",children:[F.jsx(tu,{className:"h-3 w-3"}),F.jsx("span",{className:"truncate max-w-32",children:e.email})]}),e.phone&&F.jsxs("div",{className:"flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400",children:[F.jsx(_u,{className:"h-3 w-3"}),F.jsx("span",{children:e.phone})]})]})]})]}),F.jsxs("div",{className:"text-right",children:[F.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:hm(e.total_spent||0)}),F.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[e.total_orders||0," orders"]}),e.last_order_date&&F.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Last: ",new Date(e.last_order_date).toLocaleDateString()]})]})]},e.id))),F.jsx("div",{className:"text-center pt-4",children:F.jsx(c,{to:"/customers",className:"text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300",children:"View all customers →"})})]}):F.jsxs("div",{className:"text-center py-8",children:[F.jsx(Ku,{className:"mx-auto h-12 w-12 text-gray-400"}),F.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No customer data"}),F.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Customer data will appear here once you have sales."})]}),Bm=({data:e,loading:t=!1})=>{const r=(null==e?void 0:e.monthly_breakdown)||[{month:"Jan",sales:45e3,services:25e3},{month:"Feb",sales:52e3,services:28e3},{month:"Mar",sales:48e3,services:32e3},{month:"Apr",sales:61e3,services:35e3},{month:"May",sales:55e3,services:3e4},{month:"Jun",sales:67e3,services:38e3}];if(t)return F.jsx("div",{className:"flex items-center justify-center h-64",children:F.jsx(bm,{})});if(!r||0===r.length)return F.jsxs("div",{className:"text-center py-12",children:[F.jsx(vc,{className:"mx-auto h-12 w-12 text-gray-400"}),F.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No sales data"}),F.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Sales data will appear here once you have transactions."})]});const a=Math.max(...r.map((e=>Math.max(e.sales||0,e.services||0))));return F.jsxs("div",{className:"space-y-4",children:[F.jsxs("div",{className:"flex items-center justify-center space-x-6 text-sm",children:[F.jsxs("div",{className:"flex items-center space-x-2",children:[F.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded"}),F.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Sales"})]}),F.jsxs("div",{className:"flex items-center space-x-2",children:[F.jsx("div",{className:"w-3 h-3 bg-green-500 rounded"}),F.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Services"})]})]}),F.jsx("div",{className:"space-y-3",children:r.map(((e,t)=>{const r=a>0?e.sales/a*100:0,s=a>0?e.services/a*100:0;return F.jsxs("div",{className:"flex items-end space-x-2",children:[F.jsx("div",{className:"w-8 text-xs text-gray-500 dark:text-gray-400 text-right",children:e.month}),F.jsxs("div",{className:"flex-1 flex items-end space-x-1 h-16",children:[F.jsx("div",{className:"flex-1 bg-gray-100 dark:bg-gray-700 rounded-t relative",children:F.jsx("div",{className:"bg-blue-500 rounded-t transition-all duration-300 ease-in-out",style:{height:`${r}%`},title:`Sales: ${hm(e.sales)}`})}),F.jsx("div",{className:"flex-1 bg-gray-100 dark:bg-gray-700 rounded-t relative",children:F.jsx("div",{className:"bg-green-500 rounded-t transition-all duration-300 ease-in-out",style:{height:`${s}%`},title:`Services: ${hm(e.services)}`})})]}),F.jsxs("div",{className:"w-20 text-xs text-gray-500 dark:text-gray-400",children:[F.jsx("div",{children:hm(e.sales,"USD","en-US").replace("$","$")}),F.jsx("div",{children:hm(e.services,"USD","en-US").replace("$","$")})]})]},t)}))}),F.jsxs("div",{className:"grid grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-600",children:[F.jsxs("div",{className:"text-center",children:[F.jsx("div",{className:"text-lg font-semibold text-blue-600 dark:text-blue-400",children:hm(r.reduce(((e,t)=>e+(t.sales||0)),0))}),F.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Total Sales"})]}),F.jsxs("div",{className:"text-center",children:[F.jsx("div",{className:"text-lg font-semibold text-green-600 dark:text-green-400",children:hm(r.reduce(((e,t)=>e+(t.services||0)),0))}),F.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Total Services"})]})]})]})},$m=({data:e,loading:t=!1})=>{const r=(null==e?void 0:e.alerts)||[{id:1,type:"low_stock",product_name:"iPhone 14 Pro",current_stock:2,min_stock:5,severity:"high"},{id:2,type:"out_of_stock",product_name:"Samsung Galaxy S23",current_stock:0,min_stock:3,severity:"critical"},{id:3,type:"low_stock",product_name:"MacBook Air M2",current_stock:1,min_stock:2,severity:"medium"},{id:4,type:"expiring_soon",product_name:"Screen Protectors",expiry_date:"2024-02-15",severity:"medium"}],a=(e,t)=>{const r="h-5 w-5";return"critical"===t?F.jsx(au,{className:mm(r,"text-red-500")}):"high"===t?F.jsx(nu,{className:mm(r,"text-orange-500")}):F.jsx(pu,{className:mm(r,"text-yellow-500")})},s=e=>{switch(e){case"critical":return"bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800";case"high":return"bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:border-orange-800";case"medium":return"bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800";default:return"bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800"}},n=e=>{switch(e.type){case"out_of_stock":return"Out of stock";case"low_stock":return`Low stock: ${e.current_stock} left (min: ${e.min_stock})`;case"expiring_soon":return`Expires on ${new Date(e.expiry_date).toLocaleDateString()}`;default:return"Requires attention"}};return t?F.jsx("div",{className:"flex items-center justify-center h-32",children:F.jsx(bm,{})}):r&&0!==r.length?F.jsxs("div",{className:"space-y-3",children:[r.slice(0,5).map((e=>F.jsx("div",{className:mm("p-3 rounded-lg border",s(e.severity)),children:F.jsxs("div",{className:"flex items-start space-x-3",children:[F.jsx("div",{className:"flex-shrink-0 mt-0.5",children:a(e.type,e.severity)}),F.jsxs("div",{className:"flex-1 min-w-0",children:[F.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.product_name}),F.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 mt-1",children:n(e)})]}),F.jsx("div",{className:"flex-shrink-0",children:F.jsx(c,{to:`/products/${e.product_id||e.id}`,className:"text-xs text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 font-medium",children:"View"})})]})},e.id))),e&&F.jsx("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-600",children:F.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center",children:[F.jsxs("div",{children:[F.jsx("div",{className:"text-lg font-semibold text-red-600 dark:text-red-400",children:e.out_of_stock_count||0}),F.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Out of Stock"})]}),F.jsxs("div",{children:[F.jsx("div",{className:"text-lg font-semibold text-yellow-600 dark:text-yellow-400",children:e.low_stock_count||0}),F.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Low Stock"})]})]})}),r.length>5&&F.jsx("div",{className:"text-center pt-2",children:F.jsxs(c,{to:"/products/alerts",className:"text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300",children:["View all alerts (",r.length,") →"]})})]}):F.jsxs("div",{className:"text-center py-8",children:[F.jsx(Hc,{className:"mx-auto h-12 w-12 text-green-400"}),F.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"All good!"}),F.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"No inventory alerts at the moment."})]})},qm=()=>{var e;const t=j(),r=k(ro),a=k(so),s=k(no),n=k(io),l=k(lo),o=k(ao),d=k(oo);return i.useEffect((()=>{t(Nl()),t(Sl()),t(Ll()),t(_l()),t(Ol({limit:5})),t(El({limit:10}))}),[t]),d.overview?F.jsx("div",{className:"flex items-center justify-center h-64",children:F.jsx(bm,{size:"lg",text:"Loading dashboard..."})}):F.jsxs("div",{className:"p-6 space-y-6",children:[F.jsxs("div",{className:"mb-8",children:[F.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Dashboard"}),F.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Welcome back! Here's what's happening with your business today."})]}),F.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[F.jsx(Tm,{title:"Total Revenue",value:hm((null==s?void 0:s.total_revenue)||0),change:(null==s?void 0:s.revenue_change)||0,changeType:(null==s?void 0:s.revenue_change)>=0?"increase":"decrease",icon:Zc,color:"green",loading:d.financial}),F.jsx(Tm,{title:"Total Customers",value:(null==r?void 0:r.total_customers)||0,change:(null==r?void 0:r.customer_change)||0,changeType:(null==r?void 0:r.customer_change)>=0?"increase":"decrease",icon:am,color:"blue",loading:d.overview}),F.jsx(Tm,{title:"Active Services",value:(null==r?void 0:r.active_services)||0,change:(null==r?void 0:r.service_change)||0,changeType:(null==r?void 0:r.service_change)>=0?"increase":"decrease",icon:nm,color:"purple",loading:d.overview}),F.jsx(Tm,{title:"Monthly Sales",value:(null==a?void 0:a.monthly_sales)||0,change:(null==a?void 0:a.sales_change)||0,changeType:(null==a?void 0:a.sales_change)>=0?"increase":"decrease",icon:Wu,color:"orange",loading:d.sales})]}),F.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[F.jsx("div",{className:"lg:col-span-2",children:F.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[F.jsxs("div",{className:"flex items-center justify-between mb-4",children:[F.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Financial Overview"}),F.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[F.jsx(xc,{className:"h-4 w-4"}),F.jsx("span",{children:"This Month"})]})]}),d.financial?F.jsx("div",{className:"flex items-center justify-center h-32",children:F.jsx(bm,{})}):F.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[F.jsxs("div",{className:"space-y-2",children:[F.jsxs("div",{className:"flex justify-between",children:[F.jsx("span",{className:"text-sm text-gray-500",children:"Sales Revenue"}),F.jsx("span",{className:"text-sm font-medium",children:hm((null==s?void 0:s.sales_revenue)||0)})]}),F.jsxs("div",{className:"flex justify-between",children:[F.jsx("span",{className:"text-sm text-gray-500",children:"Service Revenue"}),F.jsx("span",{className:"text-sm font-medium",children:hm((null==s?void 0:s.service_revenue)||0)})]}),F.jsxs("div",{className:"flex justify-between",children:[F.jsx("span",{className:"text-sm text-gray-500",children:"Total Expenses"}),F.jsxs("span",{className:"text-sm font-medium text-red-600",children:["-",hm((null==s?void 0:s.total_expenses)||0)]})]})]}),F.jsxs("div",{className:"space-y-2",children:[F.jsxs("div",{className:"flex justify-between",children:[F.jsx("span",{className:"text-sm text-gray-500",children:"Net Profit"}),F.jsx("span",{className:"text-sm font-medium "+(((null==s?void 0:s.net_profit)||0)>=0?"text-green-600":"text-red-600"),children:hm((null==s?void 0:s.net_profit)||0)})]}),F.jsxs("div",{className:"flex justify-between",children:[F.jsx("span",{className:"text-sm text-gray-500",children:"Profit Margin"}),F.jsxs("span",{className:"text-sm font-medium",children:[(null==s?void 0:s.profit_margin)||0,"%"]})]}),F.jsxs("div",{className:"flex justify-between",children:[F.jsx("span",{className:"text-sm text-gray-500",children:"Outstanding"}),F.jsx("span",{className:"text-sm font-medium text-yellow-600",children:hm((null==(e=null==s?void 0:s.outstanding_invoices)?void 0:e.amount)||0)})]})]})]})]})}),F.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[F.jsxs("div",{className:"flex items-center justify-between mb-4",children:[F.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Inventory Alerts"}),F.jsx(nu,{className:"h-5 w-5 text-yellow-500"})]}),F.jsx($m,{data:n,loading:d.inventory})]})]}),F.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[F.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[F.jsxs("div",{className:"flex items-center justify-between mb-4",children:[F.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Sales Trend"}),F.jsx(vc,{className:"h-5 w-5 text-gray-400"})]}),F.jsx(Bm,{data:a,loading:d.sales})]}),F.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[F.jsxs("div",{className:"flex items-center justify-between mb-4",children:[F.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Recent Activities"}),F.jsx(hc,{className:"h-5 w-5 text-gray-400"})]}),F.jsx(Dm,{activities:o,loading:d.activities})]})]}),F.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[F.jsxs("div",{className:"flex items-center justify-between mb-4",children:[F.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Top Customers"}),F.jsx(am,{className:"h-5 w-5 text-gray-400"})]}),F.jsx(Wm,{customers:l,loading:d.customers})]})]})},Vm=()=>{const e=j(),t=d(),[r,a]=i.useState(!1),[s,n]=i.useState(!1),[l,o]=i.useState(null),c=k(ca),u=k(ua),m=k(ma),h=k(ha),g=k(ga),p=k(pa),f=k(fa);i.useEffect((()=>{e(Rr({page:u.current_page,limit:u.items_per_page,search:f,...p}))}),[e,u.current_page,u.items_per_page,f,p]);const x=e=>{const t="h-4 w-4";switch(e){case"pending":return F.jsx(Wc,{className:mm(t,"text-yellow-500")});case"in_progress":return F.jsx(nm,{className:mm(t,"text-blue-500")});case"completed":return F.jsx(wc,{className:mm(t,"text-green-500")});case"cancelled":return F.jsx(lm,{className:mm(t,"text-red-500")});case"on_hold":return F.jsx(nu,{className:mm(t,"text-orange-500")});default:return F.jsx(Wc,{className:mm(t,"text-gray-500")})}},y=[{key:"service_number",title:"Service #",sortable:!0,render:(e,t)=>F.jsxs("div",{children:[F.jsxs("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:["#",t.service_number||t.id]}),F.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:t.service_type||"General Service"})]})},{key:"customer",title:"Customer",render:(e,t)=>{var r,a;return F.jsxs("div",{className:"flex items-center space-x-2",children:[F.jsx(tm,{className:"h-4 w-4 text-gray-400"}),F.jsxs("div",{children:[F.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:(null==(r=t.customer)?void 0:r.name)||"Unknown Customer"}),F.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:(null==(a=t.customer)?void 0:a.phone)||"-"})]})]})}},{key:"title",title:"Service Details",render:(e,t)=>{var r;return F.jsxs("div",{children:[F.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.title||(null==(r=t.description)?void 0:r.substring(0,50))+"..."||"Service Request"}),F.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:t.product_name&&`Product: ${t.product_name}`})]})}},{key:"status",title:"Status",sortable:!0,render:(e,t)=>F.jsxs("div",{className:"flex items-center space-x-2",children:[x(e),F.jsx("span",{className:mm("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",ym(e)),children:(null==e?void 0:e.replace("_"," "))||"pending"})]})},{key:"priority",title:"Priority",sortable:!0,render:e=>F.jsx("span",{className:mm("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium","urgent"===e?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":"high"===e?"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200":"medium"===e?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"),children:e||"medium"})},{key:"scheduled_date",title:"Scheduled",sortable:!0,render:(e,t)=>F.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400",children:[F.jsx(xc,{className:"h-4 w-4"}),F.jsx("span",{children:e?gm(e):"Not scheduled"})]})},{key:"estimated_cost",title:"Cost",sortable:!0,render:e=>F.jsx("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e?hm(e):"-"})}],v=[{label:"View",icon:du,onClick:e=>t(`/services/${e.id}`)},{label:"Edit",icon:Su,onClick:e=>t(`/services/${e.id}/edit`)},{label:"Complete",icon:wc,onClick:t=>{e(qr({id:t.id,status:"completed"}))},disabled:e=>"completed"===e.status||"cancelled"===e.status},{label:"Delete",icon:Hu,onClick:e=>{o(e),n(!0)},disabled:e=>"in_progress"===e.status}],b=(t,r)=>{e(Kr({key:t,value:r}))};return F.jsxs("div",{className:"p-6 space-y-6",children:[F.jsxs("div",{className:"flex items-center justify-between",children:[F.jsxs("div",{children:[F.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Services"}),F.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage service requests and track their progress"})]}),F.jsxs("div",{className:"flex items-center space-x-3",children:[F.jsx(jm,{variant:"outline",leftIcon:nm,onClick:()=>a(!r),children:"Filters"}),F.jsx(jm,{leftIcon:Mu,onClick:()=>t("/services/new"),children:"New Service"})]})]}),r&&F.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:F.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[F.jsx(Em,{label:"Status",value:p.status,onChange:e=>b("status",e),options:[{value:"",label:"All Statuses"},{value:"pending",label:"Pending"},{value:"in_progress",label:"In Progress"},{value:"completed",label:"Completed"},{value:"cancelled",label:"Cancelled"},{value:"on_hold",label:"On Hold"}]}),F.jsx(Em,{label:"Priority",value:p.priority,onChange:e=>b("priority",e),options:[{value:"",label:"All Priorities"},{value:"low",label:"Low"},{value:"medium",label:"Medium"},{value:"high",label:"High"},{value:"urgent",label:"Urgent"}]}),F.jsx(Pm,{label:"Customer",value:p.customer,onChange:e=>b("customer",e.target.value),placeholder:"Filter by customer"}),F.jsx("div",{className:"flex items-end space-x-2",children:F.jsx(jm,{variant:"outline",onClick:()=>{e(Yr()),e(ta(""))},className:"flex-1",children:"Clear Filters"})})]})}),F.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[F.jsx(Nm,{data:c,columns:y,loading:m,error:h,searchQuery:f,onSearch:t=>{e(ta(t))},selectedRows:g,onSelectRow:(t,r)=>{e(r?Hr(t):Ur(t))},onSelectAll:t=>{t.length>0?e(Zr()):e(zr())},selectable:!0,actions:v,onRowClick:e=>t(`/services/${e.id}`),emptyMessage:"No services found"}),F.jsx(Sm,{currentPage:u.current_page,totalPages:u.total_pages,totalItems:u.total_items,itemsPerPage:u.items_per_page,onPageChange:t=>{e(Rr({page:t,limit:u.items_per_page,search:f,...p}))}})]}),F.jsx(Cm,{isOpen:s,onClose:()=>n(!1),onConfirm:async()=>{l&&(await e(Wr(l.id)),n(!1),o(null),e(Rr({page:u.current_page,limit:u.items_per_page,search:f,...p})))},title:"Delete Service",message:`Are you sure you want to delete service #${(null==l?void 0:l.service_number)||(null==l?void 0:l.id)}? This action cannot be undone.`,confirmText:"Delete",confirmColor:"red"})]})},Qm=()=>{const e=j(),t=d(),[r,a]=i.useState(!1),[s,n]=i.useState(!1),[l,o]=i.useState(null),c=k(za),u=k(Ka),m=k(Ga),h=k(Ya),g=k(Ja),p=k(Xa),f=k(es);i.useEffect((()=>{e(xa({page:u.current_page,limit:u.items_per_page,search:f,...p}))}),[e,u.current_page,u.items_per_page,f,p]);const x=[{key:"name",title:"Customer",sortable:!0,render:(e,t)=>F.jsxs("div",{className:"flex items-center space-x-3",children:[t.avatar?F.jsx("img",{className:"h-8 w-8 rounded-full",src:t.avatar,alt:t.name}):F.jsx("div",{className:mm("h-8 w-8 rounded-full flex items-center justify-center text-white font-medium text-xs",xm(t.name)),children:fm(t.name)}),F.jsxs("div",{children:[F.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.name}),F.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["ID: ",t.customer_id||t.id]})]})]})},{key:"contact",title:"Contact",render:(e,t)=>F.jsxs("div",{className:"space-y-1",children:[t.email&&F.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400",children:[F.jsx(tu,{className:"h-4 w-4"}),F.jsx("span",{children:t.email})]}),t.phone&&F.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400",children:[F.jsx(_u,{className:"h-4 w-4"}),F.jsx("span",{children:t.phone})]})]})},{key:"location",title:"Location",render:(e,t)=>F.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:t.city&&t.state?F.jsxs("div",{className:"flex items-center space-x-1",children:[F.jsx(wu,{className:"h-4 w-4"}),F.jsxs("span",{children:[t.city,", ",t.state]})]}):F.jsx("span",{className:"text-gray-400",children:"-"})})},{key:"status",title:"Status",sortable:!0,render:e=>F.jsx("span",{className:mm("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium","active"===e?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"),children:e||"active"})},{key:"created_at",title:"Created",sortable:!0,render:e=>F.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:gm(e)})}],y=[{label:"View",icon:du,onClick:e=>t(`/customers/${e.id}`)},{label:"Edit",icon:Su,onClick:e=>t(`/customers/${e.id}/edit`)},{label:"Delete",icon:Hu,onClick:e=>{o(e),n(!0)},disabled:e=>e.has_active_services}],v=(t,r)=>{e(Pa({key:t,value:r}))};return F.jsxs("div",{className:"p-6 space-y-6",children:[F.jsxs("div",{className:"flex items-center justify-between",children:[F.jsxs("div",{children:[F.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Customers"}),F.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage your customer database and relationships"})]}),F.jsxs("div",{className:"flex items-center space-x-3",children:[F.jsx(jm,{variant:"outline",leftIcon:uu,onClick:()=>a(!r),children:"Filters"}),F.jsx(jm,{leftIcon:Mu,onClick:()=>t("/customers/new"),children:"Add Customer"})]})]}),r&&F.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:F.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[F.jsx(Em,{label:"Status",value:p.status,onChange:e=>v("status",e),options:[{value:"",label:"All Statuses"},{value:"active",label:"Active"},{value:"inactive",label:"Inactive"}]}),F.jsx(Em,{label:"Customer Type",value:p.customer_type,onChange:e=>v("customer_type",e),options:[{value:"",label:"All Types"},{value:"individual",label:"Individual"},{value:"business",label:"Business"}]}),F.jsx(Pm,{label:"City",value:p.city,onChange:e=>v("city",e.target.value),placeholder:"Filter by city"}),F.jsx("div",{className:"flex items-end space-x-2",children:F.jsx(jm,{variant:"outline",onClick:()=>{e(Ma()),e(Ta(""))},className:"flex-1",children:"Clear Filters"})})]})}),F.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[F.jsx(Nm,{data:c,columns:x,loading:m,error:h,searchQuery:f,onSearch:t=>{e(Ta(t))},selectedRows:g,onSelectRow:(t,r)=>{e(r?Sa(t):La(t))},onSelectAll:t=>{t.length>0?e(_a()):e(Oa())},selectable:!0,actions:y,onRowClick:e=>t(`/customers/${e.id}`),emptyMessage:"No customers found"}),F.jsx(Sm,{currentPage:u.current_page,totalPages:u.total_pages,totalItems:u.total_items,itemsPerPage:u.items_per_page,onPageChange:t=>{e(xa({page:t,limit:u.items_per_page,search:f,...p}))}})]}),F.jsx(Cm,{isOpen:s,onClose:()=>n(!1),onConfirm:async()=>{l&&(await e(wa(l.id)),n(!1),o(null),e(xa({page:u.current_page,limit:u.items_per_page,search:f,...p})))},title:"Delete Customer",message:`Are you sure you want to delete "${null==l?void 0:l.name}"? This action cannot be undone.`,confirmText:"Delete",confirmColor:"red"})]})},Hm=()=>{const e=j(),t=d(),[r,a]=i.useState(!1),[s,n]=i.useState(!1),[l,o]=i.useState(null),c=k(Mi),u=k(Ii),m=k(Ri),h=k(Fi),g=k(Ti),p=k(Di),f=k(Wi);i.useEffect((()=>{e(Xn({page:u.current_page,limit:u.items_per_page,search:f,...p}))}),[e,u.current_page,u.items_per_page,f,p]);const x=[{key:"name",title:"Product",sortable:!0,render:(e,t)=>F.jsxs("div",{className:"flex items-center space-x-3",children:[t.image?F.jsx("img",{className:"h-10 w-10 rounded-lg object-cover",src:t.image,alt:t.name}):F.jsx("div",{className:"h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center",children:F.jsx(Pu,{className:"h-6 w-6 text-gray-400"})}),F.jsxs("div",{children:[F.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.name}),F.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["SKU: ",t.sku||"N/A"]})]})]})},{key:"category",title:"Category",render:(e,t)=>{var r;return F.jsxs("div",{className:"flex items-center space-x-1",children:[F.jsx(Vu,{className:"h-4 w-4 text-gray-400"}),F.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:(null==(r=t.category)?void 0:r.name)||"Uncategorized"})]})}},{key:"brand",title:"Brand",render:(e,t)=>{var r;return F.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:(null==(r=t.brand)?void 0:r.name)||"-"})}},{key:"current_stock",title:"Stock",sortable:!0,render:(e,t)=>{const r=(e=>{const t=e.current_stock||0,r=e.min_stock||0;return 0===t?{status:"out_of_stock",label:"Out of Stock",color:"red"}:t<=r?{status:"low_stock",label:"Low Stock",color:"yellow"}:{status:"in_stock",label:"In Stock",color:"green"}})(t);return F.jsxs("div",{className:"flex items-center space-x-2",children:[F.jsx("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e||0}),F.jsx("span",{className:mm("inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium","red"===r.color?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":"yellow"===r.color?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"),children:r.label})]})}},{key:"selling_price",title:"Price",sortable:!0,render:(e,t)=>F.jsxs("div",{className:"flex items-center space-x-1",children:[F.jsx(Zc,{className:"h-4 w-4 text-gray-400"}),F.jsx("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:hm(e||0)})]})},{key:"cost_price",title:"Cost",sortable:!0,render:e=>F.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:hm(e||0)})},{key:"barcode",title:"Barcode",render:e=>F.jsxs("div",{className:"flex items-center space-x-1",children:[F.jsx(Tu,{className:"h-4 w-4 text-gray-400"}),F.jsx("span",{className:"text-xs font-mono text-gray-600 dark:text-gray-400",children:e||"-"})]})},{key:"status",title:"Status",sortable:!0,render:e=>F.jsx("span",{className:mm("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium","active"===e?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"),children:e||"active"})}],y=[{label:"View",icon:du,onClick:e=>t(`/products/${e.id}`)},{label:"Edit",icon:Su,onClick:e=>t(`/products/${e.id}/edit`)},{label:"Update Stock",icon:Hc,onClick:t=>{const r=prompt(`Update stock for ${t.name}:`,t.current_stock);null===r||isNaN(r)||e(Pi({id:t.id,stock:parseInt(r)}))}},{label:"Delete",icon:Hu,onClick:e=>{o(e),n(!0)},disabled:e=>e.current_stock>0}],v=(t,r)=>{e(gi({key:t,value:r}))};return F.jsxs("div",{className:"p-6 space-y-6",children:[F.jsxs("div",{className:"flex items-center justify-between",children:[F.jsxs("div",{children:[F.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Products"}),F.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage your product catalog and inventory"})]}),F.jsxs("div",{className:"flex items-center space-x-3",children:[F.jsx(jm,{variant:"outline",leftIcon:nu,onClick:()=>t("/products/alerts"),color:"yellow",children:"Stock Alerts"}),F.jsx(jm,{variant:"outline",leftIcon:Hc,onClick:()=>a(!r),children:"Filters"}),F.jsx(jm,{leftIcon:Mu,onClick:()=>t("/products/new"),children:"Add Product"})]})]}),r&&F.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:F.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[F.jsx(Em,{label:"Status",value:p.status,onChange:e=>v("status",e),options:[{value:"",label:"All Statuses"},{value:"active",label:"Active"},{value:"inactive",label:"Inactive"}]}),F.jsx(Em,{label:"Stock Level",value:p.stock_level,onChange:e=>v("stock_level",e),options:[{value:"",label:"All Stock Levels"},{value:"in_stock",label:"In Stock"},{value:"low_stock",label:"Low Stock"},{value:"out_of_stock",label:"Out of Stock"}]}),F.jsx(Pm,{label:"Category",value:p.category,onChange:e=>v("category",e.target.value),placeholder:"Filter by category"}),F.jsx("div",{className:"flex items-end space-x-2",children:F.jsx(jm,{variant:"outline",onClick:()=>{e(fi()),e(bi(""))},className:"flex-1",children:"Clear Filters"})})]})}),F.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[F.jsx(Nm,{data:c,columns:x,loading:m,error:h,searchQuery:f,onSearch:t=>{e(bi(t))},selectedRows:g,onSelectRow:(t,r)=>{e(r?ci(t):ui(t))},onSelectAll:t=>{t.length>0?e(mi()):e(hi())},selectable:!0,actions:y,onRowClick:e=>t(`/products/${e.id}`),emptyMessage:"No products found"}),F.jsx(Sm,{currentPage:u.current_page,totalPages:u.total_pages,totalItems:u.total_items,itemsPerPage:u.items_per_page,onPageChange:t=>{e(Xn({page:t,limit:u.items_per_page,search:f,...p}))}})]}),F.jsx(Cm,{isOpen:s,onClose:()=>n(!1),onConfirm:async()=>{l&&(await e(ai(l.id)),n(!1),o(null),e(Xn({page:u.current_page,limit:u.items_per_page,search:f,...p})))},title:"Delete Product",message:`Are you sure you want to delete "${null==l?void 0:l.name}"? This action cannot be undone.`,confirmText:"Delete",confirmColor:"red"})]})},Um=()=>{const e=j(),t=d(),[r,a]=i.useState(!1),[s,n]=i.useState(!1),[l,o]=i.useState(null),c=k(Un),u=k(Zn),m=k(zn),h=k(Kn),g=k(Gn),p=k(Yn),f=k(Jn);i.useEffect((()=>{e(on({page:u.current_page,limit:u.items_per_page,search:f,...p}))}),[e,u.current_page,u.items_per_page,f,p]);const x=[{key:"sale_number",title:"Sale #",sortable:!0,render:(e,t)=>F.jsxs("div",{children:[F.jsxs("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:["#",t.sale_number||t.id]}),F.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:t.sale_type||"Regular Sale"})]})},{key:"customer",title:"Customer",render:(e,t)=>{var r,a;return F.jsxs("div",{className:"flex items-center space-x-2",children:[F.jsx(tm,{className:"h-4 w-4 text-gray-400"}),F.jsxs("div",{children:[F.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:(null==(r=t.customer)?void 0:r.name)||"Walk-in Customer"}),F.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:(null==(a=t.customer)?void 0:a.phone)||"-"})]})]})}},{key:"total_amount",title:"Amount",sortable:!0,render:(e,t)=>F.jsxs("div",{children:[F.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:hm(e||0)}),F.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[t.items_count||0," items"]})]})},{key:"status",title:"Status",sortable:!0,render:e=>F.jsx("span",{className:mm("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",ym(e)),children:(null==e?void 0:e.replace("_"," "))||"draft"})},{key:"payment_status",title:"Payment",sortable:!0,render:(e,t)=>F.jsxs("div",{children:[F.jsx("span",{className:mm("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium","paid"===e?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"partial"===e?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"overdue"===e?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"),children:(null==e?void 0:e.replace("_"," "))||"pending"}),t.paid_amount>0&&F.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["Paid: ",hm(t.paid_amount)]})]})},{key:"sale_date",title:"Date",sortable:!0,render:e=>F.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400",children:[F.jsx(xc,{className:"h-4 w-4"}),F.jsx("span",{children:gm(e)})]})},{key:"salesperson",title:"Salesperson",render:(e,t)=>{var r,a;return F.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:(null==(r=t.salesperson)?void 0:r.name)||(null==(a=t.created_by)?void 0:a.name)||"-"})}}],y=[{label:"View",icon:du,onClick:e=>t(`/sales/${e.id}`)},{label:"Edit",icon:Su,onClick:e=>t(`/sales/${e.id}/edit`),disabled:e=>"completed"===e.status||"cancelled"===e.status},{label:"Print Invoice",icon:Ru,onClick:e=>window.open(`/sales/${e.id}/print`,"_blank"),disabled:e=>"draft"===e.status},{label:"Generate Invoice",icon:Yc,onClick:e=>t(`/invoices/new?sale_id=${e.id}`),disabled:e=>e.invoice_generated||"cancelled"===e.status},{label:"Delete",icon:Hu,onClick:e=>{o(e),n(!0)},disabled:e=>"completed"===e.status||"paid"===e.payment_status}],v=(t,r)=>{e(jn({key:t,value:r}))};return F.jsxs("div",{className:"p-6 space-y-6",children:[F.jsxs("div",{className:"flex items-center justify-between",children:[F.jsxs("div",{children:[F.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Sales"}),F.jsx("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage your sales transactions and orders"})]}),F.jsxs("div",{className:"flex items-center space-x-3",children:[F.jsx(jm,{variant:"outline",leftIcon:Yc,onClick:()=>t("/invoices"),children:"Invoices"}),F.jsx(jm,{variant:"outline",leftIcon:Wu,onClick:()=>a(!r),children:"Filters"}),F.jsx(jm,{leftIcon:Mu,onClick:()=>t("/sales/new"),children:"New Sale"})]})]}),r&&F.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[F.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[F.jsx(Em,{label:"Status",value:p.status,onChange:e=>v("status",e),options:[{value:"",label:"All Statuses"},{value:"draft",label:"Draft"},{value:"pending",label:"Pending"},{value:"confirmed",label:"Confirmed"},{value:"completed",label:"Completed"},{value:"cancelled",label:"Cancelled"}]}),F.jsx(Em,{label:"Payment Status",value:p.payment_status,onChange:e=>v("payment_status",e),options:[{value:"",label:"All Payment Status"},{value:"pending",label:"Pending"},{value:"partial",label:"Partially Paid"},{value:"paid",label:"Paid"},{value:"overdue",label:"Overdue"}]}),F.jsx(Im,{label:"From Date",value:p.from_date,onChange:e=>v("from_date",e)}),F.jsx(Im,{label:"To Date",value:p.to_date,onChange:e=>v("to_date",e)})]}),F.jsx("div",{className:"mt-4 flex justify-end",children:F.jsx(jm,{variant:"outline",onClick:()=>{e(Cn()),e(Ln(""))},children:"Clear Filters"})})]}),F.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[F.jsx(Nm,{data:c,columns:x,loading:m,error:h,searchQuery:f,onSearch:t=>{e(Ln(t))},selectedRows:g,onSelectRow:(t,r)=>{e(r?yn(t):vn(t))},onSelectAll:t=>{t.length>0?e(bn()):e(wn())},selectable:!0,actions:y,onRowClick:e=>t(`/sales/${e.id}`),emptyMessage:"No sales found"}),F.jsx(Sm,{currentPage:u.current_page,totalPages:u.total_pages,totalItems:u.total_items,itemsPerPage:u.items_per_page,onPageChange:t=>{e(on({page:t,limit:u.items_per_page,search:f,...p}))}})]}),F.jsx(Cm,{isOpen:s,onClose:()=>n(!1),onConfirm:async()=>{l&&(await e(mn(l.id)),n(!1),o(null),e(on({page:u.current_page,limit:u.items_per_page,search:f,...p})))},title:"Delete Sale",message:`Are you sure you want to delete sale #${(null==l?void 0:l.sale_number)||(null==l?void 0:l.id)}? This action cannot be undone.`,confirmText:"Delete",confirmColor:"red"})]})},Zm=()=>{const e=d();return F.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[F.jsxs("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[F.jsx("div",{className:"mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20",children:F.jsx(nu,{className:"h-8 w-8 text-red-600 dark:text-red-400"})}),F.jsxs("div",{className:"mt-6 text-center",children:[F.jsx("h1",{className:"text-3xl font-extrabold text-gray-900 dark:text-white",children:"Access Denied"}),F.jsx("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"You don't have permission to access this page."})]})]}),F.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:F.jsxs("div",{className:"bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[F.jsxs("div",{className:"text-center",children:[F.jsx("h2",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"What can you do?"}),F.jsxs("div",{className:"space-y-4 text-sm text-gray-600 dark:text-gray-400",children:[F.jsxs("div",{className:"flex items-start space-x-3",children:[F.jsx("div",{className:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"}),F.jsx("p",{className:"text-left",children:"Contact your administrator to request access to this feature"})]}),F.jsxs("div",{className:"flex items-start space-x-3",children:[F.jsx("div",{className:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"}),F.jsx("p",{className:"text-left",children:"Check if you're logged in with the correct account"})]}),F.jsxs("div",{className:"flex items-start space-x-3",children:[F.jsx("div",{className:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"}),F.jsx("p",{className:"text-left",children:"Return to the dashboard and try a different section"})]})]})]}),F.jsxs("div",{className:"mt-8 space-y-3",children:[F.jsx(jm,{fullWidth:!0,leftIcon:tc,onClick:()=>e(-1),variant:"outline",children:"Go Back"}),F.jsx(jm,{fullWidth:!0,leftIcon:hu,onClick:()=>e("/dashboard"),children:"Go to Dashboard"})]})]})}),F.jsx("div",{className:"mt-8 text-center",children:F.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Need help? Contact support at"," ",F.jsx("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:text-blue-500 dark:text-blue-400",children:"<EMAIL>"})]})})]})},zm=new Ld({defaultOptions:{queries:{retry:1,refetchOnWindowFocus:!1,staleTime:3e5}}});function Km(){const{isLoading:e,initializeAuth:t}=(()=>{const e=j(),t=k(Ut),r=k(Zt);return{user:t,loading:r,error:k(zt),isAuthenticated:!!t,isLoading:r,login:i.useCallback((t=>e(qt(t))),[e]),register:i.useCallback((t=>e(Vt(t))),[e]),logout:i.useCallback((()=>e(Qt())),[e]),clearError:i.useCallback((()=>{e(Ht())}),[e]),initializeAuth:i.useCallback((()=>{}),[])}})();return i.useEffect((()=>{t()}),[t]),e?F.jsx("div",{className:"min-h-screen flex items-center justify-center",children:F.jsx(bm,{size:"large"})}):F.jsx(g,{children:F.jsxs(p,{children:[F.jsx(f,{path:"/login",element:F.jsx(Om,{children:F.jsx(Rm,{})})}),F.jsx(f,{path:"/register",element:F.jsx(Om,{children:F.jsx(Fm,{})})}),F.jsx(f,{path:"/unauthorized",element:F.jsx(Zm,{})}),F.jsxs(f,{path:"/",element:F.jsx(_m,{children:F.jsx(wm,{})}),children:[F.jsx(f,{index:!0,element:F.jsx(h,{to:"/dashboard",replace:!0})}),F.jsx(f,{path:"dashboard",element:F.jsx(qm,{})}),F.jsx(f,{path:"services",element:F.jsx(Vm,{})}),F.jsx(f,{path:"services/new",element:F.jsx("div",{children:"Create Service"})}),F.jsx(f,{path:"services/:id",element:F.jsx("div",{children:"Service Details"})}),F.jsx(f,{path:"services/:id/edit",element:F.jsx("div",{children:"Edit Service"})}),F.jsx(f,{path:"customers",element:F.jsx(Qm,{})}),F.jsx(f,{path:"customers/new",element:F.jsx("div",{children:"Create Customer"})}),F.jsx(f,{path:"customers/:id",element:F.jsx("div",{children:"Customer Details"})}),F.jsx(f,{path:"customers/:id/edit",element:F.jsx("div",{children:"Edit Customer"})}),F.jsx(f,{path:"sales",element:F.jsx(Um,{})}),F.jsx(f,{path:"sales/new",element:F.jsx("div",{children:"Create Sale"})}),F.jsx(f,{path:"sales/:id",element:F.jsx("div",{children:"Sale Details"})}),F.jsx(f,{path:"sales/:id/edit",element:F.jsx("div",{children:"Edit Sale"})}),F.jsx(f,{path:"sales/:id/print",element:F.jsx("div",{children:"Print Sale"})}),F.jsx(f,{path:"products",element:F.jsx(Hm,{})}),F.jsx(f,{path:"products/new",element:F.jsx("div",{children:"Create Product"})}),F.jsx(f,{path:"products/:id",element:F.jsx("div",{children:"Product Details"})}),F.jsx(f,{path:"products/:id/edit",element:F.jsx("div",{children:"Edit Product"})}),F.jsx(f,{path:"products/alerts",element:F.jsx("div",{children:"Stock Alerts"})}),F.jsx(f,{path:"settings",element:F.jsx("div",{children:"User Settings"})}),F.jsx(f,{path:"profile",element:F.jsx("div",{children:"User Profile"})}),!1]}),F.jsx(f,{path:"*",element:F.jsx(h,{to:"/dashboard",replace:!0})})]})})}function Gm(){return F.jsx(Lm,{children:F.jsx(Xd,{children:F.jsx(C,{store:$o,children:F.jsxs(Md,{client:zm,children:[F.jsx(Km,{}),F.jsx(yt,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,theme:{primary:"green",secondary:"black"}},error:{duration:5e3,theme:{primary:"red",secondary:"black"}}}}),!1]})})})})}D.createRoot(document.getElementById("root")).render(F.jsx(o.StrictMode,{children:F.jsx(C,{store:$o,children:F.jsx(g,{children:F.jsx(Gm,{})})})}));
