import React from 'react';
import { 
  ChevronUpIcon, 
  ChevronDownIcon,
  ChevronUpDownIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/24/outline';
import { Menu } from '@headlessui/react';
import { classNames } from '../../utils/helpers';
import LoadingSpinner from './LoadingSpinner';
import Button from './Button';

const DataTable = ({
  data = [],
  columns = [],
  loading = false,
  error = null,
  sortBy = null,
  sortOrder = 'asc',
  onSort,
  searchQuery = '',
  onSearch,
  selectedRows = [],
  onSelectRow,
  onSelectAll,
  selectable = false,
  actions = [],
  onRowClick,
  emptyMessage = 'No data available',
  className = '',
  tableClassName = '',
  headerClassName = '',
  bodyClassName = '',
  rowClassName = '',
  cellClassName = '',
  ...props
}) => {
  const handleSort = (columnKey) => {
    if (!onSort) return;
    
    if (sortBy === columnKey) {
      // Toggle sort order
      onSort(columnKey, sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new sort column
      onSort(columnKey, 'asc');
    }
  };

  const handleSelectAll = (checked) => {
    if (onSelectAll) {
      onSelectAll(checked ? data.map(row => row.id) : []);
    }
  };

  const handleSelectRow = (rowId, checked) => {
    if (onSelectRow) {
      onSelectRow(rowId, checked);
    }
  };

  const isRowSelected = (rowId) => {
    return selectedRows.includes(rowId);
  };

  const isAllSelected = data.length > 0 && selectedRows.length === data.length;
  const isIndeterminate = selectedRows.length > 0 && selectedRows.length < data.length;

  const getSortIcon = (columnKey) => {
    if (sortBy !== columnKey) {
      return <ChevronUpDownIcon className="h-4 w-4 text-gray-400" />;
    }
    
    return sortOrder === 'asc' 
      ? <ChevronUpIcon className="h-4 w-4 text-gray-600" />
      : <ChevronDownIcon className="h-4 w-4 text-gray-600" />;
  };

  const renderCellContent = (row, column) => {
    if (column.render) {
      return column.render(row[column.key], row);
    }
    
    const value = row[column.key];
    
    if (value === null || value === undefined) {
      return <span className="text-gray-400">-</span>;
    }
    
    return value;
  };

  const renderRowActions = (row) => {
    if (!actions || actions.length === 0) return null;

    return (
      <Menu as="div" className="relative">
        <Menu.Button className="p-1 rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
          <EllipsisVerticalIcon className="h-5 w-5" />
        </Menu.Button>
        
        <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-700">
          <div className="py-1">
            {actions.map((action, index) => (
              <Menu.Item key={index}>
                {({ active }) => (
                  <button
                    onClick={() => action.onClick(row)}
                    disabled={action.disabled?.(row)}
                    className={classNames(
                      'flex w-full items-center px-4 py-2 text-sm',
                      active ? 'bg-gray-100 text-gray-900 dark:bg-gray-600 dark:text-white' : 'text-gray-700 dark:text-gray-300',
                      action.disabled?.(row) && 'opacity-50 cursor-not-allowed'
                    )}
                  >
                    {action.icon && <action.icon className="mr-3 h-4 w-4" />}
                    {action.label}
                  </button>
                )}
              </Menu.Item>
            ))}
          </div>
        </Menu.Items>
      </Menu>
    );
  };

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-2">Error loading data</div>
        <div className="text-sm text-gray-500">{error}</div>
      </div>
    );
  }

  return (
    <div className={classNames('w-full', className)} {...props}>
      {/* Search Bar */}
      {onSearch && (
        <div className="mb-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => onSearch(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>
      )}

      {/* Table */}
      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
        <div className="overflow-x-auto">
          <table className={classNames(
            'min-w-full divide-y divide-gray-300 dark:divide-gray-600',
            tableClassName
          )}>
            {/* Header */}
            <thead className={classNames(
              'bg-gray-50 dark:bg-gray-700',
              headerClassName
            )}>
              <tr>
                {/* Select All Checkbox */}
                {selectable && (
                  <th className="relative w-12 px-6 sm:w-16 sm:px-8">
                    <input
                      type="checkbox"
                      checked={isAllSelected}
                      ref={(input) => {
                        if (input) input.indeterminate = isIndeterminate;
                      }}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      className="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                )}

                {/* Column Headers */}
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className={classNames(
                      'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300',
                      column.sortable && 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600',
                      column.className
                    )}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{column.title}</span>
                      {column.sortable && getSortIcon(column.key)}
                    </div>
                  </th>
                ))}

                {/* Actions Column */}
                {actions && actions.length > 0 && (
                  <th className="relative px-6 py-3">
                    <span className="sr-only">Actions</span>
                  </th>
                )}
              </tr>
            </thead>

            {/* Body */}
            <tbody className={classNames(
              'bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700',
              bodyClassName
            )}>
              {loading ? (
                <tr>
                  <td 
                    colSpan={columns.length + (selectable ? 1 : 0) + (actions?.length > 0 ? 1 : 0)}
                    className="px-6 py-12 text-center"
                  >
                    <LoadingSpinner size="lg" />
                  </td>
                </tr>
              ) : data.length === 0 ? (
                <tr>
                  <td 
                    colSpan={columns.length + (selectable ? 1 : 0) + (actions?.length > 0 ? 1 : 0)}
                    className="px-6 py-12 text-center text-gray-500 dark:text-gray-400"
                  >
                    {emptyMessage}
                  </td>
                </tr>
              ) : (
                data.map((row, rowIndex) => (
                  <tr
                    key={row.id || rowIndex}
                    className={classNames(
                      'hover:bg-gray-50 dark:hover:bg-gray-700',
                      onRowClick && 'cursor-pointer',
                      isRowSelected(row.id) && 'bg-blue-50 dark:bg-blue-900/20',
                      rowClassName
                    )}
                    onClick={() => onRowClick?.(row)}
                  >
                    {/* Select Checkbox */}
                    {selectable && (
                      <td className="relative w-12 px-6 sm:w-16 sm:px-8">
                        <input
                          type="checkbox"
                          checked={isRowSelected(row.id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            handleSelectRow(row.id, e.target.checked);
                          }}
                          className="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                    )}

                    {/* Data Cells */}
                    {columns.map((column) => (
                      <td
                        key={column.key}
                        className={classNames(
                          'px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100',
                          cellClassName,
                          column.cellClassName
                        )}
                      >
                        {renderCellContent(row, column)}
                      </td>
                    ))}

                    {/* Actions */}
                    {actions && actions.length > 0 && (
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <div onClick={(e) => e.stopPropagation()}>
                          {renderRowActions(row)}
                        </div>
                      </td>
                    )}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default DataTable;
