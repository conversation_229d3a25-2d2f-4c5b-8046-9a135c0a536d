import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiMethods } from '../../services/api';

// Async thunks
export const fetchProducts = createAsyncThunk(
  'product/fetchProducts',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/products', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchProduct = createAsyncThunk(
  'product/fetchProduct',
  async (id, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get(`/products/${id}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const createProduct = createAsyncThunk(
  'product/createProduct',
  async (productData, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post('/products', productData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateProduct = createAsyncThunk(
  'product/updateProduct',
  async ({ id, data }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.put(`/products/${id}`, data);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const deleteProduct = createAsyncThunk(
  'product/deleteProduct',
  async (id, { rejectWithValue }) => {
    try {
      await apiMethods.delete(`/products/${id}`);
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchProductStats = createAsyncThunk(
  'product/fetchProductStats',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/products/stats', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchLowStockProducts = createAsyncThunk(
  'product/fetchLowStockProducts',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/products/low-stock', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateStock = createAsyncThunk(
  'product/updateStock',
  async ({ productId, stockData }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post(`/products/${productId}/stock`, stockData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const bulkUpdatePrices = createAsyncThunk(
  'product/bulkUpdatePrices',
  async ({ productIds, priceData }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post('/products/bulk-update-prices', {
        product_ids: productIds,
        ...priceData
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  // Data
  products: [],
  currentProduct: null,
  lowStockProducts: [],
  stats: null,

  // Pagination
  pagination: {
    current_page: 1,
    total_pages: 1,
    total_items: 0,
    items_per_page: 15,
    has_next_page: false,
    has_prev_page: false,
  },

  // Loading states
  loading: false,
  productLoading: false,
  lowStockLoading: false,
  statsLoading: false,
  actionLoading: false,

  // Error states
  error: null,
  productError: null,
  lowStockError: null,
  statsError: null,
  actionError: null,

  // UI states
  selectedProducts: [],
  filters: {
    category_id: '',
    brand_id: '',
    status: '',
    stock_status: '',
    price_from: '',
    price_to: '',
    warehouse_id: '',
  },
  sortBy: 'product_name',
  sortOrder: 'asc',
  searchQuery: '',
  viewMode: 'list', // list or grid
};

const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    // Selection actions
    selectProduct: (state, action) => {
      const productId = action.payload;
      if (!state.selectedProducts.includes(productId)) {
        state.selectedProducts.push(productId);
      }
    },
    deselectProduct: (state, action) => {
      const productId = action.payload;
      state.selectedProducts = state.selectedProducts.filter(id => id !== productId);
    },
    selectAllProducts: (state) => {
      state.selectedProducts = state.products.map(product => product.id);
    },
    deselectAllProducts: (state) => {
      state.selectedProducts = [];
    },

    // Filter actions
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },

    // Sort actions
    setSortBy: (state, action) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action) => {
      state.sortOrder = action.payload;
    },
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },

    // Search actions
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    clearSearchQuery: (state) => {
      state.searchQuery = '';
    },

    // View mode
    setViewMode: (state, action) => {
      state.viewMode = action.payload;
    },
    toggleViewMode: (state) => {
      state.viewMode = state.viewMode === 'list' ? 'grid' : 'list';
    },

    // Clear current product
    clearCurrentProduct: (state) => {
      state.currentProduct = null;
      state.productError = null;
    },

    // Clear errors
    clearError: (state) => {
      state.error = null;
    },
    clearProductError: (state) => {
      state.productError = null;
    },
    clearLowStockError: (state) => {
      state.lowStockError = null;
    },
    clearStatsError: (state) => {
      state.statsError = null;
    },
    clearActionError: (state) => {
      state.actionError = null;
    },

    // Reset state
    resetProductState: (state) => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    // Fetch products
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload.data.products;
        state.pagination = action.payload.data.pagination;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // Fetch single product
    builder
      .addCase(fetchProduct.pending, (state) => {
        state.productLoading = true;
        state.productError = null;
      })
      .addCase(fetchProduct.fulfilled, (state, action) => {
        state.productLoading = false;
        state.currentProduct = action.payload.data.product;
      })
      .addCase(fetchProduct.rejected, (state, action) => {
        state.productLoading = false;
        state.productError = action.payload;
      });

    // Create product
    builder
      .addCase(createProduct.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(createProduct.fulfilled, (state, action) => {
        state.actionLoading = false;
        state.products.unshift(action.payload.data.product);
      })
      .addCase(createProduct.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });

    // Update product
    builder
      .addCase(updateProduct.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(updateProduct.fulfilled, (state, action) => {
        state.actionLoading = false;
        const updatedProduct = action.payload.data.product;
        const index = state.products.findIndex(product => product.id === updatedProduct.id);
        if (index !== -1) {
          state.products[index] = updatedProduct;
        }
        if (state.currentProduct?.id === updatedProduct.id) {
          state.currentProduct = updatedProduct;
        }
      })
      .addCase(updateProduct.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });

    // Delete product
    builder
      .addCase(deleteProduct.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(deleteProduct.fulfilled, (state, action) => {
        state.actionLoading = false;
        const productId = action.payload;
        state.products = state.products.filter(product => product.id !== productId);
        state.selectedProducts = state.selectedProducts.filter(id => id !== productId);
        if (state.currentProduct?.id === productId) {
          state.currentProduct = null;
        }
      })
      .addCase(deleteProduct.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });

    // Fetch low stock products
    builder
      .addCase(fetchLowStockProducts.pending, (state) => {
        state.lowStockLoading = true;
        state.lowStockError = null;
      })
      .addCase(fetchLowStockProducts.fulfilled, (state, action) => {
        state.lowStockLoading = false;
        state.lowStockProducts = action.payload.data.products || action.payload.data.low_stock_products;
      })
      .addCase(fetchLowStockProducts.rejected, (state, action) => {
        state.lowStockLoading = false;
        state.lowStockError = action.payload;
      });

    // Fetch stats
    builder
      .addCase(fetchProductStats.pending, (state) => {
        state.statsLoading = true;
        state.statsError = null;
      })
      .addCase(fetchProductStats.fulfilled, (state, action) => {
        state.statsLoading = false;
        state.stats = action.payload.data;
      })
      .addCase(fetchProductStats.rejected, (state, action) => {
        state.statsLoading = false;
        state.statsError = action.payload;
      });

    // Update stock
    builder
      .addCase(updateStock.fulfilled, (state, action) => {
        const updatedProduct = action.payload.data.product;
        const index = state.products.findIndex(product => product.id === updatedProduct.id);
        if (index !== -1) {
          state.products[index] = updatedProduct;
        }
        if (state.currentProduct?.id === updatedProduct.id) {
          state.currentProduct = updatedProduct;
        }
      });

    // Bulk update prices
    builder
      .addCase(bulkUpdatePrices.fulfilled, (state, action) => {
        const updatedProducts = action.payload.data.products;
        updatedProducts.forEach(updatedProduct => {
          const index = state.products.findIndex(product => product.id === updatedProduct.id);
          if (index !== -1) {
            state.products[index] = updatedProduct;
          }
        });
      });
  },
});

export const {
  selectProduct,
  deselectProduct,
  selectAllProducts,
  deselectAllProducts,
  setFilter,
  setFilters,
  clearFilters,
  setSortBy,
  setSortOrder,
  toggleSortOrder,
  setSearchQuery,
  clearSearchQuery,
  setViewMode,
  toggleViewMode,
  clearCurrentProduct,
  clearError,
  clearProductError,
  clearLowStockError,
  clearStatsError,
  clearActionError,
  resetProductState,
} = productSlice.actions;

// Action aliases for compatibility
export const updateProductStock = updateStock;

export default productSlice.reducer;

// Selectors
export const selectProducts = (state) => state.product.products;
export const selectCurrentProduct = (state) => state.product.currentProduct;
export const selectLowStockProducts = (state) => state.product.lowStockProducts;
export const selectProductStats = (state) => state.product.stats;
export const selectProductPagination = (state) => state.product.pagination;
export const selectProductLoading = (state) => state.product.loading;
export const selectProductError = (state) => state.product.error;
export const selectSelectedProducts = (state) => state.product.selectedProducts;
export const selectProductFilters = (state) => state.product.filters;
export const selectProductSort = (state) => ({
  sortBy: state.product.sortBy,
  sortOrder: state.product.sortOrder,
});
export const selectProductSearchQuery = (state) => state.product.searchQuery;
export const selectProductViewMode = (state) => state.product.viewMode;
