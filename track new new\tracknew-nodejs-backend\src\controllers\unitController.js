const { Unit, User, Company, Product } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all units with filtering and pagination
const getUnits = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    unit_type,
    is_base_unit,
    is_active = true,
    sort_by = 'name',
    sort_order = 'ASC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { short_name: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add filters
  if (unit_type) {
    whereConditions.unit_type = unit_type;
  }

  if (is_base_unit !== undefined) {
    whereConditions.is_base_unit = is_base_unit === 'true';
  }

  if (is_active !== undefined) {
    whereConditions.is_active = is_active === 'true';
  }

  // Get units with associations
  const { count, rows: units } = await Unit.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Unit,
        as: 'baseUnit',
        attributes: ['id', 'name', 'short_name', 'symbol']
      }
    ],
    limit: parseInt(limit),
    offset: offset,
    order: [[sort_by, sort_order.toUpperCase()]],
    distinct: true
  });

  // Calculate pagination
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      units,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single unit by ID
const getUnit = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const unit = await Unit.findOne({
    where: {
      id,
      company_id: companyId
    },
    include: [
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Unit,
        as: 'baseUnit',
        attributes: ['id', 'name', 'short_name', 'symbol', 'conversion_factor']
      }
    ]
  });

  if (!unit) {
    return next(new AppError('Unit not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      unit
    }
  });
});

// Create new unit
const createUnit = catchAsync(async (req, res, next) => {
  const {
    name,
    short_name,
    description,
    unit_type = 'quantity',
    base_unit_id,
    conversion_factor = 1.000000,
    is_base_unit = false,
    decimal_places = 2,
    symbol,
    prefix,
    suffix,
    format_pattern,
    is_fractional = true,
    minimum_value,
    maximum_value,
    step_value = 1.000000,
    rounding_method = 'round',
    is_active = true,
    sort_order = 0,
    notes
  } = req.body;

  const companyId = req.user.company_id;

  // Check for duplicate name or short_name within company
  const existingUnit = await Unit.findOne({
    where: {
      company_id: companyId,
      [Op.or]: [
        { name: name },
        { short_name: short_name }
      ]
    }
  });

  if (existingUnit) {
    return next(new AppError('Unit with this name or short name already exists', 400));
  }

  // Validate base unit if provided
  if (base_unit_id) {
    const baseUnit = await Unit.findOne({
      where: {
        id: base_unit_id,
        company_id: companyId,
        is_active: true
      }
    });

    if (!baseUnit) {
      return next(new AppError('Base unit not found or inactive', 400));
    }
  }

  const unit = await Unit.create({
    company_id: companyId,
    name,
    short_name,
    description,
    unit_type,
    base_unit_id,
    conversion_factor,
    is_base_unit,
    decimal_places,
    symbol,
    prefix,
    suffix,
    format_pattern,
    is_fractional,
    minimum_value,
    maximum_value,
    step_value,
    rounding_method,
    is_active,
    sort_order,
    notes,
    created_by: req.user.id
  });

  // Fetch the created unit with associations
  const createdUnit = await Unit.findByPk(unit.id, {
    include: [
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Unit,
        as: 'baseUnit',
        attributes: ['id', 'name', 'short_name', 'symbol']
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      unit: createdUnit
    }
  });
});

// Update unit
const updateUnit = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const unit = await Unit.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!unit) {
    return next(new AppError('Unit not found', 404));
  }

  // Check if unit is being used by products
  if (req.body.is_active === false) {
    const productCount = await Product.count({
      where: {
        unit_id: id,
        company_id: companyId
      }
    });

    if (productCount > 0) {
      return next(new AppError(`Cannot deactivate unit. It is being used by ${productCount} product(s)`, 400));
    }
  }

  // Check for duplicate name or short_name (excluding current unit)
  if (req.body.name || req.body.short_name) {
    const duplicateConditions = [];

    if (req.body.name) {
      duplicateConditions.push({ name: req.body.name });
    }

    if (req.body.short_name) {
      duplicateConditions.push({ short_name: req.body.short_name });
    }

    const existingUnit = await Unit.findOne({
      where: {
        company_id: companyId,
        id: { [Op.ne]: id },
        [Op.or]: duplicateConditions
      }
    });

    if (existingUnit) {
      return next(new AppError('Unit with this name or short name already exists', 400));
    }
  }

  // Validate base unit if provided
  if (req.body.base_unit_id) {
    const baseUnit = await Unit.findOne({
      where: {
        id: req.body.base_unit_id,
        company_id: companyId,
        is_active: true
      }
    });

    if (!baseUnit) {
      return next(new AppError('Base unit not found or inactive', 400));
    }
  }

  // Update unit
  await unit.update({
    ...req.body,
    updated_by: req.user.id
  });

  // Fetch updated unit with associations
  const updatedUnit = await Unit.findByPk(id, {
    include: [
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updatedBy',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Unit,
        as: 'baseUnit',
        attributes: ['id', 'name', 'short_name', 'symbol']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      unit: updatedUnit
    }
  });
});

// Delete unit
const deleteUnit = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const unit = await Unit.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!unit) {
    return next(new AppError('Unit not found', 404));
  }

  // Check if unit is being used by products
  const productCount = await Product.count({
    where: {
      unit_id: id,
      company_id: companyId
    }
  });

  if (productCount > 0) {
    return next(new AppError(`Cannot delete unit. It is being used by ${productCount} product(s)`, 400));
  }

  // Check if unit is a base unit for other units
  const dependentUnits = await Unit.count({
    where: {
      base_unit_id: id,
      company_id: companyId
    }
  });

  if (dependentUnits > 0) {
    return next(new AppError(`Cannot delete unit. It is used as base unit for ${dependentUnits} other unit(s)`, 400));
  }

  await unit.destroy();

  res.status(200).json({
    status: 'success',
    message: 'Unit deleted successfully'
  });
});

// Get unit statistics
const getUnitStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  // Total units count
  const totalUnits = await Unit.count({
    where: { company_id: companyId }
  });

  // Active units count
  const activeUnits = await Unit.count({
    where: {
      company_id: companyId,
      is_active: true
    }
  });

  // Units by type
  const unitsByType = await Unit.findAll({
    where: { company_id: companyId },
    attributes: [
      'unit_type',
      [Unit.sequelize.fn('COUNT', Unit.sequelize.col('id')), 'count']
    ],
    group: ['unit_type'],
    raw: true
  });

  // Base units count
  const baseUnits = await Unit.count({
    where: {
      company_id: companyId,
      is_base_unit: true
    }
  });

  // Most used units (by product count)
  const mostUsedUnits = await Unit.findAll({
    where: { company_id: companyId },
    include: [{
      model: Product,
      as: 'products',
      attributes: []
    }],
    attributes: [
      'id',
      'name',
      'short_name',
      [Unit.sequelize.fn('COUNT', Unit.sequelize.col('products.id')), 'product_count']
    ],
    group: ['Unit.id'],
    order: [[Unit.sequelize.fn('COUNT', Unit.sequelize.col('products.id')), 'DESC']],
    limit: 5,
    raw: false
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_units: totalUnits,
      active_units: activeUnits,
      inactive_units: totalUnits - activeUnits,
      base_units: baseUnits,
      units_by_type: unitsByType,
      most_used_units: mostUsedUnits
    }
  });
});

// Get units by type
const getUnitsByType = catchAsync(async (req, res) => {
  const { unit_type } = req.params;
  const companyId = req.user.company_id;

  const units = await Unit.findAll({
    where: {
      company_id: companyId,
      unit_type: unit_type,
      is_active: true
    },
    attributes: ['id', 'name', 'short_name', 'symbol', 'conversion_factor'],
    order: [['name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      units
    }
  });
});

// Get base units
const getBaseUnits = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const baseUnits = await Unit.findAll({
    where: {
      company_id: companyId,
      is_base_unit: true,
      is_active: true
    },
    attributes: ['id', 'name', 'short_name', 'symbol', 'unit_type'],
    order: [['unit_type', 'ASC'], ['name', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: {
      base_units: baseUnits
    }
  });
});

module.exports = {
  getUnits,
  getUnit,
  createUnit,
  updateUnit,
  deleteUnit,
  getUnitStats,
  getUnitsByType,
  getBaseUnits
};
