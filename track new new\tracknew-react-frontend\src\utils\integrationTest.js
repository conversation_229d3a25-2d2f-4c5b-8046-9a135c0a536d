// Integration test utilities for Phase 4
import api, { healthCheck } from '../services/api';

// Test API connectivity
export const testAPIConnection = async () => {
  try {
    console.log('🔄 Testing API connection...');
    const response = await healthCheck();
    console.log('✅ API connection successful:', response.data);
    return { success: true, data: response.data };
  } catch (error) {
    console.error('❌ API connection failed:', error.message);
    return { success: false, error: error.message };
  }
};

// Test authentication endpoints
export const testAuthEndpoints = async () => {
  const results = {
    login: { tested: false, success: false, error: null },
    register: { tested: false, success: false, error: null },
    profile: { tested: false, success: false, error: null }
  };

  try {
    console.log('🔄 Testing authentication endpoints...');

    // Test login with demo credentials
    try {
      const loginResponse = await api.post('/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      results.login = { tested: true, success: true, data: loginResponse.data };
      console.log('✅ Login endpoint working');

      // If login successful, test profile endpoint
      try {
        const profileResponse = await api.get('/auth/profile');
        results.profile = { tested: true, success: true, data: profileResponse.data };
        console.log('✅ Profile endpoint working');
      } catch (profileError) {
        results.profile = { tested: true, success: false, error: profileError.message };
        console.log('❌ Profile endpoint failed:', profileError.message);
      }

    } catch (loginError) {
      results.login = { tested: true, success: false, error: loginError.message };
      console.log('❌ Login endpoint failed:', loginError.message);
    }

    // Test register endpoint (with test data)
    try {
      const registerResponse = await api.post('/auth/register', {
        name: 'Test User',
        email: `test${Date.now()}@example.com`,
        password: 'password123',
        password_confirmation: 'password123',
        company_name: 'Test Company'
      });
      results.register = { tested: true, success: true, data: registerResponse.data };
      console.log('✅ Register endpoint working');
    } catch (registerError) {
      results.register = { tested: true, success: false, error: registerError.message };
      console.log('❌ Register endpoint failed:', registerError.message);
    }

  } catch (error) {
    console.error('❌ Auth endpoints test failed:', error.message);
  }

  return results;
};

// Test dashboard endpoints
export const testDashboardEndpoints = async () => {
  const endpoints = [
    { name: 'overview', url: '/dashboard/overview' },
    { name: 'sales', url: '/dashboard/sales' },
    { name: 'financial', url: '/dashboard/financial' },
    { name: 'inventory', url: '/dashboard/inventory' },
    { name: 'top-customers', url: '/dashboard/top-customers' },
    { name: 'activities', url: '/dashboard/activities' }
  ];

  const results = {};

  console.log('🔄 Testing dashboard endpoints...');

  for (const endpoint of endpoints) {
    try {
      const response = await api.get(endpoint.url);
      results[endpoint.name] = { tested: true, success: true, data: response.data };
      console.log(`✅ Dashboard ${endpoint.name} endpoint working`);
    } catch (error) {
      results[endpoint.name] = { tested: true, success: false, error: error.message };
      console.log(`❌ Dashboard ${endpoint.name} endpoint failed:`, error.message);
    }
  }

  return results;
};

// Test CRUD endpoints
export const testCRUDEndpoints = async () => {
  const results = {
    customers: { tested: false, success: false, error: null },
    services: { tested: false, success: false, error: null },
    products: { tested: false, success: false, error: null },
    sales: { tested: false, success: false, error: null }
  };

  console.log('🔄 Testing CRUD endpoints...');

  // Test customers endpoint
  try {
    const customersResponse = await api.get('/customers', {
      params: { page: 1, limit: 5 }
    });
    results.customers = { tested: true, success: true, data: customersResponse.data };
    console.log('✅ Customers endpoint working');
  } catch (error) {
    results.customers = { tested: true, success: false, error: error.message };
    console.log('❌ Customers endpoint failed:', error.message);
  }

  // Test services endpoint
  try {
    const servicesResponse = await api.get('/services', {
      params: { page: 1, limit: 5 }
    });
    results.services = { tested: true, success: true, data: servicesResponse.data };
    console.log('✅ Services endpoint working');
  } catch (error) {
    results.services = { tested: true, success: false, error: error.message };
    console.log('❌ Services endpoint failed:', error.message);
  }

  // Test products endpoint
  try {
    const productsResponse = await api.get('/products', {
      params: { page: 1, limit: 5 }
    });
    results.products = { tested: true, success: true, data: productsResponse.data };
    console.log('✅ Products endpoint working');
  } catch (error) {
    results.products = { tested: true, success: false, error: error.message };
    console.log('❌ Products endpoint failed:', error.message);
  }

  // Test sales endpoint
  try {
    const salesResponse = await api.get('/sales', {
      params: { page: 1, limit: 5 }
    });
    results.sales = { tested: true, success: true, data: salesResponse.data };
    console.log('✅ Sales endpoint working');
  } catch (error) {
    results.sales = { tested: true, success: false, error: error.message };
    console.log('❌ Sales endpoint failed:', error.message);
  }

  return results;
};

// Run comprehensive integration test
export const runIntegrationTests = async () => {
  console.log('🚀 Starting Phase 4 Integration Tests...\n');

  const testResults = {
    timestamp: new Date().toISOString(),
    apiConnection: null,
    authEndpoints: null,
    dashboardEndpoints: null,
    crudEndpoints: null,
    summary: {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      successRate: 0
    }
  };

  // Test API connection
  testResults.apiConnection = await testAPIConnection();
  console.log('');

  // Test authentication endpoints
  testResults.authEndpoints = await testAuthEndpoints();
  console.log('');

  // Test dashboard endpoints
  testResults.dashboardEndpoints = await testDashboardEndpoints();
  console.log('');

  // Test CRUD endpoints
  testResults.crudEndpoints = await testCRUDEndpoints();
  console.log('');

  // Calculate summary
  let totalTests = 0;
  let passedTests = 0;

  // Count API connection test
  totalTests += 1;
  if (testResults.apiConnection.success) passedTests += 1;

  // Count auth tests
  Object.values(testResults.authEndpoints).forEach(result => {
    if (result.tested) {
      totalTests += 1;
      if (result.success) passedTests += 1;
    }
  });

  // Count dashboard tests
  Object.values(testResults.dashboardEndpoints).forEach(result => {
    if (result.tested) {
      totalTests += 1;
      if (result.success) passedTests += 1;
    }
  });

  // Count CRUD tests
  Object.values(testResults.crudEndpoints).forEach(result => {
    if (result.tested) {
      totalTests += 1;
      if (result.success) passedTests += 1;
    }
  });

  testResults.summary = {
    totalTests,
    passedTests,
    failedTests: totalTests - passedTests,
    successRate: totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0
  };

  // Print summary
  console.log('📊 Integration Test Summary:');
  console.log(`Total Tests: ${testResults.summary.totalTests}`);
  console.log(`Passed: ${testResults.summary.passedTests}`);
  console.log(`Failed: ${testResults.summary.failedTests}`);
  console.log(`Success Rate: ${testResults.summary.successRate}%`);

  if (testResults.summary.successRate >= 80) {
    console.log('🎉 Integration tests mostly successful!');
  } else if (testResults.summary.successRate >= 50) {
    console.log('⚠️ Integration tests partially successful. Some issues need attention.');
  } else {
    console.log('❌ Integration tests failed. Backend may not be running or configured correctly.');
  }

  return testResults;
};

// Export for use in development
export default {
  testAPIConnection,
  testAuthEndpoints,
  testDashboardEndpoints,
  testCRUDEndpoints,
  runIntegrationTests
};
