const express = require('express');
const { query } = require('express-validator');
const dashboardController = require('../controllers/dashboardController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation for query parameters
const queryValidation = [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),

  query('months')
    .optional()
    .isInt({ min: 1, max: 24 })
    .withMessage('Months must be between 1 and 24'),

  query('period')
    .optional()
    .isIn(['week', 'month', 'quarter', 'year'])
    .withMessage('Invalid period'),

  query('date_from')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid date'),

  query('date_to')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid date')
];

// Dashboard routes
router.get('/overview', queryValidation, validateRequest, dashboardController.getDashboardOverview);
router.get('/activities', queryValidation, validateRequest, dashboardController.getRecentActivities);
router.get('/status-distribution', dashboardController.getServiceStatusDistribution);
router.get('/trends', queryValidation, validateRequest, dashboardController.getServiceTrends);

// New dashboard widget routes
router.get('/sales', dashboardController.getSalesDashboard);
router.get('/financial', dashboardController.getFinancialDashboard);
router.get('/inventory', dashboardController.getInventoryDashboard);
router.get('/top-customers', queryValidation, validateRequest, dashboardController.getTopCustomers);
router.get('/payments', dashboardController.getPaymentDashboard);

module.exports = router;
