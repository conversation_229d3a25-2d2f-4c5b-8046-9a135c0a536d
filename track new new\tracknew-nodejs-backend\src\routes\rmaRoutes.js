const express = require('express');
const { body } = require('express-validator');
const rmaController = require('../controllers/rmaController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating RMA
const createRMAValidation = [
  body('customer_id')
    .notEmpty()
    .withMessage('Customer ID is required')
    .isInt({ min: 1 })
    .withMessage('Customer ID must be a positive integer'),
  
  body('product_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),
  
  body('sales_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Sales ID must be a positive integer'),
  
  body('service_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Service ID must be a positive integer'),
  
  body('rma_date')
    .optional()
    .isISO8601()
    .withMessage('RMA date must be a valid date'),
  
  body('rma_type')
    .notEmpty()
    .withMessage('RMA type is required')
    .isIn(['return', 'exchange', 'repair', 'warranty_claim'])
    .withMessage('Invalid RMA type'),
  
  body('reason')
    .notEmpty()
    .withMessage('Reason is required')
    .isIn(['defective', 'damaged', 'wrong_item', 'not_as_described', 'customer_request', 'warranty_issue', 'other'])
    .withMessage('Invalid reason'),
  
  body('reason_description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Reason description must be maximum 1000 characters'),
  
  body('product_name')
    .notEmpty()
    .withMessage('Product name is required')
    .isLength({ min: 1, max: 255 })
    .withMessage('Product name must be between 1 and 255 characters'),
  
  body('product_code')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Product code must be maximum 100 characters'),
  
  body('serial_number')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Serial number must be maximum 100 characters'),
  
  body('batch_number')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Batch number must be maximum 100 characters'),
  
  body('quantity')
    .optional()
    .isFloat({ min: 0.001 })
    .withMessage('Quantity must be greater than 0'),
  
  body('unit_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a positive number'),
  
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Invalid priority'),
  
  body('expected_resolution_date')
    .optional()
    .isISO8601()
    .withMessage('Expected resolution date must be a valid date'),
  
  body('warehouse_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Warehouse ID must be a positive integer'),
  
  body('assigned_to')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Assigned to must be a positive integer'),
  
  body('customer_notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Customer notes must be maximum 1000 characters'),
  
  body('internal_notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Internal notes must be maximum 1000 characters'),
  
  body('attachments')
    .optional()
    .isArray()
    .withMessage('Attachments must be an array'),
  
  body('is_warranty_valid')
    .optional()
    .isBoolean()
    .withMessage('Is warranty valid must be a boolean'),
  
  body('warranty_expiry_date')
    .optional()
    .isISO8601()
    .withMessage('Warranty expiry date must be a valid date'),
  
  body('items')
    .optional()
    .isArray()
    .withMessage('Items must be an array')
];

// Validation rules for updating RMA
const updateRMAValidation = [
  body('rma_type')
    .optional()
    .isIn(['return', 'exchange', 'repair', 'warranty_claim'])
    .withMessage('Invalid RMA type'),
  
  body('reason')
    .optional()
    .isIn(['defective', 'damaged', 'wrong_item', 'not_as_described', 'customer_request', 'warranty_issue', 'other'])
    .withMessage('Invalid reason'),
  
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Invalid priority'),
  
  body('status')
    .optional()
    .isIn(['pending', 'approved', 'rejected', 'received', 'inspected', 'processed', 'completed', 'cancelled'])
    .withMessage('Invalid status'),
  
  body('quantity')
    .optional()
    .isFloat({ min: 0.001 })
    .withMessage('Quantity must be greater than 0'),
  
  body('unit_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Unit price must be a positive number'),
  
  body('assigned_to')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Assigned to must be a positive integer'),
  
  body('is_warranty_valid')
    .optional()
    .isBoolean()
    .withMessage('Is warranty valid must be a boolean')
];

// Validation for status update
const updateStatusValidation = [
  body('status')
    .notEmpty()
    .withMessage('Status is required')
    .isIn(['pending', 'approved', 'rejected', 'received', 'inspected', 'processed', 'completed', 'cancelled'])
    .withMessage('Invalid status'),
  
  body('resolution_type')
    .optional()
    .isIn(['refund', 'replacement', 'repair', 'credit_note', 'no_action'])
    .withMessage('Invalid resolution type'),
  
  body('resolution_notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Resolution notes must be maximum 1000 characters'),
  
  body('refund_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Refund amount must be a positive number'),
  
  body('replacement_product_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Replacement product ID must be a positive integer'),
  
  body('repair_cost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Repair cost must be a positive number')
];

// Validation for assignment
const assignRMAValidation = [
  body('assigned_to')
    .notEmpty()
    .withMessage('Assigned to is required')
    .isInt({ min: 1 })
    .withMessage('Assigned to must be a positive integer')
];

// Validation for bulk status update
const bulkUpdateStatusValidation = [
  body('rma_ids')
    .isArray({ min: 1 })
    .withMessage('RMA IDs array is required and must not be empty'),
  
  body('rma_ids.*')
    .isInt({ min: 1 })
    .withMessage('Each RMA ID must be a positive integer'),
  
  body('status')
    .notEmpty()
    .withMessage('Status is required')
    .isIn(['pending', 'approved', 'rejected', 'received', 'inspected', 'processed', 'completed', 'cancelled'])
    .withMessage('Invalid status')
];

// Routes
router
  .route('/')
  .get(rmaController.getRMAs)
  .post(createRMAValidation, validateRequest, restrictTo('admin', 'sub_admin', 'service_manager', 'customer_service'), rmaController.createRMA);

router
  .route('/stats')
  .get(rmaController.getRMAStats);

router
  .route('/bulk-update-status')
  .put(bulkUpdateStatusValidation, validateRequest, restrictTo('admin', 'sub_admin', 'service_manager'), rmaController.bulkUpdateRMAStatus);

router
  .route('/:id/status')
  .put(updateStatusValidation, validateRequest, restrictTo('admin', 'sub_admin', 'service_manager', 'customer_service'), rmaController.updateRMAStatus);

router
  .route('/:id/assign')
  .put(assignRMAValidation, validateRequest, restrictTo('admin', 'sub_admin', 'service_manager'), rmaController.assignRMA);

router
  .route('/:id')
  .get(rmaController.getRMA)
  .put(updateRMAValidation, validateRequest, restrictTo('admin', 'sub_admin', 'service_manager', 'customer_service'), rmaController.updateRMA)
  .delete(restrictTo('admin', 'sub_admin'), rmaController.deleteRMA);

module.exports = router;
