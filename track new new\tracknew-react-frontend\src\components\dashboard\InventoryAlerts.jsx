import React from 'react';
import { Link } from 'react-router-dom';
import { 
  ExclamationTriangleIcon, 
  ExclamationCircleIcon,
  InformationCircleIcon,
  CubeIcon
} from '@heroicons/react/24/outline';
import { classNames } from '../../utils/helpers';
import { LoadingSpinner } from '../ui';

const InventoryAlerts = ({ data, loading = false }) => {
  // Mock data for demonstration
  const mockAlerts = [
    {
      id: 1,
      type: 'low_stock',
      product_name: 'iPhone 14 Pro',
      current_stock: 2,
      min_stock: 5,
      severity: 'high'
    },
    {
      id: 2,
      type: 'out_of_stock',
      product_name: 'Samsung Galaxy S23',
      current_stock: 0,
      min_stock: 3,
      severity: 'critical'
    },
    {
      id: 3,
      type: 'low_stock',
      product_name: 'MacBook Air M2',
      current_stock: 1,
      min_stock: 2,
      severity: 'medium'
    },
    {
      id: 4,
      type: 'expiring_soon',
      product_name: 'Screen Protectors',
      expiry_date: '2024-02-15',
      severity: 'medium'
    }
  ];

  const alerts = data?.alerts || mockAlerts;

  const getAlertIcon = (type, severity) => {
    const iconClasses = "h-5 w-5";
    
    if (severity === 'critical') {
      return <ExclamationCircleIcon className={classNames(iconClasses, "text-red-500")} />;
    } else if (severity === 'high') {
      return <ExclamationTriangleIcon className={classNames(iconClasses, "text-orange-500")} />;
    } else {
      return <InformationCircleIcon className={classNames(iconClasses, "text-yellow-500")} />;
    }
  };

  const getAlertColor = (severity) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800';
      case 'high':
        return 'bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:border-orange-800';
      case 'medium':
        return 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800';
      default:
        return 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800';
    }
  };

  const formatAlertMessage = (alert) => {
    switch (alert.type) {
      case 'out_of_stock':
        return `Out of stock`;
      case 'low_stock':
        return `Low stock: ${alert.current_stock} left (min: ${alert.min_stock})`;
      case 'expiring_soon':
        return `Expires on ${new Date(alert.expiry_date).toLocaleDateString()}`;
      default:
        return 'Requires attention';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <LoadingSpinner />
      </div>
    );
  }

  if (!alerts || alerts.length === 0) {
    return (
      <div className="text-center py-8">
        <CubeIcon className="mx-auto h-12 w-12 text-green-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
          All good!
        </h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          No inventory alerts at the moment.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {alerts.slice(0, 5).map((alert) => (
        <div
          key={alert.id}
          className={classNames(
            'p-3 rounded-lg border',
            getAlertColor(alert.severity)
          )}
        >
          <div className="flex items-start space-x-3">
            {/* Alert Icon */}
            <div className="flex-shrink-0 mt-0.5">
              {getAlertIcon(alert.type, alert.severity)}
            </div>

            {/* Alert Content */}
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {alert.product_name}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                {formatAlertMessage(alert)}
              </div>
            </div>

            {/* Action Button */}
            <div className="flex-shrink-0">
              <Link
                to={`/products/${alert.product_id || alert.id}`}
                className="text-xs text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
              >
                View
              </Link>
            </div>
          </div>
        </div>
      ))}

      {/* Summary Stats */}
      {data && (
        <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-red-600 dark:text-red-400">
                {data.out_of_stock_count || 0}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Out of Stock</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-yellow-600 dark:text-yellow-400">
                {data.low_stock_count || 0}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Low Stock</div>
            </div>
          </div>
        </div>
      )}

      {/* View All Link */}
      {alerts.length > 5 && (
        <div className="text-center pt-2">
          <Link
            to="/products/alerts"
            className="text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
          >
            View all alerts ({alerts.length}) →
          </Link>
        </div>
      )}
    </div>
  );
};

export default InventoryAlerts;
