import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  UserCircleIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  EllipsisVerticalIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

// Redux
import {
  fetchCustomers,
  deleteCustomer,
  selectCustomers,
  selectCustomerPagination,
  selectCustomerLoading,
  selectCustomerError,
  selectSelectedCustomers,
  selectCustomerFilters,
  selectCustomerSearchQuery,
  setSearchQuery,
  setFilter,
  clearFilters,
  selectCustomer,
  deselectCustomer,
  selectAllCustomers,
  deselectAllCustomers
} from '../store/slices/customerSlice';

// Components
import { Button, DataTable, Pagination, Modal, ConfirmModal } from '../components/ui';
import { Input, Select } from '../components/forms';
import { getInitials, getAvatarColor, formatDate, classNames } from '../utils/helpers';

const Customers = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  // Local state
  const [showFilters, setShowFilters] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState(null);
  
  // Selectors
  const customers = useSelector(selectCustomers);
  const pagination = useSelector(selectCustomerPagination);
  const loading = useSelector(selectCustomerLoading);
  const error = useSelector(selectCustomerError);
  const selectedCustomers = useSelector(selectSelectedCustomers);
  const filters = useSelector(selectCustomerFilters);
  const searchQuery = useSelector(selectCustomerSearchQuery);

  // Fetch customers on mount and when filters change
  useEffect(() => {
    dispatch(fetchCustomers({
      page: pagination.current_page,
      limit: pagination.items_per_page,
      search: searchQuery,
      ...filters
    }));
  }, [dispatch, pagination.current_page, pagination.items_per_page, searchQuery, filters]);

  // Table columns
  const columns = [
    {
      key: 'name',
      title: 'Customer',
      sortable: true,
      render: (value, customer) => (
        <div className="flex items-center space-x-3">
          {customer.avatar ? (
            <img
              className="h-8 w-8 rounded-full"
              src={customer.avatar}
              alt={customer.name}
            />
          ) : (
            <div className={classNames(
              'h-8 w-8 rounded-full flex items-center justify-center text-white font-medium text-xs',
              getAvatarColor(customer.name)
            )}>
              {getInitials(customer.name)}
            </div>
          )}
          <div>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {customer.name}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              ID: {customer.customer_id || customer.id}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'contact',
      title: 'Contact',
      render: (value, customer) => (
        <div className="space-y-1">
          {customer.email && (
            <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
              <EnvelopeIcon className="h-4 w-4" />
              <span>{customer.email}</span>
            </div>
          )}
          {customer.phone && (
            <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
              <PhoneIcon className="h-4 w-4" />
              <span>{customer.phone}</span>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'location',
      title: 'Location',
      render: (value, customer) => (
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {customer.city && customer.state ? (
            <div className="flex items-center space-x-1">
              <MapPinIcon className="h-4 w-4" />
              <span>{customer.city}, {customer.state}</span>
            </div>
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (value) => (
        <span className={classNames(
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          value === 'active' 
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
        )}>
          {value || 'active'}
        </span>
      )
    },
    {
      key: 'created_at',
      title: 'Created',
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {formatDate(value)}
        </span>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: 'View',
      icon: EyeIcon,
      onClick: (customer) => navigate(`/customers/${customer.id}`)
    },
    {
      label: 'Edit',
      icon: PencilIcon,
      onClick: (customer) => navigate(`/customers/${customer.id}/edit`)
    },
    {
      label: 'Delete',
      icon: TrashIcon,
      onClick: (customer) => {
        setCustomerToDelete(customer);
        setDeleteModalOpen(true);
      },
      disabled: (customer) => customer.has_active_services
    }
  ];

  // Handlers
  const handleSearch = (query) => {
    dispatch(setSearchQuery(query));
  };

  const handleFilterChange = (key, value) => {
    dispatch(setFilter({ key, value }));
  };

  const handleClearFilters = () => {
    dispatch(clearFilters());
    dispatch(setSearchQuery(''));
  };

  const handlePageChange = (page) => {
    dispatch(fetchCustomers({
      page,
      limit: pagination.items_per_page,
      search: searchQuery,
      ...filters
    }));
  };

  const handleRowSelect = (customerId, checked) => {
    if (checked) {
      dispatch(selectCustomer(customerId));
    } else {
      dispatch(deselectCustomer(customerId));
    }
  };

  const handleSelectAll = (customerIds) => {
    if (customerIds.length > 0) {
      dispatch(selectAllCustomers());
    } else {
      dispatch(deselectAllCustomers());
    }
  };

  const handleDeleteCustomer = async () => {
    if (customerToDelete) {
      await dispatch(deleteCustomer(customerToDelete.id));
      setDeleteModalOpen(false);
      setCustomerToDelete(null);
      // Refresh the list
      dispatch(fetchCustomers({
        page: pagination.current_page,
        limit: pagination.items_per_page,
        search: searchQuery,
        ...filters
      }));
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Customers
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Manage your customer database and relationships
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            leftIcon={FunnelIcon}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </Button>
          
          <Button
            leftIcon={PlusIcon}
            onClick={() => navigate('/customers/new')}
          >
            Add Customer
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              label="Status"
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
              options={[
                { value: '', label: 'All Statuses' },
                { value: 'active', label: 'Active' },
                { value: 'inactive', label: 'Inactive' }
              ]}
            />
            
            <Select
              label="Customer Type"
              value={filters.customer_type}
              onChange={(value) => handleFilterChange('customer_type', value)}
              options={[
                { value: '', label: 'All Types' },
                { value: 'individual', label: 'Individual' },
                { value: 'business', label: 'Business' }
              ]}
            />
            
            <Input
              label="City"
              value={filters.city}
              onChange={(e) => handleFilterChange('city', e.target.value)}
              placeholder="Filter by city"
            />
            
            <div className="flex items-end space-x-2">
              <Button
                variant="outline"
                onClick={handleClearFilters}
                className="flex-1"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Data Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <DataTable
          data={customers}
          columns={columns}
          loading={loading}
          error={error}
          searchQuery={searchQuery}
          onSearch={handleSearch}
          selectedRows={selectedCustomers}
          onSelectRow={handleRowSelect}
          onSelectAll={handleSelectAll}
          selectable={true}
          actions={actions}
          onRowClick={(customer) => navigate(`/customers/${customer.id}`)}
          emptyMessage="No customers found"
        />
        
        {/* Pagination */}
        <Pagination
          currentPage={pagination.current_page}
          totalPages={pagination.total_pages}
          totalItems={pagination.total_items}
          itemsPerPage={pagination.items_per_page}
          onPageChange={handlePageChange}
        />
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteCustomer}
        title="Delete Customer"
        message={`Are you sure you want to delete "${customerToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        confirmColor="red"
      />
    </div>
  );
};

export default Customers;
