import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Outlet } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';

// Components
import Header from './Header';
import Sidebar from './Sidebar';
import NotificationPanel from '../notifications/NotificationPanel';
import LoadingSpinner from '../ui/LoadingSpinner';

// Redux
import { 
  selectSidebarOpen, 
  selectTheme,
  selectGlobalLoading 
} from '../../store/slices/uiSlice';
import { 
  selectShowNotificationPanel 
} from '../../store/slices/notificationSlice';

const Layout = () => {
  const dispatch = useDispatch();
  
  // Selectors
  const sidebarOpen = useSelector(selectSidebarOpen);
  const theme = useSelector(selectTheme);
  const globalLoading = useSelector(selectGlobalLoading);
  const showNotificationPanel = useSelector(selectShowNotificationPanel);

  // Apply theme to document
  useEffect(() => {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [theme]);

  // Handle responsive sidebar
  useEffect(() => {
    const handleResize = () => {
      // Auto-close sidebar on mobile when window is resized
      if (window.innerWidth >= 1024 && sidebarOpen) {
        // Keep sidebar open on desktop
      } else if (window.innerWidth < 1024 && sidebarOpen) {
        // Close sidebar on mobile by default
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [sidebarOpen, dispatch]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Global loading overlay */}
      {globalLoading && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      )}

      {/* Sidebar */}
      <Sidebar />

      {/* Main content area */}
      <div className={`
        flex flex-col min-h-screen transition-all duration-300 ease-in-out
        lg:ml-64
      `}>
        {/* Header */}
        <Header />

        {/* Page content */}
        <main className="flex-1 relative overflow-hidden">
          <div className="h-full">
            <Outlet />
          </div>
        </main>
      </div>

      {/* Notification panel */}
      {showNotificationPanel && <NotificationPanel />}

      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: theme === 'dark' ? '#374151' : '#ffffff',
            color: theme === 'dark' ? '#f9fafb' : '#111827',
            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#e5e7eb'}`,
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: '#ffffff',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#ffffff',
            },
          },
        }}
      />
    </div>
  );
};

export default Layout;
