import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiMethods } from '../../services/api';

// Async thunks
export const fetchAMCs = createAsyncThunk(
  'amc/fetchAMCs',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/amcs', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchAMC = createAsyncThunk(
  'amc/fetchAMC',
  async (id, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get(`/amcs/${id}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const createAMC = createAsyncThunk(
  'amc/createAMC',
  async (amcData, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post('/amcs', amcData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateAMC = createAsyncThunk(
  'amc/updateAMC',
  async ({ id, data }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.put(`/amcs/${id}`, data);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const deleteAMC = createAsyncThunk(
  'amc/deleteAMC',
  async (id, { rejectWithValue }) => {
    try {
      await apiMethods.delete(`/amcs/${id}`);
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const renewAMC = createAsyncThunk(
  'amc/renewAMC',
  async ({ amcId, renewalData }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post(`/amcs/${amcId}/renew`, renewalData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchAMCStats = createAsyncThunk(
  'amc/fetchAMCStats',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/amcs/stats', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchExpiringAMCs = createAsyncThunk(
  'amc/fetchExpiringAMCs',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/amcs/expiring', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  // Data
  amcs: [],
  currentAMC: null,
  expiringAMCs: [],
  stats: null,
  
  // Pagination
  pagination: {
    current_page: 1,
    total_pages: 1,
    total_items: 0,
    items_per_page: 15,
    has_next_page: false,
    has_prev_page: false,
  },
  
  // Loading states
  loading: false,
  amcLoading: false,
  expiringLoading: false,
  statsLoading: false,
  actionLoading: false,
  
  // Error states
  error: null,
  amcError: null,
  expiringError: null,
  statsError: null,
  actionError: null,
  
  // UI states
  selectedAMCs: [],
  filters: {
    status: '',
    customer_id: '',
    start_date: '',
    end_date: '',
    expiry_from: '',
    expiry_to: '',
  },
  sortBy: 'created_at',
  sortOrder: 'desc',
  searchQuery: '',
};

const amcSlice = createSlice({
  name: 'amc',
  initialState,
  reducers: {
    // Selection actions
    selectAMC: (state, action) => {
      const amcId = action.payload;
      if (!state.selectedAMCs.includes(amcId)) {
        state.selectedAMCs.push(amcId);
      }
    },
    deselectAMC: (state, action) => {
      const amcId = action.payload;
      state.selectedAMCs = state.selectedAMCs.filter(id => id !== amcId);
    },
    selectAllAMCs: (state) => {
      state.selectedAMCs = state.amcs.map(amc => amc.id);
    },
    deselectAllAMCs: (state) => {
      state.selectedAMCs = [];
    },
    
    // Filter actions
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    // Sort actions
    setSortBy: (state, action) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action) => {
      state.sortOrder = action.payload;
    },
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    // Search actions
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    clearSearchQuery: (state) => {
      state.searchQuery = '';
    },
    
    // Clear current AMC
    clearCurrentAMC: (state) => {
      state.currentAMC = null;
      state.amcError = null;
    },
    
    // Clear errors
    clearError: (state) => {
      state.error = null;
    },
    clearAMCError: (state) => {
      state.amcError = null;
    },
    clearExpiringError: (state) => {
      state.expiringError = null;
    },
    clearStatsError: (state) => {
      state.statsError = null;
    },
    clearActionError: (state) => {
      state.actionError = null;
    },
    
    // Reset state
    resetAMCState: (state) => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    // Fetch AMCs
    builder
      .addCase(fetchAMCs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAMCs.fulfilled, (state, action) => {
        state.loading = false;
        state.amcs = action.payload.data.amcs;
        state.pagination = action.payload.data.pagination;
      })
      .addCase(fetchAMCs.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
    
    // Fetch single AMC
    builder
      .addCase(fetchAMC.pending, (state) => {
        state.amcLoading = true;
        state.amcError = null;
      })
      .addCase(fetchAMC.fulfilled, (state, action) => {
        state.amcLoading = false;
        state.currentAMC = action.payload.data.amc;
      })
      .addCase(fetchAMC.rejected, (state, action) => {
        state.amcLoading = false;
        state.amcError = action.payload;
      });
    
    // Create AMC
    builder
      .addCase(createAMC.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(createAMC.fulfilled, (state, action) => {
        state.actionLoading = false;
        state.amcs.unshift(action.payload.data.amc);
      })
      .addCase(createAMC.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Update AMC
    builder
      .addCase(updateAMC.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(updateAMC.fulfilled, (state, action) => {
        state.actionLoading = false;
        const updatedAMC = action.payload.data.amc;
        const index = state.amcs.findIndex(amc => amc.id === updatedAMC.id);
        if (index !== -1) {
          state.amcs[index] = updatedAMC;
        }
        if (state.currentAMC?.id === updatedAMC.id) {
          state.currentAMC = updatedAMC;
        }
      })
      .addCase(updateAMC.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Delete AMC
    builder
      .addCase(deleteAMC.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(deleteAMC.fulfilled, (state, action) => {
        state.actionLoading = false;
        const amcId = action.payload;
        state.amcs = state.amcs.filter(amc => amc.id !== amcId);
        state.selectedAMCs = state.selectedAMCs.filter(id => id !== amcId);
        if (state.currentAMC?.id === amcId) {
          state.currentAMC = null;
        }
      })
      .addCase(deleteAMC.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Renew AMC
    builder
      .addCase(renewAMC.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(renewAMC.fulfilled, (state, action) => {
        state.actionLoading = false;
        const renewedAMC = action.payload.data.amc;
        const index = state.amcs.findIndex(amc => amc.id === renewedAMC.id);
        if (index !== -1) {
          state.amcs[index] = renewedAMC;
        }
        if (state.currentAMC?.id === renewedAMC.id) {
          state.currentAMC = renewedAMC;
        }
      })
      .addCase(renewAMC.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Fetch expiring AMCs
    builder
      .addCase(fetchExpiringAMCs.pending, (state) => {
        state.expiringLoading = true;
        state.expiringError = null;
      })
      .addCase(fetchExpiringAMCs.fulfilled, (state, action) => {
        state.expiringLoading = false;
        state.expiringAMCs = action.payload.data.amcs || action.payload.data.expiring_amcs;
      })
      .addCase(fetchExpiringAMCs.rejected, (state, action) => {
        state.expiringLoading = false;
        state.expiringError = action.payload;
      });
    
    // Fetch stats
    builder
      .addCase(fetchAMCStats.pending, (state) => {
        state.statsLoading = true;
        state.statsError = null;
      })
      .addCase(fetchAMCStats.fulfilled, (state, action) => {
        state.statsLoading = false;
        state.stats = action.payload.data;
      })
      .addCase(fetchAMCStats.rejected, (state, action) => {
        state.statsLoading = false;
        state.statsError = action.payload;
      });
  },
});

export const {
  selectAMC,
  deselectAMC,
  selectAllAMCs,
  deselectAllAMCs,
  setFilter,
  setFilters,
  clearFilters,
  setSortBy,
  setSortOrder,
  toggleSortOrder,
  setSearchQuery,
  clearSearchQuery,
  clearCurrentAMC,
  clearError,
  clearAMCError,
  clearExpiringError,
  clearStatsError,
  clearActionError,
  resetAMCState,
} = amcSlice.actions;

export default amcSlice.reducer;

// Selectors
export const selectAMCs = (state) => state.amc.amcs;
export const selectCurrentAMC = (state) => state.amc.currentAMC;
export const selectExpiringAMCs = (state) => state.amc.expiringAMCs;
export const selectAMCStats = (state) => state.amc.stats;
export const selectAMCPagination = (state) => state.amc.pagination;
export const selectAMCLoading = (state) => state.amc.loading;
export const selectAMCError = (state) => state.amc.error;
export const selectSelectedAMCs = (state) => state.amc.selectedAMCs;
export const selectAMCFilters = (state) => state.amc.filters;
export const selectAMCSort = (state) => ({
  sortBy: state.amc.sortBy,
  sortOrder: state.amc.sortOrder,
});
export const selectAMCSearchQuery = (state) => state.amc.searchQuery;
