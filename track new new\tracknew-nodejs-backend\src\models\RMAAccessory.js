const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const RMAAccessory = sequelize.define('RMAAccessory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  rma_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'rmas',
      key: 'id'
    }
  },
  accessory_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  accessory_type: {
    type: DataTypes.ENUM('cable', 'adapter', 'battery', 'charger', 'case', 'manual', 'cd_dvd', 'remote', 'stand', 'other'),
    defaultValue: 'other'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  brand: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  model: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  serial_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  part_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1
  },
  condition_received: {
    type: DataTypes.ENUM('excellent', 'good', 'fair', 'poor', 'damaged', 'missing'),
    allowNull: false
  },
  condition_returned: {
    type: DataTypes.ENUM('excellent', 'good', 'fair', 'poor', 'damaged', 'not_returned'),
    allowNull: true
  },
  is_original: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether this is an original accessory'
  },
  is_compatible: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether accessory is compatible with main product'
  },
  is_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this accessory is required for operation'
  },
  is_included_in_return: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether this accessory should be returned'
  },
  estimated_value: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    comment: 'Estimated value of the accessory'
  },
  replacement_cost: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    comment: 'Cost to replace if missing/damaged'
  },
  received_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Date when accessory was received'
  },
  returned_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Date when accessory was returned'
  },
  inspection_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Notes from inspection'
  },
  damage_description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Description of any damage'
  },
  repair_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  repair_cost: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true
  },
  repair_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  replacement_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  replacement_part_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  replacement_ordered: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  replacement_order_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  replacement_received_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  warranty_status: {
    type: DataTypes.ENUM('in_warranty', 'out_of_warranty', 'extended_warranty', 'no_warranty'),
    allowNull: true
  },
  warranty_expiry: {
    type: DataTypes.DATE,
    allowNull: true
  },
  supplier_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'suppliers',
      key: 'id'
    },
    comment: 'Supplier for replacement parts'
  },
  location: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Storage location of the accessory'
  },
  barcode: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  qr_code: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  photos: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of photo file paths'
  },
  documents: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of related documents'
  },
  tags: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Comma-separated tags'
  },
  custom_fields: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object for custom field values'
  },
  status: {
    type: DataTypes.ENUM('received', 'inspected', 'tested', 'repaired', 'ready_to_return', 'returned', 'disposed'),
    defaultValue: 'received'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'rma_accessories',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['rma_id']
    },
    {
      fields: ['accessory_type']
    },
    {
      fields: ['condition_received']
    },
    {
      fields: ['condition_returned']
    },
    {
      fields: ['status']
    },
    {
      fields: ['serial_number']
    },
    {
      fields: ['part_number']
    },
    {
      fields: ['barcode']
    },
    {
      fields: ['supplier_id']
    },
    {
      fields: ['is_required']
    },
    {
      fields: ['is_included_in_return']
    },
    {
      fields: ['warranty_status']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = RMAAccessory;
