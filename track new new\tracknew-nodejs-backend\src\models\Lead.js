const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Lead = sequelize.define('Lead', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  lead_type_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'lead_types',
      key: 'id'
    }
  },
  leadstatus_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'lead_statuses',
      key: 'id'
    }
  },
  assigned_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  lead_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  source: {
    type: DataTypes.ENUM('website', 'phone', 'email', 'referral', 'social_media', 'advertisement', 'walk_in', 'other'),
    defaultValue: 'other'
  },
  source_details: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Additional details about the lead source'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  temperature: {
    type: DataTypes.ENUM('cold', 'warm', 'hot'),
    defaultValue: 'warm'
  },
  estimated_value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Estimated deal value'
  },
  probability: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100
    },
    comment: 'Probability of conversion (0-100%)'
  },
  expected_close_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  actual_close_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  contact_person: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  contact_email: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  contact_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  requirements: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Customer requirements and specifications'
  },
  budget_range: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  decision_maker: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  competition: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Competitor information'
  },
  next_follow_up: {
    type: DataTypes.DATE,
    allowNull: true
  },
  follow_up_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  tags: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Comma-separated tags'
  },
  is_converted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  converted_to: {
    type: DataTypes.ENUM('customer', 'service', 'sale', 'estimation'),
    allowNull: true
  },
  converted_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'ID of the converted record'
  },
  conversion_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  lost_reason: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Reason if lead is lost'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  reminder_enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  last_activity_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'leads',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['lead_type_id']
    },
    {
      fields: ['leadstatus_id']
    },
    {
      fields: ['assigned_to']
    },
    {
      fields: ['lead_number'],
      unique: true
    },
    {
      fields: ['source']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['temperature']
    },
    {
      fields: ['is_converted']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['next_follow_up']
    },
    {
      fields: ['expected_close_date']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = Lead;
