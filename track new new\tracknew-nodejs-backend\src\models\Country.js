const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Country = sequelize.define('Country', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  },
  official_name: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Official country name'
  },
  iso_code_2: {
    type: DataTypes.STRING(2),
    allowNull: false,
    unique: true,
    comment: 'ISO 3166-1 alpha-2 code'
  },
  iso_code_3: {
    type: DataTypes.STRING(3),
    allowNull: false,
    unique: true,
    comment: 'ISO 3166-1 alpha-3 code'
  },
  numeric_code: {
    type: DataTypes.STRING(3),
    allowNull: true,
    comment: 'ISO 3166-1 numeric code'
  },
  phone_code: {
    type: DataTypes.STRING(10),
    allowNull: true,
    comment: 'International dialing code'
  },
  capital: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  currency_code: {
    type: DataTypes.STRING(3),
    allowNull: true,
    comment: 'ISO 4217 currency code'
  },
  currency_name: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  currency_symbol: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  region: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Geographic region'
  },
  subregion: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Geographic subregion'
  },
  continent: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 8),
    allowNull: true
  },
  longitude: {
    type: DataTypes.DECIMAL(11, 8),
    allowNull: true
  },
  area: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Area in square kilometers'
  },
  population: {
    type: DataTypes.BIGINT,
    allowNull: true
  },
  languages: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of official languages'
  },
  timezones: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of timezones'
  },
  borders: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of bordering country codes'
  },
  flag_emoji: {
    type: DataTypes.STRING(10),
    allowNull: true,
    comment: 'Unicode flag emoji'
  },
  flag_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'URL to flag image'
  },
  coat_of_arms_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'URL to coat of arms image'
  },
  tld: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of top-level domains'
  },
  postal_code_format: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Postal code format pattern'
  },
  postal_code_regex: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Postal code validation regex'
  },
  phone_format: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Phone number format pattern'
  },
  phone_regex: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Phone number validation regex'
  },
  date_format: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Common date format'
  },
  time_format: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Common time format'
  },
  first_day_of_week: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'First day of week (0=Sunday, 1=Monday)'
  },
  weekend_days: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of weekend days'
  },
  driving_side: {
    type: DataTypes.ENUM('left', 'right'),
    allowNull: true,
    comment: 'Which side of the road to drive on'
  },
  measurement_system: {
    type: DataTypes.ENUM('metric', 'imperial', 'mixed'),
    allowNull: true
  },
  is_eu_member: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether country is EU member'
  },
  is_landlocked: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether country is landlocked'
  },
  is_island: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether country is an island nation'
  },
  independence_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  national_day: {
    type: DataTypes.STRING(10),
    allowNull: true,
    comment: 'National day (MM-DD format)'
  },
  government_type: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  head_of_state: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  head_of_government: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  gdp: {
    type: DataTypes.BIGINT,
    allowNull: true,
    comment: 'GDP in USD'
  },
  gdp_per_capita: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'GDP per capita in USD'
  },
  gini_coefficient: {
    type: DataTypes.DECIMAL(4, 2),
    allowNull: true,
    comment: 'Gini coefficient (inequality measure)'
  },
  human_development_index: {
    type: DataTypes.DECIMAL(4, 3),
    allowNull: true,
    comment: 'Human Development Index'
  },
  life_expectancy: {
    type: DataTypes.DECIMAL(4, 1),
    allowNull: true,
    comment: 'Life expectancy in years'
  },
  literacy_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Literacy rate percentage'
  },
  internet_penetration: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Internet penetration percentage'
  },
  mobile_penetration: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Mobile phone penetration percentage'
  },
  tax_system: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object describing tax system'
  },
  business_regulations: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object describing business regulations'
  },
  trade_agreements: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of trade agreements'
  },
  visa_requirements: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of visa requirements by country'
  },
  emergency_numbers: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of emergency contact numbers'
  },
  cultural_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Cultural considerations for business'
  },
  business_hours: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of typical business hours'
  },
  public_holidays: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of public holidays'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_supported: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether this country is supported by the system'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'countries',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['name'],
      unique: true
    },
    {
      fields: ['iso_code_2'],
      unique: true
    },
    {
      fields: ['iso_code_3'],
      unique: true
    },
    {
      fields: ['phone_code']
    },
    {
      fields: ['currency_code']
    },
    {
      fields: ['region']
    },
    {
      fields: ['subregion']
    },
    {
      fields: ['continent']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_supported']
    },
    {
      fields: ['sort_order']
    }
  ]
});

module.exports = Country;
