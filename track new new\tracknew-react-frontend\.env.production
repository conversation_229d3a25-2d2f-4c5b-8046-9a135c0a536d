# Production Environment Configuration
NODE_ENV=production
REACT_APP_ENV=production

# API Configuration (Production)
REACT_APP_API_URL=https://api.tracknew.com/api
REACT_APP_API_TIMEOUT=30000

# Application Configuration
REACT_APP_NAME=TrackNew
REACT_APP_VERSION=1.0.0
REACT_APP_DESCRIPTION=Professional Service Management System

# Authentication Configuration
REACT_APP_JWT_SECRET=production-jwt-secret-key-2024
REACT_APP_TOKEN_EXPIRY=24h
REACT_APP_REFRESH_TOKEN_EXPIRY=7d

# Features (Production)
REACT_APP_ENABLE_REGISTRATION=true
REACT_APP_ENABLE_FORGOT_PASSWORD=true
REACT_APP_ENABLE_EMAIL_VERIFICATION=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_DARK_MODE=true

# File Upload (Production)
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Pagination
REACT_APP_DEFAULT_PAGE_SIZE=15
REACT_APP_MAX_PAGE_SIZE=100

# Cache Configuration
REACT_APP_CACHE_TIMEOUT=300000

# Analytics (Production)
REACT_APP_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
REACT_APP_SENTRY_DSN=https://<EMAIL>/project-id

# Social Login (Production)
REACT_APP_GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
REACT_APP_FACEBOOK_APP_ID=your-facebook-app-id

# Map Integration
REACT_APP_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Company Information
REACT_APP_COMPANY_NAME=TrackNew Solutions
REACT_APP_COMPANY_ADDRESS=123 Business Street, City, State 12345
REACT_APP_COMPANY_PHONE=+****************
REACT_APP_COMPANY_EMAIL=<EMAIL>
REACT_APP_COMPANY_WEBSITE=https://tracknew.com

# Support
REACT_APP_SUPPORT_EMAIL=<EMAIL>
REACT_APP_SUPPORT_PHONE=+****************
REACT_APP_DOCUMENTATION_URL=https://docs.tracknew.com

# Legal
REACT_APP_TERMS_URL=https://tracknew.com/terms
REACT_APP_PRIVACY_URL=https://tracknew.com/privacy

# CDN Configuration
REACT_APP_CDN_URL=https://cdn.tracknew.com
REACT_APP_STATIC_URL=https://static.tracknew.com

# Performance
REACT_APP_ENABLE_SERVICE_WORKER=true
REACT_APP_ENABLE_PWA=true
REACT_APP_CACHE_STRATEGY=cache-first

# Security
REACT_APP_ENABLE_CSP=true
REACT_APP_ENABLE_HTTPS_ONLY=true

# Monitoring
REACT_APP_ENABLE_ERROR_REPORTING=true
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true

# Build Configuration
GENERATE_SOURCEMAP=false
INLINE_RUNTIME_CHUNK=false
