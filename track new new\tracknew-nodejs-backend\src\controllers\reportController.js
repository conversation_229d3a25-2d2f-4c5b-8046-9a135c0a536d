const {
  Sales,
  Service,
  Customer,
  Product,
  Invoice,
  Payment,
  Expense,
  User,
  Company,
  StockMovement,
  Warehouse
} = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get sales reports
const getSalesReport = catchAsync(async (req, res) => {
  const {
    start_date,
    end_date,
    group_by = 'month',
    customer_id,
    product_id,
    sales_person_id,
    status
  } = req.query;

  const companyId = req.user.company_id;

  // Build date filter
  const dateFilter = {};
  if (start_date || end_date) {
    dateFilter.sales_date = {};
    if (start_date) {
      dateFilter.sales_date[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      dateFilter.sales_date[Op.lte] = new Date(end_date);
    }
  }

  // Build where conditions
  const whereConditions = {
    company_id: companyId,
    ...dateFilter
  };

  if (customer_id) whereConditions.customer_id = customer_id;
  if (product_id) whereConditions.product_id = product_id;
  if (sales_person_id) whereConditions.sales_person_id = sales_person_id;
  if (status) whereConditions.status = status;

  // Determine date grouping format
  let dateFormat;
  switch (group_by) {
    case 'day':
      dateFormat = '%Y-%m-%d';
      break;
    case 'week':
      dateFormat = '%Y-%u';
      break;
    case 'month':
      dateFormat = '%Y-%m';
      break;
    case 'quarter':
      dateFormat = '%Y-Q%q';
      break;
    case 'year':
      dateFormat = '%Y';
      break;
    default:
      dateFormat = '%Y-%m';
  }

  // Sales trends
  const salesTrends = await Sales.findAll({
    where: whereConditions,
    attributes: [
      [Sales.sequelize.fn('DATE_FORMAT', Sales.sequelize.col('sales_date'), dateFormat), 'period'],
      [Sales.sequelize.fn('COUNT', Sales.sequelize.col('id')), 'count'],
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'total_amount'],
      [Sales.sequelize.fn('AVG', Sales.sequelize.col('total_amount')), 'avg_amount']
    ],
    group: [Sales.sequelize.fn('DATE_FORMAT', Sales.sequelize.col('sales_date'), dateFormat)],
    order: [[Sales.sequelize.fn('DATE_FORMAT', Sales.sequelize.col('sales_date'), dateFormat), 'ASC']],
    raw: true
  });

  // Top customers
  const topCustomers = await Sales.findAll({
    where: whereConditions,
    include: [{
      model: Customer,
      as: 'customer',
      attributes: ['id', 'customer_name']
    }],
    attributes: [
      'customer_id',
      [Sales.sequelize.fn('COUNT', Sales.sequelize.col('Sales.id')), 'sales_count'],
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'total_amount']
    ],
    group: ['customer_id', 'customer.id'],
    order: [[Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'DESC']],
    limit: 10,
    raw: false
  });

  // Top products
  const topProducts = await Sales.findAll({
    where: whereConditions,
    include: [{
      model: Product,
      as: 'product',
      attributes: ['id', 'product_name', 'product_code']
    }],
    attributes: [
      'product_id',
      [Sales.sequelize.fn('COUNT', Sales.sequelize.col('Sales.id')), 'sales_count'],
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'total_amount']
    ],
    group: ['product_id', 'product.id'],
    order: [[Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'DESC']],
    limit: 10,
    raw: false
  });

  // Sales summary
  const salesSummary = await Sales.findOne({
    where: whereConditions,
    attributes: [
      [Sales.sequelize.fn('COUNT', Sales.sequelize.col('id')), 'total_sales'],
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'total_revenue'],
      [Sales.sequelize.fn('AVG', Sales.sequelize.col('total_amount')), 'avg_sale_value'],
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('paid_amount')), 'total_paid'],
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('balance_amount')), 'total_outstanding']
    ],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      sales_trends: salesTrends,
      top_customers: topCustomers,
      top_products: topProducts,
      summary: {
        total_sales: parseInt(salesSummary.total_sales || 0),
        total_revenue: parseFloat(salesSummary.total_revenue || 0),
        avg_sale_value: parseFloat(salesSummary.avg_sale_value || 0),
        total_paid: parseFloat(salesSummary.total_paid || 0),
        total_outstanding: parseFloat(salesSummary.total_outstanding || 0)
      }
    }
  });
});

// Get service reports
const getServiceReport = catchAsync(async (req, res) => {
  const {
    start_date,
    end_date,
    group_by = 'month',
    customer_id,
    service_category_id,
    technician_id,
    service_status
  } = req.query;

  const companyId = req.user.company_id;

  // Build date filter
  const dateFilter = {};
  if (start_date || end_date) {
    dateFilter.service_date = {};
    if (start_date) {
      dateFilter.service_date[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      dateFilter.service_date[Op.lte] = new Date(end_date);
    }
  }

  // Build where conditions
  const whereConditions = {
    company_id: companyId,
    ...dateFilter
  };

  if (customer_id) whereConditions.customer_id = customer_id;
  if (service_category_id) whereConditions.service_category_id = service_category_id;
  if (technician_id) whereConditions.technician_id = technician_id;
  if (service_status) whereConditions.service_status = service_status;

  // Service trends
  const serviceTrends = await Service.findAll({
    where: whereConditions,
    attributes: [
      [Service.sequelize.fn('DATE_FORMAT', Service.sequelize.col('service_date'), '%Y-%m'), 'period'],
      [Service.sequelize.fn('COUNT', Service.sequelize.col('id')), 'count'],
      [Service.sequelize.fn('SUM', Service.sequelize.col('total_amount')), 'total_amount']
    ],
    group: [Service.sequelize.fn('DATE_FORMAT', Service.sequelize.col('service_date'), '%Y-%m')],
    order: [[Service.sequelize.fn('DATE_FORMAT', Service.sequelize.col('service_date'), '%Y-%m'), 'ASC']],
    raw: true
  });

  // Service status breakdown
  const serviceStatusBreakdown = await Service.findAll({
    where: whereConditions,
    attributes: [
      'service_status',
      [Service.sequelize.fn('COUNT', Service.sequelize.col('id')), 'count']
    ],
    group: ['service_status'],
    raw: true
  });

  // Top technicians
  const topTechnicians = await Service.findAll({
    where: whereConditions,
    include: [{
      model: User,
      as: 'technician',
      attributes: ['id', 'name']
    }],
    attributes: [
      'technician_id',
      [Service.sequelize.fn('COUNT', Service.sequelize.col('Service.id')), 'service_count'],
      [Service.sequelize.fn('SUM', Service.sequelize.col('total_amount')), 'total_amount']
    ],
    group: ['technician_id', 'technician.id'],
    order: [[Service.sequelize.fn('COUNT', Service.sequelize.col('Service.id')), 'DESC']],
    limit: 10,
    raw: false
  });

  // Service summary
  const serviceSummary = await Service.findOne({
    where: whereConditions,
    attributes: [
      [Service.sequelize.fn('COUNT', Service.sequelize.col('id')), 'total_services'],
      [Service.sequelize.fn('SUM', Service.sequelize.col('total_amount')), 'total_revenue'],
      [Service.sequelize.fn('AVG', Service.sequelize.col('total_amount')), 'avg_service_value']
    ],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      service_trends: serviceTrends,
      status_breakdown: serviceStatusBreakdown,
      top_technicians: topTechnicians,
      summary: {
        total_services: parseInt(serviceSummary.total_services || 0),
        total_revenue: parseFloat(serviceSummary.total_revenue || 0),
        avg_service_value: parseFloat(serviceSummary.avg_service_value || 0)
      }
    }
  });
});

// Get financial reports
const getFinancialReport = catchAsync(async (req, res) => {
  const {
    start_date,
    end_date,
    group_by = 'month'
  } = req.query;

  const companyId = req.user.company_id;

  // Build date filter
  const dateFilter = {};
  if (start_date || end_date) {
    dateFilter.created_at = {};
    if (start_date) {
      dateFilter.created_at[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      dateFilter.created_at[Op.lte] = new Date(end_date);
    }
  }

  // Revenue from sales
  const salesRevenue = await Sales.findOne({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'total_revenue'],
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('paid_amount')), 'total_paid']
    ],
    raw: true
  });

  // Revenue from services
  const serviceRevenue = await Service.findOne({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      [Service.sequelize.fn('SUM', Service.sequelize.col('total_amount')), 'total_revenue']
    ],
    raw: true
  });

  // Total expenses
  const totalExpenses = await Expense.findOne({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      [Expense.sequelize.fn('SUM', Expense.sequelize.col('amount')), 'total_expenses']
    ],
    raw: true
  });

  // Outstanding invoices
  const outstandingInvoices = await Invoice.findOne({
    where: {
      company_id: companyId,
      payment_status: { [Op.in]: ['unpaid', 'partially_paid'] }
    },
    attributes: [
      [Invoice.sequelize.fn('COUNT', Invoice.sequelize.col('id')), 'count'],
      [Invoice.sequelize.fn('SUM', Invoice.sequelize.col('balance_amount')), 'total_outstanding']
    ],
    raw: true
  });

  const totalSalesRevenue = parseFloat(salesRevenue?.total_revenue || 0);
  const totalServiceRevenue = parseFloat(serviceRevenue?.total_revenue || 0);
  const totalExpensesAmount = parseFloat(totalExpenses?.total_expenses || 0);
  const totalRevenue = totalSalesRevenue + totalServiceRevenue;
  const netProfit = totalRevenue - totalExpensesAmount;

  res.status(200).json({
    status: 'success',
    data: {
      revenue: {
        sales_revenue: totalSalesRevenue,
        service_revenue: totalServiceRevenue,
        total_revenue: totalRevenue
      },
      expenses: {
        total_expenses: totalExpensesAmount
      },
      profit: {
        gross_profit: totalRevenue,
        net_profit: netProfit,
        profit_margin: totalRevenue > 0 ? ((netProfit / totalRevenue) * 100).toFixed(2) : 0
      },
      outstanding: {
        invoice_count: parseInt(outstandingInvoices?.count || 0),
        total_amount: parseFloat(outstandingInvoices?.total_outstanding || 0)
      }
    }
  });
});

// Get inventory reports
const getInventoryReport = catchAsync(async (req, res) => {
  const {
    warehouse_id,
    category_id,
    low_stock_threshold = 10
  } = req.query;

  const companyId = req.user.company_id;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  if (warehouse_id) whereConditions.warehouse_id = warehouse_id;
  if (category_id) whereConditions.category_id = category_id;

  // Current stock levels
  const stockLevels = await Product.findAll({
    where: whereConditions,
    attributes: [
      'id',
      'product_name',
      'product_code',
      'current_stock',
      'minimum_stock',
      'maximum_stock',
      'unit_price'
    ],
    order: [['current_stock', 'ASC']]
  });

  // Low stock items
  const lowStockItems = stockLevels.filter(item =>
    item.current_stock <= Math.max(item.minimum_stock || 0, low_stock_threshold)
  );

  // Out of stock items
  const outOfStockItems = stockLevels.filter(item => item.current_stock <= 0);

  // Stock value
  const totalStockValue = stockLevels.reduce((total, item) =>
    total + (item.current_stock * item.unit_price), 0
  );

  // Recent stock movements
  const recentMovements = await StockMovement.findAll({
    where: {
      company_id: companyId,
      created_at: {
        [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
      }
    },
    include: [
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'product_name', 'product_code']
      },
      {
        model: Warehouse,
        as: 'warehouse',
        attributes: ['id', 'warehouse_name']
      }
    ],
    order: [['created_at', 'DESC']],
    limit: 50
  });

  res.status(200).json({
    status: 'success',
    data: {
      stock_summary: {
        total_products: stockLevels.length,
        low_stock_count: lowStockItems.length,
        out_of_stock_count: outOfStockItems.length,
        total_stock_value: totalStockValue
      },
      low_stock_items: lowStockItems,
      out_of_stock_items: outOfStockItems,
      recent_movements: recentMovements
    }
  });
});

// Get customer reports
const getCustomerReport = catchAsync(async (req, res) => {
  const {
    start_date,
    end_date,
    customer_category_id
  } = req.query;

  const companyId = req.user.company_id;

  // Build where conditions for customers
  const customerWhereConditions = {
    company_id: companyId
  };

  if (customer_category_id) {
    customerWhereConditions.customer_category_id = customer_category_id;
  }

  // Build date filter for sales
  const salesDateFilter = {};
  if (start_date || end_date) {
    salesDateFilter.sales_date = {};
    if (start_date) {
      salesDateFilter.sales_date[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      salesDateFilter.sales_date[Op.lte] = new Date(end_date);
    }
  }

  // Customer acquisition trends
  const customerAcquisition = await Customer.findAll({
    where: {
      ...customerWhereConditions,
      created_at: {
        [Op.gte]: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) // Last year
      }
    },
    attributes: [
      [Customer.sequelize.fn('DATE_FORMAT', Customer.sequelize.col('created_at'), '%Y-%m'), 'period'],
      [Customer.sequelize.fn('COUNT', Customer.sequelize.col('id')), 'new_customers']
    ],
    group: [Customer.sequelize.fn('DATE_FORMAT', Customer.sequelize.col('created_at'), '%Y-%m')],
    order: [[Customer.sequelize.fn('DATE_FORMAT', Customer.sequelize.col('created_at'), '%Y-%m'), 'ASC']],
    raw: true
  });

  // Top customers by revenue
  const topCustomersByRevenue = await Customer.findAll({
    where: customerWhereConditions,
    include: [{
      model: Sales,
      as: 'sales',
      where: {
        company_id: companyId,
        ...salesDateFilter
      },
      attributes: []
    }],
    attributes: [
      'id',
      'customer_name',
      'email',
      'mobile_number',
      [Customer.sequelize.fn('COUNT', Customer.sequelize.col('sales.id')), 'total_orders'],
      [Customer.sequelize.fn('SUM', Customer.sequelize.col('sales.total_amount')), 'total_revenue']
    ],
    group: ['Customer.id'],
    order: [[Customer.sequelize.fn('SUM', Customer.sequelize.col('sales.total_amount')), 'DESC']],
    limit: 10,
    raw: false
  });

  // Customer summary
  const customerSummary = await Customer.findOne({
    where: customerWhereConditions,
    attributes: [
      [Customer.sequelize.fn('COUNT', Customer.sequelize.col('id')), 'total_customers']
    ],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      customer_acquisition: customerAcquisition,
      top_customers: topCustomersByRevenue,
      summary: {
        total_customers: parseInt(customerSummary.total_customers || 0)
      }
    }
  });
});

// Get dashboard summary
const getDashboardSummary = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const today = new Date();
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const startOfYear = new Date(today.getFullYear(), 0, 1);

  // Sales summary
  const salesSummary = await Sales.findOne({
    where: {
      company_id: companyId,
      sales_date: { [Op.gte]: startOfMonth }
    },
    attributes: [
      [Sales.sequelize.fn('COUNT', Sales.sequelize.col('id')), 'monthly_sales'],
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'monthly_revenue']
    ],
    raw: true
  });

  // Service summary
  const serviceSummary = await Service.findOne({
    where: {
      company_id: companyId,
      service_date: { [Op.gte]: startOfMonth }
    },
    attributes: [
      [Service.sequelize.fn('COUNT', Service.sequelize.col('id')), 'monthly_services']
    ],
    raw: true
  });

  // Customer summary
  const customerSummary = await Customer.findOne({
    where: {
      company_id: companyId
    },
    attributes: [
      [Customer.sequelize.fn('COUNT', Customer.sequelize.col('id')), 'total_customers']
    ],
    raw: true
  });

  // Outstanding invoices
  const outstandingInvoices = await Invoice.findOne({
    where: {
      company_id: companyId,
      payment_status: { [Op.in]: ['unpaid', 'partially_paid'] }
    },
    attributes: [
      [Invoice.sequelize.fn('COUNT', Invoice.sequelize.col('id')), 'count'],
      [Invoice.sequelize.fn('SUM', Invoice.sequelize.col('balance_amount')), 'amount']
    ],
    raw: true
  });

  // Low stock products
  const lowStockCount = await Product.count({
    where: {
      company_id: companyId,
      current_stock: { [Op.lte]: Product.sequelize.col('minimum_stock') }
    }
  });

  res.status(200).json({
    status: 'success',
    data: {
      sales: {
        monthly_count: parseInt(salesSummary?.monthly_sales || 0),
        monthly_revenue: parseFloat(salesSummary?.monthly_revenue || 0)
      },
      services: {
        monthly_count: parseInt(serviceSummary?.monthly_services || 0)
      },
      customers: {
        total_count: parseInt(customerSummary?.total_customers || 0)
      },
      invoices: {
        outstanding_count: parseInt(outstandingInvoices?.count || 0),
        outstanding_amount: parseFloat(outstandingInvoices?.amount || 0)
      },
      inventory: {
        low_stock_count: lowStockCount
      }
    }
  });
});

// Get expense reports
const getExpenseReport = catchAsync(async (req, res) => {
  const {
    start_date,
    end_date,
    expense_category_id,
    employee_id,
    department
  } = req.query;

  const companyId = req.user.company_id;

  // Build date filter
  const dateFilter = {};
  if (start_date || end_date) {
    dateFilter.expense_date = {};
    if (start_date) {
      dateFilter.expense_date[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      dateFilter.expense_date[Op.lte] = new Date(end_date);
    }
  }

  // Build where conditions
  const whereConditions = {
    company_id: companyId,
    ...dateFilter
  };

  if (expense_category_id) whereConditions.expense_category_id = expense_category_id;
  if (employee_id) whereConditions.employee_id = employee_id;
  if (department) whereConditions.department = department;

  // Expense trends
  const expenseTrends = await Expense.findAll({
    where: whereConditions,
    attributes: [
      [Expense.sequelize.fn('DATE_FORMAT', Expense.sequelize.col('expense_date'), '%Y-%m'), 'period'],
      [Expense.sequelize.fn('COUNT', Expense.sequelize.col('id')), 'count'],
      [Expense.sequelize.fn('SUM', Expense.sequelize.col('amount')), 'total_amount']
    ],
    group: [Expense.sequelize.fn('DATE_FORMAT', Expense.sequelize.col('expense_date'), '%Y-%m')],
    order: [[Expense.sequelize.fn('DATE_FORMAT', Expense.sequelize.col('expense_date'), '%Y-%m'), 'ASC']],
    raw: true
  });

  // Expense summary
  const expenseSummary = await Expense.findOne({
    where: whereConditions,
    attributes: [
      [Expense.sequelize.fn('COUNT', Expense.sequelize.col('id')), 'total_expenses'],
      [Expense.sequelize.fn('SUM', Expense.sequelize.col('amount')), 'total_amount'],
      [Expense.sequelize.fn('AVG', Expense.sequelize.col('amount')), 'avg_amount']
    ],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      expense_trends: expenseTrends,
      summary: {
        total_expenses: parseInt(expenseSummary?.total_expenses || 0),
        total_amount: parseFloat(expenseSummary?.total_amount || 0),
        avg_amount: parseFloat(expenseSummary?.avg_amount || 0)
      }
    }
  });
});

module.exports = {
  getSalesReport,
  getServiceReport,
  getFinancialReport,
  getInventoryReport,
  getCustomerReport,
  getDashboardSummary,
  getExpenseReport
};
