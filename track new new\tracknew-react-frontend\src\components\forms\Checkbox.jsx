import React, { forwardRef } from 'react';
import { CheckIcon, MinusIcon } from '@heroicons/react/24/outline';
import { classNames } from '../../utils/helpers';

const Checkbox = forwardRef(({
  label,
  description,
  checked = false,
  indeterminate = false,
  onChange,
  onBlur,
  onFocus,
  error,
  required = false,
  disabled = false,
  size = 'md',
  color = 'blue',
  labelPosition = 'right',
  className = '',
  checkboxClassName = '',
  labelClassName = '',
  descriptionClassName = '',
  errorClassName = '',
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = React.useState(false);
  const checkboxRef = React.useRef(null);
  
  const inputId = React.useId();
  const errorId = React.useId();

  // Handle indeterminate state
  React.useEffect(() => {
    if (checkboxRef.current) {
      checkboxRef.current.indeterminate = indeterminate;
    }
  }, [indeterminate]);

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
  };

  const colorClasses = {
    blue: 'text-blue-600 focus:ring-blue-500',
    green: 'text-green-600 focus:ring-green-500',
    red: 'text-red-600 focus:ring-red-500',
    yellow: 'text-yellow-600 focus:ring-yellow-500',
    purple: 'text-purple-600 focus:ring-purple-500',
    pink: 'text-pink-600 focus:ring-pink-500',
    indigo: 'text-indigo-600 focus:ring-indigo-500',
    gray: 'text-gray-600 focus:ring-gray-500',
  };

  const labelSizeClasses = {
    sm: 'text-sm',
    md: 'text-sm',
    lg: 'text-base',
  };

  const descriptionSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-sm',
  };

  const baseCheckboxClasses = classNames(
    'rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700',
    'focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800',
    'transition-colors duration-200',
    sizeClasses[size],
    colorClasses[color],
    error && 'border-red-300 text-red-600 focus:ring-red-500 dark:border-red-500',
    disabled && 'opacity-50 cursor-not-allowed',
    checkboxClassName
  );

  const handleFocus = (e) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const handleChange = (e) => {
    onChange?.(e.target.checked, e);
  };

  const renderCheckbox = () => (
    <div className="relative flex items-center">
      <input
        ref={(node) => {
          checkboxRef.current = node;
          if (typeof ref === 'function') {
            ref(node);
          } else if (ref) {
            ref.current = node;
          }
        }}
        id={inputId}
        type="checkbox"
        checked={checked}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        required={required}
        disabled={disabled}
        className={baseCheckboxClasses}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={error ? errorId : undefined}
        {...props}
      />
      
      {/* Custom checkbox overlay for better styling */}
      <div className={classNames(
        'absolute inset-0 flex items-center justify-center pointer-events-none',
        sizeClasses[size]
      )}>
        {indeterminate ? (
          <MinusIcon className={classNames(
            'text-white',
            size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : 'h-5 w-5'
          )} />
        ) : checked ? (
          <CheckIcon className={classNames(
            'text-white',
            size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : 'h-5 w-5'
          )} />
        ) : null}
      </div>
    </div>
  );

  const renderLabel = () => (
    <div className="flex-1">
      {label && (
        <label 
          htmlFor={inputId}
          className={classNames(
            'font-medium text-gray-900 dark:text-white cursor-pointer',
            labelSizeClasses[size],
            required && "after:content-['*'] after:ml-0.5 after:text-red-500",
            disabled && 'opacity-50 cursor-not-allowed',
            labelClassName
          )}
        >
          {label}
        </label>
      )}
      
      {description && (
        <p className={classNames(
          'text-gray-500 dark:text-gray-400',
          descriptionSizeClasses[size],
          disabled && 'opacity-50',
          descriptionClassName
        )}>
          {description}
        </p>
      )}
    </div>
  );

  return (
    <div className={classNames('w-full', className)}>
      <div className={classNames(
        'flex items-start',
        labelPosition === 'left' ? 'flex-row-reverse' : 'flex-row',
        labelPosition === 'left' ? 'justify-end' : 'justify-start'
      )}>
        {renderCheckbox()}
        
        {(label || description) && (
          <div className={classNames(
            'flex-1',
            labelPosition === 'left' ? 'mr-3' : 'ml-3'
          )}>
            {renderLabel()}
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <p 
          id={errorId}
          className={classNames(
            'mt-1 text-sm text-red-600 dark:text-red-400',
            errorClassName
          )}
        >
          {error}
        </p>
      )}
    </div>
  );
});

Checkbox.displayName = 'Checkbox';

export default Checkbox;
