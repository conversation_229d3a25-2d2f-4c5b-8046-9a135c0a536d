import React, { useState } from 'react';
import { 
  UserIcon, 
  LockClosedIcon, 
  ChartBarIcon,
  UsersIcon,
  WrenchScrewdriverIcon,
  CubeIcon,
  CurrencyDollarIcon,
  DocumentChartBarIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  EyeIcon,
  HomeIcon
} from '@heroicons/react/24/outline';

// Modern Tailwind CSS 3.7 React Application
function TailwindApp() {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [user, setUser] = useState(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // Simple login function
  const handleLogin = (email, password) => {
    if (email && password) {
      setUser({ name: 'Demo User', email: email, avatar: '/api/placeholder/40/40' });
      setCurrentPage('dashboard');
    }
  };

  // Simple logout function
  const handleLogout = () => {
    setUser(null);
    setCurrentPage('login');
  };

  // Navigation items
  const navigationItems = [
    { id: 'dashboard', name: 'Dashboard', icon: HomeIcon, color: 'text-primary-600' },
    { id: 'customers', name: 'Customers', icon: UsersIcon, color: 'text-blue-600' },
    { id: 'services', name: 'Services', icon: WrenchScrewdriverIcon, color: 'text-green-600' },
    { id: 'products', name: 'Products', icon: CubeIcon, color: 'text-purple-600' },
    { id: 'sales', name: 'Sales', icon: CurrencyDollarIcon, color: 'text-yellow-600' },
    { id: 'reports', name: 'Reports', icon: DocumentChartBarIcon, color: 'text-red-600' },
    { id: 'settings', name: 'Settings', icon: Cog6ToothIcon, color: 'text-gray-600' }
  ];

  // Statistics data
  const statsData = [
    { 
      title: 'Total Customers', 
      value: '2,847', 
      change: '+12%', 
      changeType: 'increase',
      icon: UsersIcon,
      color: 'bg-gradient-to-r from-blue-500 to-blue-600'
    },
    { 
      title: 'Active Services', 
      value: '1,234', 
      change: '+8%', 
      changeType: 'increase',
      icon: WrenchScrewdriverIcon,
      color: 'bg-gradient-to-r from-green-500 to-green-600'
    },
    { 
      title: 'Monthly Revenue', 
      value: '$89,247', 
      change: '+23%', 
      changeType: 'increase',
      icon: CurrencyDollarIcon,
      color: 'bg-gradient-to-r from-purple-500 to-purple-600'
    },
    { 
      title: 'Pending Tasks', 
      value: '47', 
      change: '-5%', 
      changeType: 'decrease',
      icon: ChartBarIcon,
      color: 'bg-gradient-to-r from-orange-500 to-orange-600'
    }
  ];

  // Login Page Component
  const LoginPage = () => (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo and Branding */}
        <div className="text-center mb-8 animate-fade-in">
          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mb-4 shadow-soft">
            <ChartBarIcon className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Track New</h1>
          <p className="text-gray-600">Professional Service Management</p>
        </div>

        {/* Login Form */}
        <div className="bg-white rounded-2xl shadow-medium p-8 border border-gray-100 animate-bounce-in">
          <form onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            handleLogin(formData.get('email'), formData.get('password'));
          }} className="space-y-6">
            
            {/* Email Field */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Email Address
              </label>
              <div className="relative">
                <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="email"
                  name="email"
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  placeholder="<EMAIL>"
                  defaultValue="<EMAIL>"
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Password
              </label>
              <div className="relative">
                <LockClosedIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="password"
                  name="password"
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  placeholder="Enter your password"
                  defaultValue="password123"
                />
              </div>
            </div>

            {/* Login Button */}
            <button
              type="submit"
              className="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white font-semibold py-3 px-4 rounded-xl hover:from-primary-600 hover:to-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 shadow-soft hover:shadow-medium transform hover:-translate-y-0.5"
            >
              Sign In to Dashboard
            </button>
          </form>

          {/* Demo Info */}
          <div className="mt-6 p-4 bg-primary-50 rounded-xl border border-primary-100">
            <p className="text-sm text-primary-700 text-center">
              <span className="font-semibold">Demo Mode:</span> Credentials are pre-filled. Click "Sign In" to continue.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>© 2024 Track New. Professional Service Management System.</p>
        </div>
      </div>
    </div>
  );

  // Dashboard Page Component
  const DashboardPage = () => (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 shadow-soft sticky top-0 z-40">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Title */}
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                <ChartBarIcon className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-bold text-gray-900">Track New</h1>
            </div>

            {/* User Menu */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                  <UserIcon className="w-5 h-5 text-white" />
                </div>
                <div className="hidden sm:block">
                  <p className="text-sm font-semibold text-gray-900">{user?.name}</p>
                  <p className="text-xs text-gray-500">{user?.email}</p>
                </div>
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <ArrowRightOnRectangleIcon className="w-4 h-4" />
                <span className="hidden sm:inline">Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <nav className={`${sidebarOpen ? 'w-64' : 'w-16'} bg-white border-r border-gray-200 shadow-soft transition-all duration-300 min-h-screen`}>
          <div className="p-4">
            <div className="space-y-2">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentPage === item.id;
                
                return (
                  <button
                    key={item.id}
                    onClick={() => setCurrentPage(item.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? 'bg-primary-50 text-primary-700 border border-primary-200 shadow-soft'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className={`w-5 h-5 ${isActive ? 'text-primary-600' : 'text-gray-400'}`} />
                    {sidebarOpen && <span>{item.name}</span>}
                  </button>
                );
              })}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto">
            {/* Page Header */}
            <div className="mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                {currentPage.charAt(0).toUpperCase() + currentPage.slice(1)}
              </h2>
              <p className="text-gray-600">
                {currentPage === 'dashboard' 
                  ? 'Overview of your service management system' 
                  : `Manage your ${currentPage} efficiently`
                }
              </p>
            </div>
            
            {/* Dashboard Stats */}
            {currentPage === 'dashboard' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {statsData.map((stat, index) => {
                  const Icon = stat.icon;
                  return (
                    <div key={index} className="bg-white rounded-2xl p-6 shadow-soft border border-gray-100 hover:shadow-medium transition-all duration-300 animate-fade-in">
                      <div className="flex items-center justify-between mb-4">
                        <div className={`w-12 h-12 ${stat.color} rounded-xl flex items-center justify-center shadow-soft`}>
                          <Icon className="w-6 h-6 text-white" />
                        </div>
                        <span className={`text-sm font-semibold px-2 py-1 rounded-full ${
                          stat.changeType === 'increase' 
                            ? 'text-success-700 bg-success-100' 
                            : 'text-danger-700 bg-danger-100'
                        }`}>
                          {stat.change}
                        </span>
                      </div>
                      <h3 className="text-sm font-medium text-gray-600 mb-1">{stat.title}</h3>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                  );
                })}
              </div>
            )}
            
            {/* Content Area */}
            <div className="bg-white rounded-2xl shadow-soft border border-gray-100 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">
                  {currentPage === 'dashboard' ? 'Recent Activity' : `${currentPage.charAt(0).toUpperCase() + currentPage.slice(1)} Management`}
                </h3>
                <button className="flex items-center space-x-2 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors duration-200 shadow-soft">
                  <span>View All</span>
                  <EyeIcon className="w-4 h-4" />
                </button>
              </div>
              
              <div className="space-y-4">
                {[1, 2, 3, 4].map((item) => (
                  <div key={item} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-100 hover:bg-gray-100 transition-colors duration-200">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                        <span className="text-white font-semibold">{item}</span>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">
                          {currentPage === 'dashboard' ? `Recent Activity ${item}` : `${currentPage.slice(0, -1)} Item ${item}`}
                        </h4>
                        <p className="text-sm text-gray-600">Sample description for item {item}</p>
                      </div>
                    </div>
                    <button className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors duration-200 text-sm font-medium">
                      View Details
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );

  // Render the appropriate page
  if (!user) {
    return <LoginPage />;
  }

  return <DashboardPage />;
}

export default TailwindApp;
