import{r as e,a as s,g as r}from"./vendor-BRaCMJ4j.js";import{r as t,R as l}from"./router-knuRb_GW.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))s(e);new MutationObserver((e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&s(e)})).observe(document,{childList:!0,subtree:!0})}function s(e){if(e.ep)return;e.ep=!0;const s=function(e){const s={};return e.integrity&&(s.integrity=e.integrity),e.referrerPolicy&&(s.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?s.credentials="include":"anonymous"===e.crossOrigin?s.credentials="omit":s.credentials="same-origin",s}(e);fetch(e.href,s)}}();var a,n,o={exports:{}},i={};var c,d=(n||(n=1,o.exports=function(){if(a)return i;a=1;var s=e(),r=Symbol.for("react.element"),t=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,n=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};function c(e,s,t){var a,i={},c=null,d=null;for(a in void 0!==t&&(c=""+t),void 0!==s.key&&(c=""+s.key),void 0!==s.ref&&(d=s.ref),s)l.call(s,a)&&!o.hasOwnProperty(a)&&(i[a]=s[a]);if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===i[a]&&(i[a]=s[a]);return{$$typeof:r,type:e,key:c,ref:d,props:i,_owner:n.current}}return i.Fragment=t,i.jsx=c,i.jsxs=c,i}()),o.exports),m={};function x(){const[e,s]=t.useState("dashboard"),[r,l]=t.useState(null),a=()=>{l(null),s("login")},n=()=>d.jsx("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:d.jsxs("div",{className:"max-w-md w-full bg-white rounded-lg shadow-md p-8",children:[d.jsxs("div",{className:"text-center mb-8",children:[d.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Track New"}),d.jsx("p",{className:"text-gray-600 mt-2",children:"Service Management System"})]}),d.jsxs("form",{onSubmit:e=>{e.preventDefault();const r=new FormData(e.target);var t,a;t=r.get("email"),a=r.get("password"),t&&a&&(l({name:"Demo User",email:t}),s("dashboard"))},children:[d.jsxs("div",{className:"mb-4",children:[d.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Email"}),d.jsx("input",{type:"email",name:"email",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter your email",defaultValue:"<EMAIL>"})]}),d.jsxs("div",{className:"mb-6",children:[d.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Password"}),d.jsx("input",{type:"password",name:"password",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter your password",defaultValue:"password123"})]}),d.jsx("button",{type:"submit",className:"w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Sign In"})]}),d.jsx("div",{className:"mt-6 text-center",children:d.jsx("p",{className:"text-sm text-gray-600",children:'Demo credentials are pre-filled. Just click "Sign In"'})})]})}),o=()=>d.jsxs("div",{className:"min-h-screen bg-gray-100",children:[d.jsx("header",{className:"bg-white shadow-sm border-b",children:d.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:d.jsxs("div",{className:"flex justify-between items-center py-4",children:[d.jsx("div",{className:"flex items-center",children:d.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Track New"})}),d.jsxs("div",{className:"flex items-center space-x-4",children:[d.jsxs("span",{className:"text-gray-700",children:["Welcome, ",null==r?void 0:r.name]}),d.jsx("button",{onClick:a,className:"bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded",children:"Logout"})]})]})})}),d.jsxs("div",{className:"flex",children:[d.jsx("nav",{className:"w-64 bg-white shadow-sm min-h-screen",children:d.jsx("div",{className:"p-4",children:d.jsx("ul",{className:"space-y-2",children:[{id:"dashboard",name:"Dashboard",icon:"📊"},{id:"customers",name:"Customers",icon:"👥"},{id:"services",name:"Services",icon:"🛠️"},{id:"products",name:"Products",icon:"📦"},{id:"sales",name:"Sales",icon:"💰"},{id:"reports",name:"Reports",icon:"📈"}].map((r=>d.jsx("li",{children:d.jsxs("button",{onClick:()=>s(r.id),className:"w-full text-left px-4 py-2 rounded-md flex items-center space-x-3 "+(e===r.id?"bg-blue-100 text-blue-700":"text-gray-700 hover:bg-gray-100"),children:[d.jsx("span",{children:r.icon}),d.jsx("span",{children:r.name})]})},r.id)))})})}),d.jsx("main",{className:"flex-1 p-8",children:d.jsxs("div",{className:"max-w-7xl mx-auto",children:[d.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-8",children:e.charAt(0).toUpperCase()+e.slice(1)}),"dashboard"===e&&d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[{title:"Total Customers",value:"150",color:"bg-blue-500"},{title:"Active Services",value:"45",color:"bg-green-500"},{title:"Monthly Revenue",value:"$25,000",color:"bg-purple-500"},{title:"Pending Tasks",value:"12",color:"bg-orange-500"}].map(((e,s)=>d.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[d.jsx("div",{className:`w-12 h-12 ${e.color} rounded-lg flex items-center justify-center mb-4`,children:d.jsx("span",{className:"text-white text-xl",children:"📊"})}),d.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:e.title}),d.jsx("p",{className:"text-3xl font-bold text-gray-900 mt-2",children:e.value})]},s)))}),d.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[d.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"dashboard"===e?"Recent Activity":`${e.charAt(0).toUpperCase()+e.slice(1)} Management`}),d.jsx("div",{className:"space-y-4",children:[1,2,3].map((s=>d.jsxs("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[d.jsxs("div",{children:[d.jsx("h4",{className:"font-medium text-gray-900",children:"dashboard"===e?`Activity ${s}`:`${e.slice(0,-1)} ${s}`}),d.jsxs("p",{className:"text-gray-600",children:["Sample description for item ",s]})]}),d.jsx("button",{className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded",children:"View"})]},s)))})]})]})})]})]});return r?d.jsx(o,{}):d.jsx(n,{})}r(function(){if(c)return m;c=1;var e=s();return m.createRoot=e.createRoot,m.hydrateRoot=e.hydrateRoot,m}()).createRoot(document.getElementById("root")).render(d.jsx(l.StrictMode,{children:d.jsx(x,{})}));
