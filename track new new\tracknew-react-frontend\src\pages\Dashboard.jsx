import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  ChartBarIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ShoppingCartIcon,
  WrenchScrewdriverIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarDaysIcon,
  BellIcon
} from '@heroicons/react/24/outline';

// Redux
import {
  fetchDashboardOverview,
  fetchSalesDashboard,
  fetchFinancialDashboard,
  fetchInventoryDashboard,
  fetchTopCustomers,
  fetchRecentActivities,
  selectDashboardOverview,
  selectSalesData,
  selectFinancialData,
  selectInventoryData,
  selectTopCustomers,
  selectRecentActivities,
  selectDashboardLoading
} from '../store/slices/dashboardSlice';

// Components
import { LoadingSpinner } from '../components/ui';
import DashboardCard from '../components/dashboard/DashboardCard';
import RecentActivities from '../components/dashboard/RecentActivities';
import TopCustomersWidget from '../components/dashboard/TopCustomersWidget';
import SalesChart from '../components/dashboard/SalesChart';
import InventoryAlerts from '../components/dashboard/InventoryAlerts';

// Utils
import { formatCurrency, formatDate } from '../utils/helpers';

const Dashboard = () => {
  const dispatch = useDispatch();

  // Selectors
  const overview = useSelector(selectDashboardOverview);
  const salesData = useSelector(selectSalesData);
  const financialData = useSelector(selectFinancialData);
  const inventoryData = useSelector(selectInventoryData);
  const topCustomers = useSelector(selectTopCustomers);
  const recentActivities = useSelector(selectRecentActivities);
  const loading = useSelector(selectDashboardLoading);

  // Fetch dashboard data on mount
  useEffect(() => {
    dispatch(fetchDashboardOverview());
    dispatch(fetchSalesDashboard());
    dispatch(fetchFinancialDashboard());
    dispatch(fetchInventoryDashboard());
    dispatch(fetchTopCustomers({ limit: 5 }));
    dispatch(fetchRecentActivities({ limit: 10 }));
  }, [dispatch]);

  if (loading.overview) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Dashboard
        </h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Welcome back! Here's what's happening with your business today.
        </p>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <DashboardCard
          title="Total Revenue"
          value={formatCurrency(financialData?.total_revenue || 0)}
          change={financialData?.revenue_change || 0}
          changeType={financialData?.revenue_change >= 0 ? 'increase' : 'decrease'}
          icon={CurrencyDollarIcon}
          color="green"
          loading={loading.financial}
        />

        <DashboardCard
          title="Total Customers"
          value={overview?.total_customers || 0}
          change={overview?.customer_change || 0}
          changeType={overview?.customer_change >= 0 ? 'increase' : 'decrease'}
          icon={UsersIcon}
          color="blue"
          loading={loading.overview}
        />

        <DashboardCard
          title="Active Services"
          value={overview?.active_services || 0}
          change={overview?.service_change || 0}
          changeType={overview?.service_change >= 0 ? 'increase' : 'decrease'}
          icon={WrenchScrewdriverIcon}
          color="purple"
          loading={loading.overview}
        />

        <DashboardCard
          title="Monthly Sales"
          value={salesData?.monthly_sales || 0}
          change={salesData?.sales_change || 0}
          changeType={salesData?.sales_change >= 0 ? 'increase' : 'decrease'}
          icon={ShoppingCartIcon}
          color="orange"
          loading={loading.sales}
        />
      </div>

      {/* Financial Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Financial Overview
              </h3>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <CalendarDaysIcon className="h-4 w-4" />
                <span>This Month</span>
              </div>
            </div>

            {loading.financial ? (
              <div className="flex items-center justify-center h-32">
                <LoadingSpinner />
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Sales Revenue</span>
                    <span className="text-sm font-medium">
                      {formatCurrency(financialData?.sales_revenue || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Service Revenue</span>
                    <span className="text-sm font-medium">
                      {formatCurrency(financialData?.service_revenue || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Total Expenses</span>
                    <span className="text-sm font-medium text-red-600">
                      -{formatCurrency(financialData?.total_expenses || 0)}
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Net Profit</span>
                    <span className={`text-sm font-medium ${
                      (financialData?.net_profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatCurrency(financialData?.net_profit || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Profit Margin</span>
                    <span className="text-sm font-medium">
                      {financialData?.profit_margin || 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Outstanding</span>
                    <span className="text-sm font-medium text-yellow-600">
                      {formatCurrency(financialData?.outstanding_invoices?.amount || 0)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Inventory Alerts */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Inventory Alerts
            </h3>
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
          </div>

          <InventoryAlerts
            data={inventoryData}
            loading={loading.inventory}
          />
        </div>
      </div>

      {/* Charts and Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Sales Trend
            </h3>
            <ChartBarIcon className="h-5 w-5 text-gray-400" />
          </div>

          <SalesChart
            data={salesData}
            loading={loading.sales}
          />
        </div>

        {/* Recent Activities */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Recent Activities
            </h3>
            <BellIcon className="h-5 w-5 text-gray-400" />
          </div>

          <RecentActivities
            activities={recentActivities}
            loading={loading.activities}
          />
        </div>
      </div>

      {/* Top Customers */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Top Customers
          </h3>
          <UsersIcon className="h-5 w-5 text-gray-400" />
        </div>

        <TopCustomersWidget
          customers={topCustomers}
          loading={loading.customers}
        />
      </div>
    </div>
  );
};

export default Dashboard;
