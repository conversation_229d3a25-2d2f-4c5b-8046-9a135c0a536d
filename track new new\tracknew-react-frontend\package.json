{"name": "tracknew-react-frontend", "version": "1.0.0", "description": "Track New Service Management System - React Frontend", "private": true, "dependencies": {"@hookform/resolvers": "^3.3.2", "@reduxjs/toolkit": "^2.0.1", "ajv": "^8.17.1", "axios": "^1.6.2", "chart.js": "^4.4.0", "classnames": "^2.3.2", "firebase": "^10.7.1", "framer-motion": "^10.16.16", "jspdf": "^2.5.1", "jspdf-autotable": "^3.6.0", "lodash": "^4.17.21", "moment": "^2.29.4", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-barcode": "^1.4.6", "react-chartjs-2": "^5.2.0", "react-datepicker": "^4.25.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.5.3", "react-loading-skeleton": "^3.3.1", "react-modal": "^3.16.1", "react-qr-scanner": "^1.0.0-alpha.11", "react-query": "^3.39.3", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-spring": "^9.7.3", "react-table": "^7.8.0", "react-use": "^17.4.2", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "recharts": "^2.8.0", "use-debounce": "^10.0.0", "workbox-background-sync": "^7.0.0", "workbox-broadcast-update": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.0.0", "workbox-google-analytics": "^7.0.0", "workbox-navigation-preload": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-range-requests": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0", "workbox-streams": "^7.0.0", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "devDependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.10.4", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-react-app": "^7.0.1", "jest": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "vite": "^6.3.5", "web-vitals": "^3.5.0"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "preview": "vite preview", "test": "jest", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}