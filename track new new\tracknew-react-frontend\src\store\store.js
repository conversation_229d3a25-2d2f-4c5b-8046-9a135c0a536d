import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { combineReducers } from '@reduxjs/toolkit';

// Import reducers
import authReducer from './slices/authSlice';
import uiReducer from './slices/uiSlice';
import serviceReducer from './slices/serviceSlice';
import customerReducer from './slices/customerSlice';
import leadReducer from './slices/leadSlice';
import amcReducer from './slices/amcSlice';
import salesReducer from './slices/salesSlice';
import productReducer from './slices/productSlice';
import estimationReducer from './slices/estimationSlice';
import dashboardReducer from './slices/dashboardSlice';
import notificationReducer from './slices/notificationSlice';

// Persist configuration
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth', 'ui'], // Only persist auth and ui state
  blacklist: ['service', 'customer', 'lead', 'amc', 'sales', 'product', 'estimation', 'dashboard', 'notification']
};

// Auth persist configuration
const authPersistConfig = {
  key: 'auth',
  storage,
  whitelist: ['user', 'token', 'isAuthenticated']
};

// Root reducer
const rootReducer = combineReducers({
  auth: persistReducer(authPersistConfig, authReducer),
  ui: uiReducer,
  service: serviceReducer,
  customer: customerReducer,
  lead: leadReducer,
  amc: amcReducer,
  sales: salesReducer,
  product: productReducer,
  estimation: estimationReducer,
  dashboard: dashboardReducer,
  notification: notificationReducer,
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['register'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// Persistor
export const persistor = persistStore(store);

// Types
// TypeScript types (commented out for JS compatibility)
// export type RootState = ReturnType<typeof store.getState>;
// export type AppDispatch = typeof store.dispatch;
