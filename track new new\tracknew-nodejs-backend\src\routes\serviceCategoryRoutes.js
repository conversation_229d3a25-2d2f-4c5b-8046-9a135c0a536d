const express = require('express');
const { body } = require('express-validator');
const serviceCategoryController = require('../controllers/serviceCategoryController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating service category
const createServiceCategoryValidation = [
  body('name')
    .notEmpty()
    .withMessage('Category name is required')
    .isLength({ min: 2, max: 255 })
    .withMessage('Category name must be between 2 and 255 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('parent_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Parent ID must be a positive integer'),
  
  body('color_code')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color code must be a valid hex color (e.g., #FF0000)'),
  
  body('icon')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Icon must be maximum 100 characters'),
  
  body('estimated_duration')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Estimated duration must be a positive integer (minutes)'),
  
  body('default_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Default price must be a positive number'),
  
  body('requires_approval')
    .optional()
    .isBoolean()
    .withMessage('Requires approval must be a boolean'),
  
  body('auto_assign')
    .optional()
    .isBoolean()
    .withMessage('Auto assign must be a boolean'),
  
  body('sla_hours')
    .optional()
    .isInt({ min: 1 })
    .withMessage('SLA hours must be a positive integer'),
  
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean')
];

// Validation rules for updating service category
const updateServiceCategoryValidation = [
  body('name')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Category name must be between 2 and 255 characters'),
  
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be maximum 1000 characters'),
  
  body('parent_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Parent ID must be a positive integer'),
  
  body('color_code')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color code must be a valid hex color (e.g., #FF0000)'),
  
  body('icon')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Icon must be maximum 100 characters'),
  
  body('estimated_duration')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Estimated duration must be a positive integer (minutes)'),
  
  body('default_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Default price must be a positive number'),
  
  body('requires_approval')
    .optional()
    .isBoolean()
    .withMessage('Requires approval must be a boolean'),
  
  body('auto_assign')
    .optional()
    .isBoolean()
    .withMessage('Auto assign must be a boolean'),
  
  body('sla_hours')
    .optional()
    .isInt({ min: 1 })
    .withMessage('SLA hours must be a positive integer'),
  
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('Is active must be a boolean')
];

// Validation for bulk sort order update
const updateSortOrderValidation = [
  body('categories')
    .isArray({ min: 1 })
    .withMessage('Categories array is required and must not be empty'),
  
  body('categories.*.id')
    .isInt({ min: 1 })
    .withMessage('Each category must have a valid ID'),
  
  body('categories.*.sort_order')
    .isInt({ min: 0 })
    .withMessage('Each category must have a valid sort order')
];

// Routes
router
  .route('/')
  .get(serviceCategoryController.getServiceCategories)
  .post(createServiceCategoryValidation, validateRequest, restrictTo('admin', 'sub_admin', 'service_manager'), serviceCategoryController.createServiceCategory);

router
  .route('/stats')
  .get(serviceCategoryController.getServiceCategoryStats);

router
  .route('/tree')
  .get(serviceCategoryController.getServiceCategoryTree);

router
  .route('/active')
  .get(serviceCategoryController.getActiveServiceCategories);

router
  .route('/sort-order')
  .put(updateSortOrderValidation, validateRequest, restrictTo('admin', 'sub_admin', 'service_manager'), serviceCategoryController.updateSortOrder);

router
  .route('/by-parent/:parent_id')
  .get(serviceCategoryController.getCategoriesByParent);

router
  .route('/:id')
  .get(serviceCategoryController.getServiceCategory)
  .put(updateServiceCategoryValidation, validateRequest, restrictTo('admin', 'sub_admin', 'service_manager'), serviceCategoryController.updateServiceCategory)
  .delete(restrictTo('admin', 'sub_admin'), serviceCategoryController.deleteServiceCategory);

module.exports = router;
