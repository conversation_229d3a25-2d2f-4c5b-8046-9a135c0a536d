const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Expense = sequelize.define('Expense', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  expense_category_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'expense_categories',
      key: 'id'
    }
  },
  expense_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  expense_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'INR'
  },
  exchange_rate: {
    type: DataTypes.DECIMAL(10, 4),
    defaultValue: 1.0000
  },
  base_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Amount in base currency'
  },
  payment_method: {
    type: DataTypes.ENUM('cash', 'cheque', 'bank_transfer', 'credit_card', 'debit_card', 'upi', 'wallet', 'online', 'other'),
    defaultValue: 'cash'
  },
  payment_status: {
    type: DataTypes.ENUM('pending', 'paid', 'partially_paid', 'overdue', 'cancelled'),
    defaultValue: 'pending'
  },
  paid_amount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00
  },
  balance_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  vendor_name: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  vendor_contact: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  vendor_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  invoice_number: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Vendor invoice number'
  },
  invoice_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  due_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  reference_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  project_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Associated project ID'
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'customers',
      key: 'id'
    },
    comment: 'Customer for billable expenses'
  },
  is_billable: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether expense can be billed to customer'
  },
  billable_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Amount that can be billed to customer'
  },
  markup_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    comment: 'Markup percentage for billable expenses'
  },
  markup_amount: {
    type: DataTypes.DECIMAL(8, 2),
    defaultValue: 0.00
  },
  billed_amount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00,
    comment: 'Amount already billed to customer'
  },
  is_recurring: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  recurring_frequency: {
    type: DataTypes.ENUM('weekly', 'monthly', 'quarterly', 'yearly'),
    allowNull: true
  },
  recurring_end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  parent_expense_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'expenses',
      key: 'id'
    },
    comment: 'Reference to parent expense for recurring expenses'
  },
  tax_rate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00,
    comment: 'Tax rate percentage'
  },
  tax_amount: {
    type: DataTypes.DECIMAL(8, 2),
    defaultValue: 0.00
  },
  tax_inclusive: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether amount includes tax'
  },
  tds_applicable: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether TDS is applicable'
  },
  tds_rate: {
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0.00
  },
  tds_amount: {
    type: DataTypes.DECIMAL(8, 2),
    defaultValue: 0.00
  },
  status: {
    type: DataTypes.ENUM('draft', 'submitted', 'approved', 'rejected', 'paid', 'cancelled'),
    defaultValue: 'draft'
  },
  approval_status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected'),
    defaultValue: 'pending'
  },
  approved_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  approved_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  approved_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Amount approved (may be different from requested)'
  },
  rejection_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  reimbursement_status: {
    type: DataTypes.ENUM('not_applicable', 'pending', 'processed', 'paid'),
    defaultValue: 'not_applicable'
  },
  reimbursement_amount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0.00
  },
  reimbursement_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  employee_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Employee who incurred the expense'
  },
  department: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  cost_center: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  budget_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Associated budget ID'
  },
  location: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Location where expense was incurred'
  },
  purpose: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Business purpose of the expense'
  },
  attendees: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of attendees for meal/entertainment expenses'
  },
  mileage: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    comment: 'Mileage for travel expenses'
  },
  mileage_rate: {
    type: DataTypes.DECIMAL(6, 2),
    allowNull: true,
    comment: 'Rate per mile/km'
  },
  receipts: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of receipt file paths'
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of attachment file paths'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  internal_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Internal notes for accounting'
  },
  tags: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Comma-separated tags'
  },
  custom_fields: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object for custom field values'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'expenses',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['expense_category_id']
    },
    {
      fields: ['expense_number'],
      unique: true
    },
    {
      fields: ['expense_date']
    },
    {
      fields: ['status']
    },
    {
      fields: ['approval_status']
    },
    {
      fields: ['payment_status']
    },
    {
      fields: ['employee_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['is_billable']
    },
    {
      fields: ['is_recurring']
    },
    {
      fields: ['parent_expense_id']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = Expense;
