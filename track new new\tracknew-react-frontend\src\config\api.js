// API Configuration for different environments

const config = {
  development: {
    API_BASE_URL: 'http://localhost:8000/api',
    APP_URL: 'http://localhost:3000'
  },
  production: {
    API_BASE_URL: '/api', // Same server, relative path
    APP_URL: window.location.origin
  },
  test: {
    API_BASE_URL: 'http://localhost:8000/api',
    APP_URL: 'http://localhost:3000'
  }
};

// Determine current environment
const environment = process.env.NODE_ENV || 'development';

// Export configuration for current environment
export const API_CONFIG = config[environment];

// API endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    PROFILE: '/auth/profile',
    REFRESH: '/auth/refresh',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password'
  },
  
  // Dashboard
  DASHBOARD: {
    OVERVIEW: '/dashboard/overview',
    SALES: '/dashboard/sales',
    FINANCIAL: '/dashboard/financial',
    INVENTORY: '/dashboard/inventory',
    TOP_CUSTOMERS: '/dashboard/top-customers',
    ACTIVITIES: '/dashboard/activities'
  },
  
  // Customers
  CUSTOMERS: {
    LIST: '/customers',
    CREATE: '/customers',
    UPDATE: (id) => `/customers/${id}`,
    DELETE: (id) => `/customers/${id}`,
    GET: (id) => `/customers/${id}`
  },
  
  // Services
  SERVICES: {
    LIST: '/services',
    CREATE: '/services',
    UPDATE: (id) => `/services/${id}`,
    DELETE: (id) => `/services/${id}`,
    GET: (id) => `/services/${id}`
  },
  
  // Products
  PRODUCTS: {
    LIST: '/products',
    CREATE: '/products',
    UPDATE: (id) => `/products/${id}`,
    DELETE: (id) => `/products/${id}`,
    GET: (id) => `/products/${id}`,
    UPDATE_STOCK: (id) => `/products/${id}/stock`
  },
  
  // Sales
  SALES: {
    LIST: '/sales',
    CREATE: '/sales',
    UPDATE: (id) => `/sales/${id}`,
    DELETE: (id) => `/sales/${id}`,
    GET: (id) => `/sales/${id}`
  },
  
  // Reports
  REPORTS: {
    SALES: '/reports/sales',
    FINANCIAL: '/reports/financial',
    INVENTORY: '/reports/inventory',
    CUSTOMERS: '/reports/customers'
  },
  
  // Settings
  SETTINGS: {
    GET: '/settings',
    UPDATE: '/settings'
  }
};

// Helper function to build full URL
export const buildApiUrl = (endpoint) => {
  return `${API_CONFIG.API_BASE_URL}${endpoint}`;
};

// Default headers for API requests
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
};

// Request timeout (in milliseconds)
export const REQUEST_TIMEOUT = 30000; // 30 seconds
