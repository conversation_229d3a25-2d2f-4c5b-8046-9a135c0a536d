const express = require('express');
const { body } = require('express-validator');
const notificationController = require('../controllers/notificationController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating notification
const createNotificationValidation = [
  body('user_id')
    .notEmpty()
    .withMessage('User ID is required')
    .isInt({ min: 1 })
    .withMessage('User ID must be a positive integer'),
  
  body('type')
    .optional()
    .isIn(['info', 'success', 'warning', 'error', 'reminder', 'alert', 'system'])
    .withMessage('Invalid notification type'),
  
  body('category')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Category must be maximum 100 characters'),
  
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ min: 1, max: 255 })
    .withMessage('Title must be between 1 and 255 characters'),
  
  body('message')
    .notEmpty()
    .withMessage('Message is required')
    .isLength({ min: 1, max: 2000 })
    .withMessage('Message must be between 1 and 2000 characters'),
  
  body('data')
    .optional()
    .isObject()
    .withMessage('Data must be a valid JSON object'),
  
  body('action_url')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Action URL must be maximum 500 characters'),
  
  body('action_text')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Action text must be maximum 100 characters'),
  
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Invalid priority'),
  
  body('channel')
    .optional()
    .isIn(['in_app', 'email', 'sms', 'push', 'whatsapp', 'all'])
    .withMessage('Invalid channel'),
  
  body('related_type')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Related type must be maximum 100 characters'),
  
  body('related_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Related ID must be a positive integer'),
  
  body('expires_at')
    .optional()
    .isISO8601()
    .withMessage('Expires at must be a valid date'),
  
  body('scheduled_at')
    .optional()
    .isISO8601()
    .withMessage('Scheduled at must be a valid date'),
  
  body('template_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Template ID must be a positive integer'),
  
  body('template_data')
    .optional()
    .isObject()
    .withMessage('Template data must be a valid JSON object'),
  
  body('attachments')
    .optional()
    .isArray()
    .withMessage('Attachments must be an array'),
  
  body('tags')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Tags must be maximum 500 characters'),
  
  body('metadata')
    .optional()
    .isObject()
    .withMessage('Metadata must be a valid JSON object')
];

// Validation for bulk operations
const bulkOperationValidation = [
  body('notification_ids')
    .isArray({ min: 1 })
    .withMessage('Notification IDs array is required and must not be empty'),
  
  body('notification_ids.*')
    .isInt({ min: 1 })
    .withMessage('Each notification ID must be a positive integer')
];

// Validation for bulk notification sending
const sendBulkNotificationValidation = [
  body('user_ids')
    .isArray({ min: 1 })
    .withMessage('User IDs array is required and must not be empty'),
  
  body('user_ids.*')
    .isInt({ min: 1 })
    .withMessage('Each user ID must be a positive integer'),
  
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ min: 1, max: 255 })
    .withMessage('Title must be between 1 and 255 characters'),
  
  body('message')
    .notEmpty()
    .withMessage('Message is required')
    .isLength({ min: 1, max: 2000 })
    .withMessage('Message must be between 1 and 2000 characters'),
  
  body('type')
    .optional()
    .isIn(['info', 'success', 'warning', 'error', 'reminder', 'alert', 'system'])
    .withMessage('Invalid notification type'),
  
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Invalid priority'),
  
  body('channel')
    .optional()
    .isIn(['in_app', 'email', 'sms', 'push', 'whatsapp', 'all'])
    .withMessage('Invalid channel')
];

// Validation for notification preferences
const updatePreferencesValidation = [
  body('preferences')
    .notEmpty()
    .withMessage('Preferences object is required')
    .isObject()
    .withMessage('Preferences must be a valid JSON object'),
  
  body('preferences.email_notifications')
    .optional()
    .isBoolean()
    .withMessage('Email notifications must be a boolean'),
  
  body('preferences.sms_notifications')
    .optional()
    .isBoolean()
    .withMessage('SMS notifications must be a boolean'),
  
  body('preferences.push_notifications')
    .optional()
    .isBoolean()
    .withMessage('Push notifications must be a boolean'),
  
  body('preferences.in_app_notifications')
    .optional()
    .isBoolean()
    .withMessage('In-app notifications must be a boolean')
];

// Routes
router
  .route('/')
  .get(notificationController.getNotifications)
  .post(createNotificationValidation, validateRequest, restrictTo('admin', 'sub_admin', 'manager'), notificationController.createNotification);

router
  .route('/stats')
  .get(notificationController.getNotificationStats);

router
  .route('/preferences')
  .get(notificationController.getNotificationPreferences)
  .put(updatePreferencesValidation, validateRequest, notificationController.updateNotificationPreferences);

router
  .route('/mark-all-read')
  .put(notificationController.markAllAsRead);

router
  .route('/bulk-archive')
  .put(bulkOperationValidation, validateRequest, notificationController.bulkArchive);

router
  .route('/bulk-delete')
  .delete(bulkOperationValidation, validateRequest, notificationController.bulkDelete);

router
  .route('/send-bulk')
  .post(sendBulkNotificationValidation, validateRequest, restrictTo('admin', 'sub_admin', 'manager'), notificationController.sendBulkNotification);

router
  .route('/cleanup-expired')
  .delete(restrictTo('admin', 'sub_admin'), notificationController.cleanupExpiredNotifications);

router
  .route('/:id/read')
  .put(notificationController.markAsRead);

router
  .route('/:id/unread')
  .put(notificationController.markAsUnread);

router
  .route('/:id/archive')
  .put(notificationController.archiveNotification);

router
  .route('/:id')
  .get(notificationController.getNotification)
  .delete(notificationController.deleteNotification);

module.exports = router;
