const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const AMCProduct = sequelize.define('AMCProduct', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  amc_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'amcs',
      key: 'id'
    }
  },
  product_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1
  },
  serial_number: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  model_number: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  installation_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  warranty_start_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  warranty_end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  location: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Physical location of the product'
  },
  condition: {
    type: DataTypes.ENUM('excellent', 'good', 'fair', 'poor', 'critical'),
    defaultValue: 'good'
  },
  last_service_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  next_service_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  service_frequency_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Service frequency specific to this product'
  },
  service_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Special service instructions for this product'
  },
  replacement_value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Replacement value of the product'
  },
  depreciation_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Annual depreciation rate percentage'
  },
  current_value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Current depreciated value'
  },
  criticality: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
    defaultValue: 'medium',
    comment: 'Business criticality of this product'
  },
  downtime_cost_per_hour: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    comment: 'Cost of downtime per hour for this product'
  },
  preventive_maintenance: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether preventive maintenance is included'
  },
  breakdown_maintenance: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether breakdown maintenance is included'
  },
  parts_coverage: {
    type: DataTypes.ENUM('none', 'consumables', 'all_parts', 'major_parts'),
    defaultValue: 'consumables'
  },
  labor_coverage: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  response_time_hours: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Specific response time for this product'
  },
  resolution_time_hours: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Specific resolution time for this product'
  },
  escalation_level_1: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Level 1 escalation contact'
  },
  escalation_level_2: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Level 2 escalation contact'
  },
  escalation_level_3: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Level 3 escalation contact'
  },
  technical_specifications: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object containing technical specs'
  },
  maintenance_checklist: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of maintenance checklist items'
  },
  spare_parts_list: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of recommended spare parts'
  },
  service_history: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of service history records'
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of attachment file paths'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_under_warranty: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'amc_products',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['amc_id']
    },
    {
      fields: ['product_id']
    },
    {
      fields: ['serial_number']
    },
    {
      fields: ['model_number']
    },
    {
      fields: ['condition']
    },
    {
      fields: ['criticality']
    },
    {
      fields: ['next_service_date']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_under_warranty']
    },
    {
      fields: ['amc_id', 'product_id', 'serial_number'],
      unique: true
    }
  ]
});

module.exports = AMCProduct;
