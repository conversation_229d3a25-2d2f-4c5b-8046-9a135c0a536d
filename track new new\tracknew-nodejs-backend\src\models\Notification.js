const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Notification = sequelize.define('Notification', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  type: {
    type: DataTypes.ENUM('info', 'success', 'warning', 'error', 'reminder', 'alert', 'system'),
    defaultValue: 'info'
  },
  category: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Notification category (service, sales, payment, etc.)'
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  message: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  data: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional data related to the notification'
  },
  action_url: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'URL to navigate when notification is clicked'
  },
  action_text: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Text for action button'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  channel: {
    type: DataTypes.ENUM('in_app', 'email', 'sms', 'push', 'whatsapp', 'all'),
    defaultValue: 'in_app'
  },
  related_type: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Type of related entity (service, customer, sales, etc.)'
  },
  related_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'ID of related entity'
  },
  sender_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who triggered this notification'
  },
  is_read: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  read_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  is_archived: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  archived_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When this notification expires'
  },
  scheduled_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When this notification should be sent'
  },
  sent_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When this notification was actually sent'
  },
  delivery_status: {
    type: DataTypes.ENUM('pending', 'sent', 'delivered', 'failed', 'cancelled'),
    defaultValue: 'pending'
  },
  delivery_attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  last_attempt_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  failure_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  email_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  email_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  sms_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  sms_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  push_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  push_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  whatsapp_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  whatsapp_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  template_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Template used for this notification'
  },
  template_data: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Data used to populate template'
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of attachment file paths'
  },
  tags: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Comma-separated tags'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional metadata'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'notifications',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['type']
    },
    {
      fields: ['category']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['channel']
    },
    {
      fields: ['related_type', 'related_id']
    },
    {
      fields: ['sender_id']
    },
    {
      fields: ['is_read']
    },
    {
      fields: ['is_archived']
    },
    {
      fields: ['delivery_status']
    },
    {
      fields: ['scheduled_at']
    },
    {
      fields: ['expires_at']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['user_id', 'is_read']
    },
    {
      fields: ['user_id', 'created_at']
    }
  ]
});

module.exports = Notification;
