const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'success',
    message: 'Development server is running',
    timestamp: new Date().toISOString(),
    environment: 'development'
  });
});

// Mock authentication endpoints
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // Mock login - accept any credentials for development
  if (email && password) {
    res.json({
      status: 'success',
      message: 'Login successful',
      data: {
        user: {
          id: 1,
          email: email,
          name: 'Development User',
          role: 'admin'
        },
        token: 'dev-token-123'
      }
    });
  } else {
    res.status(400).json({
      status: 'error',
      message: 'Email and password required'
    });
  }
});

app.post('/api/auth/register', (req, res) => {
  const { name, email, password } = req.body;
  
  if (name && email && password) {
    res.json({
      status: 'success',
      message: 'Registration successful',
      data: {
        user: {
          id: 2,
          email: email,
          name: name,
          role: 'user'
        },
        token: 'dev-token-456'
      }
    });
  } else {
    res.status(400).json({
      status: 'error',
      message: 'Name, email and password required'
    });
  }
});

// Mock dashboard data
app.get('/api/dashboard/overview', (req, res) => {
  res.json({
    status: 'success',
    data: {
      total_customers: 150,
      active_services: 45,
      monthly_revenue: 25000,
      pending_invoices: 12
    }
  });
});

// Mock customers endpoint
app.get('/api/customers', (req, res) => {
  res.json({
    status: 'success',
    data: [
      { id: 1, name: 'John Doe', email: '<EMAIL>', phone: '************' },
      { id: 2, name: 'Jane Smith', email: '<EMAIL>', phone: '************' },
      { id: 3, name: 'Bob Johnson', email: '<EMAIL>', phone: '************' }
    ]
  });
});

// Mock services endpoint
app.get('/api/services', (req, res) => {
  res.json({
    status: 'success',
    data: [
      { id: 1, title: 'Computer Repair', status: 'active', customer: 'John Doe' },
      { id: 2, title: 'Network Setup', status: 'pending', customer: 'Jane Smith' },
      { id: 3, title: 'Data Recovery', status: 'completed', customer: 'Bob Johnson' }
    ]
  });
});

// Mock products endpoint
app.get('/api/products', (req, res) => {
  res.json({
    status: 'success',
    data: [
      { id: 1, name: 'Laptop', price: 999.99, stock: 10 },
      { id: 2, name: 'Mouse', price: 29.99, stock: 50 },
      { id: 3, name: 'Keyboard', price: 79.99, stock: 25 }
    ]
  });
});

// Mock sales endpoint
app.get('/api/sales', (req, res) => {
  res.json({
    status: 'success',
    data: [
      { id: 1, customer: 'John Doe', amount: 1299.99, date: '2024-01-15' },
      { id: 2, customer: 'Jane Smith', amount: 599.99, date: '2024-01-14' },
      { id: 3, customer: 'Bob Johnson', amount: 899.99, date: '2024-01-13' }
    ]
  });
});

// Catch all other API routes
app.use('/api/*', (req, res) => {
  res.status(501).json({
    status: 'error',
    message: `API endpoint ${req.path} not implemented yet`
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    status: 'error',
    message: 'Internal server error'
  });
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 Development Server running on http://localhost:' + PORT);
  console.log('🏥 Health Check: http://localhost:' + PORT + '/api/health');
  console.log('📡 API Base: http://localhost:' + PORT + '/api');
  console.log('');
  console.log('Available endpoints:');
  console.log('  POST /api/auth/login');
  console.log('  POST /api/auth/register');
  console.log('  GET  /api/dashboard/overview');
  console.log('  GET  /api/customers');
  console.log('  GET  /api/services');
  console.log('  GET  /api/products');
  console.log('  GET  /api/sales');
});

module.exports = app;
