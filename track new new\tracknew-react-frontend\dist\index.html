<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="TrackNew - Professional Service Management System" />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <link rel="manifest" href="/manifest.json" />

    <!-- Tailwind CSS CDN for development -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Configure Tailwind -->
    <script>
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            colors: {
              primary: {
                50: '#eff6ff',
                100: '#dbeafe',
                200: '#bfdbfe',
                300: '#93c5fd',
                400: '#60a5fa',
                500: '#3b82f6',
                600: '#2563eb',
                700: '#1d4ed8',
                800: '#1e40af',
                900: '#1e3a8a',
              },
              secondary: {
                50: '#f8fafc',
                100: '#f1f5f9',
                200: '#e2e8f0',
                300: '#cbd5e1',
                400: '#94a3b8',
                500: '#64748b',
                600: '#475569',
                700: '#334155',
                800: '#1e293b',
                900: '#0f172a',
              }
            }
          }
        }
      }
    </script>

    <title>TrackNew - Service Management System</title>
    <script type="module" crossorigin src="/assets/index-DHpfCBun.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-BRaCMJ4j.js">
    <link rel="modulepreload" crossorigin href="/assets/router-CH1RGDGB.js">
    <link rel="modulepreload" crossorigin href="/assets/redux-CparIzla.js">
    <link rel="modulepreload" crossorigin href="/assets/utils-rSMZb3Ae.js">
    <link rel="modulepreload" crossorigin href="/assets/ui-BWpWsGca.js">
    <link rel="stylesheet" crossorigin href="/assets/index-D0-Yan2g.css">
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
