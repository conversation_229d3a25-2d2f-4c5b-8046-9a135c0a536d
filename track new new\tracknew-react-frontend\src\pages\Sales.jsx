import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  PlusIcon,
  ShoppingCartIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  UserIcon,
  CalendarDaysIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PrinterIcon
} from '@heroicons/react/24/outline';

// Redux
import {
  fetchSales,
  deleteSale,
  selectSales,
  selectSalePagination,
  selectSaleLoading,
  selectSaleError,
  selectSelectedSales,
  selectSaleFilters,
  selectSaleSearchQuery,
  setSearchQuery,
  setFilter,
  clearFilters,
  selectSale,
  deselectSale,
  selectAllSales,
  deselectAllSales
} from '../store/slices/salesSlice';

// Components
import { Button, DataTable, Pagination, ConfirmModal } from '../components/ui';
import { Input, Select, DatePicker } from '../components/forms';
import { formatDate, formatCurrency, getStatusColor, classNames } from '../utils/helpers';

const Sales = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  // Local state
  const [showFilters, setShowFilters] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [saleToDelete, setSaleToDelete] = useState(null);
  
  // Selectors
  const sales = useSelector(selectSales);
  const pagination = useSelector(selectSalePagination);
  const loading = useSelector(selectSaleLoading);
  const error = useSelector(selectSaleError);
  const selectedSales = useSelector(selectSelectedSales);
  const filters = useSelector(selectSaleFilters);
  const searchQuery = useSelector(selectSaleSearchQuery);

  // Fetch sales on mount and when filters change
  useEffect(() => {
    dispatch(fetchSales({
      page: pagination.current_page,
      limit: pagination.items_per_page,
      search: searchQuery,
      ...filters
    }));
  }, [dispatch, pagination.current_page, pagination.items_per_page, searchQuery, filters]);

  // Status options
  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'draft', label: 'Draft' },
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  const paymentStatusOptions = [
    { value: '', label: 'All Payment Status' },
    { value: 'pending', label: 'Pending' },
    { value: 'partial', label: 'Partially Paid' },
    { value: 'paid', label: 'Paid' },
    { value: 'overdue', label: 'Overdue' }
  ];

  // Table columns
  const columns = [
    {
      key: 'sale_number',
      title: 'Sale #',
      sortable: true,
      render: (value, sale) => (
        <div>
          <div className="text-sm font-medium text-gray-900 dark:text-white">
            #{sale.sale_number || sale.id}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {sale.sale_type || 'Regular Sale'}
          </div>
        </div>
      )
    },
    {
      key: 'customer',
      title: 'Customer',
      render: (value, sale) => (
        <div className="flex items-center space-x-2">
          <UserIcon className="h-4 w-4 text-gray-400" />
          <div>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {sale.customer?.name || 'Walk-in Customer'}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {sale.customer?.phone || '-'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'total_amount',
      title: 'Amount',
      sortable: true,
      render: (value, sale) => (
        <div>
          <div className="text-sm font-medium text-gray-900 dark:text-white">
            {formatCurrency(value || 0)}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {sale.items_count || 0} items
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (value) => (
        <span className={classNames(
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          getStatusColor(value)
        )}>
          {value?.replace('_', ' ') || 'draft'}
        </span>
      )
    },
    {
      key: 'payment_status',
      title: 'Payment',
      sortable: true,
      render: (value, sale) => (
        <div>
          <span className={classNames(
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            value === 'paid' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
            value === 'partial' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
            value === 'overdue' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
            'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
          )}>
            {value?.replace('_', ' ') || 'pending'}
          </span>
          {sale.paid_amount > 0 && (
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Paid: {formatCurrency(sale.paid_amount)}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'sale_date',
      title: 'Date',
      sortable: true,
      render: (value) => (
        <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
          <CalendarDaysIcon className="h-4 w-4" />
          <span>{formatDate(value)}</span>
        </div>
      )
    },
    {
      key: 'salesperson',
      title: 'Salesperson',
      render: (value, sale) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {sale.salesperson?.name || sale.created_by?.name || '-'}
        </span>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: 'View',
      icon: EyeIcon,
      onClick: (sale) => navigate(`/sales/${sale.id}`)
    },
    {
      label: 'Edit',
      icon: PencilIcon,
      onClick: (sale) => navigate(`/sales/${sale.id}/edit`),
      disabled: (sale) => sale.status === 'completed' || sale.status === 'cancelled'
    },
    {
      label: 'Print Invoice',
      icon: PrinterIcon,
      onClick: (sale) => window.open(`/sales/${sale.id}/print`, '_blank'),
      disabled: (sale) => sale.status === 'draft'
    },
    {
      label: 'Generate Invoice',
      icon: DocumentTextIcon,
      onClick: (sale) => navigate(`/invoices/new?sale_id=${sale.id}`),
      disabled: (sale) => sale.invoice_generated || sale.status === 'cancelled'
    },
    {
      label: 'Delete',
      icon: TrashIcon,
      onClick: (sale) => {
        setSaleToDelete(sale);
        setDeleteModalOpen(true);
      },
      disabled: (sale) => sale.status === 'completed' || sale.payment_status === 'paid'
    }
  ];

  // Handlers
  const handleSearch = (query) => {
    dispatch(setSearchQuery(query));
  };

  const handleFilterChange = (key, value) => {
    dispatch(setFilter({ key, value }));
  };

  const handleClearFilters = () => {
    dispatch(clearFilters());
    dispatch(setSearchQuery(''));
  };

  const handlePageChange = (page) => {
    dispatch(fetchSales({
      page,
      limit: pagination.items_per_page,
      search: searchQuery,
      ...filters
    }));
  };

  const handleRowSelect = (saleId, checked) => {
    if (checked) {
      dispatch(selectSale(saleId));
    } else {
      dispatch(deselectSale(saleId));
    }
  };

  const handleSelectAll = (saleIds) => {
    if (saleIds.length > 0) {
      dispatch(selectAllSales());
    } else {
      dispatch(deselectAllSales());
    }
  };

  const handleDeleteSale = async () => {
    if (saleToDelete) {
      await dispatch(deleteSale(saleToDelete.id));
      setDeleteModalOpen(false);
      setSaleToDelete(null);
      // Refresh the list
      dispatch(fetchSales({
        page: pagination.current_page,
        limit: pagination.items_per_page,
        search: searchQuery,
        ...filters
      }));
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Sales
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Manage your sales transactions and orders
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            leftIcon={DocumentTextIcon}
            onClick={() => navigate('/invoices')}
          >
            Invoices
          </Button>
          
          <Button
            variant="outline"
            leftIcon={ShoppingCartIcon}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </Button>
          
          <Button
            leftIcon={PlusIcon}
            onClick={() => navigate('/sales/new')}
          >
            New Sale
          </Button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              label="Status"
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
              options={statusOptions}
            />
            
            <Select
              label="Payment Status"
              value={filters.payment_status}
              onChange={(value) => handleFilterChange('payment_status', value)}
              options={paymentStatusOptions}
            />
            
            <DatePicker
              label="From Date"
              value={filters.from_date}
              onChange={(value) => handleFilterChange('from_date', value)}
            />
            
            <DatePicker
              label="To Date"
              value={filters.to_date}
              onChange={(value) => handleFilterChange('to_date', value)}
            />
          </div>
          
          <div className="mt-4 flex justify-end">
            <Button
              variant="outline"
              onClick={handleClearFilters}
            >
              Clear Filters
            </Button>
          </div>
        </div>
      )}

      {/* Data Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <DataTable
          data={sales}
          columns={columns}
          loading={loading}
          error={error}
          searchQuery={searchQuery}
          onSearch={handleSearch}
          selectedRows={selectedSales}
          onSelectRow={handleRowSelect}
          onSelectAll={handleSelectAll}
          selectable={true}
          actions={actions}
          onRowClick={(sale) => navigate(`/sales/${sale.id}`)}
          emptyMessage="No sales found"
        />
        
        {/* Pagination */}
        <Pagination
          currentPage={pagination.current_page}
          totalPages={pagination.total_pages}
          totalItems={pagination.total_items}
          itemsPerPage={pagination.items_per_page}
          onPageChange={handlePageChange}
        />
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteSale}
        title="Delete Sale"
        message={`Are you sure you want to delete sale #${saleToDelete?.sale_number || saleToDelete?.id}? This action cannot be undone.`}
        confirmText="Delete"
        confirmColor="red"
      />
    </div>
  );
};

export default Sales;
