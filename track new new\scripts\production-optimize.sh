#!/bin/bash

# TrackNew Production Optimization Script
# This script optimizes the application for production deployment

set -e

echo "🚀 Starting TrackNew Production Optimization..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

print_header "📁 Project Directory: $PROJECT_DIR"

# Step 1: Backend Optimization
print_header "🔧 Step 1: Backend Optimization"

cd "$PROJECT_DIR/tracknew-nodejs-backend"

print_status "Installing production dependencies..."
npm ci --only=production

print_status "Removing development dependencies..."
npm prune --production

print_status "Optimizing Node.js modules..."
# Remove unnecessary files
find node_modules -name "*.md" -delete 2>/dev/null || true
find node_modules -name "*.txt" -delete 2>/dev/null || true
find node_modules -name "test" -type d -exec rm -rf {} + 2>/dev/null || true
find node_modules -name "tests" -type d -exec rm -rf {} + 2>/dev/null || true
find node_modules -name "*.test.js" -delete 2>/dev/null || true
find node_modules -name "*.spec.js" -delete 2>/dev/null || true

print_status "Creating production environment file..."
if [ ! -f .env ]; then
    cp .env.production .env
    print_warning "Please update .env with your production values"
fi

print_status "Setting up log directories..."
mkdir -p logs
mkdir -p uploads
mkdir -p temp

print_status "Setting correct permissions..."
chmod 755 logs uploads temp
chmod 600 .env

# Step 2: Frontend Optimization
print_header "🎨 Step 2: Frontend Optimization"

cd "$PROJECT_DIR/tracknew-react-frontend"

print_status "Installing frontend dependencies..."
npm ci

print_status "Building production bundle..."
npm run build

print_status "Analyzing bundle size..."
if command -v du &> /dev/null; then
    BUILD_SIZE=$(du -sh build | cut -f1)
    print_status "Build size: $BUILD_SIZE"
fi

print_status "Optimizing static assets..."
# Compress images if imagemin is available
if command -v imagemin &> /dev/null; then
    find build/static -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" | xargs imagemin --out-dir=build/static/
fi

# Step 3: Security Hardening
print_header "🔒 Step 3: Security Hardening"

print_status "Setting secure file permissions..."
cd "$PROJECT_DIR"

# Set directory permissions
find . -type d -exec chmod 755 {} \;

# Set file permissions
find . -type f -exec chmod 644 {} \;

# Make scripts executable
find . -name "*.sh" -exec chmod +x {} \;

# Secure sensitive files
chmod 600 tracknew-nodejs-backend/.env* 2>/dev/null || true
chmod 600 tracknew-react-frontend/.env* 2>/dev/null || true

print_status "Removing sensitive development files..."
# Remove git files in production
if [ "$NODE_ENV" = "production" ]; then
    rm -rf .git 2>/dev/null || true
    rm -f .gitignore 2>/dev/null || true
fi

# Remove development files
find . -name "*.log" -delete 2>/dev/null || true
find . -name ".DS_Store" -delete 2>/dev/null || true
find . -name "Thumbs.db" -delete 2>/dev/null || true

# Step 4: Performance Optimization
print_header "⚡ Step 4: Performance Optimization"

print_status "Optimizing database connections..."
cd "$PROJECT_DIR/tracknew-nodejs-backend"

# Create database optimization script
cat > optimize-db.sql << 'EOF'
-- Database optimization queries
ANALYZE;
VACUUM;

-- Update statistics
UPDATE pg_stat_user_tables SET n_tup_ins = 0, n_tup_upd = 0, n_tup_del = 0;

-- Optimize indexes
REINDEX DATABASE tracknew_production;
EOF

print_status "Database optimization script created: optimize-db.sql"

print_status "Setting up PM2 ecosystem..."
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'tracknew-api',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 8000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 8000
    },
    error_file: './logs/api-error.log',
    out_file: './logs/api-out.log',
    log_file: './logs/api.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'uploads'],
    max_restarts: 10,
    min_uptime: '10s',
    kill_timeout: 5000,
    wait_ready: true,
    listen_timeout: 10000
  }]
};
EOF

# Step 5: Monitoring Setup
print_header "📊 Step 5: Monitoring Setup"

print_status "Creating monitoring scripts..."

# Health check script
cat > health-check.sh << 'EOF'
#!/bin/bash

# Health check script for TrackNew
API_URL="http://localhost:8000/health"
FRONTEND_URL="http://localhost:3000"

echo "🏥 TrackNew Health Check - $(date)"
echo "=================================="

# Check API
echo "Checking API..."
if curl -f -s "$API_URL" > /dev/null; then
    echo "✅ API is healthy"
else
    echo "❌ API is down"
    exit 1
fi

# Check database connection
echo "Checking database..."
if psql -h localhost -U tracknew_user -d tracknew_production -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database is healthy"
else
    echo "❌ Database connection failed"
    exit 1
fi

# Check Redis
echo "Checking Redis..."
if redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis is healthy"
else
    echo "❌ Redis connection failed"
    exit 1
fi

# Check disk space
echo "Checking disk space..."
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -lt 90 ]; then
    echo "✅ Disk space is healthy ($DISK_USAGE% used)"
else
    echo "⚠️ Disk space is running low ($DISK_USAGE% used)"
fi

# Check memory usage
echo "Checking memory usage..."
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$MEMORY_USAGE" -lt 90 ]; then
    echo "✅ Memory usage is healthy ($MEMORY_USAGE% used)"
else
    echo "⚠️ Memory usage is high ($MEMORY_USAGE% used)"
fi

echo "✅ Health check completed successfully"
EOF

chmod +x health-check.sh

# Performance monitoring script
cat > performance-monitor.sh << 'EOF'
#!/bin/bash

# Performance monitoring script
LOG_FILE="./logs/performance.log"

echo "$(date): Starting performance monitoring..." >> "$LOG_FILE"

# CPU usage
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
echo "$(date): CPU Usage: $CPU_USAGE%" >> "$LOG_FILE"

# Memory usage
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.1f", $3*100/$2}')
echo "$(date): Memory Usage: $MEMORY_USAGE%" >> "$LOG_FILE"

# Disk usage
DISK_USAGE=$(df / | awk 'NR==2 {print $5}')
echo "$(date): Disk Usage: $DISK_USAGE" >> "$LOG_FILE"

# API response time
API_RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:8000/health)
echo "$(date): API Response Time: ${API_RESPONSE_TIME}s" >> "$LOG_FILE"

# Database connections
DB_CONNECTIONS=$(psql -h localhost -U tracknew_user -d tracknew_production -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" 2>/dev/null || echo "0")
echo "$(date): Active DB Connections: $DB_CONNECTIONS" >> "$LOG_FILE"

echo "$(date): Performance monitoring completed" >> "$LOG_FILE"
EOF

chmod +x performance-monitor.sh

# Step 6: Backup Setup
print_header "💾 Step 6: Backup Setup"

print_status "Creating backup scripts..."

# Database backup script
cat > backup-database.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="tracknew_production"
DB_USER="tracknew_user"

mkdir -p "$BACKUP_DIR"

echo "🗄️ Starting database backup..."

# Create backup
pg_dump -h localhost -U "$DB_USER" "$DB_NAME" > "$BACKUP_DIR/db_backup_$DATE.sql"

# Compress backup
gzip "$BACKUP_DIR/db_backup_$DATE.sql"

# Remove backups older than 30 days
find "$BACKUP_DIR" -name "db_backup_*.sql.gz" -mtime +30 -delete

echo "✅ Database backup completed: db_backup_$DATE.sql.gz"
EOF

chmod +x backup-database.sh

# Files backup script
cat > backup-files.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$BACKUP_DIR"

echo "📁 Starting files backup..."

# Backup uploads and logs
tar -czf "$BACKUP_DIR/files_backup_$DATE.tar.gz" uploads logs

# Remove backups older than 7 days
find "$BACKUP_DIR" -name "files_backup_*.tar.gz" -mtime +7 -delete

echo "✅ Files backup completed: files_backup_$DATE.tar.gz"
EOF

chmod +x backup-files.sh

# Step 7: Final Checks
print_header "✅ Step 7: Final Optimization Checks"

print_status "Running final security scan..."
# Check for common security issues
if [ -f .env ]; then
    if grep -q "password123\|secret123\|your_" .env; then
        print_warning "Default passwords/secrets detected in .env file"
    fi
fi

print_status "Checking file permissions..."
# Ensure no world-writable files
WORLD_WRITABLE=$(find . -type f -perm -002 2>/dev/null | wc -l)
if [ "$WORLD_WRITABLE" -gt 0 ]; then
    print_warning "$WORLD_WRITABLE world-writable files found"
fi

print_status "Optimization completed successfully!"

print_header "📋 Next Steps:"
echo "1. Update environment variables in .env files"
echo "2. Configure SSL certificates"
echo "3. Set up monitoring and alerting"
echo "4. Schedule backup scripts"
echo "5. Run health checks"
echo "6. Deploy to production server"

print_header "🚀 TrackNew is ready for production deployment!"
echo "=================================================="
EOF
