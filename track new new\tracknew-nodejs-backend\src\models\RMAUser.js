const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const RMAUser = sequelize.define('RMAUser', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  rma_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'rmas',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  role: {
    type: DataTypes.ENUM('technician', 'inspector', 'approver', 'coordinator', 'manager', 'specialist'),
    allowNull: false
  },
  assigned_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  assigned_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  status: {
    type: DataTypes.ENUM('assigned', 'accepted', 'rejected', 'in_progress', 'completed', 'on_hold', 'cancelled'),
    defaultValue: 'assigned'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  estimated_start_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  estimated_completion_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  actual_start_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  actual_completion_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  work_description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Description of work to be performed'
  },
  completion_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Notes added upon completion'
  },
  rejection_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  skills_required: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of required skills'
  },
  tools_required: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of required tools'
  },
  parts_required: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of required parts'
  },
  work_hours: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Estimated work hours'
  },
  actual_hours: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Actual hours spent'
  },
  hourly_rate: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    comment: 'Hourly rate for this assignment'
  },
  total_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Total cost for this assignment'
  },
  location: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Work location'
  },
  contact_person: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Contact person at work location'
  },
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  special_instructions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  safety_requirements: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  quality_checklist: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of quality check items'
  },
  inspection_results: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of inspection results'
  },
  photos_before: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of before photos'
  },
  photos_after: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of after photos'
  },
  documents: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of related documents'
  },
  customer_signature: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Customer signature file path'
  },
  technician_signature: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Technician signature file path'
  },
  rating: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 5
    },
    comment: 'Customer rating for the work (1-5)'
  },
  feedback: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Customer feedback'
  },
  is_primary: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is the primary assignee'
  },
  notification_sent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  reminder_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  last_reminder_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  escalation_level: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Current escalation level'
  },
  escalated_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  escalated_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'rma_users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['rma_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['assigned_by']
    },
    {
      fields: ['role']
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['assigned_at']
    },
    {
      fields: ['is_primary']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['escalated_to']
    },
    {
      fields: ['rma_id', 'user_id', 'role'],
      unique: true
    }
  ]
});

module.exports = RMAUser;
