const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const LeadStatus = sequelize.define('LeadStatus', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  color_code: {
    type: DataTypes.STRING(7),
    allowNull: true,
    comment: 'Hex color code for status'
  },
  icon: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Icon class or image path'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_default: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is the default status for new leads'
  },
  is_closed: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this status indicates a closed lead'
  },
  is_won: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this status indicates a won lead'
  },
  is_lost: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this status indicates a lost lead'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  stage: {
    type: DataTypes.ENUM('new', 'contacted', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost'),
    defaultValue: 'new'
  },
  probability: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100
    },
    comment: 'Default probability percentage for this status'
  },
  auto_follow_up_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Automatic follow-up days when lead reaches this status'
  },
  notification_enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether to send notifications when lead reaches this status'
  },
  email_template_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Email template to use when lead reaches this status'
  },
  sms_template_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'SMS template to use when lead reaches this status'
  },
  requires_reason: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether a reason is required when setting this status'
  },
  system_status: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is a system-defined status (cannot be deleted)'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'lead_statuses',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['name'],
      unique: true
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_default']
    },
    {
      fields: ['is_closed']
    },
    {
      fields: ['is_won']
    },
    {
      fields: ['is_lost']
    },
    {
      fields: ['sort_order']
    },
    {
      fields: ['stage']
    },
    {
      fields: ['system_status']
    }
  ]
});

module.exports = LeadStatus;
