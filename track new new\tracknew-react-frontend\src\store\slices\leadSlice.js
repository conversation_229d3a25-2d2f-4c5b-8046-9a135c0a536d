import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { apiMethods } from '../../services/api';

// Async thunks
export const fetchLeads = createAsyncThunk(
  'lead/fetchLeads',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/leads', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchLead = createAsyncThunk(
  'lead/fetchLead',
  async (id, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get(`/leads/${id}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const createLead = createAsyncThunk(
  'lead/createLead',
  async (leadData, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post('/leads', leadData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateLead = createAsyncThunk(
  'lead/updateLead',
  async ({ id, data }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.put(`/leads/${id}`, data);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const deleteLead = createAsyncThunk(
  'lead/deleteLead',
  async (id, { rejectWithValue }) => {
    try {
      await apiMethods.delete(`/leads/${id}`);
      return id;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const convertLeadToCustomer = createAsyncThunk(
  'lead/convertLeadToCustomer',
  async (leadId, { rejectWithValue }) => {
    try {
      const response = await apiMethods.post(`/leads/${leadId}/convert`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateLeadStatus = createAsyncThunk(
  'lead/updateLeadStatus',
  async ({ leadId, status, notes }, { rejectWithValue }) => {
    try {
      const response = await apiMethods.patch(`/leads/${leadId}/status`, {
        status,
        notes
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchLeadStats = createAsyncThunk(
  'lead/fetchLeadStats',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await apiMethods.get('/leads/stats', { params });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

const initialState = {
  // Data
  leads: [],
  currentLead: null,
  stats: null,
  
  // Pagination
  pagination: {
    current_page: 1,
    total_pages: 1,
    total_items: 0,
    items_per_page: 15,
    has_next_page: false,
    has_prev_page: false,
  },
  
  // Loading states
  loading: false,
  leadLoading: false,
  statsLoading: false,
  actionLoading: false,
  
  // Error states
  error: null,
  leadError: null,
  statsError: null,
  actionError: null,
  
  // UI states
  selectedLeads: [],
  filters: {
    status: '',
    priority: '',
    source: '',
    assigned_to: '',
    date_from: '',
    date_to: '',
  },
  sortBy: 'created_at',
  sortOrder: 'desc',
  searchQuery: '',
  
  // Lead pipeline
  pipeline: {
    new: 0,
    contacted: 0,
    qualified: 0,
    proposal: 0,
    negotiation: 0,
    won: 0,
    lost: 0,
  },
};

const leadSlice = createSlice({
  name: 'lead',
  initialState,
  reducers: {
    // Selection actions
    selectLead: (state, action) => {
      const leadId = action.payload;
      if (!state.selectedLeads.includes(leadId)) {
        state.selectedLeads.push(leadId);
      }
    },
    deselectLead: (state, action) => {
      const leadId = action.payload;
      state.selectedLeads = state.selectedLeads.filter(id => id !== leadId);
    },
    selectAllLeads: (state) => {
      state.selectedLeads = state.leads.map(lead => lead.id);
    },
    deselectAllLeads: (state) => {
      state.selectedLeads = [];
    },
    
    // Filter actions
    setFilter: (state, action) => {
      const { key, value } = action.payload;
      state.filters[key] = value;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    // Sort actions
    setSortBy: (state, action) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action) => {
      state.sortOrder = action.payload;
    },
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    // Search actions
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    clearSearchQuery: (state) => {
      state.searchQuery = '';
    },
    
    // Clear current lead
    clearCurrentLead: (state) => {
      state.currentLead = null;
      state.leadError = null;
    },
    
    // Update pipeline
    updatePipeline: (state, action) => {
      state.pipeline = { ...state.pipeline, ...action.payload };
    },
    
    // Clear errors
    clearError: (state) => {
      state.error = null;
    },
    clearLeadError: (state) => {
      state.leadError = null;
    },
    clearStatsError: (state) => {
      state.statsError = null;
    },
    clearActionError: (state) => {
      state.actionError = null;
    },
    
    // Reset state
    resetLeadState: (state) => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    // Fetch leads
    builder
      .addCase(fetchLeads.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLeads.fulfilled, (state, action) => {
        state.loading = false;
        state.leads = action.payload.data.leads;
        state.pagination = action.payload.data.pagination;
        
        // Update pipeline if available
        if (action.payload.data.pipeline) {
          state.pipeline = action.payload.data.pipeline;
        }
      })
      .addCase(fetchLeads.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
    
    // Fetch single lead
    builder
      .addCase(fetchLead.pending, (state) => {
        state.leadLoading = true;
        state.leadError = null;
      })
      .addCase(fetchLead.fulfilled, (state, action) => {
        state.leadLoading = false;
        state.currentLead = action.payload.data.lead;
      })
      .addCase(fetchLead.rejected, (state, action) => {
        state.leadLoading = false;
        state.leadError = action.payload;
      });
    
    // Create lead
    builder
      .addCase(createLead.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(createLead.fulfilled, (state, action) => {
        state.actionLoading = false;
        state.leads.unshift(action.payload.data.lead);
      })
      .addCase(createLead.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Update lead
    builder
      .addCase(updateLead.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(updateLead.fulfilled, (state, action) => {
        state.actionLoading = false;
        const updatedLead = action.payload.data.lead;
        const index = state.leads.findIndex(lead => lead.id === updatedLead.id);
        if (index !== -1) {
          state.leads[index] = updatedLead;
        }
        if (state.currentLead?.id === updatedLead.id) {
          state.currentLead = updatedLead;
        }
      })
      .addCase(updateLead.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Delete lead
    builder
      .addCase(deleteLead.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(deleteLead.fulfilled, (state, action) => {
        state.actionLoading = false;
        const leadId = action.payload;
        state.leads = state.leads.filter(lead => lead.id !== leadId);
        state.selectedLeads = state.selectedLeads.filter(id => id !== leadId);
        if (state.currentLead?.id === leadId) {
          state.currentLead = null;
        }
      })
      .addCase(deleteLead.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Convert lead to customer
    builder
      .addCase(convertLeadToCustomer.pending, (state) => {
        state.actionLoading = true;
        state.actionError = null;
      })
      .addCase(convertLeadToCustomer.fulfilled, (state, action) => {
        state.actionLoading = false;
        const convertedLead = action.payload.data.lead;
        const index = state.leads.findIndex(lead => lead.id === convertedLead.id);
        if (index !== -1) {
          state.leads[index] = convertedLead;
        }
        if (state.currentLead?.id === convertedLead.id) {
          state.currentLead = convertedLead;
        }
      })
      .addCase(convertLeadToCustomer.rejected, (state, action) => {
        state.actionLoading = false;
        state.actionError = action.payload;
      });
    
    // Update lead status
    builder
      .addCase(updateLeadStatus.fulfilled, (state, action) => {
        const updatedLead = action.payload.data.lead;
        const index = state.leads.findIndex(lead => lead.id === updatedLead.id);
        if (index !== -1) {
          state.leads[index] = updatedLead;
        }
        if (state.currentLead?.id === updatedLead.id) {
          state.currentLead = updatedLead;
        }
      });
    
    // Fetch stats
    builder
      .addCase(fetchLeadStats.pending, (state) => {
        state.statsLoading = true;
        state.statsError = null;
      })
      .addCase(fetchLeadStats.fulfilled, (state, action) => {
        state.statsLoading = false;
        state.stats = action.payload.data;
        
        // Update pipeline if available
        if (action.payload.data.pipeline) {
          state.pipeline = action.payload.data.pipeline;
        }
      })
      .addCase(fetchLeadStats.rejected, (state, action) => {
        state.statsLoading = false;
        state.statsError = action.payload;
      });
  },
});

export const {
  selectLead,
  deselectLead,
  selectAllLeads,
  deselectAllLeads,
  setFilter,
  setFilters,
  clearFilters,
  setSortBy,
  setSortOrder,
  toggleSortOrder,
  setSearchQuery,
  clearSearchQuery,
  clearCurrentLead,
  updatePipeline,
  clearError,
  clearLeadError,
  clearStatsError,
  clearActionError,
  resetLeadState,
} = leadSlice.actions;

export default leadSlice.reducer;

// Selectors
export const selectLeads = (state) => state.lead.leads;
export const selectCurrentLead = (state) => state.lead.currentLead;
export const selectLeadStats = (state) => state.lead.stats;
export const selectLeadPipeline = (state) => state.lead.pipeline;
export const selectLeadPagination = (state) => state.lead.pagination;
export const selectLeadLoading = (state) => state.lead.loading;
export const selectLeadError = (state) => state.lead.error;
export const selectSelectedLeads = (state) => state.lead.selectedLeads;
export const selectLeadFilters = (state) => state.lead.filters;
export const selectLeadSort = (state) => ({
  sortBy: state.lead.sortBy,
  sortOrder: state.lead.sortOrder,
});
export const selectLeadSearchQuery = (state) => state.lead.searchQuery;
