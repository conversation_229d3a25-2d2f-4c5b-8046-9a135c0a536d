const { RMA, RMAItem, Customer, Product, Sales, Service, Warehouse, User, Company } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all RMAs with filtering and pagination
const getRMAs = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    status,
    rma_type,
    reason,
    priority,
    customer_id,
    product_id,
    assigned_to,
    start_date,
    end_date,
    is_warranty_valid,
    sort_by = 'rma_date',
    sort_order = 'DESC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { rma_number: { [Op.iLike]: `%${search}%` } },
      { product_name: { [Op.iLike]: `%${search}%` } },
      { product_code: { [Op.iLike]: `%${search}%` } },
      { serial_number: { [Op.iLike]: `%${search}%` } },
      { batch_number: { [Op.iLike]: `%${search}%` } },
      { reason_description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add date range filter
  if (start_date || end_date) {
    whereConditions.rma_date = {};
    if (start_date) {
      whereConditions.rma_date[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      whereConditions.rma_date[Op.lte] = new Date(end_date);
    }
  }

  // Add filters
  if (status) {
    whereConditions.status = status;
  }

  if (rma_type) {
    whereConditions.rma_type = rma_type;
  }

  if (reason) {
    whereConditions.reason = reason;
  }

  if (priority) {
    whereConditions.priority = priority;
  }

  if (customer_id) {
    whereConditions.customer_id = customer_id;
  }

  if (product_id) {
    whereConditions.product_id = product_id;
  }

  if (assigned_to) {
    whereConditions.assigned_to = assigned_to;
  }

  if (is_warranty_valid !== undefined) {
    whereConditions.is_warranty_valid = is_warranty_valid === 'true';
  }

  // Get RMAs with associations
  const { count, rows: rmas } = await RMA.findAndCountAll({
    where: whereConditions,
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'email', 'mobile_number']
      },
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'product_name', 'product_code', 'sku']
      },
      {
        model: Product,
        as: 'replacementProduct',
        attributes: ['id', 'product_name', 'product_code', 'sku']
      },
      {
        model: Sales,
        as: 'sales',
        attributes: ['id', 'sales_number', 'sales_date', 'total_amount']
      },
      {
        model: Service,
        as: 'service',
        attributes: ['id', 'service_number', 'service_date', 'service_status']
      },
      {
        model: Warehouse,
        as: 'warehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code']
      },
      {
        model: User,
        as: 'assignedTo',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updater',
        attributes: ['id', 'name', 'email']
      },
      {
        model: RMAItem,
        as: 'items',
        attributes: ['id', 'item_name', 'quantity', 'unit_price', 'total_amount', 'status']
      }
    ],
    limit: parseInt(limit),
    offset: offset,
    order: [[sort_by, sort_order.toUpperCase()]],
    distinct: true
  });

  // Calculate pagination
  const totalPages = Math.ceil(count / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      rmas,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single RMA by ID
const getRMA = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const rma = await RMA.findOne({
    where: {
      id,
      company_id: companyId
    },
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'email', 'mobile_number', 'address']
      },
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'product_name', 'product_code', 'sku', 'warranty_period']
      },
      {
        model: Product,
        as: 'replacementProduct',
        attributes: ['id', 'product_name', 'product_code', 'sku']
      },
      {
        model: Sales,
        as: 'sales',
        attributes: ['id', 'sales_number', 'sales_date', 'total_amount', 'customer_id']
      },
      {
        model: Service,
        as: 'service',
        attributes: ['id', 'service_number', 'service_date', 'service_status', 'customer_id']
      },
      {
        model: Warehouse,
        as: 'warehouse',
        attributes: ['id', 'warehouse_name', 'warehouse_code', 'address']
      },
      {
        model: User,
        as: 'assignedTo',
        attributes: ['id', 'name', 'email', 'phone']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updater',
        attributes: ['id', 'name', 'email']
      },
      {
        model: RMAItem,
        as: 'items',
        include: [
          {
            model: Product,
            as: 'product',
            attributes: ['id', 'product_name', 'product_code', 'sku']
          },
          {
            model: Product,
            as: 'replacementProduct',
            attributes: ['id', 'product_name', 'product_code', 'sku']
          },
          {
            model: User,
            as: 'inspectedBy',
            attributes: ['id', 'name', 'email']
          }
        ]
      }
    ]
  });

  if (!rma) {
    return next(new AppError('RMA not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      rma
    }
  });
});

// Create new RMA
const createRMA = catchAsync(async (req, res, next) => {
  const {
    customer_id,
    product_id,
    sales_id,
    service_id,
    rma_date = new Date(),
    rma_type,
    reason,
    reason_description,
    product_name,
    product_code,
    serial_number,
    batch_number,
    quantity = 1,
    unit_price = 0,
    priority = 'medium',
    expected_resolution_date,
    warehouse_id,
    assigned_to,
    customer_notes,
    internal_notes,
    attachments = [],
    is_warranty_valid = false,
    warranty_expiry_date,
    items = []
  } = req.body;

  const companyId = req.user.company_id;

  // Validate customer
  const customer = await Customer.findOne({
    where: {
      id: customer_id,
      company_id: companyId
    }
  });

  if (!customer) {
    return next(new AppError('Customer not found', 404));
  }

  // Validate product if provided
  if (product_id) {
    const product = await Product.findOne({
      where: {
        id: product_id,
        company_id: companyId
      }
    });

    if (!product) {
      return next(new AppError('Product not found', 404));
    }
  }

  // Validate sales if provided
  if (sales_id) {
    const sales = await Sales.findOne({
      where: {
        id: sales_id,
        company_id: companyId
      }
    });

    if (!sales) {
      return next(new AppError('Sales record not found', 404));
    }
  }

  // Validate service if provided
  if (service_id) {
    const service = await Service.findOne({
      where: {
        id: service_id,
        company_id: companyId
      }
    });

    if (!service) {
      return next(new AppError('Service record not found', 404));
    }
  }

  // Validate warehouse if provided
  if (warehouse_id) {
    const warehouse = await Warehouse.findOne({
      where: {
        id: warehouse_id,
        company_id: companyId
      }
    });

    if (!warehouse) {
      return next(new AppError('Warehouse not found', 404));
    }
  }

  // Validate assigned user if provided
  if (assigned_to) {
    const assignedUser = await User.findOne({
      where: {
        id: assigned_to,
        company_id: companyId,
        is_active: true
      }
    });

    if (!assignedUser) {
      return next(new AppError('Assigned user not found or inactive', 404));
    }
  }

  // Calculate total amount
  const total_amount = quantity * unit_price;

  const rma = await RMA.create({
    company_id: companyId,
    customer_id,
    product_id,
    sales_id,
    service_id,
    rma_date: new Date(rma_date),
    rma_type,
    reason,
    reason_description,
    product_name,
    product_code,
    serial_number,
    batch_number,
    quantity,
    unit_price,
    total_amount,
    priority,
    expected_resolution_date: expected_resolution_date ? new Date(expected_resolution_date) : null,
    warehouse_id,
    assigned_to,
    customer_notes,
    internal_notes,
    attachments,
    is_warranty_valid,
    warranty_expiry_date: warranty_expiry_date ? new Date(warranty_expiry_date) : null,
    created_by: req.user.id
  });

  // Create RMA items if provided
  if (items && items.length > 0) {
    const rmaItems = items.map(item => ({
      rma_id: rma.id,
      ...item
    }));

    await RMAItem.bulkCreate(rmaItems);
  }

  // Fetch the created RMA with associations
  const createdRMA = await RMA.findByPk(rma.id, {
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'email', 'mobile_number']
      },
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'product_name', 'product_code', 'sku']
      },
      {
        model: User,
        as: 'assignedTo',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: RMAItem,
        as: 'items'
      }
    ]
  });

  res.status(201).json({
    status: 'success',
    data: {
      rma: createdRMA
    }
  });
});

// Update RMA
const updateRMA = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const rma = await RMA.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!rma) {
    return next(new AppError('RMA not found', 404));
  }

  // Handle date fields
  const dateFields = ['rma_date', 'expected_resolution_date', 'actual_resolution_date', 'received_date', 'inspection_date', 'warranty_expiry_date'];
  dateFields.forEach(field => {
    if (req.body[field]) {
      req.body[field] = new Date(req.body[field]);
    }
  });

  // Calculate total amount if quantity or unit_price changed
  if (req.body.quantity !== undefined || req.body.unit_price !== undefined) {
    const quantity = req.body.quantity !== undefined ? req.body.quantity : rma.quantity;
    const unit_price = req.body.unit_price !== undefined ? req.body.unit_price : rma.unit_price;
    req.body.total_amount = quantity * unit_price;
  }

  // Update RMA
  await rma.update({
    ...req.body,
    updated_by: req.user.id
  });

  // Fetch updated RMA with associations
  const updatedRMA = await RMA.findByPk(id, {
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'email', 'mobile_number']
      },
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'product_name', 'product_code', 'sku']
      },
      {
        model: User,
        as: 'assignedTo',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'name', 'email']
      },
      {
        model: User,
        as: 'updater',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      rma: updatedRMA
    }
  });
});

// Delete RMA
const deleteRMA = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  const rma = await RMA.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!rma) {
    return next(new AppError('RMA not found', 404));
  }

  // Check if RMA can be deleted (only pending or cancelled RMAs)
  if (!['pending', 'cancelled'].includes(rma.status)) {
    return next(new AppError('Cannot delete RMA that is in progress or completed', 400));
  }

  // Delete associated RMA items first
  await RMAItem.destroy({
    where: { rma_id: id }
  });

  await rma.destroy();

  res.status(200).json({
    status: 'success',
    message: 'RMA deleted successfully'
  });
});

// Update RMA status
const updateRMAStatus = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { status, resolution_type, resolution_notes, refund_amount, replacement_product_id, repair_cost } = req.body;
  const companyId = req.user.company_id;

  const rma = await RMA.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!rma) {
    return next(new AppError('RMA not found', 404));
  }

  const updateData = {
    status,
    updated_by: req.user.id
  };

  // Handle status-specific updates
  if (status === 'received') {
    updateData.received_date = new Date();
  } else if (status === 'inspected') {
    updateData.inspection_date = new Date();
  } else if (status === 'completed') {
    updateData.actual_resolution_date = new Date();
    if (resolution_type) updateData.resolution_type = resolution_type;
    if (resolution_notes) updateData.resolution_notes = resolution_notes;
    if (refund_amount !== undefined) updateData.refund_amount = refund_amount;
    if (replacement_product_id) updateData.replacement_product_id = replacement_product_id;
    if (repair_cost !== undefined) updateData.repair_cost = repair_cost;
  }

  await rma.update(updateData);

  res.status(200).json({
    status: 'success',
    message: 'RMA status updated successfully',
    data: {
      rma: {
        id: rma.id,
        rma_number: rma.rma_number,
        status: rma.status,
        resolution_type: rma.resolution_type
      }
    }
  });
});

// Get RMA statistics
const getRMAStats = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const { start_date, end_date } = req.query;

  // Build date filter
  const dateFilter = {};
  if (start_date || end_date) {
    dateFilter.rma_date = {};
    if (start_date) {
      dateFilter.rma_date[Op.gte] = new Date(start_date);
    }
    if (end_date) {
      dateFilter.rma_date[Op.lte] = new Date(end_date);
    }
  }

  // Total RMAs count
  const totalRMAs = await RMA.count({
    where: {
      company_id: companyId,
      ...dateFilter
    }
  });

  // RMAs by status
  const rmasByStatus = await RMA.findAll({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      'status',
      [RMA.sequelize.fn('COUNT', RMA.sequelize.col('id')), 'count']
    ],
    group: ['status'],
    raw: true
  });

  // RMAs by type
  const rmasByType = await RMA.findAll({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      'rma_type',
      [RMA.sequelize.fn('COUNT', RMA.sequelize.col('id')), 'count']
    ],
    group: ['rma_type'],
    raw: true
  });

  // RMAs by reason
  const rmasByReason = await RMA.findAll({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      'reason',
      [RMA.sequelize.fn('COUNT', RMA.sequelize.col('id')), 'count']
    ],
    group: ['reason'],
    order: [[RMA.sequelize.fn('COUNT', RMA.sequelize.col('id')), 'DESC']],
    raw: true
  });

  // RMAs by priority
  const rmasByPriority = await RMA.findAll({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      'priority',
      [RMA.sequelize.fn('COUNT', RMA.sequelize.col('id')), 'count']
    ],
    group: ['priority'],
    raw: true
  });

  // Warranty vs Non-warranty
  const warrantyStats = await RMA.findAll({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    attributes: [
      'is_warranty_valid',
      [RMA.sequelize.fn('COUNT', RMA.sequelize.col('id')), 'count']
    ],
    group: ['is_warranty_valid'],
    raw: true
  });

  // Average resolution time (in days)
  const avgResolutionTime = await RMA.findOne({
    where: {
      company_id: companyId,
      status: 'completed',
      actual_resolution_date: { [Op.ne]: null },
      ...dateFilter
    },
    attributes: [
      [RMA.sequelize.fn('AVG',
        RMA.sequelize.fn('EXTRACT',
          RMA.sequelize.literal('EPOCH FROM (actual_resolution_date - rma_date)')
        )
      ), 'avg_seconds']
    ],
    raw: true
  });

  // Most problematic products
  const problematicProducts = await RMA.findAll({
    where: {
      company_id: companyId,
      ...dateFilter
    },
    include: [{
      model: Product,
      as: 'product',
      attributes: ['id', 'product_name', 'product_code']
    }],
    attributes: [
      'product_id',
      [RMA.sequelize.fn('COUNT', RMA.sequelize.col('RMA.id')), 'rma_count']
    ],
    group: ['product_id', 'product.id'],
    order: [[RMA.sequelize.fn('COUNT', RMA.sequelize.col('RMA.id')), 'DESC']],
    limit: 5,
    raw: false
  });

  // Total refund amount
  const totalRefunds = await RMA.findOne({
    where: {
      company_id: companyId,
      refund_amount: { [Op.ne]: null },
      ...dateFilter
    },
    attributes: [
      [RMA.sequelize.fn('SUM', RMA.sequelize.col('refund_amount')), 'total_refunds']
    ],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_rmas: totalRMAs,
      rmas_by_status: rmasByStatus,
      rmas_by_type: rmasByType,
      rmas_by_reason: rmasByReason,
      rmas_by_priority: rmasByPriority,
      warranty_stats: warrantyStats,
      avg_resolution_days: avgResolutionTime?.avg_seconds ? Math.round(avgResolutionTime.avg_seconds / 86400) : 0,
      problematic_products: problematicProducts,
      total_refunds: parseFloat(totalRefunds?.total_refunds || 0)
    }
  });
});

// Assign RMA to user
const assignRMA = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { assigned_to } = req.body;
  const companyId = req.user.company_id;

  const rma = await RMA.findOne({
    where: {
      id,
      company_id: companyId
    }
  });

  if (!rma) {
    return next(new AppError('RMA not found', 404));
  }

  // Validate assigned user
  const assignedUser = await User.findOne({
    where: {
      id: assigned_to,
      company_id: companyId,
      is_active: true
    }
  });

  if (!assignedUser) {
    return next(new AppError('Assigned user not found or inactive', 404));
  }

  await rma.update({
    assigned_to,
    updated_by: req.user.id
  });

  res.status(200).json({
    status: 'success',
    message: 'RMA assigned successfully',
    data: {
      rma: {
        id: rma.id,
        rma_number: rma.rma_number,
        assigned_to: assigned_to,
        assigned_user: {
          id: assignedUser.id,
          name: assignedUser.name,
          email: assignedUser.email
        }
      }
    }
  });
});

// Bulk update RMA status
const bulkUpdateRMAStatus = catchAsync(async (req, res, next) => {
  const { rma_ids, status } = req.body;
  const companyId = req.user.company_id;

  if (!Array.isArray(rma_ids) || rma_ids.length === 0) {
    return next(new AppError('RMA IDs array is required', 400));
  }

  // Update RMAs
  const [updatedCount] = await RMA.update(
    {
      status,
      updated_by: req.user.id
    },
    {
      where: {
        id: { [Op.in]: rma_ids },
        company_id: companyId
      }
    }
  );

  res.status(200).json({
    status: 'success',
    message: `${updatedCount} RMA(s) updated successfully`,
    data: {
      updated_count: updatedCount
    }
  });
});

module.exports = {
  getRMAs,
  getRMA,
  createRMA,
  updateRMA,
  deleteRMA,
  updateRMAStatus,
  getRMAStats,
  assignRMA,
  bulkUpdateRMAStatus
};
