import React from 'react';
import { useSelector } from 'react-redux';
import { Navigate } from 'react-router-dom';
import { selectUser, selectAuthLoading } from '../../store/slices/authSlice';
import { LoadingSpinner } from '../ui';

const PublicRoute = ({ 
  children, 
  redirectPath = '/dashboard',
  restricted = true 
}) => {
  const user = useSelector(selectUser);
  const loading = useSelector(selectAuthLoading);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" text="Loading..." />
      </div>
    );
  }

  // If route is restricted and user is authenticated, redirect to dashboard
  if (restricted && user) {
    return <Navigate to={redirectPath} replace />;
  }

  // Allow access to public route
  return children;
};

export default PublicRoute;
