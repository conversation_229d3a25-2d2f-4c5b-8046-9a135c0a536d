import React, { forwardRef } from 'react';
import { CalendarDaysIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { classNames, formatDate } from '../../utils/helpers';

const DatePicker = forwardRef(({
  label,
  value,
  onChange,
  onBlur,
  onFocus,
  error,
  helperText,
  required = false,
  disabled = false,
  readOnly = false,
  autoFocus = false,
  min,
  max,
  placeholder = 'Select date...',
  showTime = false,
  clearable = false,
  size = 'md',
  format = 'medium',
  className = '',
  inputClassName = '',
  labelClassName = '',
  errorClassName = '',
  helperClassName = '',
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = React.useState(false);
  
  const inputId = React.useId();
  const errorId = React.useId();
  const helperId = React.useId();

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const baseInputClasses = classNames(
    'block w-full rounded-md border shadow-sm transition-colors duration-200',
    'placeholder-gray-400 text-gray-900 dark:text-white',
    'dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400',
    'focus:outline-none focus:ring-1 focus:border-blue-500 focus:ring-blue-500',
    'pr-10', // Space for calendar icon
    sizeClasses[size],
    error 
      ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-500' 
      : 'border-gray-300',
    disabled && 'bg-gray-50 text-gray-500 cursor-not-allowed dark:bg-gray-800',
    readOnly && 'bg-gray-50 cursor-default dark:bg-gray-800',
    clearable && value && 'pr-16', // Extra space for clear button
    inputClassName
  );

  const handleFocus = (e) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const handleChange = (e) => {
    const newValue = e.target.value;
    onChange?.(newValue);
  };

  const handleClear = (e) => {
    e.stopPropagation();
    onChange?.('');
  };

  const getInputType = () => {
    if (showTime) {
      return 'datetime-local';
    }
    return 'date';
  };

  const getDisplayValue = () => {
    if (!value) return '';
    
    // For date inputs, return the value as-is
    if (typeof value === 'string') {
      return value;
    }
    
    // For Date objects, format appropriately
    if (value instanceof Date) {
      if (showTime) {
        // Format for datetime-local input (YYYY-MM-DDTHH:MM)
        const year = value.getFullYear();
        const month = String(value.getMonth() + 1).padStart(2, '0');
        const day = String(value.getDate()).padStart(2, '0');
        const hours = String(value.getHours()).padStart(2, '0');
        const minutes = String(value.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day}T${hours}:${minutes}`;
      } else {
        // Format for date input (YYYY-MM-DD)
        const year = value.getFullYear();
        const month = String(value.getMonth() + 1).padStart(2, '0');
        const day = String(value.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      }
    }
    
    return value;
  };

  const getFormattedDisplayValue = () => {
    if (!value) return placeholder;
    
    try {
      const date = typeof value === 'string' ? new Date(value) : value;
      if (isNaN(date.getTime())) return value;
      
      if (showTime) {
        return new Intl.DateTimeFormat('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
        }).format(date);
      } else {
        return formatDate(date, format);
      }
    } catch {
      return value;
    }
  };

  return (
    <div className={classNames('w-full', className)}>
      {/* Label */}
      {label && (
        <label 
          htmlFor={inputId}
          className={classNames(
            'block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1',
            required && "after:content-['*'] after:ml-0.5 after:text-red-500",
            labelClassName
          )}
        >
          {label}
        </label>
      )}

      {/* Input Container */}
      <div className="relative">
        <input
          ref={ref}
          id={inputId}
          type={getInputType()}
          value={getDisplayValue()}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          readOnly={readOnly}
          autoFocus={autoFocus}
          min={min}
          max={max}
          className={baseInputClasses}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={classNames(
            error && errorId,
            helperText && helperId
          )}
          {...props}
        />

        {/* Icons */}
        <div className="absolute inset-y-0 right-0 flex items-center">
          {/* Clear button */}
          {clearable && value && !disabled && !readOnly && (
            <button
              type="button"
              onClick={handleClear}
              className="mr-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
              tabIndex={-1}
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          )}

          {/* Calendar icon */}
          <div className="mr-3 pointer-events-none">
            <CalendarDaysIcon className="h-5 w-5 text-gray-400" />
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <p 
          id={errorId}
          className={classNames(
            'mt-1 text-sm text-red-600 dark:text-red-400',
            errorClassName
          )}
        >
          {error}
        </p>
      )}

      {/* Helper Text */}
      {helperText && !error && (
        <p 
          id={helperId}
          className={classNames(
            'mt-1 text-sm text-gray-500 dark:text-gray-400',
            helperClassName
          )}
        >
          {helperText}
        </p>
      )}
    </div>
  );
});

DatePicker.displayName = 'DatePicker';

export default DatePicker;
