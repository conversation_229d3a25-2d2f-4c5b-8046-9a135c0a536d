const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Permission = sequelize.define('Permission', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  },
  display_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  guard_name: {
    type: DataTypes.STRING(255),
    defaultValue: 'web'
  },
  module: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Module this permission belongs to (e.g., users, customers, products)'
  },
  action: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Action type (e.g., create, read, update, delete, list, export)'
  },
  resource: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Specific resource within the module'
  },
  permission_type: {
    type: DataTypes.ENUM('system', 'module', 'feature', 'data', 'api'),
    defaultValue: 'module',
    comment: 'Type of permission'
  },
  category: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Permission category for grouping'
  },
  level: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    comment: 'Permission level (1 = basic, 5 = advanced)'
  },
  is_system_permission: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is a system-defined permission'
  },
  is_dangerous: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this permission is considered dangerous'
  },
  requires_approval: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether granting this permission requires approval'
  },
  parent_permission_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'permissions',
      key: 'id'
    },
    comment: 'Parent permission for hierarchical permissions'
  },
  dependencies: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of permission IDs this permission depends on'
  },
  conflicts: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of permission IDs that conflict with this permission'
  },
  conditions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of conditions for this permission'
  },
  scope: {
    type: DataTypes.ENUM('global', 'company', 'department', 'team', 'own'),
    defaultValue: 'own',
    comment: 'Scope of data access for this permission'
  },
  data_filters: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of data filtering rules'
  },
  field_permissions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of field-level permissions'
  },
  time_restrictions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of time-based restrictions'
  },
  ip_restrictions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of IP restrictions'
  },
  rate_limit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Rate limit for this permission (requests per minute)'
  },
  audit_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether actions using this permission should be audited'
  },
  notification_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether to send notifications when this permission is used'
  },
  approval_workflow: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object defining approval workflow for this permission'
  },
  risk_level: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
    defaultValue: 'low',
    comment: 'Risk level associated with this permission'
  },
  compliance_tags: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of compliance tags (GDPR, SOX, etc.)'
  },
  api_endpoints: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of API endpoints this permission grants access to'
  },
  ui_elements: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of UI elements this permission controls'
  },
  menu_items: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of menu items this permission grants access to'
  },
  reports: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of reports this permission grants access to'
  },
  export_formats: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of allowed export formats'
  },
  import_sources: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of allowed import sources'
  },
  bulk_operations: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether bulk operations are allowed'
  },
  max_records: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum number of records that can be processed'
  },
  custom_validations: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of custom validation rules'
  },
  usage_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of roles using this permission'
  },
  last_used: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Last time this permission was used'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  valid_from: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Permission validity start date'
  },
  valid_until: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Permission validity end date'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'permissions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['name'],
      unique: true
    },
    {
      fields: ['guard_name']
    },
    {
      fields: ['module']
    },
    {
      fields: ['action']
    },
    {
      fields: ['permission_type']
    },
    {
      fields: ['category']
    },
    {
      fields: ['level']
    },
    {
      fields: ['is_system_permission']
    },
    {
      fields: ['is_dangerous']
    },
    {
      fields: ['parent_permission_id']
    },
    {
      fields: ['scope']
    },
    {
      fields: ['risk_level']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['sort_order']
    },
    {
      fields: ['module', 'action', 'resource'],
      unique: true
    }
  ]
});

module.exports = Permission;
