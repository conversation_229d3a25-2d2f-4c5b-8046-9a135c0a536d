const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const SalesItem = sequelize.define('SalesItem', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  sales_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'sales',
      key: 'id'
    }
  },
  product_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  item_type: {
    type: DataTypes.ENUM('product', 'service', 'discount', 'shipping', 'tax'),
    defaultValue: 'product'
  },
  item_name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'Product/service name at time of sale'
  },
  item_description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  item_code: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Product/service code at time of sale'
  },
  hsn_code: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: 'HSN/SAC code for tax purposes'
  },
  unit_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'units',
      key: 'id'
    }
  },
  unit_name: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Unit name at time of sale'
  },
  quantity: {
    type: DataTypes.DECIMAL(10, 3),
    allowNull: false,
    defaultValue: 1.000
  },
  unit_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  line_total: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'quantity * unit_price'
  },
  discount_type: {
    type: DataTypes.ENUM('percentage', 'fixed'),
    allowNull: true
  },
  discount_value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00
  },
  discount_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  taxable_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'line_total - discount_amount'
  },
  tax_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: 'Tax rate percentage'
  },
  tax_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  total_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'taxable_amount + tax_amount'
  },
  warehouse_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'warehouses',
      key: 'id'
    }
  },
  batch_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  serial_number: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  expiry_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  manufacturing_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  warranty_period: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Warranty period in months'
  },
  warranty_start_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  warranty_end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  delivery_status: {
    type: DataTypes.ENUM('pending', 'partial', 'delivered', 'returned'),
    defaultValue: 'pending'
  },
  delivered_quantity: {
    type: DataTypes.DECIMAL(10, 3),
    defaultValue: 0.000
  },
  delivery_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  return_quantity: {
    type: DataTypes.DECIMAL(10, 3),
    defaultValue: 0.000
  },
  return_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  cost_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Cost price for profit calculation'
  },
  margin_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Profit margin amount'
  },
  margin_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Profit margin percentage'
  },
  commission_rate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Commission rate for this item'
  },
  commission_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  custom_fields: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object for custom field values'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  is_free_item: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this is a free/promotional item'
  },
  parent_item_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'sales_items',
      key: 'id'
    },
    comment: 'Reference to parent item for free items'
  },
  is_service_item: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  service_start_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  service_end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  service_frequency: {
    type: DataTypes.ENUM('one_time', 'weekly', 'monthly', 'quarterly', 'yearly'),
    defaultValue: 'one_time'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'sales_items',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['sales_id']
    },
    {
      fields: ['product_id']
    },
    {
      fields: ['warehouse_id']
    },
    {
      fields: ['item_type']
    },
    {
      fields: ['delivery_status']
    },
    {
      fields: ['serial_number']
    },
    {
      fields: ['batch_number']
    },
    {
      fields: ['is_free_item']
    },
    {
      fields: ['parent_item_id']
    },
    {
      fields: ['sort_order']
    }
  ]
});

module.exports = SalesItem;
