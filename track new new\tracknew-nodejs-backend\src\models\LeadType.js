const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const LeadType = sequelize.define('LeadType', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  color_code: {
    type: DataTypes.STRING(7),
    allowNull: true,
    comment: 'Hex color code for lead type'
  },
  icon: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Icon class or image path'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  default_priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  default_temperature: {
    type: DataTypes.ENUM('cold', 'warm', 'hot'),
    defaultValue: 'warm'
  },
  auto_assign: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether to auto-assign leads of this type'
  },
  auto_assign_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Default user to auto-assign to'
  },
  follow_up_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Default follow-up days for this lead type'
  },
  conversion_target_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Target days for conversion'
  },
  email_template_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Default email template for this lead type'
  },
  sms_template_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Default SMS template for this lead type'
  },
  requires_approval: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether leads of this type require approval'
  },
  notification_enabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether to send notifications for this lead type'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'lead_types',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['company_id', 'name'],
      unique: true
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['sort_order']
    },
    {
      fields: ['auto_assign_to']
    }
  ]
});

module.exports = LeadType;
