{"date":"Wed May 28 2025 14:06:22 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":155221.703},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":56279040,"heapUsed":25259504,"rss":72876032},"pid":21020,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:06:22","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:06:32 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":155230.89},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":56016896,"heapUsed":25026848,"rss":73162752},"pid":6336,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:06:32","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:08:18 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":155337.484},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385355,"heapTotal":55754752,"heapUsed":26059992,"rss":72912896},"pid":19444,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:08:18","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:08:25 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":155344.062},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385355,"heapTotal":55230464,"heapUsed":26106408,"rss":72482816},"pid":5164,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:08:25","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:43:20 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157439.828},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":55754752,"heapUsed":25199648,"rss":77025280},"pid":16796,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:43:21","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:44:14 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157492.859},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385355,"heapTotal":55754752,"heapUsed":26077568,"rss":78491648},"pid":6112,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:44:14","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:44:23 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157502.25},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385355,"heapTotal":55492608,"heapUsed":26302992,"rss":76935168},"pid":19604,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:44:23","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:44:31 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157510.171},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65922,"external":3264137,"heapTotal":36880384,"heapUsed":26604360,"rss":74547200},"pid":3012,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:44:31","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:46:11 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157610.281},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":56279040,"heapUsed":24887216,"rss":77393920},"pid":14548,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:46:11","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:46:55 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157654.796},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385355,"heapTotal":55439360,"heapUsed":28203760,"rss":76722176},"pid":1888,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:46:55","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:48:19 GMT+0530 (India Standard Time)","error":{"name":"SequelizeAssociationError"},"exception":true,"level":"error","message":"uncaughtException: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\nSequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157738.187},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":75677696,"heapUsed":47867056,"rss":*********},"pid":17556,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"SequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:48:19","trace":[{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js","function":"new Association","line":13,"method":null,"native":false},{"column":5,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js","function":"new BelongsTo","line":25,"method":null,"native":false},{"column":25,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js","function":null,"line":70,"method":null,"native":false},{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":"defineAssociations","line":323,"method":null,"native":false},{"column":1,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":379,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:48:37 GMT+0530 (India Standard Time)","error":{"name":"SequelizeAssociationError"},"exception":true,"level":"error","message":"uncaughtException: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\nSequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157756.015},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":75677696,"heapUsed":47647040,"rss":*********},"pid":20972,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"SequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:48:37","trace":[{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js","function":"new Association","line":13,"method":null,"native":false},{"column":5,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js","function":"new BelongsTo","line":25,"method":null,"native":false},{"column":25,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js","function":null,"line":70,"method":null,"native":false},{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":"defineAssociations","line":323,"method":null,"native":false},{"column":1,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":379,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:48:57 GMT+0530 (India Standard Time)","error":{"name":"SequelizeAssociationError"},"exception":true,"level":"error","message":"uncaughtException: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\nSequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157776.656},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":76201984,"heapUsed":47731448,"rss":*********},"pid":19696,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"SequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:48:57","trace":[{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js","function":"new Association","line":13,"method":null,"native":false},{"column":5,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js","function":"new BelongsTo","line":25,"method":null,"native":false},{"column":25,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js","function":null,"line":70,"method":null,"native":false},{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":"defineAssociations","line":323,"method":null,"native":false},{"column":1,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":379,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:49:38 GMT+0530 (India Standard Time)","error":{"name":"SequelizeAssociationError"},"exception":true,"level":"error","message":"uncaughtException: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\nSequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157817.484},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65922,"external":3264136,"heapTotal":76201984,"heapUsed":45849288,"rss":*********},"pid":18148,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"SequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:49:38","trace":[{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js","function":"new Association","line":13,"method":null,"native":false},{"column":5,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js","function":"new BelongsTo","line":25,"method":null,"native":false},{"column":25,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js","function":null,"line":70,"method":null,"native":false},{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":"defineAssociations","line":323,"method":null,"native":false},{"column":1,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":379,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:50:02 GMT+0530 (India Standard Time)","error":{"name":"SequelizeAssociationError"},"exception":true,"level":"error","message":"uncaughtException: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\nSequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157841.343},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":75362304,"heapUsed":50182888,"rss":*********},"pid":8340,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"SequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:50:02","trace":[{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js","function":"new Association","line":13,"method":null,"native":false},{"column":5,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js","function":"new BelongsTo","line":25,"method":null,"native":false},{"column":25,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js","function":null,"line":70,"method":null,"native":false},{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":"defineAssociations","line":323,"method":null,"native":false},{"column":1,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":379,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:50:02 GMT+0530 (India Standard Time)","error":{"name":"SequelizeAssociationError"},"exception":true,"level":"error","message":"uncaughtException: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\nSequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157841.359},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":75677696,"heapUsed":46908472,"rss":*********},"pid":20268,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"SequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:50:02","trace":[{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js","function":"new Association","line":13,"method":null,"native":false},{"column":5,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js","function":"new BelongsTo","line":25,"method":null,"native":false},{"column":25,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js","function":null,"line":70,"method":null,"native":false},{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":"defineAssociations","line":323,"method":null,"native":false},{"column":1,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":379,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:51:13 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157912.656},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56943624,"rss":117645312},"pid":14504,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:51:13","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:51:13 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157912.656},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79609856,"heapUsed":57091760,"rss":117256192},"pid":5132,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:51:13","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:52:35 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157994.031},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56898696,"rss":117723136},"pid":12808,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:52:35","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:53:12 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158031.343},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65922,"external":3264136,"heapTotal":80396288,"heapUsed":55123920,"rss":117846016},"pid":3756,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:53:12","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:53:42 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158061.39},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56046128,"rss":117702656},"pid":21320,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:53:42","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:53:42 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158061.546},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80396288,"heapUsed":56429336,"rss":117841920},"pid":17096,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:53:42","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:53:42 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158061.562},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":57161040,"rss":117563392},"pid":8636,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:53:42","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
