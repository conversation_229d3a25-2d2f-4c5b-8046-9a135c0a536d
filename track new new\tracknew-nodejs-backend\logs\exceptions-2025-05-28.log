{"date":"Wed May 28 2025 14:06:22 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":155221.703},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":56279040,"heapUsed":25259504,"rss":72876032},"pid":21020,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:06:22","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:06:32 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":155230.89},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":56016896,"heapUsed":25026848,"rss":73162752},"pid":6336,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:06:32","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:08:18 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":155337.484},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385355,"heapTotal":55754752,"heapUsed":26059992,"rss":72912896},"pid":19444,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:08:18","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:08:25 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":155344.062},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385355,"heapTotal":55230464,"heapUsed":26106408,"rss":72482816},"pid":5164,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:08:25","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:43:20 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157439.828},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":55754752,"heapUsed":25199648,"rss":77025280},"pid":16796,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:43:21","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:44:14 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157492.859},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385355,"heapTotal":55754752,"heapUsed":26077568,"rss":78491648},"pid":6112,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:44:14","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:44:23 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157502.25},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385355,"heapTotal":55492608,"heapUsed":26302992,"rss":76935168},"pid":19604,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:44:23","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:44:31 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157510.171},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65922,"external":3264137,"heapTotal":36880384,"heapUsed":26604360,"rss":74547200},"pid":3012,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:44:31","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:46:11 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157610.281},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":56279040,"heapUsed":24887216,"rss":77393920},"pid":14548,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:46:11","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:46:55 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157654.796},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385355,"heapTotal":55439360,"heapUsed":28203760,"rss":76722176},"pid":1888,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module './RMAAdditionalProduct'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:47:30)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:46:55","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":30,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":47,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:48:19 GMT+0530 (India Standard Time)","error":{"name":"SequelizeAssociationError"},"exception":true,"level":"error","message":"uncaughtException: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\nSequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157738.187},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":75677696,"heapUsed":47867056,"rss":*********},"pid":17556,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"SequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:48:19","trace":[{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js","function":"new Association","line":13,"method":null,"native":false},{"column":5,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js","function":"new BelongsTo","line":25,"method":null,"native":false},{"column":25,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js","function":null,"line":70,"method":null,"native":false},{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":"defineAssociations","line":323,"method":null,"native":false},{"column":1,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":379,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:48:37 GMT+0530 (India Standard Time)","error":{"name":"SequelizeAssociationError"},"exception":true,"level":"error","message":"uncaughtException: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\nSequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157756.015},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":75677696,"heapUsed":47647040,"rss":*********},"pid":20972,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"SequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:48:37","trace":[{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js","function":"new Association","line":13,"method":null,"native":false},{"column":5,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js","function":"new BelongsTo","line":25,"method":null,"native":false},{"column":25,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js","function":null,"line":70,"method":null,"native":false},{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":"defineAssociations","line":323,"method":null,"native":false},{"column":1,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":379,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:48:57 GMT+0530 (India Standard Time)","error":{"name":"SequelizeAssociationError"},"exception":true,"level":"error","message":"uncaughtException: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\nSequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157776.656},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":76201984,"heapUsed":47731448,"rss":*********},"pid":19696,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"SequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:48:57","trace":[{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js","function":"new Association","line":13,"method":null,"native":false},{"column":5,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js","function":"new BelongsTo","line":25,"method":null,"native":false},{"column":25,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js","function":null,"line":70,"method":null,"native":false},{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":"defineAssociations","line":323,"method":null,"native":false},{"column":1,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":379,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:49:38 GMT+0530 (India Standard Time)","error":{"name":"SequelizeAssociationError"},"exception":true,"level":"error","message":"uncaughtException: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\nSequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157817.484},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65922,"external":3264136,"heapTotal":76201984,"heapUsed":45849288,"rss":*********},"pid":18148,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"SequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:49:38","trace":[{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js","function":"new Association","line":13,"method":null,"native":false},{"column":5,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js","function":"new BelongsTo","line":25,"method":null,"native":false},{"column":25,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js","function":null,"line":70,"method":null,"native":false},{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":"defineAssociations","line":323,"method":null,"native":false},{"column":1,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":379,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:50:02 GMT+0530 (India Standard Time)","error":{"name":"SequelizeAssociationError"},"exception":true,"level":"error","message":"uncaughtException: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\nSequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157841.343},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":75362304,"heapUsed":50182888,"rss":*********},"pid":8340,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"SequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:50:02","trace":[{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js","function":"new Association","line":13,"method":null,"native":false},{"column":5,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js","function":"new BelongsTo","line":25,"method":null,"native":false},{"column":25,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js","function":null,"line":70,"method":null,"native":false},{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":"defineAssociations","line":323,"method":null,"native":false},{"column":1,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":379,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:50:02 GMT+0530 (India Standard Time)","error":{"name":"SequelizeAssociationError"},"exception":true,"level":"error","message":"uncaughtException: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\nSequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157841.359},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":75677696,"heapUsed":46908472,"rss":*********},"pid":20268,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"SequelizeAssociationError: You have used the alias company in two separate associations. Aliased associations must have unique aliases.\n    at new Association (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js:13:13)\n    at new BelongsTo (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js:25:5)\n    at PaymentIn.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js:70:25)\n    at defineAssociations (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:323:13)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js:379:1)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:50:02","trace":[{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\base.js","function":"new Association","line":13,"method":null,"native":false},{"column":5,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\belongs-to.js","function":"new BelongsTo","line":25,"method":null,"native":false},{"column":25,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\node_modules\\sequelize\\lib\\associations\\mixin.js","function":null,"line":70,"method":null,"native":false},{"column":13,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":"defineAssociations","line":323,"method":null,"native":false},{"column":1,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\models\\index.js","function":null,"line":379,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:51:13 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157912.656},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56943624,"rss":117645312},"pid":14504,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:51:13","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:51:13 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157912.656},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79609856,"heapUsed":57091760,"rss":117256192},"pid":5132,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:51:13","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:52:35 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":157994.031},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56898696,"rss":117723136},"pid":12808,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:52:35","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:53:12 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158031.343},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65922,"external":3264136,"heapTotal":80396288,"heapUsed":55123920,"rss":117846016},"pid":3756,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:53:12","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:53:42 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158061.39},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56046128,"rss":117702656},"pid":21320,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:53:42","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:53:42 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158061.546},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80396288,"heapUsed":56429336,"rss":117841920},"pid":17096,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:53:42","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:53:42 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158061.562},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":57161040,"rss":117563392},"pid":8636,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:53:42","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:59:13 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158392.671},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79609856,"heapUsed":57209480,"rss":117264384},"pid":17596,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:59:13","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 14:59:49 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158428.265},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65922,"external":3264136,"heapTotal":80134144,"heapUsed":55101568,"rss":117665792},"pid":17824,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 14:59:49","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:00:51 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158490.359},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79818752,"heapUsed":59659136,"rss":118779904},"pid":5872,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:00:51","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:00:51 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158490.312},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79872000,"heapUsed":56159344,"rss":119361536},"pid":14996,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:00:51","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:00:51 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158490.437},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56362592,"rss":119312384},"pid":16520,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:00:51","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:00:53 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":158491.921},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56410816,"rss":118599680},"pid":10092,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:00:53","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:15:32 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":159370.843},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79872000,"heapUsed":57062176,"rss":117182464},"pid":21016,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:15:32","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:15:32 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":159370.875},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56178728,"rss":117743616},"pid":20724,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:15:32","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:15:32 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":159370.875},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79818752,"heapUsed":59771712,"rss":117293056},"pid":8752,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:15:32","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:15:32 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":159371.203},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79556608,"heapUsed":59725920,"rss":116797440},"pid":11524,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:15:32","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:22:21 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":159779.937},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79556608,"heapUsed":59767256,"rss":117125120},"pid":20196,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:22:21","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:22:21 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":159780.046},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79872000,"heapUsed":56161104,"rss":117444608},"pid":21252,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:22:21","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:22:21 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":159780.14},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79872000,"heapUsed":56352696,"rss":117202944},"pid":6200,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:22:21","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:22:21 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":159780.171},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79872000,"heapUsed":58652840,"rss":117243904},"pid":760,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:22:21","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:22:58 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":159817.031},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79872000,"heapUsed":57000136,"rss":116985856},"pid":20332,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:22:58","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:22:58 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":159817.031},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79818752,"heapUsed":59766144,"rss":117035008},"pid":20268,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:22:58","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:22:58 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":159817.109},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80396288,"heapUsed":56116328,"rss":117874688},"pid":12208,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:22:58","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:22:58 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":159817.265},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56206272,"rss":117469184},"pid":8104,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:22:58","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:32:04 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160363.687},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56173688,"rss":117329920},"pid":20296,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:32:04","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:32:04 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160363.718},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79818752,"heapUsed":59962816,"rss":116822016},"pid":2288,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:32:04","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:32:04 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160363.734},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79609856,"heapUsed":57318032,"rss":117121024},"pid":14500,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:32:04","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:32:05 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160363.921},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56143848,"rss":117399552},"pid":7340,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:32:05","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:32:06 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160365.437},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65922,"external":3264136,"heapTotal":79609856,"heapUsed":57791736,"rss":116924416},"pid":4944,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:32:06","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:32:37 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160396.718},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79818752,"heapUsed":59699432,"rss":117215232},"pid":2016,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:32:37","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:32:38 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160396.953},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79872000,"heapUsed":56198088,"rss":117383168},"pid":17208,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:32:38","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:32:38 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160396.953},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79085568,"heapUsed":59002056,"rss":116629504},"pid":17104,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:32:38","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:32:38 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160397.015},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79609856,"heapUsed":57185048,"rss":117035008},"pid":15984,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:32:38","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:32:40 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160398.937},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65922,"external":3264136,"heapTotal":80080896,"heapUsed":58568528,"rss":117370880},"pid":19812,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:32:40","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:32:50 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160409.625},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":65922,"external":3264136,"heapTotal":79347712,"heapUsed":57663464,"rss":116781056},"pid":12652,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\production-server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:32:50","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:33:26 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160445.671},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56418824,"rss":117268480},"pid":17088,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:33:26","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:33:26 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160445.812},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80920576,"heapUsed":54795928,"rss":118472704},"pid":7696,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:33:26","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:33:27 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160445.921},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79872000,"heapUsed":56261256,"rss":117133312},"pid":14628,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:33:27","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:33:27 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160445.984},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79872000,"heapUsed":57312192,"rss":117288960},"pid":17604,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:33:27","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:35:19 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160558.437},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80396288,"heapUsed":56518320,"rss":117706752},"pid":14000,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:35:19","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:35:25 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160564.25},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79872000,"heapUsed":56999440,"rss":118296576},"pid":20080,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:35:25","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:35:30 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160569.406},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56932128,"rss":117944320},"pid":16632,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:35:30","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:35:35 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160574.656},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79609856,"heapUsed":57529976,"rss":117399552},"pid":20792,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:35:35","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:36:25 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160624.281},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80396288,"heapUsed":56532368,"rss":114176000},"pid":20948,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:36:25","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:38:46 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160765.703},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79609856,"heapUsed":57399080,"rss":117231616},"pid":19852,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:38:46","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:40:04 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":160842.968},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":79872000,"heapUsed":57143664,"rss":117788672},"pid":8320,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:40:04","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
{"date":"Wed May 28 2025 15:57:06 GMT+0530 (India Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\nError: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","os":{"loadavg":[0,0,0],"uptime":161864.89},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js"],"cwd":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":66098,"external":3385354,"heapTotal":80134144,"heapUsed":56427376,"rss":113782784},"pid":19632,"uid":null,"version":"v20.19.1"},"service":"tracknew-api","stack":"Error: Cannot find module '../utils/email'\nRequire stack:\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\routes\\authRoutes.js\n- D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\n    at Module._load (node:internal/modules/cjs/loader:1043:27)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)\n    at require (node:internal/modules/helpers:182:18)\n    at Object.<anonymous> (D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js:5:15)\n    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n    at Module.load (node:internal/modules/cjs/loader:1275:32)\n    at Module._load (node:internal/modules/cjs/loader:1096:12)\n    at Module.require (node:internal/modules/cjs/loader:1298:19)","timestamp":"2025-05-28 15:57:06","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1212,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1043,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":182,"method":null,"native":false},{"column":15,"file":"D:\\My Projects\\Track new\\track new new\\tracknew-nodejs-backend\\src\\controllers\\authController.js","function":null,"line":5,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1529,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1613,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1275,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1096,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1298,"method":"require","native":false}]}
