const express = require('express');
const { body } = require('express-validator');
const stockMovementController = require('../controllers/stockMovementController');
const { protect, restrictTo } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Validation rules for creating stock movement
const createStockMovementValidation = [
  body('product_id')
    .notEmpty()
    .withMessage('Product ID is required')
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),
  
  body('warehouse_id')
    .notEmpty()
    .withMessage('Warehouse ID is required')
    .isInt({ min: 1 })
    .withMessage('Warehouse ID must be a positive integer'),
  
  body('movement_type')
    .notEmpty()
    .withMessage('Movement type is required')
    .isIn(['in', 'out', 'transfer', 'adjustment'])
    .withMessage('Invalid movement type'),
  
  body('transaction_type')
    .notEmpty()
    .withMessage('Transaction type is required')
    .isIn(['purchase', 'sales', 'return', 'adjustment', 'transfer', 'opening_stock', 'production', 'consumption'])
    .withMessage('Invalid transaction type'),
  
  body('reference_type')
    .optional()
    .isIn(['purchase_order', 'sales_order', 'invoice', 'service', 'adjustment', 'transfer', 'opening_stock'])
    .withMessage('Invalid reference type'),
  
  body('reference_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Reference ID must be a positive integer'),
  
  body('reference_number')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Reference number must be maximum 100 characters'),
  
  body('movement_date')
    .optional()
    .isISO8601()
    .withMessage('Movement date must be a valid date'),
  
  body('quantity')
    .notEmpty()
    .withMessage('Quantity is required')
    .isFloat({ min: 0.001 })
    .withMessage('Quantity must be greater than 0'),
  
  body('unit_cost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Unit cost must be a positive number'),
  
  body('batch_number')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Batch number must be maximum 100 characters'),
  
  body('serial_numbers')
    .optional()
    .isArray()
    .withMessage('Serial numbers must be an array'),
  
  body('expiry_date')
    .optional()
    .isISO8601()
    .withMessage('Expiry date must be a valid date'),
  
  body('from_warehouse_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('From warehouse ID must be a positive integer'),
  
  body('to_warehouse_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('To warehouse ID must be a positive integer'),
  
  body('reason')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Reason must be maximum 255 characters'),
  
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must be maximum 1000 characters')
];

// Validation rules for stock adjustment
const createStockAdjustmentValidation = [
  body('product_id')
    .notEmpty()
    .withMessage('Product ID is required')
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),
  
  body('warehouse_id')
    .notEmpty()
    .withMessage('Warehouse ID is required')
    .isInt({ min: 1 })
    .withMessage('Warehouse ID must be a positive integer'),
  
  body('adjustment_type')
    .notEmpty()
    .withMessage('Adjustment type is required')
    .isIn(['increase', 'decrease'])
    .withMessage('Adjustment type must be either increase or decrease'),
  
  body('quantity')
    .notEmpty()
    .withMessage('Quantity is required')
    .isFloat({ min: 0.001 })
    .withMessage('Quantity must be greater than 0'),
  
  body('reason')
    .notEmpty()
    .withMessage('Reason is required')
    .isLength({ min: 3, max: 255 })
    .withMessage('Reason must be between 3 and 255 characters'),
  
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must be maximum 1000 characters')
];

// Routes
router
  .route('/')
  .get(stockMovementController.getStockMovements)
  .post(createStockMovementValidation, validateRequest, stockMovementController.createStockMovement);

router
  .route('/stats')
  .get(stockMovementController.getStockMovementStats);

router
  .route('/stock-levels')
  .get(stockMovementController.getCurrentStockLevels);

router
  .route('/adjustments')
  .post(createStockAdjustmentValidation, validateRequest, restrictTo('admin', 'sub_admin', 'manager'), stockMovementController.createStockAdjustment);

router
  .route('/by-product/:product_id')
  .get(stockMovementController.getMovementsByProduct);

router
  .route('/by-warehouse/:warehouse_id')
  .get(stockMovementController.getMovementsByWarehouse);

router
  .route('/:id')
  .get(stockMovementController.getStockMovement);

module.exports = router;
