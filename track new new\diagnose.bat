@echo off
echo ========================================
echo    Track New - Diagnostic Tool
echo ========================================
echo.

echo Checking application status...
echo.

echo [1] Checking if server is running...
curl -s http://localhost:8000/api/health > nul
if %errorlevel% equ 0 (
    echo ✅ Server is running on port 8000
) else (
    echo ❌ Server is not running on port 8000
    echo.
    echo To start the server, run: npm start
    pause
    exit /b 1
)

echo.
echo [2] Testing API endpoint...
curl -s http://localhost:8000/api/test
echo.

echo.
echo [3] Checking frontend files...
if exist "tracknew-nodejs-backend\public\index.html" (
    echo ✅ Frontend files are present
) else (
    echo ❌ Frontend files are missing
    echo Run: npm run build
)

echo.
echo [4] Application URLs:
echo    Frontend: http://localhost:8000
echo    API Health: http://localhost:8000/api/health
echo    API Test: http://localhost:8000/api/test
echo.

echo Diagnosis complete!
pause
