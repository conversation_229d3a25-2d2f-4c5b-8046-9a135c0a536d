import React, { forwardRef } from 'react';
import { ExclamationCircleIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { classNames } from '../../utils/helpers';

const Input = forwardRef(({
  label,
  type = 'text',
  placeholder,
  value,
  onChange,
  onBlur,
  onFocus,
  error,
  helperText,
  required = false,
  disabled = false,
  readOnly = false,
  autoComplete,
  autoFocus = false,
  maxLength,
  minLength,
  pattern,
  size = 'md',
  variant = 'default',
  leftIcon: LeftIcon,
  rightIcon: RightIcon,
  leftElement,
  rightElement,
  className = '',
  inputClassName = '',
  labelClassName = '',
  errorClassName = '',
  helperClassName = '',
  showPasswordToggle = false,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const [isFocused, setIsFocused] = React.useState(false);

  const inputId = React.useId();
  const errorId = React.useId();
  const helperId = React.useId();

  const actualType = type === 'password' && showPassword ? 'text' : type;

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const variantClasses = {
    default: 'border-gray-300 focus:border-blue-500 focus:ring-blue-500',
    filled: 'border-transparent bg-gray-100 focus:bg-white focus:border-blue-500 focus:ring-blue-500',
    outlined: 'border-2 border-gray-300 focus:border-blue-500 focus:ring-0',
  };

  const baseInputClasses = classNames(
    'block w-full rounded-md shadow-sm transition-colors duration-200',
    'placeholder-gray-400 text-gray-900 dark:text-white',
    'dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400',
    'focus:outline-none focus:ring-1',
    sizeClasses[size],
    error 
      ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-500' 
      : variantClasses[variant],
    disabled && 'bg-gray-50 text-gray-500 cursor-not-allowed dark:bg-gray-800',
    readOnly && 'bg-gray-50 cursor-default dark:bg-gray-800',
    (LeftIcon || leftElement) && 'pl-10',
    (RightIcon || rightElement || (type === 'password' && showPasswordToggle) || error) && 'pr-10',
    inputClassName
  );

  const handleFocus = (e) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className={classNames('w-full', className)}>
      {/* Label */}
      {label && (
        <label 
          htmlFor={inputId}
          className={classNames(
            'block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1',
            required && "after:content-['*'] after:ml-0.5 after:text-red-500",
            labelClassName
          )}
        >
          {label}
        </label>
      )}

      {/* Input Container */}
      <div className="relative">
        {/* Left Icon */}
        {LeftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <LeftIcon className="h-5 w-5 text-gray-400" />
          </div>
        )}

        {/* Left Element */}
        {leftElement && (
          <div className="absolute inset-y-0 left-0 flex items-center">
            {leftElement}
          </div>
        )}

        {/* Input */}
        <input
          ref={ref}
          id={inputId}
          type={actualType}
          value={value}
          onChange={onChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          readOnly={readOnly}
          autoComplete={autoComplete}
          autoFocus={autoFocus}
          maxLength={maxLength}
          minLength={minLength}
          pattern={pattern}
          className={baseInputClasses}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={classNames(
            error && errorId,
            helperText && helperId
          )}
          {...props}
        />

        {/* Right Elements */}
        <div className="absolute inset-y-0 right-0 flex items-center">
          {/* Error Icon */}
          {error && (
            <ExclamationCircleIcon className="h-5 w-5 text-red-500 mr-3" />
          )}

          {/* Password Toggle */}
          {type === 'password' && showPasswordToggle && (
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="mr-3 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeSlashIcon className="h-5 w-5" />
              ) : (
                <EyeIcon className="h-5 w-5" />
              )}
            </button>
          )}

          {/* Right Icon */}
          {RightIcon && !error && (
            <div className="mr-3">
              <RightIcon className="h-5 w-5 text-gray-400" />
            </div>
          )}

          {/* Right Element */}
          {rightElement && !error && (
            <div className="mr-3">
              {rightElement}
            </div>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <p 
          id={errorId}
          className={classNames(
            'mt-1 text-sm text-red-600 dark:text-red-400',
            errorClassName
          )}
        >
          {error}
        </p>
      )}

      {/* Helper Text */}
      {helperText && !error && (
        <p 
          id={helperId}
          className={classNames(
            'mt-1 text-sm text-gray-500 dark:text-gray-400',
            helperClassName
          )}
        >
          {helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
