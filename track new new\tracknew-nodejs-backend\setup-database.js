// Setup database script
require('dotenv').config();
const { Sequelize } = require('sequelize');

console.log('🔧 Setting up TrackNew Database...');

async function setupDatabase() {
  try {
    // First, connect to PostgreSQL without specifying a database
    const adminConnection = new Sequelize('****************************************************/postgres?sslmode=disable', {
      dialect: 'postgres',
      logging: false,
      dialectOptions: {
        ssl: false
      }
    });

    console.log('🔄 Connecting to PostgreSQL server...');
    await adminConnection.authenticate();
    console.log('✅ Connected to PostgreSQL server!');

    // Check if database exists
    console.log('🔍 Checking if database "tracnew" exists...');
    const [results] = await adminConnection.query(
      "SELECT 1 FROM pg_database WHERE datname = 'tracnew'"
    );

    if (results.length === 0) {
      console.log('📝 Creating database "tracnew"...');
      await adminConnection.query('CREATE DATABASE tracnew');
      console.log('✅ Database "tracnew" created successfully!');
    } else {
      console.log('✅ Database "tracnew" already exists!');
    }

    await adminConnection.close();

    // Now connect to the tracnew database
    console.log('🔄 Connecting to tracnew database...');
    const appConnection = new Sequelize(process.env.DATABASE_URL, {
      dialect: 'postgres',
      logging: false,
      dialectOptions: {
        ssl: false
      }
    });

    await appConnection.authenticate();
    console.log('✅ Connected to tracnew database successfully!');

    // Test a simple query
    const [versionResult] = await appConnection.query('SELECT version()');
    console.log('📊 PostgreSQL Version:', versionResult[0].version.split(' ')[0] + ' ' + versionResult[0].version.split(' ')[1]);

    await appConnection.close();
    console.log('🎉 Database setup completed successfully!');

  } catch (error) {
    console.error('❌ Database setup failed:');
    console.error('Error:', error.message);
    
    if (error.message.includes('password authentication failed')) {
      console.error('💡 Hint: Check your database password in the connection string');
    } else if (error.message.includes('could not connect to server')) {
      console.error('💡 Hint: Check if the database server is running and accessible');
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.error('💡 Hint: The database needs to be created first');
    }
  }
}

setupDatabase();
