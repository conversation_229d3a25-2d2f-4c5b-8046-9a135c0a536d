const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Estimation = sequelize.define('Estimation', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    }
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'customers',
      key: 'id'
    }
  },
  lead_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'leads',
      key: 'id'
    }
  },
  estimation_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  reference_number: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Customer reference number'
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  estimation_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  valid_until: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Estimation validity date'
  },
  status: {
    type: DataTypes.ENUM('draft', 'sent', 'viewed', 'accepted', 'rejected', 'expired', 'converted', 'cancelled'),
    defaultValue: 'draft'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  estimation_type: {
    type: DataTypes.ENUM('product', 'service', 'project', 'mixed'),
    defaultValue: 'mixed'
  },
  subtotal: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  discount_type: {
    type: DataTypes.ENUM('percentage', 'fixed'),
    allowNull: true
  },
  discount_value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0.00
  },
  discount_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  tax_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  other_charges: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  total_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'INR'
  },
  exchange_rate: {
    type: DataTypes.DECIMAL(10, 4),
    defaultValue: 1.0000
  },
  payment_terms: {
    type: DataTypes.ENUM('cash', 'credit_7', 'credit_15', 'credit_30', 'credit_45', 'credit_60', 'credit_90', 'custom'),
    defaultValue: 'cash'
  },
  payment_terms_days: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Custom payment terms in days'
  },
  delivery_terms: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  delivery_time: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Estimated delivery time'
  },
  warranty_terms: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  terms_and_conditions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  internal_notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Internal notes not visible to customer'
  },
  customer_requirements: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  technical_specifications: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object of technical specifications'
  },
  project_scope: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  project_timeline: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of project milestones'
  },
  resource_requirements: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of required resources'
  },
  risk_factors: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  assumptions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  exclusions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  billing_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  shipping_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  contact_person: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  contact_email: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  sent_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Date when estimation was sent to customer'
  },
  viewed_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Date when customer first viewed the estimation'
  },
  response_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Date when customer responded'
  },
  accepted_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  rejected_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  rejection_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  converted_to: {
    type: DataTypes.ENUM('sales', 'proforma', 'service'),
    allowNull: true
  },
  converted_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'ID of the converted record'
  },
  conversion_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  follow_up_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  follow_up_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  revision_number: {
    type: DataTypes.INTEGER,
    defaultValue: 1
  },
  parent_estimation_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'estimations',
      key: 'id'
    },
    comment: 'Reference to original estimation for revisions'
  },
  template_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Template used for this estimation'
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of attachment file paths'
  },
  custom_fields: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object for custom field values'
  },
  is_template: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this estimation is saved as template'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  approved_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  approved_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'estimations',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['company_id']
    },
    {
      fields: ['customer_id']
    },
    {
      fields: ['lead_id']
    },
    {
      fields: ['estimation_number'],
      unique: true
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['estimation_date']
    },
    {
      fields: ['valid_until']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_template']
    },
    {
      fields: ['parent_estimation_id']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = Estimation;
