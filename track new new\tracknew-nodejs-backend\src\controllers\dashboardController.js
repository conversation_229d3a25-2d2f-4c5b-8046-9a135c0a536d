const {
  Service,
  Customer,
  User,
  Company,
  Sales,
  Product,
  Invoice,
  Payment,
  Expense,
  StockMovement
} = require('../models');
const { catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get dashboard overview data
const getDashboardOverview = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const { date_from, date_to } = req.query;

  // Date range for filtering
  const dateFilter = {};
  if (date_from || date_to) {
    dateFilter.created_at = {};
    if (date_from) {
      dateFilter.created_at[Op.gte] = new Date(date_from);
    }
    if (date_to) {
      dateFilter.created_at[Op.lte] = new Date(date_to);
    }
  }

  // Service statistics
  const totalServices = await Service.count({
    where: { company_id: companyId, ...dateFilter }
  });

  const pendingServices = await Service.count({
    where: { company_id: companyId, status: '0', ...dateFilter }
  });

  const inProgressServices = await Service.count({
    where: { company_id: companyId, status: '2', ...dateFilter }
  });

  const completedServices = await Service.count({
    where: { company_id: companyId, status: '4', ...dateFilter }
  });

  // Customer statistics
  const totalCustomers = await Customer.count({
    where: { company_id: companyId, ...dateFilter }
  });

  const activeCustomers = await Customer.count({
    where: { company_id: companyId, status: true, ...dateFilter }
  });

  // Today's scheduled services
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const todayServices = await Service.count({
    where: {
      company_id: companyId,
      scheduled_date: {
        [Op.gte]: today,
        [Op.lt]: tomorrow
      }
    }
  });

  // Overdue services
  const overdueServices = await Service.count({
    where: {
      company_id: companyId,
      scheduled_date: { [Op.lt]: today },
      status: { [Op.notIn]: ['4', '5'] }
    }
  });

  // High priority services
  const highPriorityServices = await Service.count({
    where: {
      company_id: companyId,
      priority: 'high',
      status: { [Op.notIn]: ['4', '5'] }
    }
  });

  // Revenue calculation (if actual_cost is available)
  const revenueData = await Service.findAll({
    where: {
      company_id: companyId,
      status: '4', // Completed services
      actual_cost: { [Op.ne]: null },
      ...dateFilter
    },
    attributes: [
      [Service.sequelize.fn('SUM', Service.sequelize.col('actual_cost')), 'total_revenue'],
      [Service.sequelize.fn('AVG', Service.sequelize.col('actual_cost')), 'avg_revenue']
    ],
    raw: true
  });

  const totalRevenue = revenueData[0]?.total_revenue || 0;
  const avgRevenue = revenueData[0]?.avg_revenue || 0;

  res.status(200).json({
    status: 'success',
    data: {
      services: {
        total: totalServices,
        pending: pendingServices,
        in_progress: inProgressServices,
        completed: completedServices,
        today_scheduled: todayServices,
        overdue: overdueServices,
        high_priority: highPriorityServices
      },
      customers: {
        total: totalCustomers,
        active: activeCustomers
      },
      revenue: {
        total: parseFloat(totalRevenue) || 0,
        average: parseFloat(avgRevenue) || 0
      }
    }
  });
});

// Get recent activities
const getRecentActivities = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const limit = parseInt(req.query.limit) || 10;

  // Recent services
  const recentServices = await Service.findAll({
    where: { company_id: companyId },
    include: [
      {
        model: Customer,
        as: 'customer',
        attributes: ['id', 'customer_name', 'customer_code']
      },
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name']
      }
    ],
    order: [['created_at', 'DESC']],
    limit,
    attributes: ['id', 'service_code', 'service_type', 'status', 'priority', 'created_at']
  });

  // Recent customers
  const recentCustomers = await Customer.findAll({
    where: { company_id: companyId },
    include: [
      {
        model: User,
        as: 'createdBy',
        attributes: ['id', 'name']
      }
    ],
    order: [['created_at', 'DESC']],
    limit: 5,
    attributes: ['id', 'customer_name', 'customer_code', 'customer_type', 'created_at']
  });

  res.status(200).json({
    status: 'success',
    data: {
      recent_services: recentServices,
      recent_customers: recentCustomers
    }
  });
});

// Get service status distribution
const getServiceStatusDistribution = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  const statusDistribution = await Service.findAll({
    where: { company_id: companyId },
    attributes: [
      'status',
      [Service.sequelize.fn('COUNT', Service.sequelize.col('id')), 'count']
    ],
    group: ['status'],
    raw: true
  });

  const priorityDistribution = await Service.findAll({
    where: { company_id: companyId },
    attributes: [
      'priority',
      [Service.sequelize.fn('COUNT', Service.sequelize.col('id')), 'count']
    ],
    group: ['priority'],
    raw: true
  });

  // Format the data for charts
  const statusData = statusDistribution.map(item => ({
    status: item.status,
    status_text: getStatusText(item.status),
    count: parseInt(item.count)
  }));

  const priorityData = priorityDistribution.map(item => ({
    priority: item.priority,
    priority_text: item.priority.charAt(0).toUpperCase() + item.priority.slice(1),
    count: parseInt(item.count)
  }));

  res.status(200).json({
    status: 'success',
    data: {
      status_distribution: statusData,
      priority_distribution: priorityData
    }
  });
});

// Get monthly service trends
const getServiceTrends = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const months = parseInt(req.query.months) || 6;

  // Calculate date range
  const endDate = new Date();
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - months);

  const monthlyData = await Service.findAll({
    where: {
      company_id: companyId,
      created_at: {
        [Op.gte]: startDate,
        [Op.lte]: endDate
      }
    },
    attributes: [
      [Service.sequelize.fn('DATE_FORMAT', Service.sequelize.col('created_at'), '%Y-%m'), 'month'],
      [Service.sequelize.fn('COUNT', Service.sequelize.col('id')), 'count']
    ],
    group: [Service.sequelize.fn('DATE_FORMAT', Service.sequelize.col('created_at'), '%Y-%m')],
    order: [[Service.sequelize.fn('DATE_FORMAT', Service.sequelize.col('created_at'), '%Y-%m'), 'ASC']],
    raw: true
  });

  // Format data for charts
  const trendsData = monthlyData.map(item => ({
    month: item.month,
    count: parseInt(item.count)
  }));

  res.status(200).json({
    status: 'success',
    data: {
      monthly_trends: trendsData
    }
  });
});

// Helper function to get status text
function getStatusText(status) {
  const statusMap = {
    '0': 'Pending',
    '1': 'Assigned',
    '2': 'In Progress',
    '3': 'On Hold',
    '4': 'Completed',
    '5': 'Closed'
  };
  return statusMap[status] || 'Unknown';
}

// Get sales dashboard widget
const getSalesDashboard = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const today = new Date();
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const last30Days = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

  // Sales metrics
  const salesMetrics = await Sales.findOne({
    where: {
      company_id: companyId,
      sales_date: { [Op.gte]: startOfMonth }
    },
    attributes: [
      [Sales.sequelize.fn('COUNT', Sales.sequelize.col('id')), 'monthly_sales'],
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'monthly_revenue'],
      [Sales.sequelize.fn('AVG', Sales.sequelize.col('total_amount')), 'avg_sale_value']
    ],
    raw: true
  });

  // Top selling products
  const topProducts = await Sales.findAll({
    where: {
      company_id: companyId,
      sales_date: { [Op.gte]: last30Days }
    },
    include: [{
      model: Product,
      as: 'product',
      attributes: ['id', 'product_name', 'product_code']
    }],
    attributes: [
      'product_id',
      [Sales.sequelize.fn('COUNT', Sales.sequelize.col('Sales.id')), 'sales_count'],
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'total_revenue']
    ],
    group: ['product_id', 'product.id'],
    order: [[Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'DESC']],
    limit: 5,
    raw: false
  });

  res.status(200).json({
    status: 'success',
    data: {
      monthly_sales: parseInt(salesMetrics?.monthly_sales || 0),
      monthly_revenue: parseFloat(salesMetrics?.monthly_revenue || 0),
      avg_sale_value: parseFloat(salesMetrics?.avg_sale_value || 0),
      top_products: topProducts
    }
  });
});

// Get financial dashboard widget
const getFinancialDashboard = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const today = new Date();
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

  // Revenue from sales and services
  const salesRevenue = await Sales.findOne({
    where: {
      company_id: companyId,
      sales_date: { [Op.gte]: startOfMonth }
    },
    attributes: [
      [Sales.sequelize.fn('SUM', Sales.sequelize.col('total_amount')), 'sales_revenue']
    ],
    raw: true
  });

  const serviceRevenue = await Service.findOne({
    where: {
      company_id: companyId,
      service_date: { [Op.gte]: startOfMonth }
    },
    attributes: [
      [Service.sequelize.fn('SUM', Service.sequelize.col('total_amount')), 'service_revenue']
    ],
    raw: true
  });

  // Monthly expenses
  const monthlyExpenses = await Expense.findOne({
    where: {
      company_id: companyId,
      expense_date: { [Op.gte]: startOfMonth }
    },
    attributes: [
      [Expense.sequelize.fn('SUM', Expense.sequelize.col('amount')), 'total_expenses']
    ],
    raw: true
  });

  // Outstanding invoices
  const outstandingInvoices = await Invoice.findOne({
    where: {
      company_id: companyId,
      payment_status: { [Op.in]: ['unpaid', 'partially_paid'] }
    },
    attributes: [
      [Invoice.sequelize.fn('COUNT', Invoice.sequelize.col('id')), 'count'],
      [Invoice.sequelize.fn('SUM', Invoice.sequelize.col('balance_amount')), 'amount']
    ],
    raw: true
  });

  const totalSalesRevenue = parseFloat(salesRevenue?.sales_revenue || 0);
  const totalServiceRevenue = parseFloat(serviceRevenue?.service_revenue || 0);
  const totalRevenue = totalSalesRevenue + totalServiceRevenue;
  const totalExpenses = parseFloat(monthlyExpenses?.total_expenses || 0);
  const netProfit = totalRevenue - totalExpenses;

  res.status(200).json({
    status: 'success',
    data: {
      total_revenue: totalRevenue,
      sales_revenue: totalSalesRevenue,
      service_revenue: totalServiceRevenue,
      total_expenses: totalExpenses,
      net_profit: netProfit,
      profit_margin: totalRevenue > 0 ? ((netProfit / totalRevenue) * 100).toFixed(2) : 0,
      outstanding_invoices: {
        count: parseInt(outstandingInvoices?.count || 0),
        amount: parseFloat(outstandingInvoices?.amount || 0)
      }
    }
  });
});

// Get inventory dashboard widget
const getInventoryDashboard = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  // Total products
  const totalProducts = await Product.count({
    where: { company_id: companyId }
  });

  // Low stock products
  const lowStockProducts = await Product.findAll({
    where: {
      company_id: companyId,
      current_stock: { [Op.lte]: Product.sequelize.col('minimum_stock') }
    },
    attributes: ['id', 'product_name', 'product_code', 'current_stock', 'minimum_stock'],
    limit: 10
  });

  // Out of stock products
  const outOfStockCount = await Product.count({
    where: {
      company_id: companyId,
      current_stock: { [Op.lte]: 0 }
    }
  });

  // Total stock value
  const stockValue = await Product.findOne({
    where: { company_id: companyId },
    attributes: [
      [Product.sequelize.fn('SUM',
        Product.sequelize.literal('current_stock * unit_price')
      ), 'total_value']
    ],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      total_products: totalProducts,
      low_stock_count: lowStockProducts.length,
      out_of_stock_count: outOfStockCount,
      total_stock_value: parseFloat(stockValue?.total_value || 0),
      low_stock_products: lowStockProducts
    }
  });
});

// Get top customers widget
const getTopCustomers = catchAsync(async (req, res) => {
  const { limit = 5 } = req.query;
  const companyId = req.user.company_id;
  const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

  const topCustomers = await Customer.findAll({
    where: { company_id: companyId },
    include: [{
      model: Sales,
      as: 'sales',
      where: {
        company_id: companyId,
        sales_date: { [Op.gte]: last30Days }
      },
      attributes: []
    }],
    attributes: [
      'id',
      'customer_name',
      'email',
      'mobile_number',
      [Customer.sequelize.fn('COUNT', Customer.sequelize.col('sales.id')), 'order_count'],
      [Customer.sequelize.fn('SUM', Customer.sequelize.col('sales.total_amount')), 'total_spent']
    ],
    group: ['Customer.id'],
    order: [[Customer.sequelize.fn('SUM', Customer.sequelize.col('sales.total_amount')), 'DESC']],
    limit: parseInt(limit),
    raw: false
  });

  res.status(200).json({
    status: 'success',
    data: {
      top_customers: topCustomers
    }
  });
});

// Get payment dashboard widget
const getPaymentDashboard = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;
  const today = new Date();
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const last7Days = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

  // Recent payments
  const recentPayments = await Payment.findOne({
    where: {
      company_id: companyId,
      payment_date: { [Op.gte]: last7Days }
    },
    attributes: [
      [Payment.sequelize.fn('SUM', Payment.sequelize.col('amount')), 'total_received']
    ],
    raw: true
  });

  // Monthly payments
  const monthlyPayments = await Payment.findOne({
    where: {
      company_id: companyId,
      payment_date: { [Op.gte]: startOfMonth }
    },
    attributes: [
      [Payment.sequelize.fn('SUM', Payment.sequelize.col('amount')), 'monthly_total'],
      [Payment.sequelize.fn('COUNT', Payment.sequelize.col('id')), 'monthly_count']
    ],
    raw: true
  });

  // Payment methods breakdown
  const paymentMethods = await Payment.findAll({
    where: {
      company_id: companyId,
      payment_date: { [Op.gte]: startOfMonth }
    },
    attributes: [
      'payment_method',
      [Payment.sequelize.fn('SUM', Payment.sequelize.col('amount')), 'total_amount'],
      [Payment.sequelize.fn('COUNT', Payment.sequelize.col('id')), 'count']
    ],
    group: ['payment_method'],
    raw: true
  });

  res.status(200).json({
    status: 'success',
    data: {
      recent_payments: parseFloat(recentPayments?.total_received || 0),
      monthly_total: parseFloat(monthlyPayments?.monthly_total || 0),
      monthly_count: parseInt(monthlyPayments?.monthly_count || 0),
      payment_methods: paymentMethods
    }
  });
});

module.exports = {
  getDashboardOverview,
  getRecentActivities,
  getServiceStatusDistribution,
  getServiceTrends,
  getSalesDashboard,
  getFinancialDashboard,
  getInventoryDashboard,
  getTopCustomers,
  getPaymentDashboard
};
