const { User, Company } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all settings for the company
const getSettings = catchAsync(async (req, res) => {
  const companyId = req.user.company_id;

  // Get company settings
  const company = await Company.findByPk(companyId, {
    attributes: [
      'id',
      'company_name',
      'email',
      'phone',
      'address',
      'city',
      'state',
      'country',
      'postal_code',
      'website',
      'logo',
      'currency',
      'timezone',
      'date_format',
      'time_format',
      'language',
      'fiscal_year_start',
      'tax_number',
      'registration_number',
      'settings'
    ]
  });

  if (!company) {
    return next(new AppError('Company not found', 404));
  }

  // Default settings structure
  const defaultSettings = {
    general: {
      company_name: company.company_name,
      email: company.email,
      phone: company.phone,
      address: company.address,
      city: company.city,
      state: company.state,
      country: company.country,
      postal_code: company.postal_code,
      website: company.website,
      logo: company.logo,
      currency: company.currency || 'USD',
      timezone: company.timezone || 'UTC',
      date_format: company.date_format || 'YYYY-MM-DD',
      time_format: company.time_format || '24',
      language: company.language || 'en',
      fiscal_year_start: company.fiscal_year_start || '01-01'
    },
    business: {
      tax_number: company.tax_number,
      registration_number: company.registration_number,
      business_type: 'service',
      industry: 'technology',
      employee_count: '1-10'
    },
    invoice: {
      invoice_prefix: 'INV',
      invoice_number_format: 'INV-{YYYY}-{MM}-{####}',
      invoice_terms: 'Payment due within 30 days',
      invoice_footer: 'Thank you for your business!',
      auto_send_invoice: false,
      payment_terms: 30,
      late_fee_percentage: 0,
      discount_percentage: 0
    },
    sales: {
      sales_prefix: 'SAL',
      sales_number_format: 'SAL-{YYYY}-{MM}-{####}',
      auto_convert_quote_to_sale: false,
      require_approval_for_sales: false,
      sales_commission_percentage: 0,
      default_payment_method: 'cash'
    },
    service: {
      service_prefix: 'SRV',
      service_number_format: 'SRV-{YYYY}-{MM}-{####}',
      auto_assign_services: false,
      require_approval_for_services: false,
      default_service_priority: 'medium',
      sla_hours: 24,
      auto_send_service_updates: true
    },
    inventory: {
      track_inventory: true,
      low_stock_threshold: 10,
      auto_reorder: false,
      reorder_quantity: 50,
      barcode_format: 'CODE128',
      enable_serial_numbers: false
    },
    notifications: {
      email_notifications: true,
      sms_notifications: false,
      push_notifications: true,
      whatsapp_notifications: false,
      notification_frequency: 'immediate',
      quiet_hours_start: '22:00',
      quiet_hours_end: '08:00'
    },
    security: {
      password_policy: {
        min_length: 8,
        require_uppercase: true,
        require_lowercase: true,
        require_numbers: true,
        require_symbols: false,
        password_expiry_days: 90
      },
      session_timeout: 30,
      max_login_attempts: 5,
      lockout_duration: 15,
      two_factor_auth: false,
      ip_whitelist: []
    },
    backup: {
      auto_backup: true,
      backup_frequency: 'daily',
      backup_time: '02:00',
      retention_days: 30,
      backup_location: 'local'
    },
    integrations: {
      payment_gateway: {
        enabled: false,
        provider: 'stripe',
        test_mode: true
      },
      sms_gateway: {
        enabled: false,
        provider: 'twilio',
        test_mode: true
      },
      email_service: {
        enabled: true,
        provider: 'smtp',
        smtp_host: '',
        smtp_port: 587,
        smtp_username: '',
        smtp_password: '',
        from_email: company.email,
        from_name: company.company_name
      }
    }
  };

  // Merge with existing settings
  const currentSettings = company.settings || {};
  const mergedSettings = mergeSettings(defaultSettings, currentSettings);

  res.status(200).json({
    status: 'success',
    data: {
      settings: mergedSettings
    }
  });
});

// Update settings
const updateSettings = catchAsync(async (req, res, next) => {
  const companyId = req.user.company_id;
  const { settings } = req.body;

  const company = await Company.findByPk(companyId);
  if (!company) {
    return next(new AppError('Company not found', 404));
  }

  // Get current settings
  const currentSettings = company.settings || {};

  // Merge new settings with existing ones
  const updatedSettings = mergeSettings(currentSettings, settings);

  // Update company record
  await company.update({
    settings: updatedSettings,
    updated_by: req.user.id
  });

  res.status(200).json({
    status: 'success',
    message: 'Settings updated successfully',
    data: {
      settings: updatedSettings
    }
  });
});

// Get specific setting category
const getSettingCategory = catchAsync(async (req, res, next) => {
  const { category } = req.params;
  const companyId = req.user.company_id;

  const company = await Company.findByPk(companyId, {
    attributes: ['settings']
  });

  if (!company) {
    return next(new AppError('Company not found', 404));
  }

  const settings = company.settings || {};
  const categorySettings = settings[category];

  if (!categorySettings) {
    return next(new AppError(`Settings category '${category}' not found`, 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      category,
      settings: categorySettings
    }
  });
});

// Update specific setting category
const updateSettingCategory = catchAsync(async (req, res, next) => {
  const { category } = req.params;
  const { settings: categorySettings } = req.body;
  const companyId = req.user.company_id;

  const company = await Company.findByPk(companyId);
  if (!company) {
    return next(new AppError('Company not found', 404));
  }

  // Get current settings
  const currentSettings = company.settings || {};

  // Update specific category
  currentSettings[category] = {
    ...currentSettings[category],
    ...categorySettings
  };

  // Update company record
  await company.update({
    settings: currentSettings,
    updated_by: req.user.id
  });

  res.status(200).json({
    status: 'success',
    message: `${category} settings updated successfully`,
    data: {
      category,
      settings: currentSettings[category]
    }
  });
});

// Reset settings to default
const resetSettings = catchAsync(async (req, res, next) => {
  const { category } = req.query;
  const companyId = req.user.company_id;

  const company = await Company.findByPk(companyId);
  if (!company) {
    return next(new AppError('Company not found', 404));
  }

  let updatedSettings = company.settings || {};

  if (category) {
    // Reset specific category
    delete updatedSettings[category];
  } else {
    // Reset all settings
    updatedSettings = {};
  }

  await company.update({
    settings: updatedSettings,
    updated_by: req.user.id
  });

  res.status(200).json({
    status: 'success',
    message: category ? `${category} settings reset to default` : 'All settings reset to default',
    data: {
      settings: updatedSettings
    }
  });
});

// Helper function to merge settings objects
function mergeSettings(defaultSettings, userSettings) {
  const merged = { ...defaultSettings };

  for (const key in userSettings) {
    if (userSettings.hasOwnProperty(key)) {
      if (typeof userSettings[key] === 'object' && userSettings[key] !== null && !Array.isArray(userSettings[key])) {
        merged[key] = mergeSettings(merged[key] || {}, userSettings[key]);
      } else {
        merged[key] = userSettings[key];
      }
    }
  }

  return merged;
}

module.exports = {
  getSettings,
  updateSettings,
  getSettingCategory,
  updateSettingCategory,
  resetSettings
};
