const { User, Company } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// Get all templates with filtering and pagination
const getTemplates = catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 15,
    search,
    type,
    category,
    is_active = true,
    sort_by = 'name',
    sort_order = 'ASC'
  } = req.query;

  const companyId = req.user.company_id;
  const offset = (page - 1) * limit;

  // Build where conditions
  const whereConditions = {
    company_id: companyId
  };

  // Add search functionality
  if (search) {
    whereConditions[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { subject: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add filters
  if (type) {
    whereConditions.type = type;
  }

  if (category) {
    whereConditions.category = category;
  }

  if (is_active !== undefined) {
    whereConditions.is_active = is_active === 'true';
  }

  // Simulate template records (since we don't have a Template model)
  // In a real implementation, you would query the Template model
  const templates = [];
  const totalCount = 0;

  // Calculate pagination
  const totalPages = Math.ceil(totalCount / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  res.status(200).json({
    status: 'success',
    data: {
      templates,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: totalCount,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    }
  });
});

// Get single template by ID
const getTemplate = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  // In a real implementation, you would query the Template model
  const template = null;

  if (!template) {
    return next(new AppError('Template not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      template
    }
  });
});

// Create new template
const createTemplate = catchAsync(async (req, res, next) => {
  const {
    name,
    description,
    type,
    category,
    subject,
    content,
    variables,
    is_active = true,
    is_default = false,
    language = 'en',
    tags
  } = req.body;

  const companyId = req.user.company_id;

  // Check for duplicate name within company
  // In a real implementation, you would check the Template model
  const existingTemplate = null;

  if (existingTemplate) {
    return next(new AppError('Template with this name already exists', 400));
  }

  // Create template data
  const templateData = {
    company_id: companyId,
    name,
    description,
    type,
    category,
    subject,
    content,
    variables,
    is_active,
    is_default,
    language,
    tags,
    created_by: req.user.id,
    created_at: new Date()
  };

  // In a real implementation, you would save to Template model
  // const template = await Template.create(templateData);

  res.status(201).json({
    status: 'success',
    message: 'Template created successfully',
    data: {
      template: {
        id: Date.now(), // Temporary ID
        ...templateData
      }
    }
  });
});

// Update template
const updateTemplate = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  // In a real implementation, you would find and update the Template model
  const template = null;

  if (!template) {
    return next(new AppError('Template not found', 404));
  }

  // Check for duplicate name (excluding current template)
  if (req.body.name) {
    // In a real implementation, you would check for duplicates
  }

  // Update template data
  const updatedData = {
    ...req.body,
    updated_by: req.user.id,
    updated_at: new Date()
  };

  res.status(200).json({
    status: 'success',
    message: 'Template updated successfully',
    data: {
      template: {
        id: parseInt(id),
        ...updatedData
      }
    }
  });
});

// Delete template
const deleteTemplate = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const companyId = req.user.company_id;

  // In a real implementation, you would find and delete the Template model
  const template = null;

  if (!template) {
    return next(new AppError('Template not found', 404));
  }

  // Check if template is being used
  // In a real implementation, you would check for dependencies

  res.status(200).json({
    status: 'success',
    message: 'Template deleted successfully'
  });
});

// Get template categories
const getTemplateCategories = catchAsync(async (req, res) => {
  const categories = [
    { value: 'welcome', label: 'Welcome Messages', icon: 'hand-wave' },
    { value: 'invoice', label: 'Invoice Templates', icon: 'receipt' },
    { value: 'payment', label: 'Payment Reminders', icon: 'credit-card' },
    { value: 'service', label: 'Service Updates', icon: 'cog' },
    { value: 'marketing', label: 'Marketing', icon: 'megaphone' },
    { value: 'notification', label: 'Notifications', icon: 'bell' },
    { value: 'reminder', label: 'Reminders', icon: 'clock' },
    { value: 'confirmation', label: 'Confirmations', icon: 'check-circle' },
    { value: 'follow_up', label: 'Follow-ups', icon: 'arrow-right' },
    { value: 'support', label: 'Support', icon: 'support' }
  ];

  res.status(200).json({
    status: 'success',
    data: {
      categories
    }
  });
});

// Get template variables
const getTemplateVariables = catchAsync(async (req, res) => {
  const variables = {
    customer: [
      { name: 'customer_name', description: 'Customer full name' },
      { name: 'customer_email', description: 'Customer email address' },
      { name: 'customer_phone', description: 'Customer phone number' },
      { name: 'customer_address', description: 'Customer address' }
    ],
    company: [
      { name: 'company_name', description: 'Company name' },
      { name: 'company_email', description: 'Company email' },
      { name: 'company_phone', description: 'Company phone' },
      { name: 'company_address', description: 'Company address' },
      { name: 'company_website', description: 'Company website' }
    ],
    invoice: [
      { name: 'invoice_number', description: 'Invoice number' },
      { name: 'invoice_date', description: 'Invoice date' },
      { name: 'due_date', description: 'Payment due date' },
      { name: 'total_amount', description: 'Total invoice amount' },
      { name: 'balance_amount', description: 'Outstanding balance' }
    ],
    service: [
      { name: 'service_number', description: 'Service request number' },
      { name: 'service_date', description: 'Service date' },
      { name: 'service_status', description: 'Current service status' },
      { name: 'technician_name', description: 'Assigned technician name' }
    ],
    general: [
      { name: 'current_date', description: 'Current date' },
      { name: 'current_time', description: 'Current time' },
      { name: 'user_name', description: 'Current user name' }
    ]
  };

  res.status(200).json({
    status: 'success',
    data: {
      variables
    }
  });
});

// Preview template with sample data
const previewTemplate = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { sample_data } = req.body;
  const companyId = req.user.company_id;

  // In a real implementation, you would find the template
  const template = null;

  if (!template) {
    return next(new AppError('Template not found', 404));
  }

  // Process template with sample data
  const processedContent = processTemplateContent(template.content, sample_data);
  const processedSubject = processTemplateContent(template.subject, sample_data);

  res.status(200).json({
    status: 'success',
    data: {
      preview: {
        subject: processedSubject,
        content: processedContent,
        type: template.type,
        variables_used: extractVariables(template.content)
      }
    }
  });
});

// Send test template
const sendTestTemplate = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { test_email, test_phone, sample_data } = req.body;
  const companyId = req.user.company_id;

  // In a real implementation, you would:
  // 1. Find the template
  // 2. Process the template with sample data
  // 3. Send via email/SMS service

  res.status(200).json({
    status: 'success',
    message: 'Test template sent successfully',
    data: {
      sent_to: test_email || test_phone,
      sent_at: new Date()
    }
  });
});

// Helper function to process template content
function processTemplateContent(content, data) {
  if (!content || !data) return content;

  let processedContent = content;

  // Replace variables in format {{variable_name}}
  Object.keys(data).forEach(key => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    processedContent = processedContent.replace(regex, data[key] || '');
  });

  return processedContent;
}

// Helper function to extract variables from template
function extractVariables(content) {
  if (!content) return [];

  const variableRegex = /{{(\w+)}}/g;
  const variables = [];
  let match;

  while ((match = variableRegex.exec(content)) !== null) {
    if (!variables.includes(match[1])) {
      variables.push(match[1]);
    }
  }

  return variables;
}

module.exports = {
  getTemplates,
  getTemplate,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  getTemplateCategories,
  getTemplateVariables,
  previewTemplate,
  sendTestTemplate
};
